# Editor configuration, see https://editorconfig.org
root = true

[*]
charset = utf-8
indent_style = space
indent_size = 2
tab_width = 2
insert_final_newline = true
trim_trailing_whitespace = true
end_of_line = lf

[*.java]
indent_size = 4
tab_width = 4

[*.md]
max_line_length = off
trim_trailing_whitespace = false

[*.feature]
indent_size = 2
tab_width = 2

[*.gradle]
indent_size = 4
tab_width = 4

[*.groovy]
indent_size = 4
tab_width = 4

[*.sh]
indent_size = 2
tab_width = 2
end_of_line = lf

[*.{yml,yaml}]
indent_size = 2
tab_width = 2

[*.sql]
indent_size = 4
tab_width = 4

[*.json]
indent_size = 2
tab_width = 2

[*.xml]
indent_size = 4
tab_width = 4
