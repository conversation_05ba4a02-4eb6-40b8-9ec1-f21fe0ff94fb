package com.vitality.ai.mail.controller;

import com.vitality.ai.mail.delegate.MailDelegate;
import com.vitality.ai.mail.domain.mail.MailRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/mall")
@RequiredArgsConstructor
@Slf4j
public class MailController {

    private final MailDelegate mailDelegate;

    @PostMapping("/submit")
    public ResponseEntity<String> submit(@RequestBody MailRequest request) {
        mailDelegate.processRequest(request);
        return ResponseEntity.ok("Mocking It");
    }


}
