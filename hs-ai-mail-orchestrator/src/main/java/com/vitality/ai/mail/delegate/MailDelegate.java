package com.vitality.ai.mail.delegate;

import com.vitality.ai.mail.domain.mail.MailRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MailDelegate {

    public String processRequest(MailRequest request) {
        //Pop onto a topic and have this be managed.
        //Save a message to the databae on what was handled.
        return "Processed request: " + request;
    }

}
