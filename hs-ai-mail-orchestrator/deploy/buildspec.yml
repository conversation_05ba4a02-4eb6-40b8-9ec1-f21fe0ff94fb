version: 0.2
env:
  secrets-manager:
    GITHUB_TOKEN_KEY_VALUE: arn:aws:secretsmanager:us-east-1:905860299560:secret:github_personal_access_token-jPYgS8
    VID: "veracode_credentials:VID"
    VKEY: "veracode_credentials:VKEY"
    SONAR_TOKEN: "arn:aws:secretsmanager:us-east-1:905860299560:secret:v3_sonarqube-HTIpL6:token"
    SONAR_HOST_URL: "arn:aws:secretsmanager:us-east-1:905860299560:secret:v3_sonarqube-HTIpL6:url"
phases:
  install:
    runtime-versions:
      java: corretto21
      gradle: 8.3
  pre_build:
    commands:
      - export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain d2hp-v1 --domain-owner 905860299560 --query authorizationToken --output text --region us-east-1`
      - export AWS_ECR_PASS=$(aws ecr get-login-password)
      - echo $AWS_ECR_PASS | docker login --username AWS --password-stdin 905860299560.dkr.ecr.us-east-1.amazonaws.com
      - echo $AWS_ECR_PASS | helm registry login --username AWS --password-stdin 905860299560.dkr.ecr.us-east-1.amazonaws.com
      - export GITHUB_TOKEN=$(echo ${GITHUB_TOKEN_KEY_VALUE} | awk -F'"' '{print $4}')
      - export DOCKER_IMAGE_TAG=$(git rev-parse HEAD)
  build:
    commands:
      - gradle :hs-ai-mail-orchestrator:clean
      - gradle :hs-ai-mail-orchestrator:build
      - gradle :hs-ai-mail-orchestrator:jib
      - gradle :hs-ai-mail-orchestrator:publish
      -
      - cd $CODEBUILD_SRC_DIR_DEPLOY
      - ./bin/update-and-package-charts.sh hs-ai-mail-orchestrator charts/health-solutions oci://905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/ $DOCKER_IMAGE_TAG $GITHUB_TOKEN
cache:
  paths:
    - '/root/.m2/**/*'
    - '/root/.gradle/**/*'
