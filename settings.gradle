
pluginManagement {
    repositories {
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
        mavenLocal()
        maven {
            url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
            credentials {
                username "aws"
                password System.env.CODEARTIFACT_AUTH_TOKEN
            }
        }
        gradlePluginPortal()
    }
    resolutionStrategy {
        eachPlugin {
            switch (requested.id.id) {
                case "productivity-plugin":
                    useModule('za.co.discovery.health.productivity:productivity-plugin:20240830.0.0-RELEASE')
                    break
            }
        }
    }
}

rootProject.name = 'hs-ai-ops'


include ':hs-common-ai-starter'
include ':hs-platform-ai'
include ':hs-ai-mail-orchestrator'
include ':hs-ai-ivr-orchestrator'
include ':hs-ai-chat-orchestrator'
include ':hs-ai-chat-spring-cloud-gateway'
include ':hs-ai-chat-agent-angular'
include ':hs-ai-document-scanner'
include ':hs-ai-form-generator'
