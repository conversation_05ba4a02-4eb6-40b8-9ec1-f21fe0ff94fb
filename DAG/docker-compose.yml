version: '3.9'

services:
  postgres:
    image: postgres:15
    container_name: postgres
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      POSTGRES_DB: graph_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin"]
      interval: 10s
      timeout: 5s
      retries: 5
    entrypoint: >
      bash -c "
      docker-entrypoint.sh postgres &&
      psql -U admin -d graph_db -c 'CREATE EXTENSION IF NOT EXISTS postgis;' &&
      psql -U admin -d graph_db -c 'CREATE EXTENSION IF NOT EXISTS pg_trgm;'"

  vectordb:
    image: pgvector/pgvector:pg16
    container_name: pgvector_db
    environment:
      # Adjust these values as needed
      POSTGRES_USER: vectoruser
      POSTGRES_PASSWORD: vectorpass
      POSTGRES_DB: vectordb
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./pgvector:/docker-entrypoint-initdb.d
    ports:
      # Map the container’s 5432 port to the host’s 5432 port
      - "5433:5432"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "vectoruser"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped


  keycloak:
    image: quay.io/keycloak/keycloak:latest
    container_name: keycloak
    environment:
      KC_DB: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: admin
      KC_DB_PASSWORD: admin
      KC_HOSTNAME: localhost
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    command: ["start-dev"]
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8082:8080"
#    healthcheck:
#      test: ["CMD-SHELL", "curl -f http://localhost:8080/realms/master || exit 1"]
#      interval: 10s
#      timeout: 5s
#      retries: 5

  redis:
    image: redis:7
    container_name: redis
    ports:
      - "6379:6379"

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis_commander
    environment:
      REDIS_HOSTS: redis:6379
    depends_on:
      - redis
    ports:
      - "8081:8081"

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    volumes:
      - pgadmin_data:/var/lib/pgadmin

  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    command: -config.file=/etc/loki/local-config.yml
    ports:
      - "3100:3100"
    volumes:
      - ./loki-config.yml:/etc/loki/local-config.yml
      - ./loki-data:/loki
      - ./loki-compactor:/loki/compactor
      - ./loki-wal:/wal

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    depends_on:
      - loki
      - pushgateway

  pushgateway:
    image: prom/pushgateway:latest
    container_name: pushgateway
    ports:
      - "9091:9091"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9091/metrics || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5      

  grafana:
    image: grafana/grafana:10.0.1
    container_name: grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    ports:
      - "3000:3000"
    volumes:
      - grafana_storage:/var/lib/grafana
    depends_on:
      - loki
      - prometheus

  chroma:
    image: ghcr.io/chroma-core/chroma:latest
    ports:
      - "8100:8000"

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.2
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - xpack.security.http.ssl.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
    # Uncomment the following if you're running on Linux and want to avoid permission issues
    # user: "1000:1000"

volumes:
  postgres_data:
  pgadmin_data:
  grafana_storage:
  pgdata:
