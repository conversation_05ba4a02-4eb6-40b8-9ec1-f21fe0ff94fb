### Test: Basic chat prompt with summarize template
POST http://localhost:34000/v3/assistant/chat/summarize-text?sessionId=test-session-1
Content-Type: application/json

"This is a long article explaining the rise of AI in enterprise software. It includes trends, risks, and examples."

###

### Test: Cancellation Policy
POST http://localhost:34000/v3/assistant/chat/summarize-text?sessionId=test-session-2
Content-Type: application/json

"What is the cancellation policy?"

###

### Test: Policy Followup
POST http://localhost:34000/v3/assistant/chat/summarize-text?sessionId=test-session-2
Content-Type: application/json

"Is there a 3 day cancellation policy?"

###

### Test: Policy Followup
POST http://localhost:34000/v3/assistant/chat/chat?sessionId=test-session-2
Content-Type: application/json

"Summarize the cancellation policy in 3 sentences."

###

### Test: Policy Followup
POST http://localhost:34000/v3/assistant/chat/chat?sessionId=test-session-3
Content-Type: application/json

"Use of Vehicle?"

###

### Test: Policy Liability
POST http://localhost:34000/v3/assistant/chat/summarize-text?sessionId=test-session-2
Content-Type: application/json

"What are the rules around Use of Vehicle?"

###


### Test: Chat without prompt template (if endpoint exists)
POST http://localhost:34000/v3/assistant/chat
Content-Type: application/json

"What's the capital of Germany?"
