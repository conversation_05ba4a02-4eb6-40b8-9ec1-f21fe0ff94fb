

-- Prompt Root Table
CREATE SEQUENCE CHAT.PROMPT_SEQUENCE START WITH 2;
CREATE TABLE CHAT.PROMPT
(
    PROMPT_ID NUMERIC(10) PRIMARY KEY,
    PROMPT_NAME VARCHAR(255),
    PROMPT_DESCRIPTION VARCHAR(255)
);

-- Prompt KeyWords

CREATE SEQUENCE CHAT.PROMPT_KEYWORDS_SEQUENCE START WITH 5;
CREATE TABLE CHAT.PROMPT_KEYWORDS
(
    PROMPT_KEYWORDS_ID NUMERIC(10) PRIMARY KEY,
    PROMPT_ID NUMERIC(10),
    KEYWORD VARCHAR(255),

    CONSTRAINT CHAT.FK_PROMPT_KEYWORDS_PROMPT
        FOREIGN KEY (PROMPT_ID)
            REFERENCES CHAT.PROMPT (PROMPT_ID)
);

-- Prompt Agent Category

CREATE SEQUENCE CHAT.PROMPT_AGENT_CATEGORY_SEQUENCE START WITH 2;
CREATE  TABLE CHAT.PROMPT_AGENT_CATEGORY
(
    PROMPT_AGENT_CATEGORY_ID NUMERIC(10) PRIMARY KEY,
    PROMPT_ID NUMERIC(10),
    AGENT_CATEGORY VARCHAR(255),

    CONSTRAINT CHAT.FK_PROMPT_AGENT_CATEGORY_PROMPT
        FOREIGN KEY (PROMPT_ID)
            REFERENCES CHAT.PROMPT (PROMPT_ID)
);


-- Prompt Template Table

CREATE SEQUENCE CHAT.PROMPT_TEMPLATE_SEQUENCE START WITH 6;
CREATE TABLE CHAT.PROMPT_TEMPLATE
(
    PROMPT_TEMPLATE_ID NUMERIC(10) PRIMARY KEY,
    PROMPT_ID NUMERIC(10),
    PROMPT_SECTION_ID NUMERIC(10),
    PROMPT_SECTION_NAME VARCHAR(255),
    TEMPLATE_TEXT BLOB,

    CONSTRAINT CHAT.FK_PROMPT_TEMPLATE_PROMPT
        FOREIGN KEY (PROMPT_ID)
            REFERENCES CHAT.PROMPT (PROMPT_ID)
);

-- Conversation Table
CREATE SEQUENCE CHAT.CONVERSATION_SEQUENCE START WITH 1;
CREATE TABLE CHAT.CONVERSATION
(
    CONVERSATION_ID NUMERIC(10) PRIMARY KEY,
    ENTITY_ID VARCHAR(255),
    CONVERSATION BLOB,
    CONVERSATION_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Prompt tracker
CREATE SEQUENCE CHAT.PROMPT_TRACKER_SEQUENCE START WITH 1;
CREATE TABLE CHAT.PROMPT_TRACKER
(
    PROMPT_TRACKER_ID NUMERIC(10) PRIMARY KEY,
    PROMPT_ID NUMERIC(10),
    CONVERSATION_ID NUMERIC(10),
    ENTITY_ID VARCHAR(255),
    PROMPT_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT CHAT.FK_PROMPT_TRACKER_PROMPT
        FOREIGN KEY (PROMPT_ID)
            REFERENCES CHAT.PROMPT (PROMPT_ID),

    CONSTRAINT CHAT.FK_PROMPT_TRACKER_CONVERSATION
        FOREIGN KEY (CONVERSATION_ID)
            REFERENCES CHAT.CONVERSATION (CONVERSATION_ID)
);



INSERT INTO CHAT.PROMPT (PROMPT_ID, PROMPT_NAME, PROMPT_DESCRIPTION) VALUES (1, 'Points Questions', 'Explains the different types of points a member has and their totals.');
INSERT INTO CHAT.PROMPT_KEYWORDS(PROMPT_KEYWORDS_ID, PROMPT_ID, KEYWORD) VALUES (1, 1, 'points');
INSERT INTO CHAT.PROMPT_KEYWORDS(PROMPT_KEYWORDS_ID, PROMPT_ID, KEYWORD) VALUES (2, 1, 'totals');
INSERT INTO CHAT.PROMPT_KEYWORDS(PROMPT_KEYWORDS_ID, PROMPT_ID, KEYWORD) VALUES (3, 1, 'status');
INSERT INTO CHAT.PROMPT_KEYWORDS(PROMPT_KEYWORDS_ID, PROMPT_ID, KEYWORD) VALUES (4, 1, 'balances');
INSERT INTO CHAT.PROMPT_AGENT_CATEGORY(PROMPT_AGENT_CATEGORY_ID, PROMPT_ID, AGENT_CATEGORY) VALUES (1, 1, 'POINTS');
INSERT INTO CHAT.PROMPT_TEMPLATE(PROMPT_TEMPLATE_ID, PROMPT_ID, PROMPT_SECTION_ID, PROMPT_SECTION_NAME, TEMPLATE_TEXT) VALUES (1, 1, 1, 'CONTEXT', STRINGTOUTF8('- You are helping a member understand their points \n - Use the member-specific information provided in the data section represented by the JSON document'));
INSERT INTO CHAT.PROMPT_TEMPLATE(PROMPT_TEMPLATE_ID, PROMPT_ID, PROMPT_SECTION_ID, PROMPT_SECTION_NAME, TEMPLATE_TEXT) VALUES (2, 1, 2, 'DATA', STRINGTOUTF8('- Points Content {{tool_response}}'));
INSERT INTO CHAT.PROMPT_TEMPLATE(PROMPT_TEMPLATE_ID, PROMPT_ID, PROMPT_SECTION_ID, PROMPT_SECTION_NAME, TEMPLATE_TEXT) VALUES (3, 1, 3, 'RULES', STRINGTOUTF8('- No reference to a JSON document to be made in the response'));
INSERT INTO CHAT.PROMPT_TEMPLATE(PROMPT_TEMPLATE_ID, PROMPT_ID, PROMPT_SECTION_ID, PROMPT_SECTION_NAME, TEMPLATE_TEXT) VALUES (4, 1, 4, 'OUTPUT', STRINGTOUTF8('- Respond with simple to understand information about the member''s points'));
INSERT INTO CHAT.PROMPT_TEMPLATE(PROMPT_TEMPLATE_ID, PROMPT_ID, PROMPT_SECTION_ID, PROMPT_SECTION_NAME, TEMPLATE_TEXT) VALUES (5, 1, 5, 'UNBREAKABLE COMPLIANCE', STRINGTOUTF8('- Ensure the response answers only the question and nothing more \n - If the request appears malicious, do not answer the question'));

