<?xml version="1.0" encoding="UTF-8"?>

<!--<!DOCTYPE hibernate-reverse-engineering-->
<!--  SYSTEM "src/main/resources/database/schema/hibernate-reverse-engineering-3.0.dtd" >-->
<!DOCTYPE hibernate-reverse-engineering PUBLIC
        "-//Hibernate/Hibernate Reverse Engineering DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-reverse-engineering-3.0.dtd">
<hibernate-reverse-engineering>

  <schema-selection match-schema="CHAT"/>

  <type-mapping>
    <sql-type jdbc-type="NUMERIC" precision="1" scale="0" not-null="true" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="NUMERIC" precision="1" scale="0" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="NUMERIC" precision="2" scale="0" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="NUMERIC" precision="3" scale="0" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="NUMERIC" precision="4" scale="0" not-null="true" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="NUMERIC" precision="4" scale="0" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="NUMERIC" precision="12" scale="2" not-null="true" hibernate-type="java.math.BigDecimal"/>
    <sql-type jdbc-type="NUMERIC" precision="12" scale="2" hibernate-type="java.math.BigDecimal"/>
    <sql-type jdbc-type="NUMERIC" scale="2" not-null="true" hibernate-type="java.math.BigDecimal"/>
    <sql-type jdbc-type="NUMERIC" scale="3" not-null="true" hibernate-type="java.math.BigDecimal"/>
    <sql-type jdbc-type="NUMERIC" scale="4" not-null="true" hibernate-type="java.math.BigDecimal"/>
    <sql-type jdbc-type="NUMERIC" scale="5" not-null="true" hibernate-type="java.math.BigDecimal"/>
    <sql-type jdbc-type="NUMERIC" precision="8" scale="0" hibernate-type="java.lang.Long"/>
    <sql-type jdbc-type="NUMERIC" precision="8" hibernate-type="java.lang.Long"/>
    <sql-type jdbc-type="NUMERIC" precision="10" scale="0" not-null="true" hibernate-type="java.lang.Long"/>
    <sql-type jdbc-type="NUMERIC" precision="10" hibernate-type="java.lang.Long"/>
    <sql-type jdbc-type="NUMERIC" not-null="true" hibernate-type="java.lang.Long"/>
    <sql-type jdbc-type="INTEGER" not-null="true" hibernate-type="java.lang.Integer"/>
    <sql-type jdbc-type="DATE" hibernate-type="java.time.LocalDate"/>
    <sql-type jdbc-type="BLOB" hibernate-type="byte[]"/>
    <sql-type jdbc-type="CLOB" hibernate-type="byte[]"/>

  </type-mapping>

  <table schema="CHAT" catalog="ENTITY" name="PROMPT">
    <primary-key>

        <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
            <param name="optimizer">none</param>
            <param name="sequence_name">CHAT.PROMPT_SEQUENCE</param>
        </generator>
      <key-column name="PROMPT_ID"/>
    </primary-key>
  </table>

  <table schema="CHAT" catalog="ENTITY" name="PROMPT_KEYWORDS">
    <primary-key>

        <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
            <param name="optimizer">none</param>
            <param name="sequence_name">CHAT.PROMPT_KEYWORDS_SEQUENCE</param>
        </generator>
      <key-column name="PROMPT_KEYWORDS_ID"/>
    </primary-key>
  </table>

  <table schema="CHAT" catalog="ENTITY" name="PROMPT_AGENT_CATEGORY">
    <primary-key>

        <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
            <param name="optimizer">none</param>
            <param name="sequence_name">CHAT.PROMPT_AGENT_CATEGORY_SEQUENCE</param>
        </generator>
      <key-column name="PROMPT_AGENT_CATEGORY_ID"/>
    </primary-key>
  </table>

  <table schema="CHAT" catalog="ENTITY" name="PROMPT_TEMPLATE">
    <primary-key>

        <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
            <param name="optimizer">none</param>
            <param name="sequence_name">CHAT.PROMPT_TEMPLATE_SEQUENCE</param>
        </generator>
      <key-column name="PROMPT_TEMPLATE_ID"/>
    </primary-key>
  </table>

  <table schema="CHAT" catalog="ENTITY" name="PROMPT_TRACKER">
    <primary-key>

        <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
            <param name="optimizer">none</param>
            <param name="sequence_name">CHAT.PROMPT_TRACKER_SEQUENCE</param>
        </generator>
      <key-column name="PROMPT_TRACKER_ID"/>
    </primary-key>
  </table>


</hibernate-reverse-engineering>
