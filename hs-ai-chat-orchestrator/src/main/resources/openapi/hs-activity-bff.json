{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://integrationtest.powerofvitality.com/v3/bff/activity"}, {"url": "http://gateway-external.ch2.tvgtkg.com/v3/bff/activity"}], "paths": {"/test": {"post": {"tags": ["test-controller"], "operationId": "test", "parameters": [{"name": "translate-to", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestEntity"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TestEntity"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/activity/is-completed": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "isActivityCompleted", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityCompletedRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/activity/complete": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "onActivityCompletion", "parameters": [{"name": "activityCompletionRequestMessage", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityCompletionRequestMessageDto"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/activity/complete/loop-legacy": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "onActivityMnemonicCompletion", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivityMnemonicCompletionRequestMessageDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/program-enrolment-cohort-activity/activity/complete/bulk": {"post": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "onActivityBulkCompletion", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkActivityCompletionRequestMessageDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/onboarding/reset": {"post": {"tags": ["onboarding-controller"], "operationId": "resetOnboarding", "responses": {"200": {"description": "OK"}}}}, "/api/v1/enrolment/enrol": {"post": {"tags": ["enrolment-controller"], "operationId": "enrol", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolment"}}}}}}}}, "/api/v1/activity/api/programCategoryEntity/celebrate": {"post": {"tags": ["activity-category-controller"], "operationId": "celebrateCategories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CelebrateCategoriesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/sap/status": {"get": {"tags": ["separate-adult-controller"], "operationId": "getStatus", "parameters": [{"name": "recalc", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusResponse"}}}}}}}, "/api/v1/program/activity/standard-activities": {"get": {"tags": ["program-activity-controller"], "operationId": "getStandardActivities", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program/activity/custom-activities": {"get": {"tags": ["program-activity-controller"], "operationId": "getCustomActivities", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program/activity/custom-activities/{employerId}": {"get": {"tags": ["program-activity-controller"], "operationId": "getCustomActivitiesForEmployer", "parameters": [{"name": "employerId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program/activity/category/{category}": {"get": {"tags": ["program-activity-controller"], "operationId": "getCategoryActivities", "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityResponse"}}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/{programEnrolmentCohortActivityId}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getCohortActivityDtoById", "parameters": [{"name": "programEnrolmentCohortActivityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fetchRewards", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedProgramEnrolmentCohortActivityWithReward"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/name/{programEnrolmentCohortActivityName}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getCohortActivityDtoByName", "parameters": [{"name": "programEnrolmentCohortActivityName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedProgramEnrolmentCohortActivity"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/mnemonic/{programEnrolmentCohortActivityMnemonic}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getCohortActivityDtoByMnemonic", "parameters": [{"name": "programEnrolmentCohortActivityMnemonic", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedProgramEnrolmentCohortActivity"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/filter": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "filterCohortActivityDto", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"]}}, {"name": "date", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "categoryName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "fetchRewards", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTOPageable"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/cta/mnemonic/device/{device}/with-params": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getCtaWithParamsForBenefitType", "parameters": [{"name": "device", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "mnemonic", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "benefitType", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CallToActionDeviceActionDTO"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/dto/activity-id/{activityId}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getByProgramEnrolmentCohortActivityByActivityId", "parameters": [{"name": "activityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fetchRewards", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedProgramEnrolmentCohortActivityWithReward"}}}}}}}, "/api/v1/program-enrolment-cohort-activity/assessment/name/{assessmentName}": {"get": {"tags": ["program-enrolment-cohort-activity-controller"], "operationId": "getAssessmentActivityByAssessmentName", "parameters": [{"name": "assessmentName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivity"}}}}}}}, "/api/v1/onboarding/get-onboarding-activity": {"get": {"tags": ["onboarding-controller"], "operationId": "getOnboardingActivity", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtendedProgramEnrolmentCohortActivity"}}}}}}}, "/api/v1/onboarding/available": {"get": {"tags": ["onboarding-controller"], "operationId": "isOnboardingAssessmentAvailable", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OnboardingAssessmentAvailabilityDTO"}}}}}}}, "/api/v1/glp-1": {"get": {"tags": ["glp-1-activities-controller"], "operationId": "getActivitiesByAttribute", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTOPageable"}}}}}}}, "/api/v1/category/filter": {"get": {"tags": ["category-controller"], "operationId": "getCategories", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ActivityCategoryFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/api/v1/activity": {"get": {"tags": ["activity-controller"], "operationId": "getEmployerActivities", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedDTOActivityDTO"}}}}}}}, "/api/v1/activity/api/programCategoryEntity/{categoryName}/activities": {"get": {"tags": ["activity-category-controller"], "operationId": "categoryActivitiesByName", "parameters": [{"name": "categoryName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryActivities"}}}}}}}, "/api/v1/activity/api/programCategoryEntity/list": {"get": {"tags": ["activity-category-controller"], "operationId": "listUserCategories", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProgramActivityCategoryDto"}}}}}}}, "/api/v1/activity-transaction/filter": {"get": {"tags": ["activity-transaction-controller"], "operationId": "getActivityHistory", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityHistoryResponse"}}}}}}}}}, "components": {"schemas": {"TestEntity": {"type": "object", "properties": {"nonTranslatedValue": {"type": "string"}, "translatedValue": {"type": "string"}}}, "ActivityCompletedRequest": {"type": "object", "properties": {"mnemonic": {"type": "string"}, "activityName": {"type": "string"}}}, "ActivityCompletionRequestMessageDto": {"type": "object", "properties": {"programEnrolmentCohortActivityId": {"type": "integer", "format": "int64"}, "activityValue": {"type": "string"}, "date": {"type": "string", "format": "date-time"}}}, "ActivityMnemonicCompletionRequestMessageDto": {"type": "object", "properties": {"mnemonic": {"type": "string"}, "programName": {"type": "string"}, "activityValue": {"type": "string"}}}, "BulkActivityCompletionRequestMessageDto": {"type": "object", "properties": {"activityCompletionRequestMessages": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCompletionRequestMessageDto"}}}}, "ProgramEnrolmentRequestDto": {"type": "object", "properties": {"programId": {"type": "integer", "format": "int64"}, "programName": {"type": "string"}}}, "Program": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "owner": {"type": "string"}, "programGroup": {"$ref": "#/components/schemas/ProgramGroup"}, "programAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramAssocAttribute"}}}}, "ProgramAssocAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "programAttribute": {"$ref": "#/components/schemas/ProgramAttribute"}, "value": {"type": "string"}}}, "ProgramAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}}}, "ProgramEnrolment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "entityId": {"type": "integer", "format": "int64"}, "program": {"$ref": "#/components/schemas/Program"}, "lastEnrolmentTime": {"type": "string", "format": "date-time"}, "effFrom": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["ACTIVE", "TERMINATED"]}, "state": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}}}, "ProgramGroup": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "CelebrateCategoriesRequest": {"type": "object", "properties": {"categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "StatusDtoResponse": {"type": "object", "properties": {"totalPoints": {"type": "integer", "format": "int64"}, "calculatedLevel": {"type": "string"}, "achievedLevel": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "pointsToAchieveNextLevel": {"type": "integer", "format": "int64"}, "pointsToMaintainAchievedLevel": {"type": "integer", "format": "int64"}, "entityCarryoverStatus": {"type": "string"}, "rewardStatus": {"type": "string"}, "overallStatus": {"type": "string"}, "entityCarryoverPoints": {"type": "integer", "format": "int64"}, "displayStatusType": {"type": "string", "enum": ["INDIVIDUAL", "POLICY"]}, "statusLevels": {"type": "array", "items": {"$ref": "#/components/schemas/StatusLevelDtoResponse"}}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/StatusMemberDtoResponse"}}}}, "StatusLevelDto": {"type": "object", "properties": {"description": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "lowerLimit": {"type": "integer", "format": "int64"}, "upperLimit": {"type": "integer", "format": "int64"}}}, "StatusLevelDtoResponse": {"type": "object", "properties": {"description": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "lowerLimit": {"type": "integer", "format": "int64"}, "upperLimit": {"type": "integer", "format": "int64"}}}, "StatusMemberDtoResponse": {"type": "object", "properties": {"memberType": {"type": "string", "enum": ["PRIMARY", "PARTNER"]}, "role": {"type": "string"}, "totalPoints": {"type": "integer", "format": "int64"}, "totalPointsProgramYear": {"type": "integer", "format": "int64"}, "calculatedLevel": {"type": "string"}, "achievedLevel": {"type": "string"}, "entityCarryoverStatus": {"type": "string"}, "entityCarryoverPoints": {"type": "integer", "format": "int64"}, "rewardStatus": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "pointsToAchieveNextLevel": {"type": "integer", "format": "int64"}, "pointsToMaintainAchievedLevel": {"type": "integer", "format": "int64"}, "statusLevels": {"type": "array", "items": {"$ref": "#/components/schemas/StatusLevelDto"}}}}, "StatusResponse": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "payLoad": {"$ref": "#/components/schemas/StatusDtoResponse"}}}, "ActivityAllocationPeriodDTO": {"type": "object", "properties": {"allocationPeriodId": {"type": "string"}, "descr": {"type": "string"}}}, "ActivityAssocAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "activityAttribute": {"$ref": "#/components/schemas/ActivityAttribute"}, "value": {"type": "string"}, "customerDefinitionId": {"type": "integer", "format": "int64"}}}, "ActivityAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}}}, "ActivityInfoCreationRequest": {"type": "object", "properties": {"cmsName": {"type": "string"}, "instruction": {"type": "string"}, "description": {"type": "string"}, "about": {"type": "string"}, "articleContent": {"type": "string"}, "disclaimer": {"type": "string"}, "primaryCtaName": {"type": "string"}, "primaryCtaLink": {"type": "string"}, "primaryCtaLinkMobile": {"type": "string"}, "primaryCtaNameAlternative": {"type": "string"}, "primaryCtaLinkAlternative": {"type": "string"}, "primaryCtaLinkAlternativeMobile": {"type": "string"}, "secondaryCtaName": {"type": "string"}, "secondaryCtaLink": {"type": "string"}, "secondaryCtaLinkMobile": {"type": "string"}, "secondaryCtaNameAlternative": {"type": "string"}, "secondaryCtaLinkAlternative": {"type": "string"}, "secondaryCtaLinkAlternativeMobile": {"type": "string"}, "embeddedVideoLink": {"type": "string"}, "embeddedVideoTranscript": {"type": "string"}, "embeddedVideoSubtitles": {"type": "string"}, "imageLink": {"type": "string"}, "phpIconUrl": {"type": "string"}}}, "ActivityResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "effFrom": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "frequency": {"type": "integer", "format": "int64"}, "activityEvent": {"type": "string"}, "relatedHealthTopic": {"type": "string"}, "timeToComplete": {"type": "string"}, "allocation": {"type": "string", "enum": ["UponEventReceipt", "UpFront"]}, "visibility": {"type": "boolean"}, "activityAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAssocAttribute"}}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "rootCategory": {"type": "string"}, "type": {"$ref": "#/components/schemas/ActivityType"}, "activityAllocationPeriod": {"$ref": "#/components/schemas/ActivityAllocationPeriodDTO"}, "parentActivityId": {"type": "integer", "format": "int64"}, "cmsContents": {"type": "array", "items": {"$ref": "#/components/schemas/CmsContent"}}, "pointsAward": {"type": "integer", "format": "int64"}, "entityInformation": {"$ref": "#/components/schemas/EntityInformation"}, "dependentActivityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "activityOverrideName": {"type": "string"}}}, "ActivityType": {"type": "object", "properties": {"activityTypeId": {"type": "string"}, "activityTypeDescription": {"type": "string"}}}, "CmsContent": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "category": {"type": "string"}, "tag": {"type": "string"}, "activityState": {"type": "string", "enum": ["REQUIREMENTS_MET", "REQUIREMENTS_UNMET"]}, "activityInfoCreationRequest": {"$ref": "#/components/schemas/ActivityInfoCreationRequest"}}}, "EntityInformation": {"type": "object", "properties": {"allianceId": {"type": "integer", "format": "int32"}, "employerId": {"type": "integer", "format": "int64"}, "branchId": {"type": "string"}, "memberRole": {"type": "string"}, "entityNo": {"type": "string"}, "policyId": {"type": "string"}, "age": {"type": "integer", "format": "int32"}, "gender": {"type": "string"}, "notCustomer": {"type": "boolean"}}}, "ActivityInstantRewardResponse": {"type": "object", "properties": {"rewardType": {"type": "string"}, "rewardAmount": {"type": "string"}, "rewardCurrency": {"type": "string"}}}, "ActivityPointsResponse": {"type": "object", "properties": {"rewardType": {"type": "string"}, "rewardAllocation": {"type": "string"}, "pointsAchieved": {"type": "integer", "format": "int64"}}}, "CategoryInfoDto": {"type": "object", "properties": {"name": {"type": "string"}, "parentCategoryInfo": {"$ref": "#/components/schemas/CategoryInfoDto"}, "id": {"type": "integer", "format": "int64"}}}, "ExtendedActivityInfoCreationRequest": {"type": "object", "properties": {"instruction": {"type": "string"}, "description": {"type": "string"}, "about": {"type": "string"}, "articleContent": {"type": "string"}, "disclaimer": {"type": "string"}, "primaryCtaName": {"type": "string"}, "primaryCtaLink": {"type": "string"}, "primaryCtaLinkMobile": {"type": "string"}, "primaryCtaNameAlternative": {"type": "string"}, "primaryCtaLinkAlternative": {"type": "string"}, "primaryCtaLinkAlternativeMobile": {"type": "string"}, "secondaryCtaName": {"type": "string"}, "secondaryCtaLink": {"type": "string"}, "secondaryCtaLinkMobile": {"type": "string"}, "secondaryCtaNameAlternative": {"type": "string"}, "secondaryCtaLinkAlternative": {"type": "string"}, "secondaryCtaLinkAlternativeMobile": {"type": "string"}, "embeddedVideoLink": {"type": "string"}, "embeddedVideoTranscript": {"type": "string"}, "embeddedVideoSubtitles": {"type": "string"}, "imageLink": {"type": "string"}}}, "ExtendedProgramEnrolmentCohortActivity": {"type": "object", "properties": {"programEnrolmentCohortActivityId": {"type": "string"}, "programId": {"type": "string"}, "programName": {"type": "string"}, "programEnrolmentId": {"type": "string"}, "cohortName": {"type": "string"}, "activityId": {"type": "string"}, "activityName": {"type": "string"}, "activityStatus": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}, "measureAchievement": {"type": "string"}, "activityPoints": {"$ref": "#/components/schemas/ActivityPointsResponse"}, "activityInstantReward": {"$ref": "#/components/schemas/ActivityInstantRewardResponse"}, "activityCompletedDate": {"type": "string", "format": "date-time"}, "mnemonic": {"type": "string"}, "effForm": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "content": {"$ref": "#/components/schemas/CmsContent"}, "activityType": {"type": "string"}, "categoryInfos": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryInfoDto"}}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}}, "frequency": {"type": "string"}, "timeToComplete": {"type": "string"}, "activityCmsInfo": {"$ref": "#/components/schemas/ExtendedActivityInfoCreationRequest"}, "roles": {"type": "array", "items": {"type": "string"}}, "isPhysicalActivity": {"type": "boolean"}}}, "ExtendedProgramEnrolmentCohortActivityWithReward": {"type": "object", "properties": {"programEnrolmentCohortActivityId": {"type": "string"}, "programId": {"type": "string"}, "programName": {"type": "string"}, "programEnrolmentId": {"type": "string"}, "cohortName": {"type": "string"}, "activityId": {"type": "string"}, "activityName": {"type": "string"}, "activityStatus": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}, "measureAchievement": {"type": "string"}, "activityPoints": {"$ref": "#/components/schemas/ActivityPointsResponse"}, "activityInstantReward": {"$ref": "#/components/schemas/ActivityInstantRewardResponse"}, "activityCompletedDate": {"type": "string", "format": "date-time"}, "mnemonic": {"type": "string"}, "effForm": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "content": {"$ref": "#/components/schemas/CmsContent"}, "activityType": {"type": "string"}, "categoryInfos": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryInfoDto"}}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}}, "frequency": {"type": "string"}, "timeToComplete": {"type": "string"}, "activityCmsInfo": {"$ref": "#/components/schemas/ExtendedActivityInfoCreationRequest"}, "extendedSubActivities": {"type": "array", "items": {"$ref": "#/components/schemas/ExtendedProgramEnrolmentCohortActivity"}}, "roles": {"type": "array", "items": {"type": "string"}}, "isPhysicalActivity": {"type": "boolean"}, "recommendationDetails": {"$ref": "#/components/schemas/RecommendationDetails"}}}, "RecommendationDetails": {"type": "object", "properties": {"effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}, "status": {"type": "string", "enum": ["PENDING", "EXPIRED", "COMPLETED", "CANCELLED", "DISMISSED"]}, "rewardDetails": {"$ref": "#/components/schemas/RewardDetails"}}}, "Reward": {"type": "object", "properties": {"rewardType": {"type": "integer", "format": "int64"}, "rewardTypeValue": {"type": "string"}, "value": {"type": "string"}, "awardStatus": {"type": "string", "enum": ["AVAILABLE", "PENDING", "AWARDED"]}}}, "RewardDetails": {"type": "object", "properties": {"strategyId": {"type": "string"}, "effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}, "rewards": {"type": "array", "items": {"$ref": "#/components/schemas/Reward"}}}}, "PageableObject": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "unpaged": {"type": "boolean"}, "paged": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "offset": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int32"}}}, "ProgramEnrolmentCohortActivityDTOPageable": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "number": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivityDTOResponse"}}, "sort": {"$ref": "#/components/schemas/SortObject"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "ProgramEnrolmentCohortActivityDTOResponse": {"type": "object", "properties": {"programId": {"type": "string"}, "programName": {"type": "string"}, "programEnrolmentId": {"type": "string"}, "programEnrolmentCohortActivityId": {"type": "string"}, "activityId": {"type": "string"}, "activityName": {"type": "string"}, "activityStatus": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}, "measureAchievement": {"type": "string"}, "activityPoints": {"$ref": "#/components/schemas/ActivityPointsResponse"}, "activityInstantReward": {"$ref": "#/components/schemas/ActivityInstantRewardResponse"}, "recommendationDetails": {"$ref": "#/components/schemas/RecommendationDetails"}, "mnemonic": {"type": "string"}, "effForm": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "content": {"$ref": "#/components/schemas/CmsContent"}, "activityType": {"type": "string"}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "frequency": {"type": "string"}, "timeToComplete": {"type": "string"}, "activityCompletedDate": {"type": "string", "format": "date-time"}, "healthCategories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryInfoDto"}}, "categoryInfos": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryInfoDto"}}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}}, "roles": {"type": "array", "items": {"type": "string"}}, "isPhysicalActivity": {"type": "boolean"}, "manuallyAdded": {"type": "boolean"}}}, "SortObject": {"type": "object", "properties": {"sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "CallToActionDeviceActionDTO": {"type": "object", "properties": {"mnemonic": {"type": "string"}, "device": {"type": "string"}, "deviceVersion": {"type": "string"}, "operation": {"type": "string"}}}, "Activity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "effFrom": {"type": "string", "format": "date-time"}, "effTo": {"type": "string", "format": "date-time"}, "frequency": {"type": "integer", "format": "int64"}, "rewardType": {"type": "string", "enum": ["POINTS", "NONE"]}, "activityEvent": {"type": "string"}, "relatedHealthTopic": {"type": "string"}, "timeToComplete": {"type": "string"}, "allocation": {"type": "string", "enum": ["UponEventReceipt", "UpFront"]}, "visibility": {"type": "boolean"}, "activityAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityAssocAttribute"}}, "categoryIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "rootCategory": {"type": "string"}, "type": {"$ref": "#/components/schemas/ActivityType"}, "activityAllocationPeriod": {"$ref": "#/components/schemas/ActivityAllocationPeriodDTO"}, "parentActivityId": {"type": "integer", "format": "int64"}, "cmsContents": {"type": "array", "items": {"$ref": "#/components/schemas/CmsContent"}}, "dependentActivityIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "manuallyAddable": {"type": "boolean"}}}, "ActivityCompletionStatusModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}, "dateFrom": {"type": "string", "format": "date-time"}}}, "ProgramEnrolmentCohortActivity": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "programActivity": {"$ref": "#/components/schemas/Activity"}, "itemId": {"type": "integer", "format": "int64"}, "activityCompletionStatus": {"$ref": "#/components/schemas/ActivityCompletionStatusModel"}, "activityComplete": {"type": "string", "format": "date-time"}, "activityValue": {"type": "string"}, "exposureDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}, "subActivities": {"type": "array", "items": {"$ref": "#/components/schemas/ProgramEnrolmentCohortActivity"}}, "entityId": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int64"}, "manuallyAdded": {"type": "boolean"}, "programEnrolment": {"$ref": "#/components/schemas/ProgramEnrolment"}, "activityCompleteStatusEnum": {"type": "string", "enum": ["PENDING", "DONE", "CANCELLED"]}}}, "MnemonicFlagDTO": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}}}, "OnboardingAssessmentAvailabilityDTO": {"type": "object", "properties": {"onboardingAssessmentAvailabilityStatus": {"type": "boolean"}, "mnemonicFlagDTO": {"$ref": "#/components/schemas/MnemonicFlagDTO"}}}, "PagingRequest": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "sortField": {"type": "string"}, "sortDirection": {"type": "string", "enum": ["ASC", "DESC"]}}}, "ActivityCategoryFilter": {"type": "object", "properties": {"descr": {"type": "string"}}}, "ActivityDTO": {"type": "object", "properties": {"activityName": {"type": "string"}, "activityCategory": {"type": "string"}, "activityPointValue": {"type": "integer", "format": "int32"}, "activityCode": {"type": "string"}}}, "PagedDTOActivityDTO": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityDTO"}}}}, "CategoryActivities": {"type": "object", "properties": {"activityCatId": {"type": "integer", "format": "int64"}, "activityCatDescription": {"type": "string"}, "activityCatOverrideDescription": {"type": "string"}, "activityCatAssocAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryAssocAttribute"}}, "activityCatActivities": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}, "activityCatSubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryActivities"}}}}, "CategoryAssocAttribute": {"type": "object", "properties": {"id": {"type": "string"}, "activityCatAttribute": {"$ref": "#/components/schemas/CategoryAttribute"}, "value": {"type": "string"}, "effFrom": {"type": "string", "format": "date"}, "effTo": {"type": "string", "format": "date"}}}, "CategoryAttribute": {"type": "object", "properties": {"id": {"type": "string"}, "descr": {"type": "string"}}}, "ActivityCategoryDto": {"type": "object", "properties": {"activityCategoryId": {"type": "string"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}, "activityCategoryParentCategoryId": {"type": "string"}, "activityCategoryType": {"type": "string"}, "activityCategoryRanking": {"type": "string"}, "categoryImageUrl": {"type": "string"}, "primaryRisk": {"type": "string"}, "healthCategory": {"type": "string"}, "activityCompletion": {"$ref": "#/components/schemas/ActivityCompletion"}, "activityPoint": {"$ref": "#/components/schemas/ActivityPointDTO"}, "resourceList": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryResourceResponse"}}, "benefitList": {"type": "array", "items": {"$ref": "#/components/schemas/FlexEmployerBenefitResponseItem"}}, "recommended": {"type": "boolean"}}}, "ActivityCategoryIdName": {"type": "object", "properties": {"activityCategoryId": {"type": "string"}, "activityCategoryName": {"type": "string"}, "activityCategoryOverrideName": {"type": "string"}}}, "ActivityCategoryResourceResponse": {"type": "object", "properties": {"resourceId": {"type": "string"}, "resourceName": {"type": "string"}, "resourceType": {"type": "string"}, "resourceLink": {"type": "string"}, "resourceImage": {"type": "string"}}}, "ActivityCompletion": {"type": "object", "properties": {"activitiesCompleted": {"type": "integer", "format": "int32"}, "activitiesTotal": {"type": "integer", "format": "int32"}}}, "ActivityPointDTO": {"type": "object", "properties": {"activityCategoryPointAchieved": {"type": "integer", "format": "int64"}, "activityCategoryPointMax": {"type": "integer", "format": "int64"}, "activityPointsPerActivity": {"type": "string"}, "activityPointsAllocation": {"type": "string"}}}, "FlexEmployerBenefitResponseItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "media": {"$ref": "#/components/schemas/Media"}, "type": {"type": "string"}, "status": {"type": "string"}, "description": {"type": "string"}, "rewards": {"type": "array", "items": {"$ref": "#/components/schemas/Reward"}}, "attachmentUrl": {"type": "string"}, "attachmentTitle": {"type": "string"}, "benefitUrl": {"type": "string"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}}}, "Media": {"type": "object", "properties": {"url": {"type": "string"}, "altText": {"type": "string"}}}, "ProgramActivityCategoryDto": {"type": "object", "properties": {"activityCategoryList": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryDto"}}, "categoriesToCelebrate": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityCategoryIdName"}}}}, "ActivityHistoryResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "activityName": {"type": "string"}, "activityCategory": {"type": "string"}, "activitySubcategories": {"type": "array", "items": {"type": "string"}}, "transactionDate": {"type": "string", "format": "date-time"}, "userTransactionDate": {"type": "string", "format": "date-time"}, "pointAward": {"type": "integer", "format": "int64"}, "eventDate": {"type": "string", "format": "date"}, "awardDate": {"type": "string", "format": "date-time"}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}}}}}}