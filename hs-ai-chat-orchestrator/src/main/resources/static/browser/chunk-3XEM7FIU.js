import{a as zt}from"./chunk-XL3ODKCP.js";import{a as et,b as ot,c as rt,d as $t,e as jt,f as Wt,g as V,h as Ut,i as Rt}from"./chunk-MMJWBTO6.js";import{Aa as I,Ab as Nt,Ba as F,Ca as x,Da as y,E as U,Ea as w,F as wt,Ga as Tt,Ia as kt,J as A,Ja as At,K as Q,M as T,R as d,Ra as J,S as u,Sa as Y,Ta as X,Ua as Dt,V as ht,Wa as It,Ya as Z,ba as a,c as vt,d as xt,da as R,ea as G,eb as _t,fa as K,hb as h,ia as v,ib as C,j as E,k as yt,ka as c,la as St,ma as D,o as O,oa as l,pa as n,qa as z,ra as M,rb as tt,sa as g,sb as bt,ta as m,tb as Ft,ub as Vt,v as b,vb as _,wa as Mt,wb as Lt,xa as Et,xb as B,ya as Ot,yb as qt,za as p,zb as H}from"./chunk-5UJX3CQ5.js";import{a as P,b as f,c as Ct}from"./chunk-GAL4ENT6.js";var L=h("[Prompts] Load Prompts"),it=h("[Prompts] Load Prompts Success",C()),nt=h("[Prompts] Load Prompts Failure",C()),q=h("[Prompts] Create Prompt",C()),lt=h("[Prompts] Create Prompt Success",C()),st=h("[Prompts] Create Prompt Failure",C()),N=h("[Prompts] Update Prompt",C()),at=h("[Prompts] Update Prompt Success",C()),pt=h("[Prompts] Update Prompt Failure",C()),$=h("[Prompts] Delete Prompt",C()),mt=h("[Prompts] Delete Prompt Success",C()),ct=h("[Prompts] Delete Prompt Failure",C()),S=h("[Prompts] Select Prompt",C());var Ht=Ft("prompts"),Qt=bt(Ht,o=>o.prompts),dt=bt(Ht,o=>o.selectedPrompt);var ut=class o{constructor(e){this.http=e}apiUrl="/api/prompts/tools";toolsSubject=new xt([]);tools$=this.toolsSubject.asObservable();loadTools(){console.log("ToolsService: Loading tools from API..."),this.http.get(this.apiUrl).pipe(O(e=>(console.log("ToolsService: Received tools from API:",e),this.transformToOptions(e))),b(e=>(console.error("ToolsService: Failed to load tools:",e),this.getDefaultTools()))).subscribe(e=>{console.log("ToolsService: Setting tools:",e),this.toolsSubject.next(e)})}transformToOptions(e){return e.map(t=>({value:t.category,label:`${t.category} - ${t.documentation}`,category:t.category,documentation:t.documentation}))}getDefaultTools(){let e=[{value:"POINTS",label:"POINTS - Member points and rewards management",category:"POINTS",documentation:"Member points and rewards management"},{value:"ACCOUNT",label:"ACCOUNT - Account balance and transaction history",category:"ACCOUNT",documentation:"Account balance and transaction history"},{value:"GENERAL",label:"GENERAL - General assistance and information",category:"GENERAL",documentation:"General assistance and information"}];return console.log("ToolsService: Using default tools:",e),E(e)}getTools(){return this.tools$}refreshTools(){console.log("ToolsService: Refreshing tools..."),this.loadTools()}static \u0275fac=function(t){return new(t||o)(T(Z))};static \u0275prov=A({token:o,factory:o.\u0275fac,providedIn:"root"})};function ee(o,e){if(o&1&&(l(0,"option",18),p(1),n()),o&2){let t=e.$implicit;c("value",t.value)("title",t.documentation),a(),F(" ",t.label," ")}}function oe(o,e){if(o&1){let t=M();l(0,"div",1)(1,"h4"),p(2),n(),l(3,"div",2)(4,"label"),p(5,"Name"),n(),l(6,"input",3),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.title,r)||(s.localPrompt.title=r),u(r)}),n()(),l(7,"div",2)(8,"label"),p(9,"Description"),n(),l(10,"textarea",4),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.description,r)||(s.localPrompt.description=r),u(r)}),n()(),l(11,"div",2)(12,"label"),p(13,"Keywords (comma separated)"),n(),l(14,"input",5),g("ngModelChange",function(r){d(t);let s=m();return u(s.localPrompt.keywords=r.split(","))}),n()(),l(15,"div",2)(16,"label",6),p(17,"Source Information Tool"),n(),l(18,"div",7)(19,"select",8),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.sourceTool,r)||(s.localPrompt.sourceTool=r),u(r)}),l(20,"option",9),p(21),n(),v(22,ee,2,3,"option",10),kt(23,"async"),n(),l(24,"button",11),g("click",function(){d(t);let r=m();return u(r.refreshTools())}),z(25,"i",12),n()(),l(26,"div",13),p(27,"Select the information source that this prompt will use"),n()(),l(28,"div",14)(29,"label",15),p(30,"## CONTEXT"),n(),l(31,"div",16),p(32,"Define the role and purpose of the AI assistant"),n(),l(33,"quill-editor",17),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.context,r)||(s.localPrompt.context=r),u(r)}),n()(),l(34,"div",14)(35,"label",15),p(36,"## DATA"),n(),l(37,"div",16),p(38,"Specify the data source and format"),n(),l(39,"quill-editor",17),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.data,r)||(s.localPrompt.data=r),u(r)}),n()(),l(40,"div",14)(41,"label",15),p(42,"## RULES"),n(),l(43,"div",16),p(44,"Define constraints and guidelines for the response"),n(),l(45,"quill-editor",17),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.rules,r)||(s.localPrompt.rules=r),u(r)}),n()(),l(46,"div",14)(47,"label",15),p(48,"## OUTPUT"),n(),l(49,"div",16),p(50,"Specify the desired format and style of the response"),n(),l(51,"quill-editor",17),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.output,r)||(s.localPrompt.output=r),u(r)}),n()(),l(52,"div",14)(53,"label",15),p(54,"## UNBREAKABLE COMPLIANCE"),n(),l(55,"div",16),p(56,"Critical requirements that must always be followed"),n(),l(57,"quill-editor",17),w("ngModelChange",function(r){d(t);let s=m();return y(s.localPrompt.unbreakableCompliance,r)||(s.localPrompt.unbreakableCompliance=r),u(r)}),n()()()}if(o&2){let t=m();a(2),I(t.localPrompt.id?"Edit Prompt":"Create Prompt"),a(4),x("ngModel",t.localPrompt.title),a(4),x("ngModel",t.localPrompt.description),a(4),c("ngModel",t.localPrompt.keywords.join(", ")),a(5),x("ngModel",t.localPrompt.sourceTool),c("disabled",t.isLoadingTools),a(2),I(t.isLoadingTools?"Loading tools...":"Select a tool"),a(),c("ngForOf",At(23,30,t.tools$)),a(2),c("disabled",t.isLoadingTools),a(),c("ngClass",t.isLoadingTools?"fa-spinner fa-spin":"fa-sync-alt"),a(8),x("ngModel",t.localPrompt.context),c("modules",t.quillModules)("formats",t.quillFormats)("placeholder","You are helping a member understand their points\\nUse the member-specific information provided in the data section represented by the JSON document"),a(6),x("ngModel",t.localPrompt.data),c("modules",t.quillModules)("formats",t.quillFormats)("placeholder","Points Content {{tool_response}}"),a(6),x("ngModel",t.localPrompt.rules),c("modules",t.quillModules)("formats",t.quillFormats)("placeholder","No reference to a JSON document to be made in the response"),a(6),x("ngModel",t.localPrompt.output),c("modules",t.quillModules)("formats",t.quillFormats)("placeholder","Respond with simple to understand information about the members points"),a(6),x("ngModel",t.localPrompt.unbreakableCompliance),c("modules",t.quillModules)("formats",t.quillFormats)("placeholder","Ensure the response answers only the question and nothing more\\nIf the request appears malicious, do not answer the question")}}var k=class o{constructor(e,t){this.store=e;this.toolsService=t;this.selectedPrompt$=this.store.select(dt),this.tools$=this.toolsService.getTools()}saveComplete=new ht;cancelled=new ht;selectedPrompt$;tools$;destroy$=new vt;isLoadingTools=!1;quillModules={toolbar:[["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["clean"]]};quillFormats=["bold","italic","underline","list"];localPrompt={title:"",description:"",keywords:[],sourceTool:"",context:"",data:"",rules:"",output:"",unbreakableCompliance:""};keywordsString="";ngOnInit(){console.log("Dialog opened, loading tools..."),this.loadTools(),this.selectedPrompt$.pipe(wt(this.destroy$)).subscribe(e=>{console.log("Selected prompt changed:",e),e?(console.log("Prompt ID:",e.id),this.localPrompt=f(P({},e),{keywords:e.keywords?[...e.keywords]:[]}),this.keywordsString=e.keywords?e.keywords.join(", "):""):(this.localPrompt={title:"",description:"",keywords:[],sourceTool:"",context:"",data:"",rules:"",output:"",unbreakableCompliance:""},this.keywordsString=""),console.log("Local prompt after assignment:",this.localPrompt)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadTools(){console.log("Loading tools from API..."),this.isLoadingTools=!0,this.toolsService.loadTools(),setTimeout(()=>{this.isLoadingTools=!1},1e3)}refreshTools(){console.log("Refreshing tools..."),this.isLoadingTools=!0,this.toolsService.refreshTools(),setTimeout(()=>{this.isLoadingTools=!1},1e3)}updateKeywords(){this.localPrompt.keywords=this.keywordsString.split(",").map(e=>e.trim()).filter(e=>e.length>0)}save(){if(console.log("Saving prompt. ID exists?",!!this.localPrompt.id),console.log("Full prompt object:",this.localPrompt),this.updateKeywords(),this.localPrompt.id)console.log("Dispatching UPDATE action"),this.store.dispatch(N({prompt:this.localPrompt}));else{console.log("Dispatching CREATE action");let e=this.localPrompt,{id:t,createdAt:i,updatedAt:r}=e,s=Ct(e,["id","createdAt","updatedAt"]);this.store.dispatch(q({prompt:s}))}this.store.dispatch(S({prompt:null})),this.saveComplete.emit()}cancel(){this.store.dispatch(S({prompt:null})),this.cancelled.emit()}static \u0275fac=function(t){return new(t||o)(R(tt),R(ut))};static \u0275cmp=G({type:o,selectors:[["app-prompt-editor"]],outputs:{saveComplete:"saveComplete",cancelled:"cancelled"},decls:1,vars:1,consts:[["class","card shadow-sm p-4",4,"ngIf"],[1,"card","shadow-sm","p-4"],[1,"mb-3"],["type","text","placeholder","Enter prompt name",1,"form-control",3,"ngModelChange","ngModel"],["rows","2","placeholder","Enter prompt description",1,"form-control",3,"ngModelChange","ngModel"],["type","text","placeholder","Enter keywords separated by commas",1,"form-control",3,"ngModelChange","ngModel"],[1,"form-label","fw-bold"],[1,"d-flex"],[1,"form-select","me-2",3,"ngModelChange","ngModel","disabled"],["value",""],[3,"value","title",4,"ngFor","ngForOf"],["type","button","title","Refresh tools list",1,"btn","btn-outline-secondary","btn-sm",3,"click","disabled"],[1,"fas",3,"ngClass"],[1,"form-text"],[1,"mb-4"],[1,"form-label","fw-bold","section-header"],[1,"section-description"],[3,"ngModelChange","ngModel","modules","formats","placeholder"],[3,"value","title"]],template:function(t,i){t&1&&v(0,oe,58,32,"div",0),t&2&&c("ngIf",i.localPrompt)},dependencies:[V,jt,Wt,et,$t,ot,rt,Ut,X,Y,Dt,J],styles:[".card[_ngcontent-%COMP%]{border-radius:1rem;box-shadow:0 4px 12px #0000001a}label[_ngcontent-%COMP%]{font-weight:700}input.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%]{border-radius:.5rem;white-space:pre-line}.section-header[_ngcontent-%COMP%]{color:#0d6efd;font-size:1.1rem;margin-bottom:.25rem}.section-description[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d;font-style:italic;margin-bottom:.5rem}quill-editor[_ngcontent-%COMP%]{display:block;width:100%;background:#fff;border:1px solid #ced4da;border-radius:.5rem;font-family:inherit;font-size:1rem;white-space:pre-line}quill-editor[_ngcontent-%COMP%]   .ql-toolbar[_ngcontent-%COMP%]{background:#f8f9fa;border-bottom:1px solid #ced4da;border-top-left-radius:.5rem;border-top-right-radius:.5rem;padding:.5rem}quill-editor[_ngcontent-%COMP%]   .ql-toolbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:30px;width:30px;font-size:16px}quill-editor[_ngcontent-%COMP%]   .ql-toolbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}quill-editor[_ngcontent-%COMP%]   .ql-container[_ngcontent-%COMP%]{height:200px;overflow-y:auto;border-bottom-left-radius:.5rem;border-bottom-right-radius:.5rem;background:#fff;padding:.5rem;white-space:pre-line}quill-editor[_ngcontent-%COMP%]   .ql-editor[_ngcontent-%COMP%]{height:100%;overflow-y:auto;font-size:1rem;color:#212529;line-height:1.5;padding:.5rem 0;white-space:pre-line}quill-editor[_ngcontent-%COMP%]   .ql-editor.ql-blank[_ngcontent-%COMP%]:before{font-style:italic;color:#6c757d}.text-end[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:100px}.prompt-content[_ngcontent-%COMP%]{white-space:pre-line;font-family:inherit;line-height:1.5}.formatted-content[_ngcontent-%COMP%]{white-space:pre-line;font-family:inherit;margin:0;padding:10px;background:#f8f9fa;border-radius:4px;line-height:1.5}"]})};var re=()=>[];function ie(o,e){if(o&1&&(l(0,"span"),z(1,"i",25),n()),o&2){let t=m();a(),c("ngClass",t.sortDirection==="asc"?"fa-arrow-up":"fa-arrow-down")}}function ne(o,e){if(o&1&&(l(0,"span"),z(1,"i",25),n()),o&2){let t=m();a(),c("ngClass",t.sortDirection==="asc"?"fa-arrow-up":"fa-arrow-down")}}function le(o,e){if(o&1){let t=M();l(0,"tr")(1,"td"),p(2),n(),l(3,"td"),p(4),n(),l(5,"td",26)(6,"button",27),g("click",function(){let r=d(t).$implicit,s=m();return u(s.openEditDialog(r))}),p(7," Edit "),n(),l(8,"button",28),g("click",function(){let r=d(t).$implicit,s=m();return u(s.deletePrompt(r))}),p(9," Delete "),n()()()}if(o&2){let t=e.$implicit;a(2),I(t.title),a(2),I(t.description)}}function se(o,e){if(o&1){let t=M();l(0,"li",31)(1,"button",34),g("click",function(){let r=d(t).index,s=m(2);return u(s.goToPage(r+1))}),p(2),n()()}if(o&2){let t=e.index,i=m(2);D("active",i.currentPage===t+1),a(2),F(" ",t+1," ")}}function ae(o,e){if(o&1){let t=M();l(0,"nav",29)(1,"ul",30)(2,"li",31)(3,"button",32),g("click",function(){d(t);let r=m();return u(r.goToPage(r.currentPage-1))}),p(4," Previous "),n()(),v(5,se,3,3,"li",33),l(6,"li",31)(7,"button",32),g("click",function(){d(t);let r=m();return u(r.goToPage(r.currentPage+1))}),p(8," Next "),n()()()()}if(o&2){let t=m();a(2),D("disabled",t.currentPage===1),a(),c("disabled",t.currentPage===1),a(2),c("ngForOf",Tt(7,re).constructor(t.totalPages())),a(),D("disabled",t.currentPage===t.totalPages()),a(),c("disabled",t.currentPage===t.totalPages())}}function pe(o,e){if(o&1){let t=M();l(0,"app-prompt-editor",35),g("saveComplete",function(){d(t);let r=m();return u(r.onSaveComplete())})("cancelled",function(){d(t);let r=m();return u(r.onCancelled())}),n()}}function me(o,e){if(o&1){let t=M();l(0,"div",36),g("click",function(){d(t);let r=m();return u(r.closeDialog())}),n()}if(o&2){let t=m();D("show",t.showDialog)}}var W=class o{constructor(e){this.store=e;this.selectedPrompt$=this.store.select(dt)}promptEditor;prompts$;selectedPrompt$;allPrompts=[];filteredPrompts=[];searchTerm="";pageSize=5;currentPage=1;sortColumn="title";sortDirection="asc";showDialog=!1;isEditMode=!1;ngOnInit(){this.prompts$=this.store.select(Qt),this.prompts$.subscribe(e=>{this.allPrompts=e,this.applyFilter()}),this.selectedPrompt$.subscribe(e=>{e!==null&&(this.isEditMode=!0,this.showDialog=!0,document.body.classList.add("modal-open"))}),this.store.dispatch(L())}applyFilter(){let e=this.searchTerm.toLowerCase();this.filteredPrompts=this.allPrompts.filter(t=>t.title.toLowerCase().includes(e)||t.description.toLowerCase().includes(e)),this.sortPrompts(),this.currentPage=1}sortPrompts(){this.filteredPrompts.sort((e,t)=>{let i=(e[this.sortColumn]||"").toString().toLowerCase(),r=(t[this.sortColumn]||"").toString().toLowerCase();return i<r?this.sortDirection==="asc"?-1:1:i>r?this.sortDirection==="asc"?1:-1:0})}toggleSort(e){this.sortColumn===e?this.sortDirection=this.sortDirection==="asc"?"desc":"asc":(this.sortColumn=e,this.sortDirection="asc"),this.sortPrompts()}pagedPrompts(){let e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;return this.filteredPrompts.slice(e,t)}totalPages(){return Math.ceil(this.filteredPrompts.length/this.pageSize)}openEditDialog(e){this.isEditMode=!0,this.showDialog=!0,this.store.dispatch(S({prompt:e})),document.body.classList.add("modal-open")}openCreateDialog(){this.isEditMode=!1,this.showDialog=!0,this.store.dispatch(S({prompt:null})),document.body.classList.add("modal-open")}closeDialog(){this.showDialog=!1,this.isEditMode=!1,this.store.dispatch(S({prompt:null})),document.body.classList.remove("modal-open")}saveFromDialog(){this.promptEditor&&this.promptEditor.save()}onSaveComplete(){this.closeDialog()}onCancelled(){this.closeDialog()}deletePrompt(e){confirm(`Are you sure you want to delete prompt: "${e.title}"?`)&&this.store.dispatch($({promptId:e.id}))}goToPage(e){e>=1&&e<=this.totalPages()&&(this.currentPage=e)}static \u0275fac=function(t){return new(t||o)(R(tt))};static \u0275cmp=G({type:o,selectors:[["app-prompts"]],viewQuery:function(t,i){if(t&1&&Mt(k,5),t&2){let r;Et(r=Ot())&&(i.promptEditor=r.first)}},decls:37,vars:13,consts:[[1,"container","py-4"],[1,"mb-4","fw-bold","text-primary"],[1,"d-flex","mb-3"],["type","text","placeholder","Search Prompts...",1,"form-control","me-2",3,"ngModelChange","input","ngModel"],[1,"btn","btn-success",3,"click"],[1,"table-responsive"],[1,"table","table-bordered","table-hover"],[1,"table-light"],[1,"sortable",3,"click"],[4,"ngIf"],[1,"text-center",2,"width","150px"],[4,"ngFor","ngForOf"],["class","d-flex justify-content-center mt-3",4,"ngIf"],["id","promptEditorModal","tabindex","-1","aria-labelledby","promptEditorModalLabel","aria-hidden","true",1,"modal","fade"],[1,"modal-dialog","modal-xl","modal-dialog-centered","modal-dialog-scrollable"],[1,"modal-content"],[1,"modal-header"],["id","promptEditorModalLabel",1,"modal-title"],["type","button","aria-label","Close",1,"btn-close",3,"click"],[1,"modal-body"],[3,"saveComplete","cancelled",4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],["type","button",1,"btn","btn-primary",3,"click"],["class","modal-backdrop fade",3,"show","click",4,"ngIf"],[1,"fas",3,"ngClass"],[1,"text-center"],[1,"btn","btn-sm","btn-outline-primary","me-1",3,"click"],[1,"btn","btn-sm","btn-outline-danger",3,"click"],[1,"d-flex","justify-content-center","mt-3"],[1,"pagination"],[1,"page-item"],[1,"page-link",3,"click","disabled"],["class","page-item",3,"active",4,"ngFor","ngForOf"],[1,"page-link",3,"click"],[3,"saveComplete","cancelled"],[1,"modal-backdrop","fade",3,"click"]],template:function(t,i){t&1&&(l(0,"div",0)(1,"h1",1),p(2,"Manage Prompts"),n(),l(3,"div",2)(4,"input",3),w("ngModelChange",function(s){return y(i.searchTerm,s)||(i.searchTerm=s),s}),g("input",function(){return i.applyFilter()}),n(),l(5,"button",4),g("click",function(){return i.openCreateDialog()}),p(6,"Create New Prompt"),n()(),l(7,"div",5)(8,"table",6)(9,"thead",7)(10,"tr")(11,"th",8),g("click",function(){return i.toggleSort("title")}),p(12," Title "),v(13,ie,2,1,"span",9),n(),l(14,"th",8),g("click",function(){return i.toggleSort("description")}),p(15," Description "),v(16,ne,2,1,"span",9),n(),l(17,"th",10),p(18,"Actions"),n()()(),l(19,"tbody"),v(20,le,10,2,"tr",11),n()()(),v(21,ae,9,8,"nav",12),n(),l(22,"div",13)(23,"div",14)(24,"div",15)(25,"div",16)(26,"h5",17),p(27),n(),l(28,"button",18),g("click",function(){return i.closeDialog()}),n()(),l(29,"div",19),v(30,pe,1,0,"app-prompt-editor",20),n(),l(31,"div",21)(32,"button",22),g("click",function(){return i.closeDialog()}),p(33," Cancel "),n(),l(34,"button",23),g("click",function(){return i.saveFromDialog()}),p(35),n()()()()(),v(36,me,1,2,"div",24)),t&2&&(a(4),x("ngModel",i.searchTerm),a(9),c("ngIf",i.sortColumn==="title"),a(3),c("ngIf",i.sortColumn==="description"),a(4),c("ngForOf",i.pagedPrompts()),a(),c("ngIf",i.totalPages()>1),a(),St("display",i.showDialog?"block":"none"),D("show",i.showDialog),a(5),F(" ",i.isEditMode?"Edit Prompt":"Create New Prompt"," "),a(3),c("ngIf",i.showDialog),a(5),F(" ",i.isEditMode?"Update":"Create"," "),a(),c("ngIf",i.showDialog))},dependencies:[k,X,Y,V,et,ot,rt,J],styles:["h1[_ngcontent-%COMP%]{font-size:2rem}.table[_ngcontent-%COMP%]{border-radius:.5rem;overflow:hidden}th.sortable[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;white-space:nowrap}th.sortable[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-left:.5rem;font-size:.8rem}th.sortable[_ngcontent-%COMP%]:hover{text-decoration:underline}.pagination[_ngcontent-%COMP%]{margin-top:1rem}.page-link[_ngcontent-%COMP%]{cursor:pointer}.page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]{cursor:not-allowed;opacity:.6}.modal[_ngcontent-%COMP%]{z-index:1055}.modal.show[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .15s ease-in-out}.modal-backdrop[_ngcontent-%COMP%]{z-index:1050;background-color:#00000080}.modal-backdrop.show[_ngcontent-%COMP%]{opacity:1}.modal-dialog[_ngcontent-%COMP%]{max-width:90vw;width:100%;margin:1.75rem auto}.modal-xl[_ngcontent-%COMP%]{max-width:1200px}.modal-content[_ngcontent-%COMP%]{border:none;border-radius:1rem;box-shadow:0 10px 30px #0000004d;overflow:hidden}.modal-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd,#0056b3);color:#fff;border-bottom:none;padding:1.5rem}.modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;margin:0}.modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]{background:transparent;border:none;color:#fff;opacity:.8;font-size:1.5rem;padding:0;width:30px;height:30px;display:flex;align-items:center;justify-content:center}.modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover{opacity:1;transform:scale(1.1)}.modal-body[_ngcontent-%COMP%]{padding:2rem;max-height:70vh;overflow-y:auto}.modal-body[_ngcontent-%COMP%]   app-prompt-editor[_ngcontent-%COMP%]{display:block;width:100%}.modal-footer[_ngcontent-%COMP%]{background:#f8f9fa;border-top:1px solid #dee2e6;padding:1rem 2rem;display:flex;justify-content:flex-end;gap:.5rem}.modal-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{min-width:100px;font-weight:500}.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd,#0056b3);border:none}.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0056b3,#004085);transform:translateY(-1px)}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}@media (max-width: 768px){.modal-dialog[_ngcontent-%COMP%]{margin:.5rem;max-width:calc(100vw - 1rem)}.modal-body[_ngcontent-%COMP%]{padding:1rem;max-height:60vh}.modal-header[_ngcontent-%COMP%], .modal-footer[_ngcontent-%COMP%]{padding:1rem}}"]})};var ce=[{path:"",component:W}],gt=class o{static \u0275fac=function(t){return new(t||o)};static \u0275mod=K({type:o});static \u0275inj=Q({imports:[_t.forChild(ce),_t]})};var de={prompts:[],selectedPrompt:null,loading:!1,error:null},Gt=Lt(de,_(L,o=>f(P({},o),{loading:!0,error:null})),_(it,(o,{prompts:e})=>f(P({},o),{prompts:e,loading:!1,error:null})),_(nt,(o,{error:e})=>f(P({},o),{loading:!1,error:e})),_(q,o=>f(P({},o),{loading:!0,error:null})),_(lt,(o,{prompt:e})=>f(P({},o),{prompts:[...o.prompts,e],selectedPrompt:null,loading:!1,error:null})),_(st,(o,{error:e})=>f(P({},o),{loading:!1,error:e})),_(N,o=>f(P({},o),{loading:!0,error:null})),_(at,(o,{prompt:e})=>f(P({},o),{prompts:o.prompts.map(t=>t.id===e.id?e:t),selectedPrompt:null,loading:!1,error:null})),_(pt,(o,{error:e})=>f(P({},o),{loading:!1,error:e})),_($,o=>f(P({},o),{loading:!0,error:null})),_(mt,(o,{promptId:e})=>f(P({},o),{prompts:o.prompts.filter(t=>t.id!==e),loading:!1,error:null})),_(ct,(o,{error:e})=>f(P({},o),{loading:!1,error:e})),_(S,(o,{prompt:e})=>f(P({},o),{selectedPrompt:e})));var Pt=class o{constructor(e){this.http=e}apiUrl=`${zt.apiUrl}/api/prompts`;getPrompts(){return this.http.get(this.apiUrl).pipe(b(this.handleError))}getPrompt(e){return this.http.get(`${this.apiUrl}/${e}`).pipe(b(this.handleError))}createPrompt(e){return this.http.post(this.apiUrl,e).pipe(b(this.handleError))}updatePrompt(e){return this.http.put(`${this.apiUrl}/${e.id}`,e).pipe(b(this.handleError))}deletePrompt(e){return this.http.delete(`${this.apiUrl}/${e}`).pipe(b(this.handleError))}handleError(e){let t="An unknown error occurred";return e.error instanceof ErrorEvent?t=`Client Error: ${e.error.message}`:t=`Server Error: ${e.status} - ${e.message}`,console.error("PromptsService Error:",t),yt(()=>new Error(t))}static \u0275fac=function(t){return new(t||o)(T(Z))};static \u0275prov=A({token:o,factory:o.\u0275fac,providedIn:"root"})};var ft=class o{constructor(e,t){this.actions$=e;this.promptsService=t;this.loadPrompts$=B(()=>this.actions$.pipe(H(L),U(()=>this.promptsService.getPrompts().pipe(O(i=>it({prompts:i})),b(i=>E(nt({error:i.message}))))))),this.createPrompt$=B(()=>this.actions$.pipe(H(q),U(({prompt:i})=>this.promptsService.createPrompt(i).pipe(O(r=>lt({prompt:r})),b(r=>E(st({error:r.message}))))))),this.updatePrompt$=B(()=>this.actions$.pipe(H(N),U(({prompt:i})=>this.promptsService.updatePrompt(i).pipe(O(r=>at({prompt:r})),b(r=>E(pt({error:r.message}))))))),this.deletePrompt$=B(()=>this.actions$.pipe(H($),U(({promptId:i})=>this.promptsService.deletePrompt(i).pipe(O(()=>mt({promptId:i})),b(r=>E(ct({error:r.message})))))))}loadPrompts$;createPrompt$;updatePrompt$;deletePrompt$;static \u0275fac=function(t){return new(t||o)(T(qt),T(Pt))};static \u0275prov=A({token:o,factory:o.\u0275fac})};var Kt=class o{static \u0275fac=function(t){return new(t||o)};static \u0275mod=K({type:o});static \u0275inj=Q({imports:[It,V,Rt.forRoot(),gt,Vt.forFeature("prompts",Gt),Nt.forFeature([ft]),W,k]})};export{Kt as PromptsModule};
