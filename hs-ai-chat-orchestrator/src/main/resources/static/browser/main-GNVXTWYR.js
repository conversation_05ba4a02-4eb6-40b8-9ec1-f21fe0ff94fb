import{a as bt}from"./chunk-XL3ODKCP.js";import{A as Ut,Ab as ge,B as Gt,D as Ht,E as ft,F as U,H as Zt,Ha as X,J as G,K as H,L as z,M as O,N as Vt,O as Kt,P as gt,Ra as W,T as Xt,Ta as te,V as Wt,W as qt,Wa as ee,X as Jt,Za as ne,_a as oe,ab as q,b as dt,ba as N,bb as ie,cb as ae,db as re,e as Nt,ea as A,eb as se,f as Dt,fa as Z,g as Rt,gb as ce,h as Lt,ia as V,j as mt,jb as ht,ka as w,kb as J,lb as le,mb as pe,n as jt,nb as de,o as k,oa as m,ob as vt,pa as g,pb as me,qa as C,qb as ue,sa as K,t as ut,u as D,ua as Yt,ub as fe,v as Ft,va as Qt,w as zt,x as $t,y as Bt,za as M}from"./chunk-5UJX3CQ5.js";import{a as j,b as F}from"./chunk-GAL4ENT6.js";var Y=class t{title="member-matcher";static \u0275fac=function(e){return new(e||t)};static \u0275cmp=A({type:t,selectors:[["app-root"]],decls:1,vars:0,template:function(e,o){e&1&&C(0,"router-outlet")},dependencies:[q],encapsulation:2})};var B="PERFORM_ACTION",Ue="REFRESH",Se="RESET",xe="ROLLBACK",Oe="COMMIT",Me="SWEEP",Ie="TOGGLE_ACTION",Ge="SET_ACTIONS_ACTIVE",Ee="JUMP_TO_STATE",_e="JUMP_TO_ACTION",wt="IMPORT_STATE",Te="LOCK_CHANGES",Ae="PAUSE_RECORDING",L=class{constructor(n,e){if(this.action=n,this.timestamp=e,this.type=B,typeof n.type>"u")throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?')}},Ct=class{constructor(){this.type=Ue}},yt=class{constructor(n){this.timestamp=n,this.type=Se}},St=class{constructor(n){this.timestamp=n,this.type=xe}},xt=class{constructor(n){this.timestamp=n,this.type=Oe}},Ot=class{constructor(){this.type=Me}},Mt=class{constructor(n){this.id=n,this.type=Ie}};var It=class{constructor(n){this.index=n,this.type=Ee}},Et=class{constructor(n){this.actionId=n,this.type=_e}},_t=class{constructor(n){this.nextLiftedState=n,this.type=wt}},Tt=class{constructor(n){this.status=n,this.type=Te}},At=class{constructor(n){this.status=n,this.type=Ae}};var ot=new z("@ngrx/store-devtools Options"),he=new z("@ngrx/store-devtools Initial Config");function Pe(){return null}var He="NgRx Store DevTools";function Ze(t){let n={maxAge:!1,monitor:Pe,actionSanitizer:void 0,stateSanitizer:void 0,name:He,serialize:!1,logOnly:!1,autoPause:!1,trace:!1,traceLimit:75,features:{pause:!0,lock:!0,persist:!0,export:!0,import:"custom",jump:!0,skip:!0,reorder:!0,dispatch:!0,test:!0},connectInZone:!1},e=typeof t=="function"?t():t,o=e.logOnly?{pause:!0,export:!0,test:!0}:!1,i=e.features||o||n.features;i.import===!0&&(i.import="custom");let c=Object.assign({},n,{features:i},e);if(c.maxAge&&c.maxAge<2)throw new Error(`Devtools 'maxAge' cannot be less than 2, got ${c.maxAge}`);return c}function ve(t,n){return t.filter(e=>n.indexOf(e)<0)}function we(t){let{computedStates:n,currentStateIndex:e}=t;if(e>=n.length){let{state:i}=n[n.length-1];return i}let{state:o}=n[e];return o}function $(t){return new L(t,+Date.now())}function Ve(t,n){return Object.keys(n).reduce((e,o)=>{let i=Number(o);return e[i]=ke(t,n[i],i),e},{})}function ke(t,n,e){return F(j({},n),{action:t(n.action,e)})}function Ke(t,n){return n.map((e,o)=>({state:Ne(t,e.state,o),error:e.error}))}function Ne(t,n,e){return t(n,e)}function De(t){return t.predicate||t.actionsSafelist||t.actionsBlocklist}function Xe(t,n,e,o){let i=[],c={},v=[];return t.stagedActionIds.forEach((p,b)=>{let a=t.actionsById[p];a&&(b&&kt(t.computedStates[b],a,n,e,o)||(c[p]=a,i.push(p),v.push(t.computedStates[b])))}),F(j({},t),{stagedActionIds:i,actionsById:c,computedStates:v})}function kt(t,n,e,o,i){let c=e&&!e(t,n.action),v=o&&!n.action.type.match(o.map(b=>be(b)).join("|")),p=i&&n.action.type.match(i.map(b=>be(b)).join("|"));return c||v||p}function be(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Re(t){return{ngZone:t?Vt(qt):null,connectInZone:t}}var it=(()=>{class t extends J{static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=Xt(t)))(i||t)}})()}static{this.\u0275prov=G({token:t,factory:t.\u0275fac})}}return t})(),tt={START:"START",DISPATCH:"DISPATCH",STOP:"STOP",ACTION:"ACTION"},Pt=new z("@ngrx/store-devtools Redux Devtools Extension"),Le=(()=>{class t{constructor(e,o,i){this.config=o,this.dispatcher=i,this.zoneConfig=Re(this.config.connectInZone),this.devtoolsExtension=e,this.createActionStreams()}notify(e,o){if(this.devtoolsExtension)if(e.type===B){if(o.isLocked||o.isPaused)return;let i=we(o);if(De(this.config)&&kt(i,e,this.config.predicate,this.config.actionsSafelist,this.config.actionsBlocklist))return;let c=this.config.stateSanitizer?Ne(this.config.stateSanitizer,i,o.currentStateIndex):i,v=this.config.actionSanitizer?ke(this.config.actionSanitizer,e,o.nextActionId):e;this.sendToReduxDevtools(()=>this.extensionConnection.send(v,c))}else{let i=F(j({},o),{stagedActionIds:o.stagedActionIds,actionsById:this.config.actionSanitizer?Ve(this.config.actionSanitizer,o.actionsById):o.actionsById,computedStates:this.config.stateSanitizer?Ke(this.config.stateSanitizer,o.computedStates):o.computedStates});this.sendToReduxDevtools(()=>this.devtoolsExtension.send(null,i,this.getExtensionConfig(this.config)))}}createChangesObservable(){return this.devtoolsExtension?new dt(e=>{let o=this.zoneConfig.connectInZone?this.zoneConfig.ngZone.runOutsideAngular(()=>this.devtoolsExtension.connect(this.getExtensionConfig(this.config))):this.devtoolsExtension.connect(this.getExtensionConfig(this.config));return this.extensionConnection=o,o.init(),o.subscribe(i=>e.next(i)),o.unsubscribe}):Rt}createActionStreams(){let e=this.createChangesObservable().pipe(Gt()),o=e.pipe(D(a=>a.type===tt.START)),i=e.pipe(D(a=>a.type===tt.STOP)),c=e.pipe(D(a=>a.type===tt.DISPATCH),k(a=>this.unwrapAction(a.payload)),zt(a=>a.type===wt?this.dispatcher.pipe(D(y=>y.type===vt),jt(1e3),$t(1e3),k(()=>a),Ft(()=>mt(a)),Bt(1)):mt(a))),p=e.pipe(D(a=>a.type===tt.ACTION),k(a=>this.unwrapAction(a.payload))).pipe(U(i)),b=c.pipe(U(i));this.start$=o.pipe(U(i)),this.actions$=this.start$.pipe(ft(()=>p)),this.liftedActions$=this.start$.pipe(ft(()=>b))}unwrapAction(e){return typeof e=="string"?(0,eval)(`(${e})`):e}getExtensionConfig(e){let o={name:e.name,features:e.features,serialize:e.serialize,autoPause:e.autoPause??!1,trace:e.trace??!1,traceLimit:e.traceLimit??75};return e.maxAge!==!1&&(o.maxAge=e.maxAge),o}sendToReduxDevtools(e){try{e()}catch(o){console.warn("@ngrx/store-devtools: something went wrong inside the redux devtools",o)}}static{this.\u0275fac=function(o){return new(o||t)(O(Pt),O(ot),O(it))}}static{this.\u0275prov=G({token:t,factory:t.\u0275fac})}}return t})(),nt={type:ht},We="@ngrx/store-devtools/recompute",qe={type:We};function je(t,n,e,o,i){if(o)return{state:e,error:"Interrupted by an error up the chain"};let c=e,v;try{c=t(e,n)}catch(p){v=p.toString(),i.handleError(p)}return{state:c,error:v}}function et(t,n,e,o,i,c,v,p,b){if(n>=t.length&&t.length===c.length)return t;let a=t.slice(0,n),y=c.length-(b?1:0);for(let r=n;r<y;r++){let d=c[r],x=i[d].action,l=a[r-1],s=l?l.state:o,E=l?l.error:void 0,_=v.indexOf(d)>-1?l:je(e,x,s,E,p);a.push(_)}return b&&a.push(t[t.length-1]),a}function Je(t,n){return{monitorState:n(void 0,{}),nextActionId:1,actionsById:{0:$(nt)},stagedActionIds:[0],skippedActionIds:[],committedState:t,currentStateIndex:0,computedStates:[],isLocked:!1,isPaused:!1}}function Ye(t,n,e,o,i={}){return c=>(v,p)=>{let{monitorState:b,actionsById:a,nextActionId:y,stagedActionIds:r,skippedActionIds:d,committedState:x,currentStateIndex:l,computedStates:s,isLocked:E,isPaused:S}=v||n;v||(a=Object.create(a));function _(h){let f=h,P=r.slice(1,f+1);for(let I=0;I<P.length;I++)if(s[I+1].error){f=I,P=r.slice(1,f+1);break}else delete a[P[I]];d=d.filter(I=>P.indexOf(I)===-1),r=[0,...r.slice(f+1)],x=s[f].state,s=s.slice(f),l=l>f?l-f:0}function T(){a={0:$(nt)},y=1,r=[0],d=[],x=s[l].state,l=0,s=[]}let u=0;switch(p.type){case Te:{E=p.status,u=1/0;break}case Ae:{S=p.status,S?(r=[...r,y],a[y]=new L({type:"@ngrx/devtools/pause"},+Date.now()),y++,u=r.length-1,s=s.concat(s[s.length-1]),l===r.length-2&&l++,u=1/0):T();break}case Se:{a={0:$(nt)},y=1,r=[0],d=[],x=t,l=0,s=[];break}case Oe:{T();break}case xe:{a={0:$(nt)},y=1,r=[0],d=[],l=0,s=[];break}case Ie:{let{id:h}=p;d.indexOf(h)===-1?d=[h,...d]:d=d.filter(P=>P!==h),u=r.indexOf(h);break}case Ge:{let{start:h,end:f,active:P}=p,I=[];for(let pt=h;pt<f;pt++)I.push(pt);P?d=ve(d,I):d=[...d,...I],u=r.indexOf(h);break}case Ee:{l=p.index,u=1/0;break}case _e:{let h=r.indexOf(p.actionId);h!==-1&&(l=h),u=1/0;break}case Me:{r=ve(r,d),d=[],l=Math.min(l,r.length-1);break}case B:{if(E)return v||n;if(S||v&&kt(v.computedStates[l],p,i.predicate,i.actionsSafelist,i.actionsBlocklist)){let f=s[s.length-1];s=[...s.slice(0,-1),je(c,p.action,f.state,f.error,e)],u=1/0;break}i.maxAge&&r.length===i.maxAge&&_(1),l===r.length-1&&l++;let h=y++;a[h]=p,r=[...r,h],u=r.length-1;break}case wt:{({monitorState:b,actionsById:a,nextActionId:y,stagedActionIds:r,skippedActionIds:d,committedState:x,currentStateIndex:l,computedStates:s,isLocked:E,isPaused:S}=p.nextLiftedState);break}case ht:{u=0,i.maxAge&&r.length>i.maxAge&&(s=et(s,u,c,x,a,r,d,e,S),_(r.length-i.maxAge),u=1/0);break}case vt:{if(s.filter(f=>f.error).length>0)u=0,i.maxAge&&r.length>i.maxAge&&(s=et(s,u,c,x,a,r,d,e,S),_(r.length-i.maxAge),u=1/0);else{if(!S&&!E){l===r.length-1&&l++;let f=y++;a[f]=new L(p,+Date.now()),r=[...r,f],u=r.length-1,s=et(s,u,c,x,a,r,d,e,S)}s=s.map(f=>F(j({},f),{state:c(f.state,qe)})),l=r.length-1,i.maxAge&&r.length>i.maxAge&&_(r.length-i.maxAge),u=1/0}break}default:{u=1/0;break}}return s=et(s,u,c,x,a,r,d,e,S),b=o(b,p),{monitorState:b,actionsById:a,nextActionId:y,stagedActionIds:r,skippedActionIds:d,committedState:x,currentStateIndex:l,computedStates:s,isLocked:E,isPaused:S}}}var Ce=(()=>{class t{constructor(e,o,i,c,v,p,b,a){let y=Je(b,a.monitor),r=Ye(b,y,p,a.monitor,a),d=ut(ut(o.asObservable().pipe(Ht(1)),c.actions$).pipe(k($)),e,c.liftedActions$).pipe(Lt(Dt)),x=i.pipe(k(r)),l=Re(a.connectInZone),s=new Nt(1);this.liftedStateSubscription=d.pipe(Zt(x),ye(l),Ut(({state:_},[T,u])=>{let h=u(_,T);return T.type!==B&&De(a)&&(h=Xe(h,a.predicate,a.actionsSafelist,a.actionsBlocklist)),c.notify(T,h),{state:h,action:T}},{state:y,action:null})).subscribe(({state:_,action:T})=>{if(s.next(_),T.type===B){let u=T.action;v.next(u)}}),this.extensionStartSubscription=c.start$.pipe(ye(l)).subscribe(()=>{this.refresh()});let E=s.asObservable(),S=E.pipe(k(we));Object.defineProperty(S,"state",{value:ce(S,{manualCleanup:!0,requireSync:!0})}),this.dispatcher=e,this.liftedState=E,this.state=S}ngOnDestroy(){this.liftedStateSubscription.unsubscribe(),this.extensionStartSubscription.unsubscribe()}dispatch(e){this.dispatcher.next(e)}next(e){this.dispatcher.next(e)}error(e){}complete(){}performAction(e){this.dispatch(new L(e,+Date.now()))}refresh(){this.dispatch(new Ct)}reset(){this.dispatch(new yt(+Date.now()))}rollback(){this.dispatch(new St(+Date.now()))}commit(){this.dispatch(new xt(+Date.now()))}sweep(){this.dispatch(new Ot)}toggleAction(e){this.dispatch(new Mt(e))}jumpToAction(e){this.dispatch(new Et(e))}jumpToState(e){this.dispatch(new It(e))}importState(e){this.dispatch(new _t(e))}lockChanges(e){this.dispatch(new Tt(e))}pauseRecording(e){this.dispatch(new At(e))}static{this.\u0275fac=function(o){return new(o||t)(O(it),O(J),O(pe),O(Le),O(me),O(Jt),O(le),O(ot))}}static{this.\u0275prov=G({token:t,factory:t.\u0275fac})}}return t})();function ye({ngZone:t,connectInZone:n}){return e=>n?new dt(o=>e.subscribe({next:i=>t.run(()=>o.next(i)),error:i=>t.run(()=>o.error(i)),complete:()=>t.run(()=>o.complete())})):e}var Qe=new z("@ngrx/store-devtools Is Devtools Extension or Monitor Present");function tn(t,n){return!!t||n.monitor!==Pe}function en(){let t="__REDUX_DEVTOOLS_EXTENSION__";return typeof window=="object"&&typeof window[t]<"u"?window[t]:null}function nn(t){return t.state}function on(t={}){return Kt([Le,it,Ce,{provide:he,useValue:t},{provide:Qe,deps:[Pt,ot],useFactory:tn},{provide:Pt,useFactory:en},{provide:ot,deps:[he],useFactory:Ze},{provide:ue,deps:[Ce],useFactory:nn},{provide:de,useExisting:it}])}var Fe=(()=>{class t{static instrument(e={}){return{ngModule:t,providers:[on(e)]}}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275mod=Z({type:t})}static{this.\u0275inj=H({})}}return t})();var at=class t{toggleSidebar=new Wt;onToggleSidebar(){this.toggleSidebar.emit()}static \u0275fac=function(e){return new(e||t)};static \u0275cmp=A({type:t,selectors:[["app-top-bar"]],outputs:{toggleSidebar:"toggleSidebar"},standalone:!1,decls:37,vars:0,consts:[[1,"navbar","navbar-expand-lg","navbar-dark","bg-dark","px-3"],[1,"container-fluid"],["type","button",1,"btn","btn-outline-light","sidebar-toggle","me-2",3,"click"],[1,"fas","fa-bars"],["href","#",1,"navbar-brand"],[1,"fas","fa-link","me-2"],["type","button","data-bs-toggle","collapse","data-bs-target","#navbarNav","aria-controls","navbarNav","aria-expanded","false","aria-label","Toggle navigation",1,"navbar-toggler"],[1,"navbar-toggler-icon"],["id","navbarNav",1,"collapse","navbar-collapse"],[1,"navbar-nav","ms-auto"],[1,"nav-item"],["href","#",1,"nav-link","position-relative"],[1,"fas","fa-bell"],[1,"position-absolute","top-0","start-100","translate-middle","badge","rounded-pill","bg-danger"],[1,"visually-hidden"],[1,"nav-item","dropdown"],["href","#","id","userDropdown","role","button","data-bs-toggle","dropdown","aria-expanded","false",1,"nav-link","dropdown-toggle"],[1,"fas","fa-user-circle","me-1"],["aria-labelledby","userDropdown",1,"dropdown-menu","dropdown-menu-end"],["href","#",1,"dropdown-item"],[1,"fas","fa-user","me-2"],[1,"fas","fa-cog","me-2"],[1,"dropdown-divider"],[1,"fas","fa-sign-out-alt","me-2"]],template:function(e,o){e&1&&(m(0,"nav",0)(1,"div",1)(2,"button",2),K("click",function(){return o.onToggleSidebar()}),C(3,"i",3),g(),m(4,"a",4),C(5,"i",5),M(6,"Chat UI "),g(),m(7,"button",6),C(8,"span",7),g(),m(9,"div",8)(10,"ul",9)(11,"li",10)(12,"a",11),C(13,"i",12),m(14,"span",13),M(15," 3 "),m(16,"span",14),M(17,"unread notifications"),g()()()(),m(18,"li",15)(19,"a",16),C(20,"i",17),M(21,"Admin User "),g(),m(22,"ul",18)(23,"li")(24,"a",19),C(25,"i",20),M(26,"Profile"),g()(),m(27,"li")(28,"a",19),C(29,"i",21),M(30,"Settings"),g()(),m(31,"li"),C(32,"hr",22),g(),m(33,"li")(34,"a",19),C(35,"i",23),M(36,"Logout"),g()()()()()()()())},styles:[".navbar[_ngcontent-%COMP%]{padding:.5rem 1rem;box-shadow:0 2px 4px #0000001a}.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]{font-weight:600}.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ffc107}.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center}.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{margin-left:.5rem}.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.5rem;position:relative}.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.6rem;padding:.25em .4em}.navbar[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]{right:0;left:auto;margin-top:.5rem;border:none;box-shadow:0 .5rem 1rem #00000026}.navbar[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem}.navbar[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:1rem;text-align:center}@media (max-width: 991.98px){.navbar-nav[_ngcontent-%COMP%]{flex-direction:column!important}.navbar-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{margin-left:0!important;margin-bottom:.5rem}}"]})};var cn=t=>({collapsed:t});function ln(t,n){t&1&&(m(0,"span",11),M(1,"Dashboard"),g())}function pn(t,n){t&1&&(m(0,"span",11),M(1,"Prompts"),g())}function dn(t,n){t&1&&(m(0,"span",11),M(1,"Documents"),g())}var rt=class t{isCollapsed=!1;static \u0275fac=function(e){return new(e||t)};static \u0275cmp=A({type:t,selectors:[["app-side-nav"]],inputs:{isCollapsed:"isCollapsed"},standalone:!1,decls:15,vars:6,consts:[[1,"sidebar",3,"ngClass"],[1,"sidebar-content"],[1,"nav","flex-column"],[1,"nav-item"],["routerLink","/dashboard","routerLinkActive","active",1,"nav-link"],[1,"fas","fa-tachometer-alt","nav-icon"],["class","nav-label",4,"ngIf"],["routerLink","/prompt","routerLinkActive","active",1,"nav-link"],[1,"fas","fa-user-plus","nav-icon"],["routerLink","/matched-members","routerLinkActive","active",1,"nav-link"],[1,"fas","fa-link","nav-icon"],[1,"nav-label"]],template:function(e,o){e&1&&(m(0,"div",0)(1,"div",1)(2,"ul",2)(3,"li",3)(4,"a",4),C(5,"i",5),V(6,ln,2,0,"span",6),g()(),m(7,"li",3)(8,"a",7),C(9,"i",8),V(10,pn,2,0,"span",6),g()(),m(11,"li",3)(12,"a",9),C(13,"i",10),V(14,dn,2,0,"span",6),g()()()()()),e&2&&(w("ngClass",X(4,cn,o.isCollapsed)),N(6),w("ngIf",!o.isCollapsed),N(4),w("ngIf",!o.isCollapsed),N(4),w("ngIf",!o.isCollapsed))},dependencies:[W,te,ie,ae],styles:[".sidebar[_ngcontent-%COMP%]{width:var(--sidebar-width);min-height:calc(100vh - var(--topbar-height));background-color:var(--dark-bg);color:#fffc;transition:width .3s ease;position:fixed;top:var(--topbar-height);left:0;overflow-y:auto;z-index:1020}.sidebar.collapsed[_ngcontent-%COMP%]{width:var(--sidebar-collapsed-width)}.sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]{padding:1rem 0}.sidebar[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{margin-bottom:.25rem}.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.75rem 1.5rem;color:#ffffffb3;display:flex;align-items:center;border-radius:0}.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover{color:#fff;background-color:#ffffff1a}.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]{color:#fff;background-color:#fff3;border-left:3px solid #ffeb3b}.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{width:20px;text-align:center;margin-right:1rem;font-size:1rem}"]})};var un=["*"],st=class t{static \u0275fac=function(e){return new(e||t)};static \u0275cmp=A({type:t,selectors:[["app-content"]],standalone:!1,ngContentSelectors:un,decls:2,vars:0,consts:[[1,"content-wrapper"]],template:function(e,o){e&1&&(Yt(),m(0,"div",0),Qt(1),g())},styles:[".content-wrapper[_ngcontent-%COMP%]{padding:1.5rem;height:calc(100vh - var(--topbar-height));overflow-y:auto;background-color:#f5f7fa}"]})};var gn=t=>({"sidebar-collapsed":t}),ct=class t{sidebarCollapsed=!1;toggleSidebar(){this.sidebarCollapsed=!this.sidebarCollapsed}static \u0275fac=function(e){return new(e||t)};static \u0275cmp=A({type:t,selectors:[["app-main-layout"]],standalone:!1,decls:7,vars:4,consts:[[1,"layout-container"],[3,"toggleSidebar"],[1,"layout-main"],[3,"isCollapsed"],[1,"content-container",3,"ngClass"]],template:function(e,o){e&1&&(m(0,"div",0)(1,"app-top-bar",1),K("toggleSidebar",function(){return o.toggleSidebar()}),g(),m(2,"div",2),C(3,"app-side-nav",3),m(4,"div",4)(5,"app-content"),C(6,"router-outlet"),g()()()()),e&2&&(N(3),w("isCollapsed",o.sidebarCollapsed),N(),w("ngClass",X(2,gn,o.sidebarCollapsed)))},dependencies:[W,q,at,rt,st],styles:[".layout-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh}.layout-main[_ngcontent-%COMP%]{display:flex;flex:1}.content-container[_ngcontent-%COMP%]{flex:1;margin-left:var(--sidebar-width);transition:margin-left .3s ease}.content-container.sidebar-collapsed[_ngcontent-%COMP%]{margin-left:var(--sidebar-collapsed-width)}"]})};var ze=[{path:"",component:ct,children:[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",loadChildren:()=>import("./chunk-5V3GZVEO.js").then(t=>t.DashboardModule)},{path:"prompt",loadChildren:()=>import("./chunk-3XEM7FIU.js").then(t=>t.PromptsModule)}]}];var lt=class t{static \u0275fac=function(e){return new(e||t)};static \u0275mod=Z({type:t});static \u0275inj=H({imports:[ee,se]})};var $e=[re(ze),ne(),gt(lt,fe.forRoot({}),ge.forRoot([]))];bt.production||$e.push(gt(Fe.instrument({maxAge:25,logOnly:bt.production})));var Be={providers:$e};oe(Y,Be).catch(t=>console.error("Error bootstrapping app:",t));
