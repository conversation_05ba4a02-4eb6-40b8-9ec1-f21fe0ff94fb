import{$ as Oe,$a as Ge,C as Fe,Fa as M,G as Se,I as C,J as se,K as k,L as b,La as Q,Ma as je,N as c,Na as A,Oa as Z,Pa as de,Q as z,Qa as ce,T as F,U as Ie,V as h,W as oe,Xa as qe,Y as a,Z as V,_ as S,a as be,aa as Ne,b as Ve,ba as xe,c as De,ca as D,da as d,ea as Te,fa as R,fb as j,ga as g,ha as y,i as U,ia as ae,j as ne,ja as Pe,l as Ee,m as Me,ma as ke,na as le,o as T,p as Ae,q as we,qa as ue,r as P,s as E,sa as $,ua as Re,va as W,x as re}from"./chunk-5UJX3CQ5.js";import{a as m,b as v,g as Ce}from"./chunk-GAL4ENT6.js";var Ze=(()=>{class i{_renderer;_elementRef;onChange=e=>{};onTouched=()=>{};constructor(e,n){this._renderer=e,this._elementRef=n}setProperty(e,n){this._renderer.setProperty(this._elementRef.nativeElement,e,n)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static \u0275fac=function(n){return new(n||i)(d(D),d(V))};static \u0275dir=g({type:i})}return i})(),pe=(()=>{class i extends Ze{static \u0275fac=(()=>{let e;return function(r){return(e||(e=F(i)))(r||i)}})();static \u0275dir=g({type:i,features:[y]})}return i})(),x=new b("");var ct={provide:x,useExisting:C(()=>Je),multi:!0};function ht(){let i=de()?de().getUserAgent():"";return/android (\d+)/.test(i.toLowerCase())}var ft=new b(""),Je=(()=>{class i extends Ze{_compositionMode;_composing=!1;constructor(e,n,r){super(e,n),this._compositionMode=r,this._compositionMode==null&&(this._compositionMode=!ht())}writeValue(e){let n=e??"";this.setProperty("value",n)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static \u0275fac=function(n){return new(n||i)(d(D),d(V),d(ft,8))};static \u0275dir=g({type:i,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(n,r){n&1&&$("input",function(o){return r._handleInput(o.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(o){return r._compositionEnd(o.target.value)})},standalone:!1,features:[M([ct]),y]})}return i})();var ve=new b(""),mt=new b("");function Ke(i){return i!=null}function Xe(i){return Pe(i)?U(i):i}function Ye(i){let t={};return i.forEach(e=>{t=e!=null?m(m({},t),e):t}),Object.keys(t).length===0?null:t}function et(i,t){return t.map(e=>e(i))}function gt(i){return!i.validate}function tt(i){return i.map(t=>gt(t)?t:e=>t.validate(e))}function pt(i){if(!i)return null;let t=i.filter(Ke);return t.length==0?null:function(e){return Ye(et(e,t))}}function it(i){return i!=null?pt(tt(i)):null}function vt(i){if(!i)return null;let t=i.filter(Ke);return t.length==0?null:function(e){let n=et(e,t).map(Xe);return P(n).pipe(T(Ye))}}function nt(i){return i!=null?vt(tt(i)):null}function Le(i,t){return i===null?[t]:Array.isArray(i)?[...i,t]:[i,t]}function yt(i){return i._rawValidators}function _t(i){return i._rawAsyncValidators}function he(i){return i?Array.isArray(i)?i:[i]:[]}function K(i,t){return Array.isArray(i)?i.includes(t):i===t}function Be(i,t){let e=he(t);return he(i).forEach(r=>{K(e,r)||e.push(r)}),e}function He(i,t){return he(t).filter(e=>!K(i,e))}var X=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=it(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=nt(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,e){return this.control?this.control.hasError(t,e):!1}getError(t,e){return this.control?this.control.getError(t,e):null}},fe=class extends X{name;get formDirective(){return null}get path(){return null}},H=class extends X{_parent=null;name=null;valueAccessor=null},me=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},Ct={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},pi=v(m({},Ct),{"[class.ng-submitted]":"isSubmitted"}),vi=(()=>{class i extends me{constructor(e){super(e)}static \u0275fac=function(n){return new(n||i)(d(H,2))};static \u0275dir=g({type:i,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(n,r){n&2&&ke("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},standalone:!1,features:[y]})}return i})();var q="VALID",J="INVALID",I="PENDING",G="DISABLED",N=class{},Y=class extends N{value;source;constructor(t,e){super(),this.value=t,this.source=e}},L=class extends N{pristine;source;constructor(t,e){super(),this.pristine=t,this.source=e}},B=class extends N{touched;source;constructor(t,e){super(),this.touched=t,this.source=e}},O=class extends N{status;source;constructor(t,e){super(),this.status=t,this.source=e}};function bt(i){return(ee(i)?i.validators:i)||null}function Vt(i){return Array.isArray(i)?it(i):i||null}function Dt(i,t){return(ee(t)?t.asyncValidators:i)||null}function Et(i){return Array.isArray(i)?nt(i):i||null}function ee(i){return i!=null&&!Array.isArray(i)&&typeof i=="object"}var ge=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,e){this._assignValidators(t),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return A(this.statusReactive)}set status(t){A(()=>this.statusReactive.set(t))}_status=Z(()=>this.statusReactive());statusReactive=S(void 0);get valid(){return this.status===q}get invalid(){return this.status===J}get pending(){return this.status==I}get disabled(){return this.status===G}get enabled(){return this.status!==G}errors;get pristine(){return A(this.pristineReactive)}set pristine(t){A(()=>this.pristineReactive.set(t))}_pristine=Z(()=>this.pristineReactive());pristineReactive=S(!0);get dirty(){return!this.pristine}get touched(){return A(this.touchedReactive)}set touched(t){A(()=>this.touchedReactive.set(t))}_touched=Z(()=>this.touchedReactive());touchedReactive=S(!1);get untouched(){return!this.touched}_events=new De;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Be(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Be(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(He(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(He(t,this._rawAsyncValidators))}hasValidator(t){return K(this._rawValidators,t)}hasAsyncValidator(t){return K(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let e=this.touched===!1;this.touched=!0;let n=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(v(m({},t),{sourceControl:n})),e&&t.emitEvent!==!1&&this._events.next(new B(!0,n))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(e=>e.markAllAsTouched(t))}markAsUntouched(t={}){let e=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let n=t.sourceControl??this;this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:n})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n),e&&t.emitEvent!==!1&&this._events.next(new B(!1,n))}markAsDirty(t={}){let e=this.pristine===!0;this.pristine=!1;let n=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(v(m({},t),{sourceControl:n})),e&&t.emitEvent!==!1&&this._events.next(new L(!1,n))}markAsPristine(t={}){let e=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let n=t.sourceControl??this;this._forEachChild(r=>{r.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),e&&t.emitEvent!==!1&&this._events.next(new L(!0,n))}markAsPending(t={}){this.status=I;let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new O(this.status,e)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(v(m({},t),{sourceControl:e}))}disable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=G,this.errors=null,this._forEachChild(r=>{r.disable(v(m({},t),{onlySelf:!0}))}),this._updateValue();let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Y(this.value,n)),this._events.next(new O(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(v(m({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(r=>r(!0))}enable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=q,this._forEachChild(n=>{n.enable(v(m({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(v(m({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(n=>n(!1))}_updateAncestors(t,e){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},e),this._parent._updateTouched({},e))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let n=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===q||this.status===I)&&this._runAsyncValidator(n,t.emitEvent)}let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Y(this.value,e)),this._events.next(new O(this.status,e)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(v(m({},t),{sourceControl:e}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?G:q}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,e){if(this.asyncValidator){this.status=I,this._hasOwnPendingAsyncValidator={emitEvent:e!==!1};let n=Xe(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(r=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(r,{emitEvent:e,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,e={}){this.errors=t,this._updateControlsErrors(e.emitEvent!==!1,this,e.shouldHaveEmitted)}get(t){let e=t;return e==null||(Array.isArray(e)||(e=e.split(".")),e.length===0)?null:e.reduce((n,r)=>n&&n._find(r),this)}getError(t,e){let n=e?this.get(e):this;return n&&n.errors?n.errors[t]:null}hasError(t,e){return!!this.getError(t,e)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,e,n){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||n)&&this._events.next(new O(this.status,e)),this._parent&&this._parent._updateControlsErrors(t,e,n)}_initObservables(){this.valueChanges=new h,this.statusChanges=new h}_calculateStatus(){return this._allControlsDisabled()?G:this.errors?J:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(I)?I:this._anyControlsHaveStatus(J)?J:q}_anyControlsHaveStatus(t){return this._anyControls(e=>e.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,e){let n=!this._anyControlsDirty(),r=this.pristine!==n;this.pristine=n,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,e),r&&this._events.next(new L(this.pristine,e))}_updateTouched(t={},e){this.touched=this._anyControlsTouched(),this._events.next(new B(this.touched,e)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,e)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){ee(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let e=this._parent&&this._parent.dirty;return!t&&!!e&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=Vt(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=Et(this._rawAsyncValidators)}};var rt=new b("",{providedIn:"root",factory:()=>ye}),ye="always";function Mt(i,t){return[...t.path,i]}function At(i,t,e=ye){Ft(i,t),t.valueAccessor.writeValue(i.value),(i.disabled||e==="always")&&t.valueAccessor.setDisabledState?.(i.disabled),St(i,t),Ot(i,t),It(i,t),wt(i,t)}function Ue(i,t){i.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(t)})}function wt(i,t){if(t.valueAccessor.setDisabledState){let e=n=>{t.valueAccessor.setDisabledState(n)};i.registerOnDisabledChange(e),t._registerOnDestroy(()=>{i._unregisterOnDisabledChange(e)})}}function Ft(i,t){let e=yt(i);t.validator!==null?i.setValidators(Le(e,t.validator)):typeof e=="function"&&i.setValidators([e]);let n=_t(i);t.asyncValidator!==null?i.setAsyncValidators(Le(n,t.asyncValidator)):typeof n=="function"&&i.setAsyncValidators([n]);let r=()=>i.updateValueAndValidity();Ue(t._rawValidators,r),Ue(t._rawAsyncValidators,r)}function St(i,t){t.valueAccessor.registerOnChange(e=>{i._pendingValue=e,i._pendingChange=!0,i._pendingDirty=!0,i.updateOn==="change"&&st(i,t)})}function It(i,t){t.valueAccessor.registerOnTouched(()=>{i._pendingTouched=!0,i.updateOn==="blur"&&i._pendingChange&&st(i,t),i.updateOn!=="submit"&&i.markAsTouched()})}function st(i,t){i._pendingDirty&&i.markAsDirty(),i.setValue(i._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(i._pendingValue),i._pendingChange=!1}function Ot(i,t){let e=(n,r)=>{t.valueAccessor.writeValue(n),r&&t.viewToModelUpdate(n)};i.registerOnChange(e),t._registerOnDestroy(()=>{i._unregisterOnChange(e)})}function Nt(i,t){if(!i.hasOwnProperty("model"))return!1;let e=i.model;return e.isFirstChange()?!0:!Object.is(t,e.currentValue)}function xt(i){return Object.getPrototypeOf(i.constructor)===pe}function Tt(i,t){if(!t)return null;Array.isArray(t);let e,n,r;return t.forEach(s=>{s.constructor===Je?e=s:xt(s)?n=s:r=s}),r||n||e||null}function ze(i,t){let e=i.indexOf(t);e>-1&&i.splice(e,1)}function $e(i){return typeof i=="object"&&i!==null&&Object.keys(i).length===2&&"value"in i&&"disabled"in i}var Pt=class extends ge{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,e,n){super(bt(e),Dt(n,e)),this._applyFormState(t),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),ee(e)&&(e.nonNullable||e.initialValueIsDefault)&&($e(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,e={}){this.value=this._pendingValue=t,this._onChange.length&&e.emitModelToViewChange!==!1&&this._onChange.forEach(n=>n(this.value,e.emitViewToModelChange!==!1)),this.updateValueAndValidity(e)}patchValue(t,e={}){this.setValue(t,e)}reset(t=this.defaultValue,e={}){this._applyFormState(t),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){ze(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){ze(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){$e(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var kt={provide:H,useExisting:C(()=>Rt)},We=Promise.resolve(),Rt=(()=>{class i extends H{_changeDetectorRef;callSetDisabledState;control=new Pt;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new h;constructor(e,n,r,s,o,l){super(),this._changeDetectorRef=o,this.callSetDisabledState=l,this._parent=e,this._setValidators(n),this._setAsyncValidators(r),this.valueAccessor=Tt(this,s)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){let n=e.name.previousValue;this.formDirective.removeControl({name:n,path:this._getPath(n)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),Nt(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){At(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(e){We.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){let n=e.isDisabled.currentValue,r=n!==0&&je(n);We.then(()=>{r&&!this.control.disabled?this.control.disable():!r&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?Mt(e,this._parent):[e]}static \u0275fac=function(n){return new(n||i)(d(fe,9),d(ve,10),d(mt,10),d(x,10),d(Q,8),d(rt,8))};static \u0275dir=g({type:i,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[M([kt]),y,z]})}return i})();var jt={provide:x,useExisting:C(()=>at),multi:!0};function ot(i,t){return i==null?`${t}`:(t&&typeof t=="object"&&(t="Object"),`${i}: ${t}`.slice(0,50))}function qt(i){return i.split(":")[0]}var at=(()=>{class i extends pe{value;_optionMap=new Map;_idCounter=0;set compareWith(e){this._compareWith=e}_compareWith=Object.is;writeValue(e){this.value=e;let n=this._getOptionId(e),r=ot(n,e);this.setProperty("value",r)}registerOnChange(e){this.onChange=n=>{this.value=this._getOptionValue(n),e(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(e){for(let n of this._optionMap.keys())if(this._compareWith(this._optionMap.get(n),e))return n;return null}_getOptionValue(e){let n=qt(e);return this._optionMap.has(n)?this._optionMap.get(n):e}static \u0275fac=(()=>{let e;return function(r){return(e||(e=F(i)))(r||i)}})();static \u0275dir=g({type:i,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(n,r){n&1&&$("change",function(o){return r.onChange(o.target.value)})("blur",function(){return r.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[M([jt]),y]})}return i})(),_i=(()=>{class i{_element;_renderer;_select;id;constructor(e,n,r){this._element=e,this._renderer=n,this._select=r,this._select&&(this.id=this._select._registerOption())}set ngValue(e){this._select!=null&&(this._select._optionMap.set(this.id,e),this._setElementValue(ot(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._setElementValue(e),this._select&&this._select.writeValue(this._select.value)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(n){return new(n||i)(d(V),d(D),d(at,9))};static \u0275dir=g({type:i,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return i})(),Gt={provide:x,useExisting:C(()=>lt),multi:!0};function Qe(i,t){return i==null?`${t}`:(typeof t=="string"&&(t=`'${t}'`),t&&typeof t=="object"&&(t="Object"),`${i}: ${t}`.slice(0,50))}function Lt(i){return i.split(":")[0]}var lt=(()=>{class i extends pe{value;_optionMap=new Map;_idCounter=0;set compareWith(e){this._compareWith=e}_compareWith=Object.is;writeValue(e){this.value=e;let n;if(Array.isArray(e)){let r=e.map(s=>this._getOptionId(s));n=(s,o)=>{s._setSelected(r.indexOf(o.toString())>-1)}}else n=(r,s)=>{r._setSelected(!1)};this._optionMap.forEach(n)}registerOnChange(e){this.onChange=n=>{let r=[],s=n.selectedOptions;if(s!==void 0){let o=s;for(let l=0;l<o.length;l++){let u=o[l],f=this._getOptionValue(u.value);r.push(f)}}else{let o=n.options;for(let l=0;l<o.length;l++){let u=o[l];if(u.selected){let f=this._getOptionValue(u.value);r.push(f)}}}this.value=r,e(r)}}_registerOption(e){let n=(this._idCounter++).toString();return this._optionMap.set(n,e),n}_getOptionId(e){for(let n of this._optionMap.keys())if(this._compareWith(this._optionMap.get(n)._value,e))return n;return null}_getOptionValue(e){let n=Lt(e);return this._optionMap.has(n)?this._optionMap.get(n)._value:e}static \u0275fac=(()=>{let e;return function(r){return(e||(e=F(i)))(r||i)}})();static \u0275dir=g({type:i,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(n,r){n&1&&$("change",function(o){return r.onChange(o.target)})("blur",function(){return r.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[M([Gt]),y]})}return i})(),Ci=(()=>{class i{_element;_renderer;_select;id;_value;constructor(e,n,r){this._element=e,this._renderer=n,this._select=r,this._select&&(this.id=this._select._registerOption(this))}set ngValue(e){this._select!=null&&(this._value=e,this._setElementValue(Qe(this.id,e)),this._select.writeValue(this._select.value))}set value(e){this._select?(this._value=e,this._setElementValue(Qe(this.id,e)),this._select.writeValue(this._select.value)):this._setElementValue(e)}_setElementValue(e){this._renderer.setProperty(this._element.nativeElement,"value",e)}_setSelected(e){this._renderer.setProperty(this._element.nativeElement,"selected",e)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(n){return new(n||i)(d(V),d(D),d(lt,9))};static \u0275dir=g({type:i,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return i})();var Bt=(()=>{class i{static \u0275fac=function(n){return new(n||i)};static \u0275mod=R({type:i});static \u0275inj=k({})}return i})();var bi=(()=>{class i{static withConfig(e){return{ngModule:i,providers:[{provide:rt,useValue:e.callSetDisabledState??ye}]}}static \u0275fac=function(n){return new(n||i)};static \u0275mod=R({type:i});static \u0275inj=k({imports:[Bt]})}return i})();var te={toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image","video"],["table"]]},_e=new b("config",{providedIn:"root",factory:()=>({modules:te})});var Ht=[[["","above-quill-editor-toolbar",""]],[["","quill-editor-toolbar",""]],[["","below-quill-editor-toolbar",""]]],Ut=["[above-quill-editor-toolbar]","[quill-editor-toolbar]","[below-quill-editor-toolbar]"];function zt(i,t){i&1&&ue(0,"div",0)}function $t(i,t){i&1&&ue(0,"div",0)}var ie=(i,t)=>i||t||"html",Wt=()=>new Ve(i=>{let t=requestAnimationFrame(()=>{i.next(),i.complete()});return()=>cancelAnimationFrame(t)}),Qt=(()=>{class i{constructor(){this.config=c(_e)||{modules:te},this.document=c(ce),this.quill$=we(()=>Ce(this,null,function*(){if(!this.Quill){let e=this.document.addEventListener;this.document.addEventListener=this.document.__zone_symbol__addEventListener||this.document.addEventListener;let n=yield import("./chunk-TVW5FX7Q.js");this.document.addEventListener=e,this.Quill=n.default?.default??n.default??n}return this.config.customOptions?.forEach(e=>{let n=this.Quill.import(e.import);n.whitelist=e.whitelist,this.Quill.register(n,!0,this.config.suppressGlobalRegisterWarning)}),Me(this.registerCustomModules(this.Quill,this.config.customModules,this.config.suppressGlobalRegisterWarning))})).pipe(Fe({bufferSize:1,refCount:!1})),this.registeredModules=new Set}getQuill(){return this.quill$}beforeRender(e,n,r=this.config.beforeRender){let s=[this.registerCustomModules(e,n)];return r&&s.push(U(r())),P(s).pipe(T(()=>e))}registerCustomModules(e,n,r){if(!Array.isArray(n))return ne(e);let s=[];for(let o of n){let{path:l,implementation:u}=o;this.registeredModules.has(l)||(this.registeredModules.add(l),Ee(u)?s.push(u.pipe(Se(f=>{e.register(l,f,r)}))):e.register(l,u,r))}return s.length>0?P(s).pipe(T(()=>e)):ne(e)}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275prov=se({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Zt=(()=>{class i{constructor(){this.format=a(void 0),this.theme=a(void 0),this.modules=a(void 0),this.debug=a(!1),this.readOnly=a(!1),this.placeholder=a(void 0),this.maxLength=a(void 0),this.minLength=a(void 0),this.required=a(!1),this.formats=a(void 0),this.customToolbarPosition=a("top"),this.sanitize=a(void 0),this.beforeRender=a(void 0),this.styles=a(null),this.registry=a(void 0),this.bounds=a(void 0),this.customOptions=a([]),this.customModules=a([]),this.trackChanges=a(void 0),this.classes=a(void 0),this.trimOnValidation=a(!1),this.linkPlaceholder=a(void 0),this.compareValues=a(!1),this.filterNull=a(!1),this.debounceTime=a(void 0),this.defaultEmptyValue=a(null),this.onEditorCreated=new h,this.onEditorChanged=new h,this.onContentChanged=new h,this.onSelectionChanged=new h,this.onFocus=new h,this.onBlur=new h,this.onNativeFocus=new h,this.onNativeBlur=new h,this.disabled=!1,this.toolbarPosition=S("top"),this.subscription=null,this.quillSubscription=null,this.elementRef=c(V),this.document=c(ce),this.cd=c(Q),this.domSanitizer=c(Ge),this.platformId=c(Oe),this.renderer=c(D),this.zone=c(oe),this.service=c(Qt),this.destroyRef=c(Ie),this.valueGetter=a(e=>{let n=e.getSemanticHTML();this.isEmptyValue(n)&&(n=this.defaultEmptyValue());let r=n,s=ie(this.format(),this.service.config.format);if(s==="text")r=e.getText();else if(s==="object")r=e.getContents();else if(s==="json")try{r=JSON.stringify(e.getContents())}catch{r=e.getText()}return r}),this.valueSetter=a((e,n)=>{let r=ie(this.format(),this.service.config.format);if(r==="html")return([!0,!1].includes(this.sanitize())?this.sanitize():this.service.config.sanitize||!1)&&(n=this.domSanitizer.sanitize(Ne.HTML,n)),e.clipboard.convert({html:n});if(r==="json")try{return JSON.parse(n)}catch{return[{insert:n}]}return n}),this.selectionChangeHandler=(e,n,r)=>{let s=this.trackChanges()||this.service.config.trackChanges,o=!e&&!!this.onModelTouched&&(r==="user"||s&&s==="all");!this.onBlur.observed&&!this.onFocus.observed&&!this.onSelectionChanged.observed&&!o||this.zone.run(()=>{e===null?this.onBlur.emit({editor:this.quillEditor,source:r}):n===null&&this.onFocus.emit({editor:this.quillEditor,source:r}),this.onSelectionChanged.emit({editor:this.quillEditor,oldRange:n,range:e,source:r}),o&&this.onModelTouched(),this.cd.markForCheck()})},this.textChangeHandler=(e,n,r)=>{let s=this.quillEditor.getText(),o=this.quillEditor.getContents(),l=this.quillEditor.getSemanticHTML();this.isEmptyValue(l)&&(l=this.defaultEmptyValue());let u=this.trackChanges()||this.service.config.trackChanges,f=(r==="user"||u&&u==="all")&&!!this.onModelChange;!this.onContentChanged.observed&&!f||this.zone.run(()=>{if(f){let w=this.valueGetter();this.onModelChange(w(this.quillEditor))}this.onContentChanged.emit({content:o,delta:e,editor:this.quillEditor,html:l,oldDelta:n,source:r,text:s}),this.cd.markForCheck()})},this.editorChangeHandler=(e,n,r,s)=>{if(this.onEditorChanged.observed)if(e==="text-change"){let o=this.quillEditor.getText(),l=this.quillEditor.getContents(),u=this.quillEditor.getSemanticHTML();this.isEmptyValue(u)&&(u=this.defaultEmptyValue()),this.zone.run(()=>{this.onEditorChanged.emit({content:l,delta:n,editor:this.quillEditor,event:e,html:u,oldDelta:r,source:s,text:o}),this.cd.markForCheck()})}else this.zone.run(()=>{this.onEditorChanged.emit({editor:this.quillEditor,event:e,oldRange:r,range:n,source:s}),this.cd.markForCheck()})}}static normalizeClassNames(e){return e.trim().split(" ").reduce((r,s)=>{let o=s.trim();return o&&r.push(o),r},[])}ngOnInit(){this.toolbarPosition.set(this.customToolbarPosition())}ngAfterViewInit(){qe(this.platformId)||(this.quillSubscription=this.service.getQuill().pipe(Ae(e=>this.service.beforeRender(e,this.customModules(),this.beforeRender()))).subscribe(e=>{this.editorElem=this.elementRef.nativeElement.querySelector("[quill-editor-element]");let n=this.elementRef.nativeElement.querySelector("[quill-editor-toolbar]"),r=Object.assign({},this.modules()||this.service.config.modules);n?r.toolbar=n:r.toolbar===void 0&&(r.toolbar=te.toolbar);let s=this.placeholder()!==void 0?this.placeholder():this.service.config.placeholder;s===void 0&&(s="Insert text here ...");let o=this.styles();o&&Object.keys(o).forEach(p=>{this.renderer.setStyle(this.editorElem,p,o[p])}),this.classes()&&this.addClasses(this.classes()),this.customOptions().forEach(p=>{let _=e.import(p.import);_.whitelist=p.whitelist,e.register(_,!0)});let l=this.bounds()&&this.bounds()==="self"?this.editorElem:this.bounds();l||(l=this.service.config.bounds?this.service.config.bounds:this.document.body);let u=this.debug();!u&&u!==!1&&this.service.config.debug&&(u=this.service.config.debug);let f=this.readOnly();!f&&this.readOnly()!==!1&&(f=this.service.config.readOnly!==void 0?this.service.config.readOnly:!1);let w=this.formats();if(!w&&w===void 0&&(w=this.service.config.formats?[...this.service.config.formats]:this.service.config.formats===null?null:void 0),this.zone.runOutsideAngular(()=>{if(this.quillEditor=new e(this.editorElem,{bounds:l,debug:u,formats:w,modules:r,placeholder:s,readOnly:f,registry:this.registry(),theme:this.theme()||(this.service.config.theme?this.service.config.theme:"snow")}),this.onNativeBlur.observed){E(this.quillEditor.scroll.domNode,"blur").pipe(j(this.destroyRef)).subscribe(()=>this.onNativeBlur.next({editor:this.quillEditor,source:"dom"}));let p=this.quillEditor.getModule("toolbar");p.container&&E(p.container,"mousedown").pipe(j(this.destroyRef)).subscribe(_=>_.preventDefault())}if(this.onNativeFocus.observed&&E(this.quillEditor.scroll.domNode,"focus").pipe(j(this.destroyRef)).subscribe(()=>this.onNativeFocus.next({editor:this.quillEditor,source:"dom"})),this.linkPlaceholder()){let _=this.quillEditor?.theme?.tooltip?.root?.querySelector("input[data-link]");_?.dataset&&(_.dataset.link=this.linkPlaceholder())}}),this.content){if(ie(this.format(),this.service.config.format)==="text")this.quillEditor.setText(this.content,"silent");else{let ut=this.valueSetter()(this.quillEditor,this.content);this.quillEditor.setContents(ut,"silent")}this.quillEditor.getModule("history").clear()}this.setDisabledState(),this.addQuillEventListeners(),!(!this.onEditorCreated.observed&&!this.onValidatorChanged)&&Wt().pipe(j(this.destroyRef)).subscribe(()=>{this.onValidatorChanged&&this.onValidatorChanged(),this.onEditorCreated.emit(this.quillEditor)})}))}ngOnDestroy(){this.dispose(),this.quillSubscription?.unsubscribe(),this.quillSubscription=null}ngOnChanges(e){if(this.quillEditor){if(e.readOnly&&this.quillEditor.enable(!e.readOnly.currentValue),e.placeholder&&(this.quillEditor.root.dataset.placeholder=e.placeholder.currentValue),e.styles){let n=e.styles.currentValue,r=e.styles.previousValue;r&&Object.keys(r).forEach(s=>{this.renderer.removeStyle(this.editorElem,s)}),n&&Object.keys(n).forEach(s=>{this.renderer.setStyle(this.editorElem,s,this.styles()[s])})}if(e.classes){let n=e.classes.currentValue,r=e.classes.previousValue;r&&this.removeClasses(r),n&&this.addClasses(n)}e.debounceTime&&this.addQuillEventListeners()}}addClasses(e){i.normalizeClassNames(e).forEach(n=>{this.renderer.addClass(this.editorElem,n)})}removeClasses(e){i.normalizeClassNames(e).forEach(n=>{this.renderer.removeClass(this.editorElem,n)})}writeValue(e){if(this.filterNull()&&e===null||(this.content=e,!this.quillEditor))return;let n=ie(this.format(),this.service.config.format),s=this.valueSetter()(this.quillEditor,e);if(this.compareValues()){let o=this.quillEditor.getContents();if(JSON.stringify(o)===JSON.stringify(s))return}if(e){n==="text"?this.quillEditor.setText(e):this.quillEditor.setContents(s);return}this.quillEditor.setText("")}setDisabledState(e=this.disabled){this.disabled=e,this.quillEditor&&(e?(this.quillEditor.disable(),this.renderer.setAttribute(this.elementRef.nativeElement,"disabled","disabled")):(this.readOnly()||this.quillEditor.enable(),this.renderer.removeAttribute(this.elementRef.nativeElement,"disabled")))}registerOnChange(e){this.onModelChange=e}registerOnTouched(e){this.onModelTouched=e}registerOnValidatorChange(e){this.onValidatorChanged=e}validate(){if(!this.quillEditor)return null;let e={},n=!0,r=this.quillEditor.getText(),s=this.trimOnValidation()?r.trim().length:r.length===1&&r.trim().length===0?0:r.length-1,o=this.quillEditor.getContents().ops,l=!!o&&o.length===1&&[`
`,""].includes(o[0].insert?.toString());return this.minLength()&&s&&s<this.minLength()&&(e.minLengthError={given:s,minLength:this.minLength()},n=!1),this.maxLength()&&s>this.maxLength()&&(e.maxLengthError={given:s,maxLength:this.maxLength()},n=!1),this.required()&&!s&&l&&(e.requiredError={empty:!0},n=!1),n?null:e}addQuillEventListeners(){this.dispose(),this.zone.runOutsideAngular(()=>{this.subscription=new be,this.subscription.add(E(this.quillEditor,"selection-change").subscribe(([r,s,o])=>{this.selectionChangeHandler(r,s,o)}));let e=E(this.quillEditor,"text-change"),n=E(this.quillEditor,"editor-change");typeof this.debounceTime()=="number"&&(e=e.pipe(re(this.debounceTime())),n=n.pipe(re(this.debounceTime()))),this.subscription.add(e.subscribe(([r,s,o])=>{this.textChangeHandler(r,s,o)})),this.subscription.add(n.subscribe(([r,s,o,l])=>{this.editorChangeHandler(r,s,o,l)}))})}dispose(){this.subscription!==null&&(this.subscription.unsubscribe(),this.subscription=null)}isEmptyValue(e){return e==="<p></p>"||e==="<div></div>"||e==="<p><br></p>"||e==="<div><br></div>"}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275dir=g({type:i,inputs:{format:[1,"format"],theme:[1,"theme"],modules:[1,"modules"],debug:[1,"debug"],readOnly:[1,"readOnly"],placeholder:[1,"placeholder"],maxLength:[1,"maxLength"],minLength:[1,"minLength"],required:[1,"required"],formats:[1,"formats"],customToolbarPosition:[1,"customToolbarPosition"],sanitize:[1,"sanitize"],beforeRender:[1,"beforeRender"],styles:[1,"styles"],registry:[1,"registry"],bounds:[1,"bounds"],customOptions:[1,"customOptions"],customModules:[1,"customModules"],trackChanges:[1,"trackChanges"],classes:[1,"classes"],trimOnValidation:[1,"trimOnValidation"],linkPlaceholder:[1,"linkPlaceholder"],compareValues:[1,"compareValues"],filterNull:[1,"filterNull"],debounceTime:[1,"debounceTime"],defaultEmptyValue:[1,"defaultEmptyValue"],valueGetter:[1,"valueGetter"],valueSetter:[1,"valueSetter"]},outputs:{onEditorCreated:"onEditorCreated",onEditorChanged:"onEditorChanged",onContentChanged:"onContentChanged",onSelectionChanged:"onSelectionChanged",onFocus:"onFocus",onBlur:"onBlur",onNativeFocus:"onNativeFocus",onNativeBlur:"onNativeBlur"},features:[z]})}}return i})(),Qi=(()=>{class i extends Zt{static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=F(i)))(r||i)}})()}static{this.\u0275cmp=Te({type:i,selectors:[["quill-editor"]],features:[M([{multi:!0,provide:x,useExisting:C(()=>i)},{multi:!0,provide:ve,useExisting:C(()=>i)}]),y],ngContentSelectors:Ut,decls:5,vars:2,consts:[["quill-editor-element",""]],template:function(n,r){n&1&&(Re(Ht),ae(0,zt,1,0,"div",0),W(1),W(2,1),W(3,2),ae(4,$t,1,0,"div",0)),n&2&&(le(r.toolbarPosition()!=="top"?0:-1),xe(4),le(r.toolbarPosition()==="top"?4:-1))},styles:["[_nghost-%COMP%]{display:inline-block}"]})}}return i})();var Zi=(()=>{class i{static forRoot(e){return{ngModule:i,providers:[{provide:_e,useValue:e}]}}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=R({type:i})}static{this.\u0275inj=k({})}}return i})();export{Je as a,vi as b,Rt as c,at as d,_i as e,Ci as f,bi as g,Qi as h,Zi as i};
