import{a as D,b as j}from"./chunk-GAL4ENT6.js";function bd(e,t){return Object.is(e,t)}var re=null,ko=!1,ha=1,Ne=Symbol("SIGNAL");function P(e){let t=re;return re=e,t}function Cd(){return re}var wn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Po(e){if(ko)throw new Error("");if(re===null)return;re.consumerOnSignalRead(e);let t=re.nextProducerIndex++;if(Uo(re),t<re.producerNode.length&&re.producerNode[t]!==e&&wr(re)){let n=re.producerNode[t];Vo(n,re.producerIndexOfThis[t])}re.producerNode[t]!==e&&(re.producerNode[t]=e,re.producerIndexOfThis[t]=wr(re)?_d(e,re,t):0),re.producerLastReadVersion[t]=e.version}function bv(){ha++}function Sd(e){if(!(wr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ha)){if(!e.producerMustRecompute(e)&&!jo(e)){Id(e);return}e.producerRecomputeValue(e),Id(e)}}function Md(e){if(e.liveConsumerNode===void 0)return;let t=ko;ko=!0;try{for(let n of e.liveConsumerNode)n.dirty||Cv(n)}finally{ko=t}}function Td(){return re?.consumerAllowSignalWrites!==!1}function Cv(e){e.dirty=!0,Md(e),e.consumerMarkedDirty?.(e)}function Id(e){e.dirty=!1,e.lastCleanEpoch=ha}function Ir(e){return e&&(e.nextProducerIndex=0),P(e)}function Lo(e,t){if(P(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(wr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Vo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function jo(e){Uo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Sd(n),r!==n.version))return!0}return!1}function br(e){if(Uo(e),wr(e))for(let t=0;t<e.producerNode.length;t++)Vo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function _d(e,t,n){if(Rd(e),e.liveConsumerNode.length===0&&xd(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=_d(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Vo(e,t){if(Rd(e),e.liveConsumerNode.length===1&&xd(e))for(let r=0;r<e.producerNode.length;r++)Vo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Uo(o),o.producerIndexOfThis[r]=t}}function wr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Uo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Rd(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function xd(e){return e.producerNode!==void 0}function Nd(e,t){let n=Object.create(Sv);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Sd(n),Po(n),n.value===Fo)throw n.error;return n.value};return r[Ne]=n,r}var da=Symbol("UNSET"),fa=Symbol("COMPUTING"),Fo=Symbol("ERRORED"),Sv=j(D({},wn),{value:da,dirty:!0,error:null,equal:bd,kind:"computed",producerMustRecompute(e){return e.value===da||e.value===fa},producerRecomputeValue(e){if(e.value===fa)throw new Error("Detected cycle in computations.");let t=e.value;e.value=fa;let n=Ir(e),r,o=!1;try{r=e.computation(),P(null),o=t!==da&&t!==Fo&&r!==Fo&&e.equal(t,r)}catch(i){r=Fo,e.error=i}finally{Lo(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Mv(){throw new Error}var Ad=Mv;function Od(e){Ad(e)}function kd(e){Ad=e}var Tv=null;function Fd(e,t){let n=Object.create(pa);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Po(n),n.value);return r[Ne]=n,r}function Bo(e,t){Td()||Od(e),e.equal(e.value,t)||(e.value=t,_v(e))}function Pd(e,t){Td()||Od(e),Bo(e,t(e.value))}var pa=j(D({},wn),{equal:bd,value:void 0,kind:"signal"});function _v(e){e.version++,bv(),Md(e),Tv?.()}function Ld(e){let t=P(null);try{return e()}finally{P(t)}}var ga;function Cr(){return ga}function ut(e){let t=ga;return ga=e,t}var ma=Symbol("NotFound");function S(e){return typeof e=="function"}function Rt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var $o=Rt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Gt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Y=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(S(r))try{r()}catch(i){t=i instanceof $o?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{jd(i)}catch(s){t=t??[],s instanceof $o?t=[...t,...s.errors]:t.push(s)}}if(t)throw new $o(t)}}add(t){var n;if(t&&t!==this)if(this.closed)jd(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Gt(n,t)}remove(t){let{_finalizers:n}=this;n&&Gt(n,t),t instanceof e&&t._removeParent(this)}};Y.EMPTY=(()=>{let e=new Y;return e.closed=!0,e})();var ya=Y.EMPTY;function Ho(e){return e instanceof Y||e&&"closed"in e&&S(e.remove)&&S(e.add)&&S(e.unsubscribe)}function jd(e){S(e)?e():e.unsubscribe()}var je={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var In={setTimeout(e,t,...n){let{delegate:r}=In;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=In;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function zo(e){In.setTimeout(()=>{let{onUnhandledError:t}=je;if(t)t(e);else throw e})}function lt(){}var Vd=va("C",void 0,void 0);function Ud(e){return va("E",void 0,e)}function Bd(e){return va("N",e,void 0)}function va(e,t,n){return{kind:e,value:t,error:n}}var Wt=null;function bn(e){if(je.useDeprecatedSynchronousErrorHandling){let t=!Wt;if(t&&(Wt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Wt;if(Wt=null,n)throw r}}else e()}function $d(e){je.useDeprecatedSynchronousErrorHandling&&Wt&&(Wt.errorThrown=!0,Wt.error=e)}var Zt=class extends Y{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Ho(t)&&t.add(this)):this.destination=Nv}static create(t,n,r){return new Ve(t,n,r)}next(t){this.isStopped?Ea(Bd(t),this):this._next(t)}error(t){this.isStopped?Ea(Ud(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Ea(Vd,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Rv=Function.prototype.bind;function Da(e,t){return Rv.call(e,t)}var wa=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){qo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){qo(r)}else qo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){qo(n)}}},Ve=class extends Zt{constructor(t,n,r){super();let o;if(S(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&je.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Da(t.next,i),error:t.error&&Da(t.error,i),complete:t.complete&&Da(t.complete,i)}):o=t}this.destination=new wa(o)}};function qo(e){je.useDeprecatedSynchronousErrorHandling?$d(e):zo(e)}function xv(e){throw e}function Ea(e,t){let{onStoppedNotification:n}=je;n&&In.setTimeout(()=>n(e,t))}var Nv={closed:!0,next:lt,error:xv,complete:lt};var Cn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ue(e){return e}function Ia(...e){return ba(e)}function ba(e){return e.length===0?ue:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var R=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Ov(n)?n:new Ve(n,r,o);return bn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Hd(r),new r((o,i)=>{let s=new Ve({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Cn](){return this}pipe(...n){return ba(n)(this)}toPromise(n){return n=Hd(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Hd(e){var t;return(t=e??je.Promise)!==null&&t!==void 0?t:Promise}function Av(e){return e&&S(e.next)&&S(e.error)&&S(e.complete)}function Ov(e){return e&&e instanceof Zt||Av(e)&&Ho(e)}function Ca(e){return S(e?.lift)}function C(e){return t=>{if(Ca(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function b(e,t,n,r,o){return new Sr(e,t,n,r,o)}var Sr=class extends Zt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Sn(){return C((e,t)=>{let n=null;e._refCount++;let r=b(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Mn=class extends R{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Ca(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Y;let n=this.getSubject();t.add(this.source.subscribe(b(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Y.EMPTY)}return t}refCount(){return Sn()(this)}};var zd=Rt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var U=(()=>{class e extends R{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Go(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new zd}next(n){bn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){bn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){bn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ya:(this.currentObservers=null,i.push(n),new Y(()=>{this.currentObservers=null,Gt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new R;return n.source=this,n}}return e.create=(t,n)=>new Go(t,n),e})(),Go=class extends U{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ya}};var K=class extends U{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Mr={now(){return(Mr.delegate||Date).now()},delegate:void 0};var Tn=class extends U{constructor(t=1/0,n=1/0,r=Mr){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Wo=class extends Y{constructor(t,n){super()}schedule(t,n=0){return this}};var Tr={setInterval(e,t,...n){let{delegate:r}=Tr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Tr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var _n=class extends Wo{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Tr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Tr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Gt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Rn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Rn.now=Mr.now;var xn=class extends Rn{constructor(t,n=Rn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Zo=new xn(_n);var Yo=class extends _n{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}schedule(t,n=0){return n>0?super.schedule(t,n):(this.delay=n,this.state=t,this.scheduler.flush(this),this)}execute(t,n){return n>0||this.closed?super.execute(t,n):this._execute(t,n)}requestAsyncId(t,n,r=0){return r!=null&&r>0||r==null&&this.delay>0?super.requestAsyncId(t,n,r):(t.flush(this),0)}};var Qo=class extends xn{};var Sa=new Qo(Yo);var ie=new R(e=>e.complete());function qd(e){return e&&S(e.schedule)}function Ma(e){return e[e.length-1]}function Nn(e){return S(Ma(e))?e.pop():void 0}function Ye(e){return qd(Ma(e))?e.pop():void 0}function Gd(e,t){return typeof Ma(e)=="number"?e.pop():t}function Zd(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function Wd(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Yt(e){return this instanceof Yt?(this.v=e,this):new Yt(e)}function Yd(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(p){return Promise.resolve(p).then(f,d)}}function a(f,p){r[f]&&(o[f]=function(v){return new Promise(function(I,_){i.push([f,v,I,_])>1||c(f,v)})},p&&(o[f]=p(o[f])))}function c(f,p){try{u(r[f](p))}catch(v){h(i[0][3],v)}}function u(f){f.value instanceof Yt?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,p){f(p),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Qd(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Wd=="function"?Wd(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var An=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ko(e){return S(e?.then)}function Jo(e){return S(e[Cn])}function Xo(e){return Symbol.asyncIterator&&S(e?.[Symbol.asyncIterator])}function ei(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function kv(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ti=kv();function ni(e){return S(e?.[ti])}function ri(e){return Yd(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Yt(n.read());if(o)return yield Yt(void 0);yield yield Yt(r)}}finally{n.releaseLock()}})}function oi(e){return S(e?.getReader)}function L(e){if(e instanceof R)return e;if(e!=null){if(Jo(e))return Fv(e);if(An(e))return Pv(e);if(Ko(e))return Lv(e);if(Xo(e))return Kd(e);if(ni(e))return jv(e);if(oi(e))return Vv(e)}throw ei(e)}function Fv(e){return new R(t=>{let n=e[Cn]();if(S(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Pv(e){return new R(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Lv(e){return new R(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,zo)})}function jv(e){return new R(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Kd(e){return new R(t=>{Uv(e,t).catch(n=>t.error(n))})}function Vv(e){return Kd(ri(e))}function Uv(e,t){var n,r,o,i;return Zd(this,void 0,void 0,function*(){try{for(n=Qd(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function de(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Qt(e,t=0){return C((n,r)=>{n.subscribe(b(r,o=>de(r,e,()=>r.next(o),t),()=>de(r,e,()=>r.complete(),t),o=>de(r,e,()=>r.error(o),t)))})}function ii(e,t=0){return C((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Jd(e,t){return L(e).pipe(ii(t),Qt(t))}function Xd(e,t){return L(e).pipe(ii(t),Qt(t))}function ef(e,t){return new R(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function tf(e,t){return new R(n=>{let r;return de(n,t,()=>{r=e[ti](),de(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>S(r?.return)&&r.return()})}function si(e,t){if(!e)throw new Error("Iterable cannot be null");return new R(n=>{de(n,t,()=>{let r=e[Symbol.asyncIterator]();de(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function nf(e,t){return si(ri(e),t)}function rf(e,t){if(e!=null){if(Jo(e))return Jd(e,t);if(An(e))return ef(e,t);if(Ko(e))return Xd(e,t);if(Xo(e))return si(e,t);if(ni(e))return tf(e,t);if(oi(e))return nf(e,t)}throw ei(e)}function q(e,t){return t?rf(e,t):L(e)}function M(...e){let t=Ye(e);return q(e,t)}function xt(e,t){let n=S(e)?e:()=>e,r=o=>o.error(n());return new R(t?o=>t.schedule(r,0,o):r)}var Nt=class e{constructor(t,n,r){this.kind=t,this.value=n,this.error=r,this.hasValue=t==="N"}observe(t){return Ta(this,t)}do(t,n,r){let{kind:o,value:i,error:s}=this;return o==="N"?t?.(i):o==="E"?n?.(s):r?.()}accept(t,n,r){var o;return S((o=t)===null||o===void 0?void 0:o.next)?this.observe(t):this.do(t,n,r)}toObservable(){let{kind:t,value:n,error:r}=this,o=t==="N"?M(n):t==="E"?xt(()=>r):t==="C"?ie:0;if(!o)throw new TypeError(`Unexpected notification kind ${t}`);return o}static createNext(t){return new e("N",t)}static createError(t){return new e("E",void 0,t)}static createComplete(){return e.completeNotification}};Nt.completeNotification=new Nt("C");function Ta(e,t){var n,r,o;let{kind:i,value:s,error:a}=e;if(typeof i!="string")throw new TypeError('Invalid notification, missing "kind"');i==="N"?(n=t.next)===null||n===void 0||n.call(t,s):i==="E"?(r=t.error)===null||r===void 0||r.call(t,a):(o=t.complete)===null||o===void 0||o.call(t)}function _a(e){return!!e&&(e instanceof R||S(e.lift)&&S(e.subscribe))}var Ue=Rt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Bv(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new Ve({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new Ue)}});e.subscribe(i)})}function of(e){return e instanceof Date&&!isNaN(e)}var $v=Rt(e=>function(n=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n});function Hv(e,t){let{first:n,each:r,with:o=zv,scheduler:i=t??Zo,meta:s=null}=of(e)?{first:e}:typeof e=="number"?{each:e}:e;if(n==null&&r==null)throw new TypeError("No timeout provided.");return C((a,c)=>{let u,l,d=null,h=0,f=p=>{l=de(c,i,()=>{try{u.unsubscribe(),L(o({meta:s,lastValue:d,seen:h})).subscribe(c)}catch(v){c.error(v)}},p)};u=a.subscribe(b(c,p=>{l?.unsubscribe(),h++,c.next(d=p),r>0&&f(r)},void 0,void 0,()=>{l?.closed||l?.unsubscribe(),d=null})),!h&&f(n!=null?typeof n=="number"?n:+n-i.now():r)})}function zv(e){throw new $v(e)}function T(e,t){return C((n,r)=>{let o=0;n.subscribe(b(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:qv}=Array;function Gv(e,t){return qv(t)?e(...t):e(t)}function On(e){return T(t=>Gv(e,t))}var{isArray:Wv}=Array,{getPrototypeOf:Zv,prototype:Yv,keys:Qv}=Object;function ai(e){if(e.length===1){let t=e[0];if(Wv(t))return{args:t,keys:null};if(Kv(t)){let n=Qv(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Kv(e){return e&&typeof e=="object"&&Zv(e)===Yv}function ci(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function _r(...e){let t=Ye(e),n=Nn(e),{args:r,keys:o}=ai(e);if(r.length===0)return q([],t);let i=new R(Jv(r,t,o?s=>ci(o,s):ue));return n?i.pipe(On(n)):i}function Jv(e,t,n=ue){return r=>{sf(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)sf(t,()=>{let u=q(e[c],t),l=!1;u.subscribe(b(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function sf(e,t,n){e?de(n,e,t):t()}function af(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=v=>u<r?p(v):c.push(v),p=v=>{i&&t.next(v),u++;let I=!1;L(n(v,l++)).subscribe(b(t,_=>{o?.(_),i?f(_):t.next(_)},()=>{I=!0},void 0,()=>{if(I)try{for(u--;c.length&&u<r;){let _=c.shift();s?de(t,s,()=>p(_)):p(_)}h()}catch(_){t.error(_)}}))};return e.subscribe(b(t,f,()=>{d=!0,h()})),()=>{a?.()}}function G(e,t,n=1/0){return S(t)?G((r,o)=>T((i,s)=>t(r,i,o,s))(L(e(r,o))),n):(typeof t=="number"&&(n=t),C((r,o)=>af(r,o,e,n)))}function Qe(e=1/0){return G(ue,e)}function cf(){return Qe(1)}function kn(...e){return cf()(q(e,Ye(e)))}function Rr(e){return new R(t=>{L(e()).subscribe(t)})}function Xv(...e){let t=Nn(e),{args:n,keys:r}=ai(e),o=new R(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;L(n[l]).subscribe(b(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?ci(r,a):a),i.complete())}))}});return t?o.pipe(On(t)):o}var eD=["addListener","removeListener"],tD=["addEventListener","removeEventListener"],nD=["on","off"];function Ra(e,t,n,r){if(S(n)&&(r=n,n=void 0),r)return Ra(e,t,n).pipe(On(r));let[o,i]=iD(e)?tD.map(s=>a=>e[s](t,a,n)):rD(e)?eD.map(uf(e,t)):oD(e)?nD.map(uf(e,t)):[];if(!o&&An(e))return G(s=>Ra(s,t,n))(L(e));if(!o)throw new TypeError("Invalid event target");return new R(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function uf(e,t){return n=>r=>e[n](t,r)}function rD(e){return S(e.addListener)&&S(e.removeListener)}function oD(e){return S(e.on)&&S(e.off)}function iD(e){return S(e.addEventListener)&&S(e.removeEventListener)}function ui(...e){let t=Ye(e),n=Gd(e,1/0),r=e;return r.length?r.length===1?L(r[0]):Qe(n)(q(r,t)):ie}function X(e,t){return C((n,r)=>{let o=0;n.subscribe(b(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Be(e){return C((t,n)=>{let r=null,o=!1,i;r=t.subscribe(b(n,void 0,void 0,s=>{i=L(e(s,Be(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function lf(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(b(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ke(e,t){return S(t)?G(e,t,1):G(e,1)}function sD(e,t=Zo){return C((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(b(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function At(e){return C((t,n)=>{let r=!1;t.subscribe(b(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function $e(e){return e<=0?()=>ie:C((t,n)=>{let r=0;t.subscribe(b(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function xa(){return C((e,t)=>{e.subscribe(b(t,lt))})}function Na(){return C((e,t)=>{e.subscribe(b(t,n=>Ta(n,t)))})}function Aa(e,t=ue){return e=e??aD,C((n,r)=>{let o,i=!0;n.subscribe(b(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function aD(e,t){return e===t}function li(e=cD){return C((t,n)=>{let r=!1;t.subscribe(b(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function cD(){return new Ue}function di(e,t){return t?n=>n.pipe(di((r,o)=>L(e(r,o)).pipe(T((i,s)=>t(r,i,o,s))))):C((n,r)=>{let o=0,i=null,s=!1;n.subscribe(b(r,a=>{i||(i=b(r,void 0,()=>{i=null,s&&r.complete()}),L(e(a,o++)).subscribe(i))},()=>{s=!0,!i&&r.complete()}))})}function Ot(e){return C((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function dt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?X((o,i)=>e(o,i,r)):ue,$e(1),n?At(t):li(()=>new Ue))}function fi(e,t,n,r){return C((o,i)=>{let s;!t||typeof t=="function"?s=t:{duration:n,element:s,connector:r}=t;let a=new Map,c=p=>{a.forEach(p),p(i)},u=p=>c(v=>v.error(p)),l=0,d=!1,h=new Sr(i,p=>{try{let v=e(p),I=a.get(v);if(!I){a.set(v,I=r?r():new U);let _=f(v,I);if(i.next(_),n){let Z=b(I,()=>{I.complete(),Z?.unsubscribe()},void 0,void 0,()=>a.delete(v));h.add(L(n(_)).subscribe(Z))}}I.next(s?s(p):p)}catch(v){u(v)}},()=>c(p=>p.complete()),u,()=>a.clear(),()=>(d=!0,l===0));o.subscribe(h);function f(p,v){let I=new R(_=>{l++;let Z=v.subscribe(_);return()=>{Z.unsubscribe(),--l===0&&d&&h.unsubscribe()}});return I.key=p,I}})}function Fn(e){return e<=0?()=>ie:C((t,n)=>{let r=[];t.subscribe(b(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Oa(e,t){let n=arguments.length>=2;return r=>r.pipe(e?X((o,i)=>e(o,i,r)):ue,Fn(1),n?At(t):li(()=>new Ue))}function ka(){return C((e,t)=>{e.subscribe(b(t,n=>{t.next(Nt.createNext(n))},()=>{t.next(Nt.createComplete()),t.complete()},n=>{t.next(Nt.createError(n)),t.complete()}))})}function Fa(...e){let t=e.length;if(t===0)throw new Error("list of properties cannot be empty.");return T(n=>{let r=n;for(let o=0;o<t;o++){let i=r?.[e[o]];if(typeof i<"u")r=i;else return}return r})}function xr(e,t){return C(lf(e,t,arguments.length>=2,!0))}function La(e={}){let{connector:t=()=>new U,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=c=void 0,l=d=!1},p=()=>{let v=s;f(),v?.unsubscribe()};return C((v,I)=>{u++,!d&&!l&&h();let _=c=c??t();I.add(()=>{u--,u===0&&!d&&!l&&(a=Pa(p,o))}),_.subscribe(I),!s&&u>0&&(s=new Ve({next:Z=>_.next(Z),error:Z=>{d=!0,h(),a=Pa(f,n,Z),_.error(Z)},complete:()=>{l=!0,h(),a=Pa(f,r),_.complete()}}),L(v).subscribe(s))})(i)}}function Pa(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ve({next:()=>{r.unsubscribe(),e()}});return L(t(...n)).subscribe(r)}function uD(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,La({connector:()=>new Tn(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function lD(e){return X((t,n)=>e<=n)}function ja(...e){let t=Ye(e);return C((n,r)=>{(t?kn(e,n,t):kn(e,n)).subscribe(r)})}function pe(e,t){return C((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(b(r,c=>{o?.unsubscribe();let u=0,l=i++;L(e(c,l)).subscribe(o=b(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Nr(e){return C((t,n)=>{L(e).subscribe(b(n,()=>n.complete(),lt)),!n.closed&&t.subscribe(n)})}function se(e,t,n){let r=S(e)||t||n?{next:e,error:t,complete:n}:e;return r?C((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(b(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ue}function Va(...e){let t=Nn(e);return C((n,r)=>{let o=e.length,i=new Array(o),s=e.map(()=>!1),a=!1;for(let c=0;c<o;c++)L(e[c]).subscribe(b(r,u=>{i[c]=u,!a&&!s[c]&&(s[c]=!0,(a=s.every(ue))&&(s=null))},lt));n.subscribe(b(r,c=>{if(a){let u=[c,...i];r.next(t?t(...u):u)}}))})}var nh="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",E=class extends Error{code;constructor(t,n){super(Ki(t,n)),this.code=t}};function Ki(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}var rh=Symbol("InputSignalNode#UNSET"),dD=j(D({},pa),{transformFn:void 0,applyValueToInputSignal(e,t){Bo(e,t)}});function oh(e,t){let n=Object.create(dD);n.value=e,n.transformFn=t?.transform;function r(){if(Po(n),n.value===rh){let o=null;throw new E(-950,o)}return n.value}return r[Ne]=n,r}function qr(e){return{toString:e}.toString()}var hi="__parameters__";function fD(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function eu(e,t,n){return qr(()=>{let r=fD(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(hi)?c[hi]:Object.defineProperty(c,hi,{value:[]})[hi];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var ft=globalThis;function $(e){for(let t in e)if(e[t]===$)return t;throw Error("Could not find renamed property on target object.")}function hD(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ie(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Ie).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function df(e,t){return e?t?`${e} ${t}`:e:t||""}var pD=$({__forward_ref__:$});function ih(e){return e.__forward_ref__=ih,e.toString=function(){return Ie(this())},e}function ge(e){return sh(e)?e():e}function sh(e){return typeof e=="function"&&e.hasOwnProperty(pD)&&e.__forward_ref__===ih}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Ce(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ji(e){return ff(e,ch)||ff(e,uh)}function ah(e){return Ji(e)!==null}function ff(e,t){return e.hasOwnProperty(t)?e[t]:null}function gD(e){let t=e&&(e[ch]||e[uh]);return t||null}function hf(e){return e&&(e.hasOwnProperty(pf)||e.hasOwnProperty(mD))?e[pf]:null}var ch=$({\u0275prov:$}),pf=$({\u0275inj:$}),uh=$({ngInjectableDef:$}),mD=$({ngInjectorDef:$}),m=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=w({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function lh(e){return e&&!!e.\u0275providers}var yD=$({\u0275cmp:$}),vD=$({\u0275dir:$}),DD=$({\u0275pipe:$}),ED=$({\u0275mod:$}),Ci=$({\u0275fac:$}),Fr=$({__NG_ELEMENT_ID__:$}),gf=$({__NG_ENV_ID__:$});function Xi(e){return typeof e=="string"?e:e==null?"":String(e)}function wD(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Xi(e)}function dh(e,t){throw new E(-200,e)}function tu(e,t){throw new E(-201,!1)}var O=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(O||{}),ec;function fh(){return ec}function Ee(e){let t=ec;return ec=e,t}function hh(e,t,n){let r=Ji(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&O.Optional)return null;if(t!==void 0)return t;tu(e,"Injector")}var ID={},Jt=ID,tc="__NG_DI_FLAG__",Si=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?ma:Jt,r)}},Mi="ngTempTokenPath",bD="ngTokenPath",CD=/\n/gm,SD="\u0275",mf="__source";function MD(e,t=O.Default){if(Cr()===void 0)throw new E(-203,!1);if(Cr()===null)return hh(e,void 0,t);{let n=Cr(),r;return n instanceof Si?r=n.injector:r=n,r.get(e,t&O.Optional?null:void 0,t)}}function y(e,t=O.Default){return(fh()||MD)(ge(e),t)}function g(e,t=O.Default){return y(e,es(t))}function es(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function nc(e){let t=[];for(let n=0;n<e.length;n++){let r=ge(e[n]);if(Array.isArray(r)){if(r.length===0)throw new E(900,!1);let o,i=O.Default;for(let s=0;s<r.length;s++){let a=r[s],c=TD(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(y(o,i))}else t.push(y(r))}return t}function nu(e,t){return e[tc]=t,e.prototype[tc]=t,e}function TD(e){return e[tc]}function _D(e,t,n,r){let o=e[Mi];throw t[mf]&&o.unshift(t[mf]),e.message=RD(`
`+e.message,o,n,r),e[bD]=o,e[Mi]=null,e}function RD(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==SD?e.slice(2):e;let o=Ie(t);if(Array.isArray(t))o=t.map(Ie).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ie(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(CD,`
  `)}`}var ru=nu(eu("Inject",e=>({token:e})),-1),ph=nu(eu("Optional"),8);var xD=nu(eu("SkipSelf"),4);function en(e,t){let n=e.hasOwnProperty(Ci);return n?e[Ci]:null}function ND(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function AD(e){return e.flat(Number.POSITIVE_INFINITY)}function ou(e,t){e.forEach(n=>Array.isArray(n)?ou(n,t):t(n))}function gh(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ti(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function OD(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function kD(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function FD(e,t,n){let r=Gr(e,t);return r>=0?e[r|1]=n:(r=~r,kD(e,r,t,n)),r}function Ua(e,t){let n=Gr(e,t);if(n>=0)return e[n|1]}function Gr(e,t){return PD(e,t,1)}function PD(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var tn={},Ae=[],Pt=new m(""),mh=new m("",-1),yh=new m(""),_i=class{get(t,n=Jt){if(n===Jt){let r=new Error(`NullInjectorError: No provider for ${Ie(t)}!`);throw r.name="NullInjectorError",r}return n}};function vh(e,t){let n=e[ED]||null;if(!n&&t===!0)throw new Error(`Type ${Ie(e)} does not have '\u0275mod' property.`);return n}function nn(e){return e[yD]||null}function LD(e){return e[vD]||null}function jD(e){return e[DD]||null}function hn(e){return{\u0275providers:e}}function VD(...e){return{\u0275providers:Dh(!0,e),\u0275fromNgModule:!0}}function Dh(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ou(t,s=>{let a=s;rc(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Eh(o,i),n}function Eh(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];iu(o,i=>{t(i,r)})}}function rc(e,t,n,r){if(e=ge(e),!e)return!1;let o=null,i=hf(e),s=!i&&nn(e);if(!i&&!s){let c=e.ngModule;if(i=hf(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)rc(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{ou(i.imports,l=>{rc(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Eh(u,t)}if(!a){let u=en(o)||(()=>new o);t({provide:o,useFactory:u,deps:Ae},o),t({provide:yh,useValue:o,multi:!0},o),t({provide:Pt,useValue:()=>y(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;iu(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function iu(e,t){for(let n of e)lh(n)&&(n=n.\u0275providers),Array.isArray(n)?iu(n,t):t(n)}var UD=$({provide:String,useValue:$});function wh(e){return e!==null&&typeof e=="object"&&UD in e}function BD(e){return!!(e&&e.useExisting)}function $D(e){return!!(e&&e.useFactory)}function $n(e){return typeof e=="function"}function HD(e){return!!e.useClass}var ts=new m(""),vi={},yf={},Ba;function su(){return Ba===void 0&&(Ba=new _i),Ba}var me=class{},Pr=class extends me{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,ic(t,s=>this.processProvider(s)),this.records.set(mh,Pn(void 0,this)),o.has("environment")&&this.records.set(me,Pn(void 0,this));let i=this.records.get(ts);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(yh,Ae,O.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?ma:Jt,r)}destroy(){Or(this),this._destroyed=!0;let t=P(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),P(t)}}onDestroy(t){return Or(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Or(this);let n=ut(this),r=Ee(void 0),o;try{return t()}finally{ut(n),Ee(r)}}get(t,n=Jt,r=O.Default){if(Or(this),t.hasOwnProperty(gf))return t[gf](this);r=es(r);let o,i=ut(this),s=Ee(void 0);try{if(!(r&O.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=ZD(t)&&Ji(t);u&&this.injectableDefInScope(u)?c=Pn(oc(t),vi):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&O.Self?su():this.parent;return n=r&O.Optional&&n===Jt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Mi]=a[Mi]||[]).unshift(Ie(t)),i)throw a;return _D(a,t,"R3InjectorError",this.source)}else throw a}finally{Ee(s),ut(i)}}resolveInjectorInitializers(){let t=P(null),n=ut(this),r=Ee(void 0),o;try{let i=this.get(Pt,Ae,O.Self);for(let s of i)s()}finally{ut(n),Ee(r),P(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Ie(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ge(t);let n=$n(t)?t:ge(t&&t.provide),r=qD(t);if(!$n(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Pn(void 0,vi,!0),o.factory=()=>nc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=P(null);try{return n.value===yf?dh(Ie(t)):n.value===vi&&(n.value=yf,n.value=n.factory()),typeof n.value=="object"&&n.value&&WD(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{P(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ge(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function oc(e){let t=Ji(e),n=t!==null?t.factory:en(e);if(n!==null)return n;if(e instanceof m)throw new E(204,!1);if(e instanceof Function)return zD(e);throw new E(204,!1)}function zD(e){if(e.length>0)throw new E(204,!1);let n=gD(e);return n!==null?()=>n.factory(e):()=>new e}function qD(e){if(wh(e))return Pn(void 0,e.useValue);{let t=Ih(e);return Pn(t,vi)}}function Ih(e,t,n){let r;if($n(e)){let o=ge(e);return en(o)||oc(o)}else if(wh(e))r=()=>ge(e.useValue);else if($D(e))r=()=>e.useFactory(...nc(e.deps||[]));else if(BD(e))r=()=>y(ge(e.useExisting));else{let o=ge(e&&(e.useClass||e.provide));if(GD(e))r=()=>new o(...nc(e.deps));else return en(o)||oc(o)}return r}function Or(e){if(e.destroyed)throw new E(205,!1)}function Pn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function GD(e){return!!e.deps}function WD(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function ZD(e){return typeof e=="function"||typeof e=="object"&&e instanceof m}function ic(e,t){for(let n of e)Array.isArray(n)?ic(n,t):n&&lh(n)?ic(n.\u0275providers,t):t(n)}function Se(e,t){let n;e instanceof Pr?(Or(e),n=e):n=new Si(e);let r,o=ut(n),i=Ee(void 0);try{return t()}finally{ut(o),Ee(i)}}function bh(){return fh()!==void 0||Cr()!=null}function Zn(e){if(!bh())throw new E(-203,!1)}function YD(e){return typeof e=="function"}var Dt=0,A=1,x=2,fe=3,ze=4,Me=5,Lr=6,Ri=7,ye=8,jr=9,ht=10,ee=11,Vr=12,vf=13,Yn=14,ke=15,rn=16,Ln=17,pt=18,ns=19,Ch=20,kt=21,$a=22,on=23,Oe=24,Un=25,he=26,Sh=1;var sn=7,xi=8,Hn=9,be=10;function Ft(e){return Array.isArray(e)&&typeof e[Sh]=="object"}function Et(e){return Array.isArray(e)&&e[Sh]===!0}function Mh(e){return(e.flags&4)!==0}function Qn(e){return e.componentOffset>-1}function au(e){return(e.flags&1)===1}function Xe(e){return!!e.template}function Ni(e){return(e[x]&512)!==0}function Wr(e){return(e[x]&256)===256}var sc=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Th(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Zr=(()=>{let e=()=>_h;return e.ngInherit=!0,e})();function _h(e){return e.type.prototype.ngOnChanges&&(e.setInput=KD),QD}function QD(){let e=xh(this),t=e?.current;if(t){let n=e.previous;if(n===tn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function KD(e,t,n,r,o){let i=this.declaredInputs[r],s=xh(e)||JD(e,{previous:tn,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new sc(u&&u.currentValue,n,c===tn),Th(e,t,o,n)}var Rh="__ngSimpleChanges__";function xh(e){return e[Rh]||null}function JD(e,t){return e[Rh]=t}var Df=null;var B=function(e,t=null,n){Df?.(e,t,n)},XD="svg",eE="math";function et(e){for(;Array.isArray(e);)e=e[Dt];return e}function Nh(e,t){return et(t[e])}function rt(e,t){return et(t[e.index])}function cu(e,t){return e.data[t]}function Ah(e,t){return e[t]}function tt(e,t){let n=t[e];return Ft(n)?n:n[Dt]}function tE(e){return(e[x]&4)===4}function uu(e){return(e[x]&128)===128}function nE(e){return Et(e[fe])}function Ai(e,t){return t==null?null:e[t]}function Oh(e){e[Ln]=0}function kh(e){e[x]&1024||(e[x]|=1024,uu(e)&&Kn(e))}function rE(e,t){for(;e>0;)t=t[Yn],e--;return t}function rs(e){return!!(e[x]&9216||e[Oe]?.dirty)}function ac(e){e[ht].changeDetectionScheduler?.notify(8),e[x]&64&&(e[x]|=1024),rs(e)&&Kn(e)}function Kn(e){e[ht].changeDetectionScheduler?.notify(0);let t=an(e);for(;t!==null&&!(t[x]&8192||(t[x]|=8192,!uu(t)));)t=an(t)}function Fh(e,t){if(Wr(e))throw new E(911,!1);e[kt]===null&&(e[kt]=[]),e[kt].push(t)}function oE(e,t){if(e[kt]===null)return;let n=e[kt].indexOf(t);n!==-1&&e[kt].splice(n,1)}function an(e){let t=e[fe];return Et(t)?t[fe]:t}function Ph(e){return e[Ri]??=[]}function Lh(e){return e.cleanup??=[]}function iE(e,t,n,r){let o=Ph(t);o.push(n),e.firstCreatePass&&Lh(e).push(r,o.length-1)}var k={lFrame:Wh(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var cc=!1;function sE(){return k.lFrame.elementDepthCount}function aE(){k.lFrame.elementDepthCount++}function cE(){k.lFrame.elementDepthCount--}function jh(){return k.bindingsEnabled}function Vh(){return k.skipHydrationRootTNode!==null}function uE(e){return k.skipHydrationRootTNode===e}function lE(){k.skipHydrationRootTNode=null}function F(){return k.lFrame.lView}function ae(){return k.lFrame.tView}function o1(e){return k.lFrame.contextLView=e,e[ye]}function i1(e){return k.lFrame.contextLView=null,e}function ve(){let e=Uh();for(;e!==null&&e.type===64;)e=e.parent;return e}function Uh(){return k.lFrame.currentTNode}function dE(){let e=k.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Yr(e,t){let n=k.lFrame;n.currentTNode=e,n.isParent=t}function Bh(){return k.lFrame.isParent}function $h(){k.lFrame.isParent=!1}function Hh(){return cc}function Oi(e){let t=cc;return cc=e,t}function os(){let e=k.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function fE(e){return k.lFrame.bindingIndex=e}function Qr(){return k.lFrame.bindingIndex++}function hE(e){let t=k.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function pE(){return k.lFrame.inI18n}function gE(e,t){let n=k.lFrame;n.bindingIndex=n.bindingRootIndex=e,uc(t)}function mE(){return k.lFrame.currentDirectiveIndex}function uc(e){k.lFrame.currentDirectiveIndex=e}function yE(e){let t=k.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function zh(){return k.lFrame.currentQueryIndex}function lu(e){k.lFrame.currentQueryIndex=e}function vE(e){let t=e[A];return t.type===2?t.declTNode:t.type===1?e[Me]:null}function qh(e,t,n){if(n&O.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&O.Host);)if(o=vE(i),o===null||(i=i[Yn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=k.lFrame=Gh();return r.currentTNode=t,r.lView=e,!0}function du(e){let t=Gh(),n=e[A];k.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Gh(){let e=k.lFrame,t=e===null?null:e.child;return t===null?Wh(e):t}function Wh(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Zh(){let e=k.lFrame;return k.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Yh=Zh;function fu(){let e=Zh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function DE(e){return(k.lFrame.contextLView=rE(e,k.lFrame.contextLView))[ye]}function Jn(){return k.lFrame.selectedIndex}function cn(e){k.lFrame.selectedIndex=e}function hu(){let e=k.lFrame;return cu(e.tView,e.selectedIndex)}function EE(){return k.lFrame.currentNamespace}var Qh=!0;function pu(){return Qh}function gu(e){Qh=e}function wE(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=_h(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Kh(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Di(e,t,n){Jh(e,t,3,n)}function Ei(e,t,n,r){(e[x]&3)===n&&Jh(e,t,n,r)}function Ha(e,t){let n=e[x];(n&3)===t&&(n&=16383,n+=1,e[x]=n)}function Jh(e,t,n,r){let o=r!==void 0?e[Ln]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Ln]+=65536),(a<i||i==-1)&&(IE(e,n,t,c),e[Ln]=(e[Ln]&**********)+c+2),c++}function Ef(e,t){B(4,e,t);let n=P(null);try{t.call(e)}finally{P(n),B(5,e,t)}}function IE(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[x]>>14<e[Ln]>>16&&(e[x]&3)===t&&(e[x]+=16384,Ef(a,i)):Ef(a,i)}var Bn=-1,un=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function bE(e){return(e.flags&8)!==0}function CE(e){return(e.flags&16)!==0}function SE(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];ME(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Xh(e){return e===3||e===4||e===6}function ME(e){return e.charCodeAt(0)===64}function Ur(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?wf(e,n,o,null,t[++r]):wf(e,n,o,null,null))}}return e}function wf(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}var za={},lc=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=es(r);let o=this.injector.get(t,za,r);return o!==za||n===za?o:this.parentInjector.get(t,n,r)}};function ep(e){return e!==Bn}function ki(e){return e&32767}function TE(e){return e>>16}function Fi(e,t){let n=TE(e),r=t;for(;n>0;)r=r[Yn],n--;return r}var dc=!0;function Pi(e){let t=dc;return dc=e,t}var _E=256,tp=_E-1,np=5,RE=0,Je={};function xE(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Fr)&&(r=n[Fr]),r==null&&(r=n[Fr]=RE++);let o=r&tp,i=1<<o;t.data[e+(o>>np)]|=i}function Li(e,t){let n=rp(e,t);if(n!==-1)return n;let r=t[A];r.firstCreatePass&&(e.injectorIndex=t.length,qa(r.data,e),qa(t,null),qa(r.blueprint,null));let o=mu(e,t),i=e.injectorIndex;if(ep(o)){let s=ki(o),a=Fi(o,t),c=a[A].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function qa(e,t){e.push(0,0,0,0,0,0,0,0,t)}function rp(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function mu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=cp(o),r===null)return Bn;if(n++,o=o[Yn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Bn}function fc(e,t,n){xE(e,t,n)}function NE(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Xh(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function op(e,t,n){if(n&O.Optional||e!==void 0)return e;tu(t,"NodeInjector")}function ip(e,t,n,r){if(n&O.Optional&&r===void 0&&(r=null),(n&(O.Self|O.Host))===0){let o=e[jr],i=Ee(void 0);try{return o?o.get(t,r,n&O.Optional):hh(t,r,n&O.Optional)}finally{Ee(i)}}return op(r,t,n)}function sp(e,t,n,r=O.Default,o){if(e!==null){if(t[x]&2048&&!(r&O.Self)){let s=FE(e,t,n,r,Je);if(s!==Je)return s}let i=ap(e,t,n,r,Je);if(i!==Je)return i}return ip(t,n,r,o)}function ap(e,t,n,r,o){let i=OE(n);if(typeof i=="function"){if(!qh(t,e,r))return r&O.Host?op(o,n,r):ip(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&O.Optional))tu(n);else return s}finally{Yh()}}else if(typeof i=="number"){let s=null,a=rp(e,t),c=Bn,u=r&O.Host?t[ke][Me]:null;for((a===-1||r&O.SkipSelf)&&(c=a===-1?mu(e,t):t[a+8],c===Bn||!bf(r,!1)?a=-1:(s=t[A],a=ki(c),t=Fi(c,t)));a!==-1;){let l=t[A];if(If(i,a,l.data)){let d=AE(a,t,n,s,r,u);if(d!==Je)return d}c=t[a+8],c!==Bn&&bf(r,t[A].data[a+8]===u)&&If(i,a,t)?(s=l,a=ki(c),t=Fi(c,t)):a=-1}}return o}function AE(e,t,n,r,o,i){let s=t[A],a=s.data[e+8],c=r==null?Qn(a)&&dc:r!=s&&(a.type&3)!==0,u=o&O.Host&&i===a,l=wi(a,s,n,c,u);return l!==null?Br(t,s,l,a):Je}function wi(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let p=s[f];if(f<c&&n===p||f>=c&&p.type===n)return f}if(o){let f=s[c];if(f&&Xe(f)&&f.type===n)return c}return null}function Br(e,t,n,r){let o=e[n],i=t.data;if(o instanceof un){let s=o;s.resolving&&dh(wD(i[n]));let a=Pi(s.canSeeViewProviders);s.resolving=!0;let c,u=s.injectImpl?Ee(s.injectImpl):null,l=qh(e,r,O.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&wE(n,i[n],t)}finally{u!==null&&Ee(u),Pi(a),s.resolving=!1,Yh()}}return o}function OE(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Fr)?e[Fr]:void 0;return typeof t=="number"?t>=0?t&tp:kE:t}function If(e,t,n){let r=1<<e;return!!(n[t+(e>>np)]&r)}function bf(e,t){return!(e&O.Self)&&!(e&O.Host&&t)}var Xt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return sp(this._tNode,this._lView,t,es(r),n)}};function kE(){return new Xt(ve(),F())}function Kr(e){return qr(()=>{let t=e.prototype.constructor,n=t[Ci]||hc(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Ci]||hc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function hc(e){return sh(e)?()=>{let t=hc(ge(e));return t&&t()}:en(e)}function FE(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[x]&2048&&!Ni(s);){let a=ap(i,s,n,r|O.Self,Je);if(a!==Je)return a;let c=i.parent;if(!c){let u=s[Ch];if(u){let l=u.get(n,Je,r);if(l!==Je)return l}c=cp(s),s=s[Yn]}i=c}return o}function cp(e){let t=e[A],n=t.type;return n===2?t.declTNode:n===1?e[Me]:null}function yu(e){return NE(ve(),e)}function Cf(e,t=null,n=null,r){let o=up(e,t,n,r);return o.resolveInjectorInitializers(),o}function up(e,t=null,n=null,r,o=new Set){let i=[n||Ae,VD(e)];return r=r||(typeof e=="object"?void 0:Ie(e)),new Pr(i,t||su(),r||null,o)}var oe=class e{static THROW_IF_NOT_FOUND=Jt;static NULL=new _i;static create(t,n){if(Array.isArray(t))return Cf({name:""},n,t,"");{let r=t.name??"";return Cf({name:r},t.parent,t.providers,r)}}static \u0275prov=w({token:e,providedIn:"any",factory:()=>y(mh)});static __NG_ELEMENT_ID__=-1};var PE=new m("");PE.__NG_ELEMENT_ID__=e=>{let t=ve();if(t===null)throw new E(204,!1);if(t.type&2)return t.value;if(e&O.Optional)return null;throw new E(204,!1)};var lp=!1,ot=(()=>{class e{static __NG_ELEMENT_ID__=LE;static __NG_ENV_ID__=n=>n}return e})(),ji=class extends ot{_lView;constructor(t){super(),this._lView=t}onDestroy(t){return Fh(this._lView,t),()=>oE(this._lView,t)}};function LE(){return new ji(F())}var ln=class{},vu=new m("",{providedIn:"root",factory:()=>!1});var dp=new m(""),fp=new m(""),Lt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new K(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})();var pc=class extends U{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,bh()&&(this.destroyRef=g(ot,{optional:!0})??void 0,this.pendingTasks=g(Lt,{optional:!0})??void 0)}emit(t){let n=P(null);try{super.next(t)}finally{P(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Y&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},we=pc;function $r(...e){}function hp(e){let t,n;function r(){e=$r;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Sf(e){return queueMicrotask(()=>e()),()=>{e=$r}}var Du="isAngularZone",Vi=Du+"_ID",jE=0,Q=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new we(!1);onMicrotaskEmpty=new we(!1);onStable=new we(!1);onError=new we(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=lp}=t;if(typeof Zone>"u")throw new E(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,BE(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Du)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new E(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new E(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,VE,$r,$r);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},VE={};function Eu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function UE(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){hp(()=>{e.callbackScheduled=!1,gc(e),e.isCheckStableRunning=!0,Eu(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),gc(e)}function BE(e){let t=()=>{UE(e)},n=jE++;e._inner=e._inner.fork({name:"angular",properties:{[Du]:!0,[Vi]:n,[Vi+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if($E(c))return r.invokeTask(i,s,a,c);try{return Mf(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Tf(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return Mf(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!HE(c)&&t(),Tf(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,gc(e),Eu(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function gc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Mf(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Tf(e){e._nesting--,Eu(e)}var mc=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new we;onMicrotaskEmpty=new we;onStable=new we;onError=new we;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function $E(e){return pp(e,"__ignore_ng_zone__")}function HE(e){return pp(e,"__scheduler_tick__")}function pp(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var qe=class{_console=console;handleError(t){this._console.error("ERROR",t)}},zE=new m("",{providedIn:"root",factory:()=>{let e=g(Q),t=g(qe);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function _f(e,t){return oh(e,t)}function qE(e){return oh(rh,e)}var gp=(_f.required=qE,_f);function GE(){return Xn(ve(),F())}function Xn(e,t){return new it(rt(e,t))}var it=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=GE}return e})();function WE(e){return e instanceof it?e.nativeElement:e}function ZE(e){return typeof e=="function"&&e[Ne]!==void 0}function is(e,t){let n=Fd(e,t?.equal),r=n[Ne];return n.set=o=>Bo(r,o),n.update=o=>Pd(r,o),n.asReadonly=YE.bind(n),n}function YE(){let e=this[Ne];if(e.readonlyFn===void 0){let t=()=>this();t[Ne]=e,e.readonlyFn=t}return e.readonlyFn}function mp(e){return ZE(e)&&typeof e.set=="function"}function QE(){return this._results[Symbol.iterator]()}var yc=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new U}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=AD(t);(this._changesDetected=!ND(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=QE};function yp(e){return(e.flags&128)===128}var vp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(vp||{}),Dp=new Map,KE=0;function JE(){return KE++}function XE(e){Dp.set(e[ns],e)}function vc(e){Dp.delete(e[ns])}var Rf="__ngContext__";function Jr(e,t){Ft(t)?(e[Rf]=t[ns],XE(t)):e[Rf]=t}function Ep(e){return Ip(e[Vr])}function wp(e){return Ip(e[ze])}function Ip(e){for(;e!==null&&!Et(e);)e=e[ze];return e}var Dc;function bp(e){Dc=e}function ew(){if(Dc!==void 0)return Dc;if(typeof document<"u")return document;throw new E(210,!1)}var wu=new m("",{providedIn:"root",factory:()=>tw}),tw="ng",Iu=new m(""),pn=new m("",{providedIn:"platform",factory:()=>"unknown"});var bu=new m("",{providedIn:"root",factory:()=>ew().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var nw="h",rw="b";var Cp=!1,ow=new m("",{providedIn:"root",factory:()=>Cp});var Cu=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Cu||{}),er=new m(""),xf=new Set;function Xr(e){xf.has(e)||(xf.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Su=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=iw}return e})();function iw(){return new Su(F(),ve())}var jn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(jn||{}),Sp=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),sw=[jn.EarlyRead,jn.Write,jn.MixedReadWrite,jn.Read],aw=(()=>{class e{ngZone=g(Q);scheduler=g(ln);errorHandler=g(qe,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){g(er,{optional:!0})}execute(){let n=this.sequences.size>0;n&&B(16),this.executing=!0;for(let r of sw)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&B(17)}register(n){let{view:r}=n;r!==void 0?((r[Un]??=[]).push(n),Kn(r),r[x]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Cu.AFTER_NEXT_RENDER,n):n()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),Ec=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Un];t&&(this.view[Un]=t.filter(n=>n!==this))}};function Mu(e,t){!t?.injector&&Zn(Mu);let n=t?.injector??g(oe);return Xr("NgAfterNextRender"),uw(e,n,t,!0)}function cw(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function uw(e,t,n,r){let o=t.get(Sp);o.impl??=t.get(aw);let i=t.get(er,null,{optional:!0}),s=n?.phase??jn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(ot):null,c=t.get(Su,null,{optional:!0}),u=new Ec(o.impl,cw(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var lw=()=>null;function Mp(e,t,n=!1){return lw(e,t,n)}function Tp(e,t){let n=e.contentQueries;if(n!==null){let r=P(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];lu(i),a.contentQueries(2,t[s],s)}}}finally{P(r)}}}function wc(e,t,n){lu(0);let r=P(null);try{t(e,n)}finally{P(r)}}function _p(e,t,n){if(Mh(t)){let r=P(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{P(r)}}}var nt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(nt||{}),pi;function dw(){if(pi===void 0&&(pi=null,ft.trustedTypes))try{pi=ft.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return pi}function ss(e){return dw()?.createHTML(e)||e}var gi;function fw(){if(gi===void 0&&(gi=null,ft.trustedTypes))try{gi=ft.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return gi}function Nf(e){return fw()?.createScriptURL(e)||e}var gt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${nh})`}},Ic=class extends gt{getTypeName(){return"HTML"}},bc=class extends gt{getTypeName(){return"Style"}},Cc=class extends gt{getTypeName(){return"Script"}},Sc=class extends gt{getTypeName(){return"URL"}},Mc=class extends gt{getTypeName(){return"ResourceURL"}};function st(e){return e instanceof gt?e.changingThisBreaksApplicationSecurity:e}function jt(e,t){let n=hw(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${nh})`)}return n===t}function hw(e){return e instanceof gt&&e.getTypeName()||null}function Rp(e){return new Ic(e)}function xp(e){return new bc(e)}function Np(e){return new Cc(e)}function Ap(e){return new Sc(e)}function Op(e){return new Mc(e)}function pw(e){let t=new _c(e);return gw()?new Tc(t):t}var Tc=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(ss(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},_c=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=ss(t),n}};function gw(){try{return!!new window.DOMParser().parseFromString(ss(""),"text/html")}catch{return!1}}var mw=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function as(e){return e=String(e),e.match(mw)?e:"unsafe:"+e}function wt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function eo(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var kp=wt("area,br,col,hr,img,wbr"),Fp=wt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Pp=wt("rp,rt"),yw=eo(Pp,Fp),vw=eo(Fp,wt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Dw=eo(Pp,wt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Af=eo(kp,vw,Dw,yw),Lp=wt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Ew=wt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),ww=wt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Iw=eo(Lp,Ew,ww),bw=wt("script,style,template"),Rc=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Mw(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Sw(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Of(t).toLowerCase();if(!Af.hasOwnProperty(n))return this.sanitizedSomething=!0,!bw.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Iw.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Lp[a]&&(c=as(c)),this.buf.push(" ",s,'="',kf(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Of(t).toLowerCase();Af.hasOwnProperty(n)&&!kp.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(kf(t))}};function Cw(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Sw(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw jp(t);return t}function Mw(e){let t=e.firstChild;if(t&&Cw(e,t))throw jp(t);return t}function Of(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function jp(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Tw=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,_w=/([^\#-~ |!])/g;function kf(e){return e.replace(/&/g,"&amp;").replace(Tw,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(_w,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var mi;function Vp(e,t){let n=null;try{mi=mi||pw(e);let r=t?String(t):"";n=mi.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=mi.getInertBodyElement(r)}while(r!==i);let a=new Rc().sanitizeChildren(Ff(n)||n);return ss(a)}finally{if(n){let r=Ff(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Ff(e){return"content"in e&&Rw(e)?e.content:null}function Rw(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var at=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(at||{});function xw(e){let t=Bp();return t?t.sanitize(at.URL,e)||"":jt(e,"URL")?st(e):as(Xi(e))}function Nw(e){let t=Bp();if(t)return Nf(t.sanitize(at.RESOURCE_URL,e)||"");if(jt(e,"ResourceURL"))return Nf(st(e));throw new E(904,!1)}function Aw(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Nw:xw}function Up(e,t,n){return Aw(t,n)(e)}function Bp(){let e=F();return e&&e[ht].sanitizer}function $p(e){return e instanceof Function?e():e}function Ow(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Hp="ng-template";function kw(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Ow(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Tu(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Tu(e){return e.type===4&&e.value!==Hp}function Fw(e,t,n){let r=e.type===4&&!n?Hp:e.value;return t===r}function Pw(e,t,n){let r=4,o=e.attrs,i=o!==null?Vw(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!He(r)&&!He(c))return!1;if(s&&He(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Fw(e,c,n)||c===""&&t.length===1){if(He(r))return!1;s=!0}}else if(r&8){if(o===null||!kw(e,o,c,n)){if(He(r))return!1;s=!0}}else{let u=t[++a],l=Lw(c,o,Tu(e),n);if(l===-1){if(He(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(He(r))return!1;s=!0}}}}return He(r)||s}function He(e){return(e&1)===0}function Lw(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Uw(t,e)}function zp(e,t,n=!1){for(let r=0;r<t.length;r++)if(Pw(e,t[r],n))return!0;return!1}function jw(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function Vw(e){for(let t=0;t<e.length;t++){let n=e[t];if(Xh(n))return t}return e.length}function Uw(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Bw(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Pf(e,t){return e?":not("+t.trim()+")":t}function $w(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!He(s)&&(t+=Pf(i,o),o=""),r=s,i=i||!He(r);n++}return o!==""&&(t+=Pf(i,o)),t}function Hw(e){return e.map($w).join(",")}function zw(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!He(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var It={};function qw(e,t){return e.createText(t)}function Gw(e,t,n){e.setValue(t,n)}function qp(e,t,n){return e.createElement(t,n)}function Ui(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Gp(e,t,n){e.appendChild(t,n)}function Lf(e,t,n,r,o){r!==null?Ui(e,t,n,r,o):Gp(e,t,n)}function Ww(e,t,n){e.removeChild(null,t,n)}function Zw(e,t,n){e.setAttribute(t,"style",n)}function Yw(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Wp(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&SE(e,t,r),o!==null&&Yw(e,t,o),i!==null&&Zw(e,t,i)}function _u(e,t,n,r,o,i,s,a,c,u,l){let d=he+r,h=d+o,f=Qw(d,h),p=typeof u=="function"?u():u;return f[A]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:p,incompleteFirstPass:!1,ssrId:l}}function Qw(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:It);return n}function Kw(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=_u(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ru(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[Dt]=o,d[x]=r|4|128|8|64|1024,(u!==null||e&&e[x]&2048)&&(d[x]|=2048),Oh(d),d[fe]=d[Yn]=e,d[ye]=n,d[ht]=s||e&&e[ht],d[ee]=a||e&&e[ee],d[jr]=c||e&&e[jr]||null,d[Me]=i,d[ns]=JE(),d[Lr]=l,d[Ch]=u,d[ke]=t.type==2?e[ke]:d,d}function Jw(e,t,n){let r=rt(t,e),o=Kw(n),i=e[ht].rendererFactory,s=xu(e,Ru(e,o,null,Zp(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Zp(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Yp(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function xu(e,t){return e[Vr]?e[vf][ze]=t:e[Vr]=t,e[vf]=t,t}function s1(e=1){Qp(ae(),F(),Jn()+e,!1)}function Qp(e,t,n,r){if(!r)if((t[x]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Di(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Ei(t,i,0,n)}cn(n)}var cs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(cs||{});function xc(e,t,n,r){let o=P(null);try{let[i,s,a]=e.inputs[n],c=null;(s&cs.SignalBased)!==0&&(c=t[i][Ne]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Th(t,c,i,r)}finally{P(o)}}function Kp(e,t,n,r,o){let i=Jn(),s=r&2;try{cn(-1),s&&t.length>he&&Qp(e,t,he,!1),B(s?2:0,o),n(r,o)}finally{cn(i),B(s?3:1,o)}}function Nu(e,t,n){oI(e,t,n),(n.flags&64)===64&&iI(e,t,n)}function Jp(e,t,n=rt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Xw(e,t,n,r){let i=r.get(ow,Cp)||n===nt.ShadowDom,s=e.selectRootElement(t,i);return eI(s),s}function eI(e){tI(e)}var tI=()=>null;function nI(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Xp(e,t,n,r,o,i,s,a){if(!a&&Au(t,e,n,r,o)){Qn(t)&&rI(n,t.index);return}if(t.type&3){let c=rt(t,n);r=nI(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function rI(e,t){let n=tt(t,e);n[x]&16||(n[x]|=64)}function oI(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Qn(n)&&Jw(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Li(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Br(t,e,s,n);if(Jr(c,t),i!==null&&uI(t,s-r,c,a,n,i),Xe(a)){let u=tt(n.index,t);u[ye]=Br(t,e,s,n)}}}function iI(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=mE();try{cn(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];uc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&sI(c,u)}}finally{cn(-1),uc(s)}}function sI(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function eg(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];zp(t,i.selectors,!1)&&(r??=[],Xe(i)?r.unshift(i):r.push(i))}return r}function aI(e,t,n,r,o,i){let s=rt(e,t);cI(t[ee],s,i,e.value,n,r,o)}function cI(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Xi(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function uI(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];xc(r,n,c,u)}}function tg(e,t){let n=e[jr],r=n?n.get(qe,null):null;r&&r.handleError(t)}function Au(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];xc(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];xc(l,u,r,o),a=!0}return a}function lI(e,t){let n=tt(t,e),r=n[A];dI(r,n);let o=n[Dt];o!==null&&n[Lr]===null&&(n[Lr]=Mp(o,n[jr])),B(18),Ou(r,n,n[ye]),B(19,n[ye])}function dI(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Ou(e,t,n){du(t);try{let r=e.viewQuery;r!==null&&wc(1,r,n);let o=e.template;o!==null&&Kp(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[pt]?.finishViewCreation(e),e.staticContentQueries&&Tp(e,t),e.staticViewQueries&&wc(2,e.viewQuery,n);let i=e.components;i!==null&&fI(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[x]&=-5,fu()}}function fI(e,t){for(let n=0;n<t.length;n++)lI(e,t[n])}function ku(e,t,n,r){let o=P(null);try{let i=t.tView,a=e[x]&4096?4096:16,c=Ru(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[rn]=u;let l=e[pt];return l!==null&&(c[pt]=l.createEmbeddedView(i)),Ou(i,c,n),c}finally{P(o)}}function Bi(e,t){return!t||t.firstChild===null||yp(e)}var hI;function Fu(e,t){return hI(e,t)}var mt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(mt||{});function Pu(e){return(e.flags&32)===32}function Vn(e,t,n,r,o){if(r!=null){let i,s=!1;Et(r)?i=r:Ft(r)&&(s=!0,r=r[Dt]);let a=et(r);e===0&&n!==null?o==null?Gp(t,n,a):Ui(t,n,a,o||null,!0):e===1&&n!==null?Ui(t,n,a,o||null,!0):e===2?Ww(t,a,s):e===3&&t.destroyNode(a),i!=null&&bI(t,e,i,n,o)}}function pI(e,t){ng(e,t),t[Dt]=null,t[Me]=null}function gI(e,t,n,r,o,i){r[Dt]=o,r[Me]=t,us(e,r,n,1,o,i)}function ng(e,t){t[ht].changeDetectionScheduler?.notify(9),us(e,t,t[ee],2,null,null)}function mI(e){let t=e[Vr];if(!t)return Ga(e[A],e);for(;t;){let n=null;if(Ft(t))n=t[Vr];else{let r=t[be];r&&(n=r)}if(!n){for(;t&&!t[ze]&&t!==e;)Ft(t)&&Ga(t[A],t),t=t[fe];t===null&&(t=e),Ft(t)&&Ga(t[A],t),n=t&&t[ze]}t=n}}function Lu(e,t){let n=e[Hn],r=n.indexOf(t);n.splice(r,1)}function ju(e,t){if(Wr(t))return;let n=t[ee];n.destroyNode&&us(e,t,n,3,null,null),mI(t)}function Ga(e,t){if(Wr(t))return;let n=P(null);try{t[x]&=-129,t[x]|=256,t[Oe]&&br(t[Oe]),vI(e,t),yI(e,t),t[A].type===1&&t[ee].destroy();let r=t[rn];if(r!==null&&Et(t[fe])){r!==t[fe]&&Lu(r,t);let o=t[pt];o!==null&&o.detachView(e)}vc(t)}finally{P(n)}}function yI(e,t){let n=e.cleanup,r=t[Ri];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Ri]=null);let o=t[kt];if(o!==null){t[kt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[on];if(i!==null){t[on]=null;for(let s of i)s.destroy()}}function vI(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof un)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];B(4,a,c);try{c.call(a)}finally{B(5,a,c)}}else{B(4,o,i);try{i.call(o)}finally{B(5,o,i)}}}}}function rg(e,t,n){return DI(e,t.parent,n)}function DI(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Dt];if(Qn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===nt.None||o===nt.Emulated)return null}return rt(r,n)}function og(e,t,n){return wI(e,t,n)}function EI(e,t,n){return e.type&40?rt(e,n):null}var wI=EI,jf;function Vu(e,t,n,r){let o=rg(e,r,t),i=t[ee],s=r.parent||t[Me],a=og(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Lf(i,o,n[c],a,!1);else Lf(i,o,n,a,!1);jf!==void 0&&jf(i,r,t,n,o)}function kr(e,t){if(t!==null){let n=t.type;if(n&3)return rt(t,e);if(n&4)return Nc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return kr(e,r);{let o=e[t.index];return Et(o)?Nc(-1,o):et(o)}}else{if(n&128)return kr(e,t.next);if(n&32)return Fu(t,e)()||et(e[t.index]);{let r=ig(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=an(e[ke]);return kr(o,r)}else return kr(e,t.next)}}}return null}function ig(e,t){if(t!==null){let r=e[ke][Me],o=t.projection;return r.projection[o]}return null}function Nc(e,t){let n=be+e+1;if(n<t.length){let r=t[n],o=r[A].firstChild;if(o!==null)return kr(r,o)}return t[sn]}function Uu(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Jr(et(a),r),n.flags|=2),!Pu(n))if(c&8)Uu(e,t,n.child,r,o,i,!1),Vn(t,e,o,a,i);else if(c&32){let u=Fu(n,r),l;for(;l=u();)Vn(t,e,o,l,i);Vn(t,e,o,a,i)}else c&16?sg(e,t,r,n,o,i):Vn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function us(e,t,n,r,o,i){Uu(n,r,e.firstChild,t,o,i,!1)}function II(e,t,n){let r=t[ee],o=rg(e,n,t),i=n.parent||t[Me],s=og(i,n,t);sg(r,0,t,n,o,s)}function sg(e,t,n,r,o,i){let s=n[ke],c=s[Me].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];Vn(t,e,o,l,i)}else{let u=c,l=s[fe];yp(r)&&(u.flags|=128),Uu(e,t,u,l,o,i,!0)}}function bI(e,t,n,r,o){let i=n[sn],s=et(n);i!==s&&Vn(t,e,r,i,o);for(let a=be;a<n.length;a++){let c=n[a];us(c[A],c,e,t,r,i)}}function CI(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:mt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=mt.Important),e.setStyle(n,r,o,i))}}function $i(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(et(i)),Et(i)&&SI(i,r);let s=n.type;if(s&8)$i(e,t,n.child,r);else if(s&32){let a=Fu(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=ig(t,n);if(Array.isArray(a))r.push(...a);else{let c=an(t[ke]);$i(c[A],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function SI(e,t){for(let n=be;n<e.length;n++){let r=e[n],o=r[A].firstChild;o!==null&&$i(r[A],r,o,t)}e[sn]!==e[Dt]&&t.push(e[sn])}function ag(e){if(e[Un]!==null){for(let t of e[Un])t.impl.addSequence(t);e[Un].length=0}}var cg=[];function MI(e){return e[Oe]??TI(e)}function TI(e){let t=cg.pop()??Object.create(RI);return t.lView=e,t}function _I(e){e.lView[Oe]!==e&&(e.lView=null,cg.push(e))}var RI=j(D({},wn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Kn(e.lView)},consumerOnSignalRead(){this.lView[Oe]=this}});function xI(e){let t=e[Oe]??Object.create(NI);return t.lView=e,t}var NI=j(D({},wn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=an(e.lView);for(;t&&!ug(t[A]);)t=an(t);t&&kh(t)},consumerOnSignalRead(){this.lView[Oe]=this}});function ug(e){return e.type!==2}function lg(e){if(e[on]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[on])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[x]&8192)}}var AI=100;function dg(e,t=!0,n=0){let o=e[ht].rendererFactory,i=!1;i||o.begin?.();try{OI(e,n)}catch(s){throw t&&tg(e,s),s}finally{i||o.end?.()}}function OI(e,t){let n=Hh();try{Oi(!0),Ac(e,t);let r=0;for(;rs(e);){if(r===AI)throw new E(103,!1);r++,Ac(e,1)}}finally{Oi(n)}}function kI(e,t,n,r){if(Wr(t))return;let o=t[x],i=!1,s=!1;du(t);let a=!0,c=null,u=null;i||(ug(e)?(u=MI(t),c=Ir(u)):Cd()===null?(a=!1,u=xI(t),c=Ir(u)):t[Oe]&&(br(t[Oe]),t[Oe]=null));try{Oh(t),fE(e.bindingStartIndex),n!==null&&Kp(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Di(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Ei(t,f,0,null),Ha(t,0)}if(s||FI(t),lg(t),fg(t,0),e.contentQueries!==null&&Tp(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Di(t,f)}else{let f=e.contentHooks;f!==null&&Ei(t,f,1),Ha(t,1)}LI(e,t);let d=e.components;d!==null&&pg(t,d,0);let h=e.viewQuery;if(h!==null&&wc(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Di(t,f)}else{let f=e.viewHooks;f!==null&&Ei(t,f,2),Ha(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[$a]){for(let f of t[$a])f();t[$a]=null}i||(ag(t),t[x]&=-73)}catch(l){throw i||Kn(t),l}finally{u!==null&&(Lo(u,c),a&&_I(u)),fu()}}function fg(e,t){for(let n=Ep(e);n!==null;n=wp(n))for(let r=be;r<n.length;r++){let o=n[r];hg(o,t)}}function FI(e){for(let t=Ep(e);t!==null;t=wp(t)){if(!(t[x]&2))continue;let n=t[Hn];for(let r=0;r<n.length;r++){let o=n[r];kh(o)}}}function PI(e,t,n){B(18);let r=tt(t,e);hg(r,n),B(19,r[ye])}function hg(e,t){uu(e)&&Ac(e,t)}function Ac(e,t){let r=e[A],o=e[x],i=e[Oe],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&jo(i)),s||=!1,i&&(i.dirty=!1),e[x]&=-9217,s)kI(r,e,r.template,e[ye]);else if(o&8192){lg(e),fg(e,1);let a=r.components;a!==null&&pg(e,a,1),ag(e)}}function pg(e,t,n){for(let r=0;r<t.length;r++)PI(e,t[r],n)}function LI(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)cn(~o);else{let i=o,s=n[++r],a=n[++r];gE(s,i);let c=t[i];B(24,c),a(2,c),B(25,c)}}}finally{cn(-1)}}function Bu(e,t){let n=Hh()?64:1088;for(e[ht].changeDetectionScheduler?.notify(t);e;){e[x]|=n;let r=an(e);if(Ni(e)&&!r)return e;e=r}return null}function gg(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function jI(e,t){let n=be+t;if(n<e.length)return e[n]}function $u(e,t,n,r=!0){let o=t[A];if(UI(o,t,e,n),r){let s=Nc(n,e),a=t[ee],c=a.parentNode(e[sn]);c!==null&&gI(o,e[Me],a,t,c,s)}let i=t[Lr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function VI(e,t){let n=Hi(e,t);return n!==void 0&&ju(n[A],n),n}function Hi(e,t){if(e.length<=be)return;let n=be+t,r=e[n];if(r){let o=r[rn];o!==null&&o!==e&&Lu(o,r),t>0&&(e[n-1][ze]=r[ze]);let i=Ti(e,be+t);pI(r[A],r);let s=i[pt];s!==null&&s.detachView(i[A]),r[fe]=null,r[ze]=null,r[x]&=-129}return r}function UI(e,t,n,r){let o=be+r,i=n.length;r>0&&(n[o-1][ze]=t),r<i-be?(t[ze]=n[o],gh(n,be+r,t)):(n.push(t),t[ze]=null),t[fe]=n;let s=t[rn];s!==null&&n!==s&&mg(s,t);let a=t[pt];a!==null&&a.insertView(e),ac(t),t[x]|=128}function mg(e,t){let n=e[Hn],r=t[fe];if(Ft(r))e[x]|=2;else{let o=r[fe][ke];t[ke]!==o&&(e[x]|=2)}n===null?e[Hn]=[t]:n.push(t)}var Hr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[A];return $i(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[ye]}set context(t){this._lView[ye]=t}get destroyed(){return Wr(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[fe];if(Et(t)){let n=t[xi],r=n?n.indexOf(this):-1;r>-1&&(Hi(t,r),Ti(n,r))}this._attachedToViewContainer=!1}ju(this._lView[A],this._lView)}onDestroy(t){Fh(this._lView,t)}markForCheck(){Bu(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[x]&=-129}reattach(){ac(this._lView),this._lView[x]|=128}detectChanges(){this._lView[x]|=1024,dg(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new E(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Ni(this._lView),n=this._lView[rn];n!==null&&!t&&Lu(n,this._lView),ng(this._lView[A],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new E(902,!1);this._appRef=t;let n=Ni(this._lView),r=this._lView[rn];r!==null&&!n&&mg(r,this._lView),ac(this._lView)}};var dn=(()=>{class e{static __NG_ELEMENT_ID__=HI}return e})(),BI=dn,$I=class extends BI{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=ku(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Hr(o)}};function HI(){return Hu(ve(),F())}function Hu(e,t){return e.type&4?new $I(t,e,Xn(e,t)):null}function ls(e,t,n,r,o){let i=e.data[t];if(i===null)i=zI(e,t,n,r,o),pE()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=dE();i.injectorIndex=s===null?-1:s.injectorIndex}return Yr(i,!0),i}function zI(e,t,n,r,o){let i=Uh(),s=Bh(),a=s?i:i&&i.parent,c=e.data[t]=GI(e,a,n,t,r,o);return qI(e,c,i,s),c}function qI(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function GI(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Vh()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var u1=new RegExp(`^(\\d+)*(${rw}|${nw})*(.*)`);var WI=()=>null;function zi(e,t){return WI(e,t)}var ZI=class{},yg=class{},Oc=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Ie(t)}.`)}},ds=class{static NULL=new Oc},zn=class{},tr=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>YI()}return e})();function YI(){let e=F(),t=ve(),n=tt(t.index,e);return(Ft(n)?n:e)[ee]}var QI=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>null})}return e})();function Vf(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=df(o,a);else if(i==2){let c=a,u=t[++s];r=df(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function H(e,t=O.Default){let n=F();if(n===null)return y(e,t);let r=ve();return sp(r,n,ge(e),t)}function vg(){let e="invalid";throw new Error(e)}function Dg(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=JI(s);l===null?a=s:[a,c,u]=l,tb(e,t,n,a,i,c,u)}i!==null&&r!==null&&KI(n,r,i)}function KI(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new E(-301,!1);r.push(t[o],i)}}function JI(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&Xe(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,XI(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function XI(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function eb(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function tb(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&Xe(f)&&(c=!0,eb(e,n,h)),fc(Li(n,t),e,f.type)}ab(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Yp(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=Ur(n.mergedAttrs,f.hostAttrs),rb(e,n,t,d,f),sb(d,f,o),s!==null&&s.has(f)){let[v,I]=s.get(f);n.directiveToIndex.set(f.type,[d,v+n.directiveStart,I+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let p=f.type.prototype;!u&&(p.ngOnChanges||p.ngOnInit||p.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(p.ngOnChanges||p.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}nb(e,n,i)}function nb(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Uf(0,t,o,r),Uf(1,t,o,r),$f(t,r,!1);else{let i=n.get(o);Bf(0,t,i,r),Bf(1,t,i,r),$f(t,r,!0)}}}function Uf(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Eg(t,i)}}function Bf(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Eg(t,s)}}function Eg(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function $f(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Tu(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function rb(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=en(o.type,!0)),s=new un(i,Xe(o),H);e.blueprint[r]=s,n[r]=s,ob(e,t,r,Yp(e,n,o.hostVars,It),o)}function ob(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;ib(s)!=a&&s.push(a),s.push(n,r,i)}}function ib(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function sb(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Xe(t)&&(n[""]=e)}}function ab(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function wg(e,t,n,r,o,i,s,a){let c=t.consts,u=Ai(c,s),l=ls(t,e,2,r,u);return i&&Dg(t,n,l,Ai(c,a),o),l.mergedAttrs=Ur(l.mergedAttrs,l.attrs),l.attrs!==null&&Vf(l,l.attrs,!1),l.mergedAttrs!==null&&Vf(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function Ig(e,t){Kh(e,t),Mh(t)&&e.queries.elementEnd(t)}var qi=class extends ds{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=nn(t);return new qn(n,this.ngModule)}};function cb(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&cs.SignalBased)!==0};return o&&(i.transform=o),i})}function ub(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function lb(e,t,n){let r=t instanceof me?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new lc(n,r):n}function db(e){let t=e.get(zn,null);if(t===null)throw new E(407,!1);let n=e.get(QI,null),r=e.get(ln,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function fb(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return qp(t,n,n==="svg"?XD:n==="math"?eE:null)}var qn=class extends yg{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=cb(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=ub(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Hw(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){B(22);let i=P(null);try{let s=this.componentDef,a=r?["ng-version","19.2.3"]:zw(this.componentDef.selectors[0]),c=_u(0,null,null,1,0,null,null,null,null,[a],null),u=lb(s,o||this.ngModule,t),l=db(u),d=l.rendererFactory.createRenderer(null,s),h=r?Xw(d,r,s.encapsulation,u):fb(s,d),f=Ru(null,c,null,512|Zp(s),null,null,l,d,u,null,Mp(h,u,!0));f[he]=h,du(f);let p=null;try{let v=wg(he,c,f,"#host",()=>[this.componentDef],!0,0);h&&(Wp(d,h,v),Jr(h,f)),Nu(c,f,v),_p(c,v,f),Ig(c,v),n!==void 0&&hb(v,this.ngContentSelectors,n),p=tt(v.index,f),f[ye]=p[ye],Ou(c,f,null)}catch(v){throw p!==null&&vc(p),vc(f),v}finally{B(23),fu()}return new kc(this.componentType,f)}finally{P(i)}}},kc=class extends ZI{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=cu(n[A],he),this.location=Xn(this._tNode,n),this.instance=tt(this._tNode.index,n)[ye],this.hostView=this.changeDetectorRef=new Hr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Au(r,o[A],o,t,n);this.previousInputValues.set(t,n);let s=tt(r.index,o);Bu(s,1)}get injector(){return new Xt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function hb(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Vt=(()=>{class e{static __NG_ELEMENT_ID__=pb}return e})();function pb(){let e=ve();return Cg(e,F())}var gb=Vt,bg=class extends gb{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Xn(this._hostTNode,this._hostLView)}get injector(){return new Xt(this._hostTNode,this._hostLView)}get parentInjector(){let t=mu(this._hostTNode,this._hostLView);if(ep(t)){let n=Fi(t,this._hostLView),r=ki(t),o=n[A].data[r+8];return new Xt(o,n)}else return new Xt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Hf(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-be}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=zi(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Bi(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!YD(t),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let c=s?t:new qn(nn(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let v=(s?u:this.parentInjector).get(me,null);v&&(i=v)}let l=nn(c.componentType??{}),d=zi(this._lContainer,l?.id??null),h=d?.firstChild??null,f=c.create(u,o,h,i);return this.insertImpl(f.hostView,a,Bi(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(nE(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[fe],u=new bg(c,c[Me],c[fe]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return $u(s,o,i,r),t.attachToViewContainerRef(),gh(Wa(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Hf(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Hi(this._lContainer,n);r&&(Ti(Wa(this._lContainer),n),ju(r[A],r))}detach(t){let n=this._adjustIndex(t,-1),r=Hi(this._lContainer,n);return r&&Ti(Wa(this._lContainer),n)!=null?new Hr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Hf(e){return e[xi]}function Wa(e){return e[xi]||(e[xi]=[])}function Cg(e,t){let n,r=t[e.index];return Et(r)?n=r:(n=gg(r,t,null,e),t[e.index]=n,xu(t,n)),yb(n,t,e,r),new bg(n,e,t)}function mb(e,t){let n=e[ee],r=n.createComment(""),o=rt(t,e),i=n.parentNode(o);return Ui(n,i,r,n.nextSibling(o),!1),r}var yb=Eb,vb=()=>!1;function Db(e,t,n){return vb(e,t,n)}function Eb(e,t,n,r){if(e[sn])return;let o;n.type&8?o=et(r):o=mb(t,n),e[sn]=o}var Fc=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Pc=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)zu(t,n).matches!==null&&this.queries[n].setDirty()}},Gi=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=_b(t):this.predicate=t}},Lc=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},jc=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,wb(n,i)),this.matchTNodeWithReadOption(t,n,wi(n,t,i,!1,!1))}else r===dn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,wi(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===it||o===Vt||o===dn&&n.type&4)this.addMatch(n.index,-2);else{let i=wi(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function wb(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Ib(e,t){return e.type&11?Xn(e,t):e.type&4?Hu(e,t):null}function bb(e,t,n,r){return n===-1?Ib(t,e):n===-2?Cb(e,t,r):Br(e,e[A],n,t)}function Cb(e,t,n){if(n===it)return Xn(t,e);if(n===dn)return Hu(t,e);if(n===Vt)return Cg(t,e)}function Sg(e,t,n,r){let o=t[pt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(bb(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Vc(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Sg(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=be;d<l.length;d++){let h=l[d];h[rn]===h[fe]&&Vc(h[A],h,u,r)}if(l[Hn]!==null){let d=l[Hn];for(let h=0;h<d.length;h++){let f=d[h];Vc(f[A],f,u,r)}}}}}return r}function Sb(e,t){return e[pt].queries[t].queryList}function Mg(e,t,n){let r=new yc((n&4)===4);return iE(e,t,r,r.destroy),(t[pt]??=new Pc).queries.push(new Fc(r))-1}function Mb(e,t,n){let r=ae();return r.firstCreatePass&&(Tg(r,new Gi(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Mg(r,F(),t)}function Tb(e,t,n,r){let o=ae();if(o.firstCreatePass){let i=ve();Tg(o,new Gi(t,n,r),i.index),Rb(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Mg(o,F(),n)}function _b(e){return e.split(",").map(t=>t.trim())}function Tg(e,t,n){e.queries===null&&(e.queries=new Lc),e.queries.track(new jc(t,n))}function Rb(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function zu(e,t){return e.queries.getByIndex(t)}function xb(e,t){let n=e[A],r=zu(n,t);return r.crossesNgTemplate?Vc(n,e,t,[]):Sg(n,e,r,t)}var Gn=class{},qu=class{};var Uc=class extends Gn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new qi(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=vh(t);this._bootstrapComponents=$p(i.bootstrap),this._r3Injector=up(t,n,[{provide:Gn,useValue:this},{provide:ds,useValue:this.componentFactoryResolver},...r],Ie(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Bc=class extends qu{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Uc(this.moduleType,t,[])}};var Wi=class extends Gn{injector;componentFactoryResolver=new qi(this);instance=null;constructor(t){super();let n=new Pr([...t.providers,{provide:Gn,useValue:this},{provide:ds,useValue:this.componentFactoryResolver}],t.parent||su(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function fs(e,t,n=null){return new Wi({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Nb=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Dh(!1,n.type),o=r.length>0?fs([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(y(me))})}return e})();function _g(e){return qr(()=>{let t=Rg(e),n=j(D({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===vp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Nb).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||nt.Emulated,styles:e.styles||Ae,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Xr("NgStandalone"),xg(n);let r=e.dependencies;return n.directiveDefs=zf(r,!1),n.pipeDefs=zf(r,!0),n.id=Pb(n),n})}function Ab(e){return nn(e)||LD(e)}function Ob(e){return e!==null}function Te(e){return qr(()=>({type:e.type,bootstrap:e.bootstrap||Ae,declarations:e.declarations||Ae,imports:e.imports||Ae,exports:e.exports||Ae,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function kb(e,t){if(e==null)return tn;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=cs.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Fb(e){if(e==null)return tn;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function Ut(e){return qr(()=>{let t=Rg(e);return xg(t),t})}function Gu(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Rg(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||tn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ae,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:kb(e.inputs,t),outputs:Fb(e.outputs),debugInfo:null}}function xg(e){e.features?.forEach(t=>t(e))}function zf(e,t){if(!e)return null;let n=t?jD:Ab;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(Ob)}function Pb(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Lb(e){return Object.getPrototypeOf(e.prototype).constructor}function jb(e){let t=Lb(e.type),n=!0,r=[e];for(;t;){let o;if(Xe(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new E(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Za(e.inputs),s.declaredInputs=Za(e.declaredInputs),s.outputs=Za(e.outputs);let a=o.hostBindings;a&&Hb(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&Bb(e,c),u&&$b(e,u),Vb(e,o),hD(e.outputs,o.outputs),Xe(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===jb&&(n=!1)}}t=Object.getPrototypeOf(t)}Ub(r)}function Vb(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Ub(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Ur(o.hostAttrs,n=Ur(n,o.hostAttrs))}}function Za(e){return e===tn?{}:e===Ae?[]:e}function Bb(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function $b(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Hb(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Ng(e){return qb(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function zb(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function qb(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Wu(e,t,n){return e[t]=n}function Gb(e,t){return e[t]}function yt(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Wb(e,t,n,r){let o=yt(e,t,n);return yt(e,t+1,r)||o}function Zb(e,t,n,r,o,i,s,a,c){let u=t.consts,l=ls(t,e,4,s||null,a||null);jh()&&Dg(t,n,l,Ai(u,c),eg),l.mergedAttrs=Ur(l.mergedAttrs,l.attrs),Kh(t,l);let d=l.tView=_u(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function Ag(e,t,n,r,o,i,s,a,c,u){let l=n+he,d=t.firstCreatePass?Zb(l,t,e,r,o,i,s,a,c):t.data[l];Yr(d,!1);let h=Qb(t,e,d,n);pu()&&Vu(t,e,h,d),Jr(h,e);let f=gg(h,e,h,d);return e[l]=f,xu(e,f),Db(f,d,e),au(d)&&Nu(t,e,d),c!=null&&Jp(e,d,u),d}function Yb(e,t,n,r,o,i,s,a){let c=F(),u=ae(),l=Ai(u.consts,i);return Ag(c,u,e,t,n,r,o,l,s,a),Yb}var Qb=Kb;function Kb(e,t,n,r){return gu(!0),t[ee].createComment("")}var Zu=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Og=new m("");var kg=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new $c})}return e})(),$c=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function nr(e){return!!e&&typeof e.then=="function"}function Yu(e){return!!e&&typeof e.subscribe=="function"}var hs=new m("");var Fg=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=g(hs,{optional:!0})??[];injector=g(oe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Se(this.injector,o);if(nr(i))n.push(i);else if(Yu(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),to=new m("");function Jb(){kd(()=>{throw new E(600,!1)})}function Xb(e){return e.isBoundToModule}var eC=10;var vt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=g(zE);afterRenderManager=g(Sp);zonelessEnabled=g(vu);rootEffectScheduler=g(kg);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new U;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=g(Lt).hasPendingTasks.pipe(T(n=>!n));constructor(){g(er,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=g(me);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){B(10);let o=n instanceof yg;if(!this._injector.get(Fg).done){let h="";throw new E(405,h)}let s;o?s=n:s=this._injector.get(ds).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=Xb(s)?void 0:this._injector.get(Gn),c=r||s.selector,u=s.create(oe.NULL,[],c,a),l=u.location.nativeElement,d=u.injector.get(Og,null);return d?.registerApplication(l),u.onDestroy(()=>{this.detachView(u.hostView),Ii(this.components,u),d?.unregisterApplication(l)}),this._loadComponent(u),B(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){B(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Cu.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new E(101,!1);let n=P(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,P(n),this.afterTick.next(),B(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(zn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<eC;)B(14),this.synchronizeOnce(),B(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)tC(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>rs(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Ii(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(to,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Ii(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new E(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ii(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function tC(e,t,n,r){if(!n&&!rs(e))return;dg(e,t,n&&!r?0:1)}function Qu(e,t,n,r){let o=F(),i=Qr();if(yt(o,i,t)){let s=ae(),a=hu();aI(a,o,e,t,n,r)}return Qu}function nC(e,t,n,r){return yt(e,Qr(),n)?t+Xi(n)+r:It}function yi(e,t){return e<<17|t<<2}function fn(e){return e>>17&32767}function rC(e){return(e&2)==2}function oC(e,t){return e&131071|t<<17}function Hc(e){return e|2}function Wn(e){return(e&131068)>>2}function Ya(e,t){return e&-131069|t<<2}function iC(e){return(e&1)===1}function zc(e){return e|1}function sC(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=fn(s),c=Wn(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Gr(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=fn(e[a+1]);e[r+1]=yi(h,a),h!==0&&(e[h+1]=Ya(e[h+1],r)),e[a+1]=oC(e[a+1],r)}else e[r+1]=yi(a,0),a!==0&&(e[a+1]=Ya(e[a+1],r)),a=r;else e[r+1]=yi(c,0),a===0?a=r:e[c+1]=Ya(e[c+1],r),c=r;u&&(e[r+1]=Hc(e[r+1])),qf(e,l,r,!0),qf(e,l,r,!1),aC(t,l,e,r,i),s=yi(a,c),i?t.classBindings=s:t.styleBindings=s}function aC(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Gr(i,t)>=0&&(n[r+1]=zc(n[r+1]))}function qf(e,t,n,r){let o=e[n+1],i=t===null,s=r?fn(o):Wn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];cC(c,t)&&(a=!0,e[s+1]=r?zc(u):Hc(u)),s=r?fn(u):Wn(u)}a&&(e[n+1]=r?Hc(o):zc(o))}function cC(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Gr(e,t)>=0:!1}function uC(e,t,n){let r=F(),o=Qr();if(yt(r,o,t)){let i=ae(),s=hu();Xp(i,s,r,e,t,r[ee],n,!1)}return uC}function Gf(e,t,n,r,o){Au(t,e,n,o?"class":"style",r)}function Pg(e,t,n){return Lg(e,t,n,!1),Pg}function lC(e,t){return Lg(e,t,null,!0),lC}function Lg(e,t,n,r){let o=F(),i=ae(),s=hE(2);if(i.firstUpdatePass&&fC(i,e,s,r),t!==It&&yt(o,s,t)){let a=i.data[Jn()];yC(i,a,o,o[ee],e,o[s+1]=vC(t,n),r,s)}}function dC(e,t){return t>=e.expandoStartIndex}function fC(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Jn()],s=dC(e,n);DC(i,r)&&t===null&&!s&&(t=!1),t=hC(o,i,t,r),sC(o,i,t,n,s,r)}}function hC(e,t,n,r){let o=yE(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Qa(null,e,t,n,r),n=zr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Qa(o,e,t,n,r),i===null){let c=pC(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Qa(null,e,t,c[1],r),c=zr(c,t.attrs,r),gC(e,t,r,c))}else i=mC(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function pC(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Wn(r)!==0)return e[fn(r)]}function gC(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[fn(o)]=r}function mC(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=zr(r,s,n)}return zr(r,t.attrs,n)}function Qa(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=zr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function zr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),FD(e,s,n?!0:t[++i]))}return e===void 0?null:e}function yC(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=iC(u)?Wf(c,t,n,o,Wn(u),s):void 0;if(!Zi(l)){Zi(i)||rC(u)&&(i=Wf(c,null,n,o,a,s));let d=Nh(Jn(),n);CI(r,s,d,o,i)}}function Wf(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=n[o+1];h===It&&(h=d?Ae:void 0);let f=d?Ua(h,r):l===r?h:void 0;if(u&&!Zi(f)&&(f=Ua(c,r)),Zi(f)&&(a=f,s))return a;let p=e[o+1];o=s?fn(p):Wn(p)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Ua(c,r))}return a}function Zi(e){return e!==void 0}function vC(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Ie(st(e)))),e}function DC(e,t){return(e.flags&(t?8:16))!==0}function m1(e,t){Xr("NgControlFlow");let n=F(),r=Qr(),o=n[r]!==It?n[r]:-1,i=o!==-1?Zf(n,he+o):void 0,s=0;if(yt(n,r,e)){let a=P(null);try{if(i!==void 0&&VI(i,s),e!==-1){let c=he+e,u=Zf(n,c),l=EC(n[A],c),d=zi(u,l.tView.ssrId),h=ku(n,l,t,{dehydratedView:d});$u(u,h,s,Bi(l,d))}}finally{P(a)}}else if(i!==void 0){let a=jI(i,s);a!==void 0&&(a[ye]=t)}}function Zf(e,t){return e[t]}function EC(e,t){return cu(e,t)}function jg(e,t,n,r){let o=F(),i=ae(),s=he+e,a=o[ee],c=i.firstCreatePass?wg(s,i,o,t,eg,jh(),n,r):i.data[s],u=wC(i,o,c,a,t,e);o[s]=u;let l=au(c);return Yr(c,!0),Wp(a,u,c),!Pu(c)&&pu()&&Vu(i,o,u,c),(sE()===0||l)&&Jr(u,o),aE(),l&&(Nu(i,o,c),_p(i,c,o)),r!==null&&Jp(o,c),jg}function Vg(){let e=ve();Bh()?$h():(e=e.parent,Yr(e,!1));let t=e;uE(t)&&lE(),cE();let n=ae();return n.firstCreatePass&&Ig(n,t),t.classesWithoutHost!=null&&bE(t)&&Gf(n,t,F(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&CE(t)&&Gf(n,t,F(),t.stylesWithoutHost,!1),Vg}function Ku(e,t,n,r){return jg(e,t,n,r),Vg(),Ku}var wC=(e,t,n,r,o,i)=>(gu(!0),qp(r,o,EE()));function y1(){return F()}var Kt=void 0;function IC(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var bC=["en",[["a","p"],["AM","PM"],Kt],[["AM","PM"],Kt,Kt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Kt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Kt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Kt,"{1} 'at' {0}",Kt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",IC],Ka={};function Fe(e){let t=CC(e),n=Yf(t);if(n)return n;let r=t.split("-")[0];if(n=Yf(r),n)return n;if(r==="en")return bC;throw new E(701,!1)}function Yf(e){return e in Ka||(Ka[e]=ft.ng&&ft.ng.common&&ft.ng.common.locales&&ft.ng.common.locales[e]),Ka[e]}var J=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(J||{});function CC(e){return e.toLowerCase().replace(/_/g,"-")}var Yi="en-US";var SC=Yi;function MC(e){typeof e=="string"&&(SC=e.toLowerCase().replace(/_/g,"-"))}var TC=(e,t,n)=>{};function Ju(e,t,n,r){let o=F(),i=ae(),s=ve();return Ug(i,o,o[ee],s,e,t,r),Ju}function _C(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Ri],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Ug(e,t,n,r,o,i,s){let a=au(r),u=e.firstCreatePass?Lh(e):null,l=t[ye],d=Ph(t),h=!0;if(r.type&3||s){let f=rt(r,t),p=s?s(f):f,v=d.length,I=s?Z=>s(et(Z[r.index])):r.index,_=null;if(!s&&a&&(_=_C(e,t,o,r.index)),_!==null){let Z=_.__ngLastListenerFn__||_;Z.__ngNextListenerFn__=i,_.__ngLastListenerFn__=i,h=!1}else{i=Jf(r,t,l,i),TC(p,o,i);let Z=n.listen(p,o,i);d.push(i,Z),u&&u.push(o,I,v,v+1)}}else i=Jf(r,t,l,i);if(h){let f=r.outputs?.[o],p=r.hostDirectiveOutputs?.[o];if(p&&p.length)for(let v=0;v<p.length;v+=2){let I=p[v],_=p[v+1];Qf(r,e,t,I,_,o,i,d,u)}if(f&&f.length)for(let v of f)Qf(r,e,t,v,o,o,i,d,u)}}function Qf(e,t,n,r,o,i,s,a,c){let u=n[r],d=t.data[r].outputs[o],f=u[d].subscribe(s),p=a.length;a.push(s,f),c&&c.push(i,e.index,p,-(p+1))}function Kf(e,t,n,r){let o=P(null);try{return B(6,t,n),n(r)!==!1}catch(i){return tg(e,i),!1}finally{B(7,t,n),P(o)}}function Jf(e,t,n,r){return function o(i){if(i===Function)return r;let s=Qn(e)?tt(e.index,t):t;Bu(s,5);let a=Kf(t,n,r,i),c=o.__ngNextListenerFn__;for(;c;)a=Kf(t,n,c,i)&&a,c=c.__ngNextListenerFn__;return a}}function v1(e=1){return DE(e)}function RC(e,t){let n=null,r=jw(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?zp(e,i,!0):Bw(r,i))return o}return n}function D1(e){let t=F()[ke][Me];if(!t.projection){let n=e?e.length:1,r=t.projection=OD(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?RC(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function E1(e,t=0,n,r,o,i){let s=F(),a=ae(),c=r?e+1:null;c!==null&&Ag(s,a,c,r,o,i,null,n);let u=ls(a,he+e,16,null,n||null);u.projection===null&&(u.projection=t),$h();let d=!s[Lr]||Vh();s[ke][Me].projection[u.projection]===null&&c!==null?xC(s,a,c):d&&!Pu(u)&&II(a,s,u)}function xC(e,t,n){let r=he+n,o=t.data[r],i=e[r],s=zi(i,o.tView.ssrId),a=ku(e,o,void 0,{dehydratedView:s});$u(i,a,0,Bi(o,s))}function Bg(e,t,n,r){Tb(e,t,n,r)}function w1(e,t,n){Mb(e,t,n)}function $g(e){let t=F(),n=ae(),r=zh();lu(r+1);let o=zu(n,r);if(e.dirty&&tE(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=xb(t,r);e.reset(i,WE),e.notifyOnChanges()}return!0}return!1}function Hg(){return Sb(F(),zh())}function NC(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function I1(e,t=""){let n=F(),r=ae(),o=e+he,i=r.firstCreatePass?ls(r,o,1,t,null):r.data[o],s=AC(r,n,i,t,e);n[o]=s,pu()&&Vu(r,n,s,i),Yr(i,!1)}var AC=(e,t,n,r,o)=>(gu(!0),qw(t[ee],r));function OC(e){return zg("",e,""),OC}function zg(e,t,n){let r=F(),o=nC(r,e,t,n);return o!==It&&kC(r,Jn(),o),zg}function kC(e,t,n){let r=Nh(t,e);Gw(e[ee],r,n)}function FC(e,t,n){mp(t)&&(t=t());let r=F(),o=Qr();if(yt(r,o,t)){let i=ae(),s=hu();Xp(i,s,r,e,t,r[ee],n,!1)}return FC}function b1(e,t){let n=mp(e);return n&&e.set(t),n}function PC(e,t){let n=F(),r=ae(),o=ve();return Ug(r,n,n[ee],o,e,t),PC}function LC(e,t,n){let r=ae();if(r.firstCreatePass){let o=Xe(e);qc(n,r.data,r.blueprint,o,!0),qc(t,r.data,r.blueprint,o,!1)}}function qc(e,t,n,r,o){if(e=ge(e),Array.isArray(e))for(let i=0;i<e.length;i++)qc(e[i],t,n,r,o);else{let i=ae(),s=F(),a=ve(),c=$n(e)?e:ge(e.provide),u=Ih(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if($n(e)||!e.multi){let f=new un(u,o,H),p=Xa(c,t,o?l:l+h,d);p===-1?(fc(Li(a,s),i,c),Ja(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[p]=f,s[p]=f)}else{let f=Xa(c,t,l+h,d),p=Xa(c,t,l,l+h),v=f>=0&&n[f],I=p>=0&&n[p];if(o&&!I||!o&&!v){fc(Li(a,s),i,c);let _=UC(o?VC:jC,n.length,o,r,u);!o&&I&&(n[p].providerFactory=_),Ja(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(_),s.push(_)}else{let _=qg(n[o?p:f],u,!o&&r);Ja(i,e,f>-1?f:p,_)}!o&&r&&I&&n[p].componentProviders++}}}function Ja(e,t,n,r){let o=$n(t),i=HD(t);if(o||i){let c=(i?ge(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function qg(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Xa(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function jC(e,t,n,r){return Gc(this.multi,[])}function VC(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Br(n,n[A],this.providerFactory.index,r);i=a.slice(0,s),Gc(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Gc(o,i);return i}function Gc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function UC(e,t,n,r,o){let i=new un(e,n,H);return i.multi=[],i.index=t,i.componentProviders=0,qg(i,o,r&&!n),i}function C1(e,t=[]){return n=>{n.providersResolver=(r,o)=>LC(r,o?o(e):e,t)}}function S1(e,t,n){let r=os()+e,o=F();return o[r]===It?Wu(o,r,n?t.call(n):t()):Gb(o,r)}function M1(e,t,n,r){return Wg(F(),os(),e,t,n,r)}function Gg(e,t){let n=e[t];return n===It?void 0:n}function Wg(e,t,n,r,o,i){let s=t+n;return yt(e,s,o)?Wu(e,s+1,i?r.call(i,o):r(o)):Gg(e,s+1)}function BC(e,t,n,r,o,i,s){let a=t+n;return Wb(e,a,o,i)?Wu(e,a+2,s?r.call(s,o,i):r(o,i)):Gg(e,a+2)}function T1(e,t){let n=ae(),r,o=e+he;n.firstCreatePass?(r=$C(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=en(r.type,!0)),s,a=Ee(H);try{let c=Pi(!1),u=i();return Pi(c),NC(n,F(),o,u),u}finally{Ee(a)}}function $C(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function _1(e,t,n){let r=e+he,o=F(),i=Ah(o,r);return Zg(o,r)?Wg(o,os(),t,i.transform,n,i):i.transform(n)}function R1(e,t,n,r){let o=e+he,i=F(),s=Ah(i,o);return Zg(i,o)?BC(i,os(),t,s.transform,n,r,s):s.transform(n,r)}function Zg(e,t){return e[A].data[t].pure}var Wc=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ps=(()=>{class e{compileModuleSync(n){return new Bc(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=vh(n),i=$p(o.declarations).reduce((s,a)=>{let c=nn(a);return c&&s.push(new qn(c)),s},[]);return new Wc(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var HC=(()=>{class e{zone=g(Q);changeDetectionScheduler=g(ln);applicationRef=g(vt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function zC({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new Q(j(D({},qC()),{scheduleInRootZone:n})),[{provide:Q,useFactory:e},{provide:Pt,multi:!0,useFactory:()=>{let r=g(HC,{optional:!0});return()=>r.initialize()}},{provide:Pt,multi:!0,useFactory:()=>{let r=g(GC);return()=>{r.initialize()}}},t===!0?{provide:dp,useValue:!0}:[],{provide:fp,useValue:n??lp}]}function qC(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var GC=(()=>{class e{subscription=new Y;initialized=!1;zone=g(Q);pendingTasks=g(Lt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Q.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Q.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var WC=(()=>{class e{appRef=g(vt);taskService=g(Lt);ngZone=g(Q);zonelessEnabled=g(vu);tracing=g(er,{optional:!0});disableScheduling=g(dp,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new Y;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Vi):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(g(fp,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof mc||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Sf:hp;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Vi+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Sf(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ZC(){return typeof $localize<"u"&&$localize.locale||Yi}var gs=new m("",{providedIn:"root",factory:()=>g(gs,O.Optional|O.SkipSelf)||ZC()});var Zc=new m(""),YC=new m("");function Ar(e){return!e.moduleRef}function QC(e){let t=Ar(e)?e.r3Injector:e.moduleRef.injector,n=t.get(Q);return n.run(()=>{Ar(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(qe,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Ar(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Zc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Zc);s.add(i),e.moduleRef.onDestroy(()=>{Ii(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return JC(r,n,()=>{let i=t.get(Fg);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(gs,Yi);if(MC(s||Yi),!t.get(YC,!0))return Ar(e)?t.get(vt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ar(e)){let c=t.get(vt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return KC(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function KC(e,t){let n=e.injector.get(vt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new E(-403,!1);t.push(e)}function JC(e,t,n){try{let r=n();return nr(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var bi=null;function XC(e=[],t){return oe.create({name:t,providers:[{provide:ts,useValue:"platform"},{provide:Zc,useValue:new Set([()=>bi=null])},...e]})}function eS(e=[]){if(bi)return bi;let t=XC(e);return bi=t,Jb(),tS(t),t}function tS(e){let t=e.get(Iu,null);Se(e,()=>{t?.forEach(n=>n())})}function Xu(){return!1}var gn=(()=>{class e{static __NG_ELEMENT_ID__=nS}return e})();function nS(e){return rS(ve(),F(),(e&16)===16)}function rS(e,t,n){if(Qn(e)&&!n){let r=tt(e.index,t);return new Hr(r,r)}else if(e.type&175){let r=t[ke];return new Hr(r,t)}return null}var Yc=class{constructor(){}supports(t){return Ng(t)}create(t){return new Qc(t)}},oS=(e,t)=>t,Qc=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||oS}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Xf(r,o,i)?n:r,a=Xf(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,p=f+h;l<=p&&p<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Ng(t))throw new E(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,zb(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new Kc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Qi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Qi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Kc=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Jc=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Qi=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Jc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Xf(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function eh(){return new el([new Yc])}var el=(()=>{class e{factories;static \u0275prov=w({token:e,providedIn:"root",factory:eh});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||eh()),deps:[[e,new xD,new ph]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new E(901,!1)}}return e})();function Yg(e){B(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=eS(r),i=[zC({}),{provide:ln,useExisting:WC},...n||[]],s=new Wi({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return QC({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{B(9)}}function no(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function rr(e){return Ld(e)}function ro(e,t){return Nd(e,t?.equal)}var Xc=class{[Ne];constructor(t){this[Ne]=t}destroy(){this[Ne].destroy()}};function ms(e,t){!t?.injector&&Zn(ms);let n=t?.injector??g(oe),r=t?.manualCleanup!==!0?n.get(ot):null,o,i=n.get(Su,null,{optional:!0}),s=n.get(ln);return i!==null&&!t?.forceRoot?(o=aS(i.view,s,e),r instanceof ji&&r._lView===i.view&&(r=null)):o=cS(e,n.get(kg),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Xc(o)}var Qg=j(D({},wn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:$r,run(){if(this.dirty=!1,this.hasRun&&!jo(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Ir(this),n=Oi(!1);try{this.maybeCleanup(),this.fn(e)}finally{Oi(n),Lo(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),iS=j(D({},Qg),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){br(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),sS=j(D({},Qg),{consumerMarkedDirty(){this.view[x]|=8192,Kn(this.view),this.notifier.notify(13)},destroy(){br(this),this.onDestroyFn(),this.maybeCleanup(),this.view[on]?.delete(this)}});function aS(e,t,n){let r=Object.create(sS);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[on]??=new Set,e[on].add(r),r.consumerMarkedDirty(r),r}function cS(e,t,n){let r=Object.create(iS);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function Kg(e){let t=nn(e);if(!t)return null;let n=new qn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var im=null;function ir(){return im}function sm(e){im??=e}var Ss=class{};var le=new m(""),ll=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(uS),providedIn:"platform"})}return e})(),am=new m(""),uS=(()=>{class e extends ll{_location;_history;_doc=g(le);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ir().getBaseHref(this._doc)}onPopState(n){let r=ir().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=ir().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function dl(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Jg(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Ct(e){return e&&e[0]!=="?"?`?${e}`:e}var St=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(fl),providedIn:"root"})}return e})(),cm=new m(""),fl=(()=>{class e extends St{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??g(le).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return dl(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Ct(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Ct(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Ct(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(y(ll),y(cm,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),um=(()=>{class e extends St{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=dl(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Ct(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Ct(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(y(ll),y(cm,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ar=(()=>{class e{_subject=new U;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=fS(Jg(Xg(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Ct(r))}normalize(n){return e.stripTrailingSlash(dS(this._basePath,Xg(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ct(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ct(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Ct;static joinWithSlash=dl;static stripTrailingSlash=Jg;static \u0275fac=function(r){return new(r||e)(y(St))};static \u0275prov=w({token:e,factory:()=>lS(),providedIn:"root"})}return e})();function lS(){return new ar(y(St))}function dS(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Xg(e){return e.replace(/\/index.html$/,"")}function fS(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var De=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(De||{}),z=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(z||{}),_e=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(_e||{}),Bt={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function hS(e){return Fe(e)[J.LocaleId]}function pS(e,t,n){let r=Fe(e),o=[r[J.DayPeriodsFormat],r[J.DayPeriodsStandalone]],i=Pe(o,t);return Pe(i,n)}function gS(e,t,n){let r=Fe(e),o=[r[J.DaysFormat],r[J.DaysStandalone]],i=Pe(o,t);return Pe(i,n)}function mS(e,t,n){let r=Fe(e),o=[r[J.MonthsFormat],r[J.MonthsStandalone]],i=Pe(o,t);return Pe(i,n)}function yS(e,t){let r=Fe(e)[J.Eras];return Pe(r,t)}function ys(e,t){let n=Fe(e);return Pe(n[J.DateFormat],t)}function vs(e,t){let n=Fe(e);return Pe(n[J.TimeFormat],t)}function Ds(e,t){let r=Fe(e)[J.DateTimeFormat];return Pe(r,t)}function Ts(e,t){let n=Fe(e),r=n[J.NumberSymbols][t];if(typeof r>"u"){if(t===Bt.CurrencyDecimal)return n[J.NumberSymbols][Bt.Decimal];if(t===Bt.CurrencyGroup)return n[J.NumberSymbols][Bt.Group]}return r}function lm(e){if(!e[J.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[J.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function vS(e){let t=Fe(e);return lm(t),(t[J.ExtraData][2]||[]).map(r=>typeof r=="string"?tl(r):[tl(r[0]),tl(r[1])])}function DS(e,t,n){let r=Fe(e);lm(r);let o=[r[J.ExtraData][0],r[J.ExtraData][1]],i=Pe(o,t)||[];return Pe(i,n)||[]}function Pe(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function tl(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var ES=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Es={},wS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function IS(e,t,n,r){let o=NS(e);t=bt(n,t)||t;let s=[],a;for(;t;)if(a=wS.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=fm(r,c),o=xS(o,r));let u="";return s.forEach(l=>{let d=_S(l);u+=d?d(o,n,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function Ms(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function bt(e,t){let n=hS(e);if(Es[n]??={},Es[n][t])return Es[n][t];let r="";switch(t){case"shortDate":r=ys(e,_e.Short);break;case"mediumDate":r=ys(e,_e.Medium);break;case"longDate":r=ys(e,_e.Long);break;case"fullDate":r=ys(e,_e.Full);break;case"shortTime":r=vs(e,_e.Short);break;case"mediumTime":r=vs(e,_e.Medium);break;case"longTime":r=vs(e,_e.Long);break;case"fullTime":r=vs(e,_e.Full);break;case"short":let o=bt(e,"shortTime"),i=bt(e,"shortDate");r=ws(Ds(e,_e.Short),[o,i]);break;case"medium":let s=bt(e,"mediumTime"),a=bt(e,"mediumDate");r=ws(Ds(e,_e.Medium),[s,a]);break;case"long":let c=bt(e,"longTime"),u=bt(e,"longDate");r=ws(Ds(e,_e.Long),[c,u]);break;case"full":let l=bt(e,"fullTime"),d=bt(e,"fullDate");r=ws(Ds(e,_e.Full),[l,d]);break}return r&&(Es[n][t]=r),r}function ws(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function Ge(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function bS(e,t){return Ge(e,3).substring(0,t)}function te(e,t,n=0,r=!1,o=!1){return function(i,s){let a=CS(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return bS(a,t);let c=Ts(s,Bt.MinusSign);return Ge(a,t,c,r,o)}}function CS(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function W(e,t,n=De.Format,r=!1){return function(o,i){return SS(o,i,e,t,n,r)}}function SS(e,t,n,r,o,i){switch(n){case 2:return mS(t,o,r)[e.getMonth()];case 1:return gS(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let u=vS(t),l=DS(t,o,r),d=u.findIndex(h=>{if(Array.isArray(h)){let[f,p]=h,v=s>=f.hours&&a>=f.minutes,I=s<p.hours||s===p.hours&&a<p.minutes;if(f.hours<p.hours){if(v&&I)return!0}else if(v||I)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return pS(t,o,r)[s<12?0:1];case 3:return yS(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function Is(e){return function(t,n,r){let o=-1*r,i=Ts(n,Bt.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+Ge(s,2,i)+Ge(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+Ge(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+Ge(s,2,i)+":"+Ge(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+Ge(s,2,i)+":"+Ge(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var MS=0,Cs=4;function TS(e){let t=Ms(e,MS,1).getDay();return Ms(e,0,1+(t<=Cs?Cs:Cs+7)-t)}function dm(e){let t=e.getDay(),n=t===0?-3:Cs-t;return Ms(e.getFullYear(),e.getMonth(),e.getDate()+n)}function nl(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=dm(n),s=TS(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Ge(o,e,Ts(r,Bt.MinusSign))}}function bs(e,t=!1){return function(n,r){let i=dm(n).getFullYear();return Ge(i,e,Ts(r,Bt.MinusSign),t)}}var rl={};function _S(e){if(rl[e])return rl[e];let t;switch(e){case"G":case"GG":case"GGG":t=W(3,z.Abbreviated);break;case"GGGG":t=W(3,z.Wide);break;case"GGGGG":t=W(3,z.Narrow);break;case"y":t=te(0,1,0,!1,!0);break;case"yy":t=te(0,2,0,!0,!0);break;case"yyy":t=te(0,3,0,!1,!0);break;case"yyyy":t=te(0,4,0,!1,!0);break;case"Y":t=bs(1);break;case"YY":t=bs(2,!0);break;case"YYY":t=bs(3);break;case"YYYY":t=bs(4);break;case"M":case"L":t=te(1,1,1);break;case"MM":case"LL":t=te(1,2,1);break;case"MMM":t=W(2,z.Abbreviated);break;case"MMMM":t=W(2,z.Wide);break;case"MMMMM":t=W(2,z.Narrow);break;case"LLL":t=W(2,z.Abbreviated,De.Standalone);break;case"LLLL":t=W(2,z.Wide,De.Standalone);break;case"LLLLL":t=W(2,z.Narrow,De.Standalone);break;case"w":t=nl(1);break;case"ww":t=nl(2);break;case"W":t=nl(1,!0);break;case"d":t=te(2,1);break;case"dd":t=te(2,2);break;case"c":case"cc":t=te(7,1);break;case"ccc":t=W(1,z.Abbreviated,De.Standalone);break;case"cccc":t=W(1,z.Wide,De.Standalone);break;case"ccccc":t=W(1,z.Narrow,De.Standalone);break;case"cccccc":t=W(1,z.Short,De.Standalone);break;case"E":case"EE":case"EEE":t=W(1,z.Abbreviated);break;case"EEEE":t=W(1,z.Wide);break;case"EEEEE":t=W(1,z.Narrow);break;case"EEEEEE":t=W(1,z.Short);break;case"a":case"aa":case"aaa":t=W(0,z.Abbreviated);break;case"aaaa":t=W(0,z.Wide);break;case"aaaaa":t=W(0,z.Narrow);break;case"b":case"bb":case"bbb":t=W(0,z.Abbreviated,De.Standalone,!0);break;case"bbbb":t=W(0,z.Wide,De.Standalone,!0);break;case"bbbbb":t=W(0,z.Narrow,De.Standalone,!0);break;case"B":case"BB":case"BBB":t=W(0,z.Abbreviated,De.Format,!0);break;case"BBBB":t=W(0,z.Wide,De.Format,!0);break;case"BBBBB":t=W(0,z.Narrow,De.Format,!0);break;case"h":t=te(3,1,-12);break;case"hh":t=te(3,2,-12);break;case"H":t=te(3,1);break;case"HH":t=te(3,2);break;case"m":t=te(4,1);break;case"mm":t=te(4,2);break;case"s":t=te(5,1);break;case"ss":t=te(5,2);break;case"S":t=te(6,1);break;case"SS":t=te(6,2);break;case"SSS":t=te(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Is(0);break;case"ZZZZZ":t=Is(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Is(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Is(2);break;default:return null}return rl[e]=t,t}function fm(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function RS(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function xS(e,t,n){let o=e.getTimezoneOffset(),i=fm(t,o);return RS(e,-1*(i-o))}function NS(e){if(em(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Ms(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(ES))return AS(r)}let t=new Date(e);if(!em(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function AS(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),u=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,u),t}function em(e){return e instanceof Date&&!isNaN(e.valueOf())}function _s(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ol=/\s+/,tm=[],tL=(()=>{class e{_ngEl;_renderer;initialClasses=tm;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(ol):tm}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(ol):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(ol).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(H(it),H(tr))};static \u0275dir=Ut({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var il=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},nL=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new il(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),nm(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);nm(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(Vt),H(dn),H(el))};static \u0275dir=Ut({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function nm(e,t){e.context.$implicit=t.item}var rL=(()=>{class e{_viewContainer;_context=new sl;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){rm(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){rm(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(Vt),H(dn))};static \u0275dir=Ut({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),sl=class{$implicit=null;ngIf=null};function rm(e,t){if(e&&!e.createEmbeddedView)throw new E(2020,!1)}function hm(e,t){return new E(2100,!1)}var al=class{createSubscription(t,n){return rr(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){rr(()=>t.unsubscribe())}},cl=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},OS=new cl,kS=new al,oL=(()=>{class e{_ref;_latestValue=null;markForCheckOnValueUpdate=!0;_subscription=null;_obj=null;_strategy=null;constructor(n){this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(nr(n))return OS;if(Yu(n))return kS;throw hm(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static \u0275fac=function(r){return new(r||e)(H(gn,16))};static \u0275pipe=Gu({name:"async",type:e,pure:!1})}return e})();var FS="mediumDate",PS=new m(""),LS=new m(""),iL=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??FS,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return IS(n,s,i||this.locale,a)}catch(s){throw hm(e,s.message)}}static \u0275fac=function(r){return new(r||e)(H(gs,16),H(PS,24),H(LS,24))};static \u0275pipe=Gu({name:"date",type:e,pure:!0})}return e})();var jS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Te({type:e});static \u0275inj=Ce({})}return e})(),pm="browser",VS="server";function hl(e){return e===VS}var gm=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new ul(g(le),window)})}return e})(),ul=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=US(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function US(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var sr=class{};var io=class{},xs=class{},mn=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var gl=class{encodeKey(t){return ym(t)}encodeValue(t){return ym(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function BS(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var $S=/%(\d[a-f0-9])/gi,HS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ym(e){return encodeURIComponent(e).replace($S,(t,n)=>HS[n]??t)}function Rs(e){return`${e}`}var $t=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new gl,t.fromString){if(t.fromObject)throw new E(2805,!1);this.map=BS(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Rs):[Rs(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Rs(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Rs(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var ml=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function zS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function vm(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Dm(e){return typeof Blob<"u"&&e instanceof Blob}function Em(e){return typeof FormData<"u"&&e instanceof FormData}function qS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var wm="Content-Type",Im="Accept",Sm="X-Request-URL",Mm="text/plain",Tm="application/json",GS=`${Tm}, ${Mm}, */*`,oo=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(zS(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new mn,this.context??=new ml,!this.params)this.params=new $t,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||vm(this.body)||Dm(this.body)||Em(this.body)||qS(this.body)?this.body:this.body instanceof $t?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Em(this.body)?null:Dm(this.body)?this.body.type||null:vm(this.body)?null:typeof this.body=="string"?Mm:this.body instanceof $t?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Tm:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,f)=>h.set(f,t.setHeaders[f]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,f)=>h.set(f,t.setParams[f]),l)),new e(n,r,s,{params:l,headers:u,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},cr=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(cr||{}),so=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new mn,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},yl=class e extends so{constructor(t={}){super(t)}type=cr.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Ns=class e extends so{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=cr.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},As=class extends so{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},WS=200,ZS=204;function pl(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var YS=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof oo)i=n;else{let c;o.headers instanceof mn?c=o.headers:c=new mn(o.headers);let u;o.params&&(o.params instanceof $t?u=o.params:u=new $t({fromObject:o.params})),i=new oo(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=M(i).pipe(Ke(c=>this.handler.handle(c)));if(n instanceof oo||o.observe==="events")return s;let a=s.pipe(X(c=>c instanceof Ns));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(T(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new E(2806,!1);return c.body}));case"blob":return a.pipe(T(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new E(2807,!1);return c.body}));case"text":return a.pipe(T(c=>{if(c.body!==null&&typeof c.body!="string")throw new E(2808,!1);return c.body}));case"json":default:return a.pipe(T(c=>c.body))}case"response":return a;default:throw new E(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new $t().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,pl(o,r))}post(n,r,o={}){return this.request("POST",n,pl(o,r))}put(n,r,o={}){return this.request("PUT",n,pl(o,r))}static \u0275fac=function(r){return new(r||e)(y(io))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var QS=new m("");function KS(e,t){return t(e)}function JS(e,t,n){return(r,o)=>Se(n,()=>t(r,i=>e(i,o)))}var _m=new m(""),XS=new m(""),eM=new m("",{providedIn:"root",factory:()=>!0});var bm=(()=>{class e extends io{backend;injector;chain=null;pendingTasks=g(Lt);contributeToStability=g(eM);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(_m),...this.injector.get(XS,[])]));this.chain=r.reduceRight((o,i)=>JS(o,i,this.injector),KS)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Ot(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(y(xs),y(me))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var tM=/^\)\]\}',?\n/,nM=RegExp(`^${Sm}:`,"m");function rM(e){return"responseURL"in e&&e.responseURL?e.responseURL:nM.test(e.getAllResponseHeaders())?e.getResponseHeader(Sm):null}var Cm=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new E(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?q(r.\u0275loadImpl()):M(null)).pipe(pe(()=>new R(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((v,I)=>s.setRequestHeader(v,I.join(","))),n.headers.has(Im)||s.setRequestHeader(Im,GS),!n.headers.has(wm)){let v=n.detectContentTypeHeader();v!==null&&s.setRequestHeader(wm,v)}if(n.responseType){let v=n.responseType.toLowerCase();s.responseType=v!=="json"?v:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let v=s.statusText||"OK",I=new mn(s.getAllResponseHeaders()),_=rM(s)||n.url;return c=new yl({headers:I,status:s.status,statusText:v,url:_}),c},l=()=>{let{headers:v,status:I,statusText:_,url:Z}=u(),ne=null;I!==ZS&&(ne=typeof s.response>"u"?s.responseText:s.response),I===0&&(I=ne?WS:0);let la=I>=200&&I<300;if(n.responseType==="json"&&typeof ne=="string"){let wv=ne;ne=ne.replace(tM,"");try{ne=ne!==""?JSON.parse(ne):null}catch(Iv){ne=wv,la&&(la=!1,ne={error:Iv,text:ne})}}la?(i.next(new Ns({body:ne,headers:v,status:I,statusText:_,url:Z||void 0})),i.complete()):i.error(new As({error:ne,headers:v,status:I,statusText:_,url:Z||void 0}))},d=v=>{let{url:I}=u(),_=new As({error:v,status:s.status||0,statusText:s.statusText||"Unknown Error",url:I||void 0});i.error(_)},h=!1,f=v=>{h||(i.next(u()),h=!0);let I={type:cr.DownloadProgress,loaded:v.loaded};v.lengthComputable&&(I.total=v.total),n.responseType==="text"&&s.responseText&&(I.partialText=s.responseText),i.next(I)},p=v=>{let I={type:cr.UploadProgress,loaded:v.loaded};v.lengthComputable&&(I.total=v.total),i.next(I)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:cr.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(y(sr))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Rm=new m(""),oM="XSRF-TOKEN",iM=new m("",{providedIn:"root",factory:()=>oM}),sM="X-XSRF-TOKEN",aM=new m("",{providedIn:"root",factory:()=>sM}),Os=class{},cM=(()=>{class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=_s(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(y(le),y(pn),y(iM))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function uM(e,t){let n=e.url.toLowerCase();if(!g(Rm)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=g(Os).getToken(),o=g(aM);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}function bL(...e){let t=[YS,Cm,bm,{provide:io,useExisting:bm},{provide:xs,useFactory:()=>g(QS,{optional:!0})??g(Cm)},{provide:_m,useValue:uM,multi:!0},{provide:Rm,useValue:!0},{provide:Os,useClass:cM}];for(let n of e)t.push(...n.\u0275providers);return hn(t)}var Dl=class extends Ss{supportsDOMEvents=!0},El=class e extends Dl{static makeCurrent(){sm(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=lM();return n==null?null:dM(n)}resetBaseElement(){ao=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return _s(document.cookie,t)}},ao=null;function lM(){return ao=ao||document.querySelector("base"),ao?ao.getAttribute("href"):null}function dM(e){return new URL(e,document.baseURI).pathname}var fM=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),wl=new m(""),Fm=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new E(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(y(wl),y(Q))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Fs=class{_doc;constructor(t){this._doc=t}manager},ks="ng-app-id";function xm(e){for(let t of e)t.remove()}function Nm(e,t){let n=t.createElement("style");return n.textContent=e,n}function hM(e,t,n,r){let o=e.head?.querySelectorAll(`style[${ks}="${t}"],link[${ks}="${t}"]`);if(o)for(let i of o)i.removeAttribute(ks),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Il(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Pm=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=hl(i),hM(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Nm);r?.forEach(o=>this.addUsage(o,this.external,Il))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(xm(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])xm(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Nm(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Il(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(ks,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(y(le),y(wu),y(bu,8),y(pn))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),vl={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Cl=/%COMP%/g;var Lm="%COMP%",pM=`_nghost-${Lm}`,gM=`_ngcontent-${Lm}`,mM=!0,yM=new m("",{providedIn:"root",factory:()=>mM});function vM(e){return gM.replace(Cl,e)}function DM(e){return pM.replace(Cl,e)}function jm(e,t){return t.map(n=>n.replace(Cl,e))}var Am=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=hl(a),this.defaultRenderer=new co(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===nt.ShadowDom&&(r=j(D({},r),{encapsulation:nt.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Ps?o.applyToHost(n):o instanceof uo&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case nt.Emulated:i=new Ps(c,u,r,this.appId,l,s,a,d,h);break;case nt.ShadowDom:return new bl(c,u,n,r,s,a,this.nonce,d,h);default:i=new uo(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(y(Fm),y(Pm),y(wu),y(yM),y(le),y(pn),y(Q),y(bu),y(er,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),co=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(vl[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Om(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Om(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new E(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=vl[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=vl[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(mt.DashCase|mt.Important)?t.style.setProperty(n,r,o&mt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&mt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=ir().getGlobalEventTarget(this.doc,t),!t))throw new E(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Om(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var bl=class extends co{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=jm(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=Il(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},uo=class extends co{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?jm(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ps=class extends uo{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=vM(l),this.hostAttr=DM(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},EM=(()=>{class e extends Fs{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(y(le))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),km=["alt","control","meta","shift"],wM={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},IM={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},bM=(()=>{class e extends Fs{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>ir().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),km.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=wM[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),km.forEach(s=>{if(s!==o){let a=IM[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(y(le))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function HL(e,t){return Yg(D({rootComponent:e},CM(t)))}function CM(e){return{appProviders:[...RM,...e?.providers??[]],platformProviders:_M}}function SM(){El.makeCurrent()}function MM(){return new qe}function TM(){return bp(document),document}var _M=[{provide:pn,useValue:pm},{provide:Iu,useValue:SM,multi:!0},{provide:le,useFactory:TM,deps:[]}];var RM=[{provide:ts,useValue:"root"},{provide:qe,useFactory:MM,deps:[]},{provide:wl,useClass:EM,multi:!0,deps:[le]},{provide:wl,useClass:bM,multi:!0,deps:[le]},Am,Pm,Fm,{provide:zn,useExisting:Am},{provide:sr,useClass:fM,deps:[]},[]];var Vm=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(y(le))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var xM=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=y(NM),o},providedIn:"root"})}return e})(),NM=(()=>{class e extends xM{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case at.NONE:return r;case at.HTML:return jt(r,"HTML")?st(r):Vp(this._doc,String(r)).toString();case at.STYLE:return jt(r,"Style")?st(r):r;case at.SCRIPT:if(jt(r,"Script"))return st(r);throw new E(5200,!1);case at.URL:return jt(r,"URL")?st(r):as(String(r));case at.RESOURCE_URL:if(jt(r,"ResourceURL"))return st(r);throw new E(5201,!1);default:throw new E(5202,!1)}}bypassSecurityTrustHtml(n){return Rp(n)}bypassSecurityTrustStyle(n){return xp(n)}bypassSecurityTrustScript(n){return Np(n)}bypassSecurityTrustUrl(n){return Ap(n)}bypassSecurityTrustResourceUrl(n){return Op(n)}static \u0275fac=function(r){return new(r||e)(y(le))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function OM(e){e||(Zn(OM),e=g(ot));let t=new R(n=>e.onDestroy(n.next.bind(n)));return n=>n.pipe(Nr(t))}function Sl(e,t){let n=!t?.manualCleanup;n&&!t?.injector&&Zn(Sl);let r=n?t?.injector?.get(ot)??g(ot):null,o=kM(t?.equal),i;t?.requireSync?i=is({kind:0},{equal:o}):i=is({kind:1,value:t?.initialValue},{equal:o});let s=e.subscribe({next:a=>i.set({kind:1,value:a}),error:a=>{if(t?.rejectErrors)throw a;i.set({kind:2,error:a})}});if(t?.requireSync&&i().kind===0)throw new E(601,!1);return r?.onDestroy(s.unsubscribe.bind(s)),ro(()=>{let a=i();switch(a.kind){case 1:return a.value;case 2:throw a.error;case 0:throw new E(601,!1)}},{equal:t?.equal})}function kM(e=Object.is){return(t,n)=>t.kind===1&&n.kind===1&&e(t.value,n.value)}var Rl={};function ty(e,t){if(Rl[e]=(Rl[e]||0)+1,typeof t=="function")return Ml(e,(...r)=>j(D({},t(...r)),{type:e}));switch(t?t._as:"empty"){case"empty":return Ml(e,()=>({type:e}));case"props":return Ml(e,r=>j(D({},r),{type:e}));default:throw new Error("Unexpected config.")}}function cj(){return{_as:"props",_p:void 0}}function Ml(e,t){return Object.defineProperty(t,"type",{value:e,writable:!1})}function FM(e,t){if(e==null)throw new Error(`${t} must be defined.`)}var ny="@ngrx/store/init",ur=(()=>{class e extends K{constructor(){super({type:ny})}next(n){if(typeof n=="function")throw new TypeError(`
        Dispatch expected an object, instead it received a function.
        If you're using the createAction function, make sure to invoke the function
        before dispatching the action. For example, someAction should be someAction().`);if(typeof n>"u")throw new TypeError("Actions must be objects");if(typeof n.type>"u")throw new TypeError("Actions must have a type property");super.next(n)}complete(){}ngOnDestroy(){super.complete()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),PM=[ur],ry=new m("@ngrx/store Internal Root Guard"),Um=new m("@ngrx/store Internal Initial State"),Ol=new m("@ngrx/store Initial State"),oy=new m("@ngrx/store Reducer Factory"),Bm=new m("@ngrx/store Internal Reducer Factory Provider"),iy=new m("@ngrx/store Initial Reducers"),Tl=new m("@ngrx/store Internal Initial Reducers"),$m=new m("@ngrx/store Store Features"),Hm=new m("@ngrx/store Internal Store Reducers"),_l=new m("@ngrx/store Internal Feature Reducers"),zm=new m("@ngrx/store Internal Feature Configs"),sy=new m("@ngrx/store Internal Store Features"),qm=new m("@ngrx/store Internal Feature Reducers Token"),ay=new m("@ngrx/store Feature Reducers"),Gm=new m("@ngrx/store User Provided Meta Reducers"),Ls=new m("@ngrx/store Meta Reducers"),Wm=new m("@ngrx/store Internal Resolved Meta Reducers"),Zm=new m("@ngrx/store User Runtime Checks Config"),Ym=new m("@ngrx/store Internal User Runtime Checks Config"),lo=new m("@ngrx/store Internal Runtime Checks"),kl=new m("@ngrx/store Check if Action types are unique"),LM=new m("@ngrx/store Root Store Provider"),jM=new m("@ngrx/store Feature State Provider");function Fl(e,t={}){let n=Object.keys(e),r={};for(let i=0;i<n.length;i++){let s=n[i];typeof e[s]=="function"&&(r[s]=e[s])}let o=Object.keys(r);return function(s,a){s=s===void 0?t:s;let c=!1,u={};for(let l=0;l<o.length;l++){let d=o[l],h=r[d],f=s[d],p=h(f,a);u[d]=p,c=c||p!==f}return c?u:s}}function VM(e,t){return Object.keys(e).filter(n=>n!==t).reduce((n,r)=>Object.assign(n,{[r]:e[r]}),{})}function cy(...e){return function(t){if(e.length===0)return t;let n=e[e.length-1];return e.slice(0,-1).reduceRight((o,i)=>i(o),n(t))}}function uy(e,t){return Array.isArray(t)&&t.length>0&&(e=cy.apply(null,[...t,e])),(n,r)=>{let o=e(n);return(i,s)=>(i=i===void 0?r:i,o(i,s))}}function UM(e){let t=Array.isArray(e)&&e.length>0?cy(...e):n=>n;return(n,r)=>(n=t(n),(o,i)=>(o=o===void 0?r:o,n(o,i)))}var fo=class extends R{},js=class extends ur{},BM="@ngrx/store/update-reducers",Vs=(()=>{class e extends K{get currentReducers(){return this.reducers}constructor(n,r,o,i){super(i(o,r)),this.dispatcher=n,this.initialState=r,this.reducers=o,this.reducerFactory=i}addFeature(n){this.addFeatures([n])}addFeatures(n){let r=n.reduce((o,{reducers:i,reducerFactory:s,metaReducers:a,initialState:c,key:u})=>{let l=typeof i=="function"?UM(a)(i,c):uy(s,a)(i,c);return o[u]=l,o},{});this.addReducers(r)}removeFeature(n){this.removeFeatures([n])}removeFeatures(n){this.removeReducers(n.map(r=>r.key))}addReducer(n,r){this.addReducers({[n]:r})}addReducers(n){this.reducers=D(D({},this.reducers),n),this.updateReducers(Object.keys(n))}removeReducer(n){this.removeReducers([n])}removeReducers(n){n.forEach(r=>{this.reducers=VM(this.reducers,r)}),this.updateReducers(n)}updateReducers(n){this.next(this.reducerFactory(this.reducers,this.initialState)),this.dispatcher.next({type:BM,features:n})}ngOnDestroy(){this.complete()}static{this.\u0275fac=function(r){return new(r||e)(y(js),y(Ol),y(iy),y(oy))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),$M=[Vs,{provide:fo,useExisting:Vs},{provide:js,useExisting:ur}],ho=(()=>{class e extends U{ngOnDestroy(){this.complete()}static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=Kr(e)))(o||e)}})()}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),HM=[ho],Us=class extends R{},Qm=(()=>{class e extends K{static{this.INIT=ny}constructor(n,r,o,i){super(i);let a=n.pipe(Qt(Sa)).pipe(Va(r)),c={state:i},u=a.pipe(xr(zM,c));this.stateSubscription=u.subscribe(({state:l,action:d})=>{this.next(l),o.next(d)}),this.state=Sl(this,{manualCleanup:!0,requireSync:!0})}ngOnDestroy(){this.stateSubscription.unsubscribe(),this.complete()}static{this.\u0275fac=function(r){return new(r||e)(y(ur),y(fo),y(ho),y(Ol))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function zM(e={state:void 0},[t,n]){let{state:r}=e;return{state:n(r,t),action:t}}var qM=[Qm,{provide:Us,useExisting:Qm}],yn=(()=>{class e extends R{constructor(n,r,o,i){super(),this.actionsObserver=r,this.reducerManager=o,this.injector=i,this.source=n,this.state=n.state}select(n,...r){return WM.call(null,n,...r)(this)}selectSignal(n,r){return ro(()=>n(this.state()),r)}lift(n){let r=new e(this,this.actionsObserver,this.reducerManager);return r.operator=n,r}dispatch(n,r){if(typeof n=="function")return this.processDispatchFn(n,r);this.actionsObserver.next(n)}next(n){this.actionsObserver.next(n)}error(n){this.actionsObserver.error(n)}complete(){this.actionsObserver.complete()}addReducer(n,r){this.reducerManager.addReducer(n,r)}removeReducer(n){this.reducerManager.removeReducer(n)}processDispatchFn(n,r){FM(this.injector,"Store Injector");let o=r?.injector??ZM()??this.injector;return ms(()=>{let i=n();rr(()=>this.dispatch(i))},{injector:o})}static{this.\u0275fac=function(r){return new(r||e)(y(Us),y(ur),y(Vs),y(oe))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),GM=[yn];function WM(e,t,...n){return function(o){let i;if(typeof e=="string"){let s=[t,...n].filter(Boolean);i=o.pipe(Fa(e,...s))}else if(typeof e=="function")i=o.pipe(T(s=>e(s,t)));else throw new TypeError(`Unexpected type '${typeof e}' in select operator, expected 'string' or 'function'`);return i.pipe(Aa())}}function ZM(){try{return g(oe)}catch{return}}var Pl="https://ngrx.io/guide/store/configuration/runtime-checks";function Km(e){return e===void 0}function Jm(e){return e===null}function ly(e){return Array.isArray(e)}function YM(e){return typeof e=="string"}function QM(e){return typeof e=="boolean"}function KM(e){return typeof e=="number"}function dy(e){return typeof e=="object"&&e!==null}function JM(e){return dy(e)&&!ly(e)}function XM(e){if(!JM(e))return!1;let t=Object.getPrototypeOf(e);return t===Object.prototype||t===null}function xl(e){return typeof e=="function"}function eT(e){return xl(e)&&e.hasOwnProperty("\u0275cmp")}function tT(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var nT=!1;function rT(){return nT}function Xm(e,t){return e===t}function oT(e,t,n){for(let r=0;r<e.length;r++)if(!n(e[r],t[r]))return!0;return!1}function fy(e,t=Xm,n=Xm){let r=null,o=null,i;function s(){r=null,o=null}function a(l=void 0){i={result:l}}function c(){i=void 0}function u(){if(i!==void 0)return i.result;if(!r)return o=e.apply(null,arguments),r=arguments,o;if(!oT(arguments,r,t))return o;let l=e.apply(null,arguments);return r=arguments,n(o,l)?o:(o=l,l)}return{memoized:u,reset:s,setResult:a,clearResult:c}}function iT(...e){return aT(fy)(...e)}function sT(e,t,n,r){if(n===void 0){let i=t.map(s=>s(e));return r.memoized.apply(null,i)}let o=t.map(i=>i(e,n));return r.memoized.apply(null,[...o,n])}function aT(e,t={stateFn:sT}){return function(...n){let r=n;if(Array.isArray(r[0])){let[l,...d]=r;r=[...l,...d]}else r.length===1&&cT(r[0])&&(r=uT(r[0]));let o=r.slice(0,r.length-1),i=r[r.length-1],s=o.filter(l=>l.release&&typeof l.release=="function"),a=e(function(...l){return i.apply(null,l)}),c=fy(function(l,d){return t.stateFn.apply(null,[l,o,d,a])});function u(){c.reset(),a.reset(),s.forEach(l=>l.release())}return Object.assign(c.memoized,{release:u,projector:a.memoized,setResult:c.setResult,clearResult:c.clearResult})}}function uj(e){return iT(t=>{let n=t[e];return!rT()&&Xu()&&!(e in t)&&console.warn(`@ngrx/store: The feature name "${e}" does not exist in the state, therefore createFeatureSelector cannot access it.  Be sure it is imported in a loaded module using StoreModule.forRoot('${e}', ...) or StoreModule.forFeature('${e}', ...).  If the default state is intended to be undefined, as is the case with router state, this development-only warning message can be ignored.`),n},t=>t)}function cT(e){return!!e&&typeof e=="object"&&Object.values(e).every(t=>typeof t=="function")}function uT(e){let t=Object.values(e),n=Object.keys(e),r=(...o)=>n.reduce((i,s,a)=>j(D({},i),{[s]:o[a]}),{});return[...t,r]}function lT(e){return e instanceof m?g(e):e}function dT(e,t){return t.map((n,r)=>{if(e[r]instanceof m){let o=g(e[r]);return{key:n.key,reducerFactory:o.reducerFactory?o.reducerFactory:Fl,metaReducers:o.metaReducers?o.metaReducers:[],initialState:o.initialState}}return n})}function fT(e){return e.map(t=>t instanceof m?g(t):t)}function hy(e){return typeof e=="function"?e():e}function hT(e,t){return e.concat(t)}function pT(){if(g(yn,{optional:!0,skipSelf:!0}))throw new TypeError("The root Store has been provided more than once. Feature modules should provide feature states instead.");return"guarded"}function gT(e,t){return function(n,r){let o=t.action(r)?Nl(r):r,i=e(n,o);return t.state()?Nl(i):i}}function Nl(e){Object.freeze(e);let t=xl(e);return Object.getOwnPropertyNames(e).forEach(n=>{if(!n.startsWith("\u0275")&&tT(e,n)&&(!t||n!=="caller"&&n!=="callee"&&n!=="arguments")){let r=e[n];(dy(r)||xl(r))&&!Object.isFrozen(r)&&Nl(r)}}),e}function mT(e,t){return function(n,r){if(t.action(r)){let i=Al(r);ey(i,"action")}let o=e(n,r);if(t.state()){let i=Al(o);ey(i,"state")}return o}}function Al(e,t=[]){return(Km(e)||Jm(e))&&t.length===0?{path:["root"],value:e}:Object.keys(e).reduce((r,o)=>{if(r)return r;let i=e[o];return eT(i)?r:Km(i)||Jm(i)||KM(i)||QM(i)||YM(i)||ly(i)?!1:XM(i)?Al(i,[...t,o]):{path:[...t,o],value:i}},!1)}function ey(e,t){if(e===!1)return;let n=e.path.join("."),r=new Error(`Detected unserializable ${t} at "${n}". ${Pl}#strict${t}serializability`);throw r.value=e.value,r.unserializablePath=n,r}function yT(e,t){return function(n,r){if(t.action(r)&&!Q.isInAngularZone())throw new Error(`Action '${r.type}' running outside NgZone. ${Pl}#strictactionwithinngzone`);return e(n,r)}}function vT(e){return Xu()?D({strictStateSerializability:!1,strictActionSerializability:!1,strictStateImmutability:!0,strictActionImmutability:!0,strictActionWithinNgZone:!1,strictActionTypeUniqueness:!1},e):{strictStateSerializability:!1,strictActionSerializability:!1,strictStateImmutability:!1,strictActionImmutability:!1,strictActionWithinNgZone:!1,strictActionTypeUniqueness:!1}}function DT({strictActionSerializability:e,strictStateSerializability:t}){return n=>e||t?mT(n,{action:r=>e&&!Ll(r),state:()=>t}):n}function ET({strictActionImmutability:e,strictStateImmutability:t}){return n=>e||t?gT(n,{action:r=>e&&!Ll(r),state:()=>t}):n}function Ll(e){return e.type.startsWith("@ngrx")}function wT({strictActionWithinNgZone:e}){return t=>e?yT(t,{action:n=>e&&!Ll(n)}):t}function IT(e){return[{provide:Ym,useValue:e},{provide:Zm,useFactory:bT,deps:[Ym]},{provide:lo,deps:[Zm],useFactory:vT},{provide:Ls,multi:!0,deps:[lo],useFactory:ET},{provide:Ls,multi:!0,deps:[lo],useFactory:DT},{provide:Ls,multi:!0,deps:[lo],useFactory:wT}]}function py(){return[{provide:kl,multi:!0,deps:[lo],useFactory:CT}]}function bT(e){return e}function CT(e){if(!e.strictActionTypeUniqueness)return;let t=Object.entries(Rl).filter(([,n])=>n>1).map(([n])=>n);if(t.length)throw new Error(`Action types are registered more than once, ${t.map(n=>`"${n}"`).join(", ")}. ${Pl}#strictactiontypeuniqueness`)}function ST(e={},t={}){return[{provide:ry,useFactory:pT},{provide:Um,useValue:t.initialState},{provide:Ol,useFactory:hy,deps:[Um]},{provide:Tl,useValue:e},{provide:Hm,useExisting:e instanceof m?e:Tl},{provide:iy,deps:[Tl,[new ru(Hm)]],useFactory:lT},{provide:Gm,useValue:t.metaReducers?t.metaReducers:[]},{provide:Wm,deps:[Ls,Gm],useFactory:hT},{provide:Bm,useValue:t.reducerFactory?t.reducerFactory:Fl},{provide:oy,deps:[Bm,Wm],useFactory:uy},PM,$M,HM,qM,GM,IT(t.runtimeChecks),py()]}function MT(e,t,n={}){return[{provide:zm,multi:!0,useValue:e instanceof Object?{}:n},{provide:$m,multi:!0,useValue:{key:e instanceof Object?e.name:e,reducerFactory:!(n instanceof m)&&n.reducerFactory?n.reducerFactory:Fl,metaReducers:!(n instanceof m)&&n.metaReducers?n.metaReducers:[],initialState:!(n instanceof m)&&n.initialState?n.initialState:void 0}},{provide:sy,deps:[zm,$m],useFactory:dT},{provide:_l,multi:!0,useValue:e instanceof Object?e.reducer:t},{provide:qm,multi:!0,useExisting:t instanceof m?t:_l},{provide:ay,multi:!0,deps:[_l,[new ru(qm)]],useFactory:fT},py()]}var po=(()=>{class e{constructor(n,r,o,i,s,a){}static{this.\u0275fac=function(r){return new(r||e)(y(ur),y(fo),y(ho),y(yn),y(ry,8),y(kl,8))}}static{this.\u0275mod=Te({type:e})}static{this.\u0275inj=Ce({})}}return e})(),Bs=(()=>{class e{constructor(n,r,o,i,s){this.features=n,this.featureReducers=r,this.reducerManager=o;let a=n.map((c,u)=>{let d=r.shift()[u];return j(D({},c),{reducers:d,initialState:hy(c.initialState)})});o.addFeatures(a)}ngOnDestroy(){this.reducerManager.removeFeatures(this.features)}static{this.\u0275fac=function(r){return new(r||e)(y(sy),y(ay),y(Vs),y(po),y(kl,8))}}static{this.\u0275mod=Te({type:e})}static{this.\u0275inj=Ce({})}}return e})(),lj=(()=>{class e{static forRoot(n,r){return{ngModule:po,providers:[...ST(n,r)]}}static forFeature(n,r,o={}){return{ngModule:Bs,providers:[...MT(n,r,o)]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Te({type:e})}static{this.\u0275inj=Ce({})}}return e})();function dj(...e){let t=e.pop(),n=e.map(r=>r.type);return{reducer:t,types:n}}function fj(e,...t){let n=new Map;for(let r of t)for(let o of r.types){let i=n.get(o);if(i){let s=(a,c)=>r.reducer(i(a,c),c);n.set(o,s)}else n.set(o,r.reducer)}return function(r=e,o){let i=n.get(o.type);return i?i(r,o):r}}var _T={dispatch:!0,functional:!1,useEffectsErrorHandler:!0},Hs="__@ngrx/effects_create__";function Ij(e,t={}){let n=t.functional?e:e(),r=D(D({},_T),t);return Object.defineProperty(n,Hs,{value:r}),n}function RT(e){return Object.getOwnPropertyNames(e).filter(r=>e[r]&&e[r].hasOwnProperty(Hs)?e[r][Hs].hasOwnProperty("dispatch"):!1).map(r=>{let o=e[r][Hs];return D({propertyName:r},o)})}function xT(e){return RT(e)}function vy(e){return Object.getPrototypeOf(e)}function NT(e){return!!e.constructor&&e.constructor.name!=="Object"&&e.constructor.name!=="Function"}function Dy(e){return typeof e=="function"}function gy(e){return e.filter(Dy)}function AT(e){return e instanceof m||Dy(e)}function OT(e,t,n){let r=vy(e),i=!!r&&r.constructor.name!=="Object"?r.constructor.name:null,s=xT(e).map(({propertyName:a,dispatch:c,useEffectsErrorHandler:u})=>{let l=typeof e[a]=="function"?e[a]():e[a],d=u?n(l,t):l;return c===!1?d.pipe(xa()):d.pipe(ka()).pipe(T(f=>({effect:e[a],notification:f,propertyName:a,sourceName:i,sourceInstance:e})))});return ui(...s)}var kT=10;function Ey(e,t,n=kT){return e.pipe(Be(r=>(t&&t.handleError(r),n<=1?e:Ey(e,t,n-1))))}var bj=(()=>{class e extends R{constructor(n){super(),n&&(this.source=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}static{this.\u0275fac=function(r){return new(r||e)(y(ho))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Cj(...e){return X(t=>e.some(n=>typeof n=="string"?n===t.type:n.type===t.type))}var wy=new m("@ngrx/effects Internal Root Guard"),$s=new m("@ngrx/effects User Provided Effects"),jl=new m("@ngrx/effects Internal Root Effects"),Iy=new m("@ngrx/effects Internal Root Effects Instances"),my=new m("@ngrx/effects Internal Feature Effects"),by=new m("@ngrx/effects Internal Feature Effects Instance Groups"),FT=new m("@ngrx/effects Effects Error Handler",{providedIn:"root",factory:()=>Ey}),Cy="@ngrx/effects/init",Sj=ty(Cy);function PT(e,t){if(e.notification.kind==="N"){let n=e.notification.value;!LT(n)&&t.handleError(new Error(`Effect ${jT(e)} dispatched an invalid action: ${VT(n)}`))}}function LT(e){return typeof e!="function"&&e&&e.type&&typeof e.type=="string"}function jT({propertyName:e,sourceInstance:t,sourceName:n}){let r=typeof t[e]=="function";return!!n?`"${n}.${String(e)}${r?"()":""}"`:`"${String(e)}()"`}function VT(e){try{return JSON.stringify(e)}catch{return e}}var UT="ngrxOnIdentifyEffects";function BT(e){return Vl(e,UT)}var $T="ngrxOnRunEffects";function HT(e){return Vl(e,$T)}var zT="ngrxOnInitEffects";function qT(e){return Vl(e,zT)}function Vl(e,t){return e&&t in e&&typeof e[t]=="function"}var Sy=(()=>{class e extends U{constructor(n,r){super(),this.errorHandler=n,this.effectsErrorHandler=r}addEffects(n){this.next(n)}toActions(){return this.pipe(fi(n=>NT(n)?vy(n):n),G(n=>n.pipe(fi(GT))),G(n=>{let r=n.pipe(di(i=>WT(this.errorHandler,this.effectsErrorHandler)(i)),T(i=>(PT(i,this.errorHandler),i.notification)),X(i=>i.kind==="N"&&i.value!=null),Na()),o=n.pipe($e(1),X(qT),T(i=>i.ngrxOnInitEffects()));return ui(r,o)}))}static{this.\u0275fac=function(r){return new(r||e)(y(qe),y(FT))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function GT(e){return BT(e)?e.ngrxOnIdentifyEffects():""}function WT(e,t){return n=>{let r=OT(n,e,t);return HT(n)?n.ngrxOnRunEffects(r):r}}var My=(()=>{class e{get isStarted(){return!!this.effectsSubscription}constructor(n,r){this.effectSources=n,this.store=r,this.effectsSubscription=null}start(){this.effectsSubscription||(this.effectsSubscription=this.effectSources.toActions().subscribe(this.store))}ngOnDestroy(){this.effectsSubscription&&(this.effectsSubscription.unsubscribe(),this.effectsSubscription=null)}static{this.\u0275fac=function(r){return new(r||e)(y(Sy),y(yn))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Ty=(()=>{class e{constructor(n,r,o,i,s,a,c){this.sources=n,r.start();for(let u of i)n.addEffects(u);o.dispatch({type:Cy})}addEffects(n){this.sources.addEffects(n)}static{this.\u0275fac=function(r){return new(r||e)(y(Sy),y(My),y(yn),y(Iy),y(po,8),y(Bs,8),y(wy,8))}}static{this.\u0275mod=Te({type:e})}static{this.\u0275inj=Ce({})}}return e})(),ZT=(()=>{class e{constructor(n,r,o,i){let s=r.flat();for(let a of s)n.addEffects(a)}static{this.\u0275fac=function(r){return new(r||e)(y(Ty),y(by),y(po,8),y(Bs,8))}}static{this.\u0275mod=Te({type:e})}static{this.\u0275inj=Ce({})}}return e})(),Mj=(()=>{class e{static forFeature(...n){let r=n.flat(),o=gy(r);return{ngModule:ZT,providers:[o,{provide:my,multi:!0,useValue:r},{provide:$s,multi:!0,useValue:[]},{provide:by,multi:!0,useFactory:yy,deps:[my,$s]}]}}static forRoot(...n){let r=n.flat(),o=gy(r);return{ngModule:Ty,providers:[o,{provide:jl,useValue:[r]},{provide:wy,useFactory:YT},{provide:$s,multi:!0,useValue:[]},{provide:Iy,useFactory:yy,deps:[jl,$s]}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Te({type:e})}static{this.\u0275inj=Ce({})}}return e})();function yy(e,t){let n=[];for(let r of e)n.push(...r);for(let r of t)n.push(...r);return n.map(r=>AT(r)?g(r):r)}function YT(){let e=g(My,{optional:!0,skipSelf:!0}),t=g(jl,{self:!0});if(!(t.length===1&&t[0].length===0)&&e)throw new TypeError("EffectsModule.forRoot() called twice. Feature modules should use EffectsModule.forFeature() instead.");return"guarded"}var N="primary",To=Symbol("RouteTitle"),zl=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function gr(e){return new zl(e)}function QT(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function KT(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!ct(e[n],t[n]))return!1;return!0}function ct(e,t){let n=e?ql(e):void 0,r=t?ql(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!jy(e[o],t[o]))return!1;return!0}function ql(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function jy(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function Vy(e){return e.length>0?e[e.length-1]:null}function qt(e){return _a(e)?e:nr(e)?q(Promise.resolve(e)):M(e)}var JT={exact:By,subset:$y},Uy={exact:XT,subset:e_,ignored:()=>!0};function _y(e,t,n){return JT[n.paths](e.root,t.root,n.matrixParams)&&Uy[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function XT(e,t){return ct(e,t)}function By(e,t,n){if(!Dn(e.segments,t.segments)||!Gs(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!By(e.children[r],t.children[r],n))return!1;return!0}function e_(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>jy(e[n],t[n]))}function $y(e,t,n){return Hy(e,t,t.segments,n)}function Hy(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Dn(o,n)||t.hasChildren()||!Gs(o,n,r))}else if(e.segments.length===n.length){if(!Dn(e.segments,n)||!Gs(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!$y(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Dn(e.segments,o)||!Gs(e.segments,o,r)||!e.children[N]?!1:Hy(e.children[N],t,i,r)}}function Gs(e,t,n){return t.every((r,o)=>Uy[n](e[o].parameters,r.parameters))}var Tt=class{root;queryParams;fragment;_queryParamMap;constructor(t=new V([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=gr(this.queryParams),this._queryParamMap}toString(){return r_.serialize(this)}},V=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ws(this)}},vn=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=gr(this.parameters),this._parameterMap}toString(){return qy(this)}};function t_(e,t){return Dn(e,t)&&e.every((n,r)=>ct(n.parameters,t[r].parameters))}function Dn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function n_(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===N&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==N&&(n=n.concat(t(o,r)))}),n}var _o=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new mr,providedIn:"root"})}return e})(),mr=class{parse(t){let n=new Wl(t);return new Tt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${go(t.root,!0)}`,r=s_(t.queryParams),o=typeof t.fragment=="string"?`#${o_(t.fragment)}`:"";return`${n}${r}${o}`}},r_=new mr;function Ws(e){return e.segments.map(t=>qy(t)).join("/")}function go(e,t){if(!e.hasChildren())return Ws(e);if(t){let n=e.children[N]?go(e.children[N],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==N&&r.push(`${o}:${go(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=n_(e,(r,o)=>o===N?[go(e.children[N],!1)]:[`${o}:${go(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[N]!=null?`${Ws(e)}/${n[0]}`:`${Ws(e)}/(${n.join("//")})`}}function zy(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function zs(e){return zy(e).replace(/%3B/gi,";")}function o_(e){return encodeURI(e)}function Gl(e){return zy(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Zs(e){return decodeURIComponent(e)}function Ry(e){return Zs(e.replace(/\+/g,"%20"))}function qy(e){return`${Gl(e.path)}${i_(e.parameters)}`}function i_(e){return Object.entries(e).map(([t,n])=>`;${Gl(t)}=${Gl(n)}`).join("")}function s_(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${zs(n)}=${zs(o)}`).join("&"):`${zs(n)}=${zs(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var a_=/^[^\/()?;#]+/;function Ul(e){let t=e.match(a_);return t?t[0]:""}var c_=/^[^\/()?;=#]+/;function u_(e){let t=e.match(c_);return t?t[0]:""}var l_=/^[^=?&#]+/;function d_(e){let t=e.match(l_);return t?t[0]:""}var f_=/^[^&#]+/;function h_(e){let t=e.match(f_);return t?t[0]:""}var Wl=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new V([],{}):new V([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[N]=new V(t,n)),r}parseSegment(){let t=Ul(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new E(4009,!1);return this.capture(t),new vn(Zs(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=u_(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Ul(this.remaining);o&&(r=o,this.capture(r))}t[Zs(n)]=Zs(r)}parseQueryParam(t){let n=d_(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=h_(this.remaining);s&&(r=s,this.capture(r))}let o=Ry(n),i=Ry(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Ul(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new E(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=N);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[N]:new V([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new E(4011,!1)}};function Gy(e){return e.segments.length>0?new V([],{[N]:e}):e}function Wy(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Wy(o);if(r===N&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new V(e.segments,t);return p_(n)}function p_(e){if(e.numberOfChildren===1&&e.children[N]){let t=e.children[N];return new V(e.segments.concat(t.segments),t.children)}return e}function En(e){return e instanceof Tt}function g_(e,t,n=null,r=null){let o=Zy(e);return Yy(o,t,n,r)}function Zy(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new V(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Gy(r);return t??o}function Yy(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Bl(o,o,o,n,r);let i=m_(t);if(i.toRoot())return Bl(o,o,new V([],{}),n,r);let s=y_(i,o,e),a=s.processChildren?yo(s.segmentGroup,s.index,i.commands):Ky(s.segmentGroup,s.index,i.commands);return Bl(o,s.segmentGroup,a,n,r)}function Qs(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Do(e){return typeof e=="object"&&e!=null&&e.outlets}function Bl(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=Qy(e,t,n);let a=Gy(Wy(s));return new Tt(a,i,o)}function Qy(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Qy(i,t,n)}),new V(e.segments,r)}var Ks=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Qs(r[0]))throw new E(4003,!1);let o=r.find(Do);if(o&&o!==Vy(r))throw new E(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function m_(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Ks(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Ks(n,t,r)}var fr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function y_(e,t,n){if(e.isAbsolute)return new fr(t,!0,0);if(!n)return new fr(t,!1,NaN);if(n.parent===null)return new fr(n,!0,0);let r=Qs(e.commands[0])?0:1,o=n.segments.length-1+r;return v_(n,o,e.numberOfDoubleDots)}function v_(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new E(4005,!1);o=r.segments.length}return new fr(r,!1,o-i)}function D_(e){return Do(e[0])?e[0].outlets:{[N]:e}}function Ky(e,t,n){if(e??=new V([],{}),e.segments.length===0&&e.hasChildren())return yo(e,t,n);let r=E_(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new V(e.segments.slice(0,r.pathIndex),{});return i.children[N]=new V(e.segments.slice(r.pathIndex),e.children),yo(i,0,o)}else return r.match&&o.length===0?new V(e.segments,{}):r.match&&!e.hasChildren()?Zl(e,t,n):r.match?yo(e,0,o):Zl(e,t,n)}function yo(e,t,n){if(n.length===0)return new V(e.segments,{});{let r=D_(n),o={};if(Object.keys(r).some(i=>i!==N)&&e.children[N]&&e.numberOfChildren===1&&e.children[N].segments.length===0){let i=yo(e.children[N],t,n);return new V(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Ky(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new V(e.segments,o)}}function E_(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(Do(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!Ny(c,u,s))return i;r+=2}else{if(!Ny(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Zl(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(Do(i)){let c=w_(i.outlets);return new V(r,c)}if(o===0&&Qs(n[0])){let c=e.segments[t];r.push(new vn(c.path,xy(n[0]))),o++;continue}let s=Do(i)?i.outlets[N]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Qs(a)?(r.push(new vn(s,xy(a))),o+=2):(r.push(new vn(s,{})),o++)}return new V(r,{})}function w_(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Zl(new V([],{}),0,r))}),t}function xy(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function Ny(e,t,n){return e==n.path&&ct(t,n.parameters)}var Ys="imperative",ce=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(ce||{}),Le=class{id;url;constructor(t,n){this.id=t,this.url=n}},yr=class extends Le{type=ce.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ze=class extends Le{urlAfterRedirects;type=ce.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},xe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(xe||{}),Js=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Js||{}),Mt=class extends Le{reason;code;type=ce.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Ht=class extends Le{reason;code;type=ce.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Eo=class extends Le{error;target;type=ce.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Xs=class extends Le{urlAfterRedirects;state;type=ce.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Yl=class extends Le{urlAfterRedirects;state;type=ce.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ql=class extends Le{urlAfterRedirects;state;shouldActivate;type=ce.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Kl=class extends Le{urlAfterRedirects;state;type=ce.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Jl=class extends Le{urlAfterRedirects;state;type=ce.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xl=class{route;type=ce.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ed=class{route;type=ce.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},td=class{snapshot;type=ce.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},nd=class{snapshot;type=ce.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},rd=class{snapshot;type=ce.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},od=class{snapshot;type=ce.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ea=class{routerEvent;position;anchor;type=ce.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},wo=class{},vr=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function I_(e,t){return e.providers&&!e._injector&&(e._injector=fs(e.providers,t,`Route: ${e.path}`)),e._injector??t}function We(e){return e.outlet||N}function b_(e,t){let n=e.filter(r=>We(r)===t);return n.push(...e.filter(r=>We(r)!==t)),n}function Ro(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var id=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Ro(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new xo(this.rootInjector)}},xo=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new id(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(y(me))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ta=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=sd(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=sd(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=ad(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return ad(t,this._root).map(n=>n.value)}};function sd(e,t){if(e===t.value)return t;for(let n of t.children){let r=sd(e,n);if(r)return r}return null}function ad(e,t){if(e===t.value)return[t];for(let n of t.children){let r=ad(e,n);if(r.length)return r.unshift(t),r}return[]}var Re=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function dr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var na=class extends ta{snapshot;constructor(t,n){super(t),this.snapshot=n,md(this,t)}toString(){return this.snapshot.toString()}};function Jy(e){let t=C_(e),n=new K([new vn("",{})]),r=new K({}),o=new K({}),i=new K({}),s=new K(""),a=new zt(n,r,i,s,o,N,e,t.root);return a.snapshot=t.root,new na(new Re(a,[]),t)}function C_(e){let t={},n={},r={},o="",i=new hr([],t,r,o,n,N,e,null,{});return new oa("",new Re(i,[]))}var zt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(T(u=>u[To]))??M(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(T(t=>gr(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(T(t=>gr(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ra(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:D(D({},t.params),e.params),data:D(D({},t.data),e.data),resolve:D(D(D(D({},e.data),t.data),o?.data),e._resolvedData)}:r={params:D({},e.params),data:D({},e.data),resolve:D(D({},e.data),e._resolvedData??{})},o&&ev(o)&&(r.resolve[To]=o.title),r}var hr=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[To]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=gr(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=gr(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},oa=class extends ta{url;constructor(t,n){super(n),this.url=t,md(this,n)}toString(){return Xy(this._root)}};function md(e,t){t.value._routerState=e,t.children.forEach(n=>md(e,n))}function Xy(e){let t=e.children.length>0?` { ${e.children.map(Xy).join(", ")} } `:"";return`${e.value}${t}`}function $l(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,ct(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),ct(t.params,n.params)||e.paramsSubject.next(n.params),KT(t.url,n.url)||e.urlSubject.next(n.url),ct(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function cd(e,t){let n=ct(e.params,t.params)&&t_(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||cd(e.parent,t.parent))}function ev(e){return typeof e.title=="string"||e.title===null}var S_=new m(""),M_=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=N;activateEvents=new we;deactivateEvents=new we;attachEvents=new we;detachEvents=new we;routerOutletData=gp(void 0);parentContexts=g(xo);location=g(Vt);changeDetector=g(gn);inputBinder=g(ca,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new E(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new E(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new E(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new E(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new ud(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=Ut({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Zr]})}return e})(),ud=class e{route;childContexts;parent;outletData;__ngOutletInjector(t){return new e(this.route,this.childContexts,t,this.outletData)}constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===zt?this.route:t===xo?this.childContexts:t===S_?this.outletData:this.parent.get(t,n)}},ca=new m(""),Ay=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=_r([r.queryParams,r.params,r.data]).pipe(pe(([i,s,a],c)=>(a=D(D(D({},i),s),a),c===0?M(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Kg(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function T_(e,t,n){let r=Io(e,t._root,n?n._root:void 0);return new na(r,t)}function Io(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=__(e,t,n);return new Re(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Io(e,a)),s}}let r=R_(t.value),o=t.children.map(i=>Io(e,i));return new Re(r,o)}}function __(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Io(e,r,o);return Io(e,r)})}function R_(e){return new zt(new K(e.url),new K(e.params),new K(e.queryParams),new K(e.fragment),new K(e.data),e.outlet,e.component,e)}var bo=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},tv="ngNavigationCancelingError";function ia(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=En(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=nv(!1,xe.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function nv(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[tv]=!0,n.cancellationCode=t,n}function x_(e){return rv(e)&&En(e.url)}function rv(e){return!!e&&e[tv]}var N_=(e,t,n,r)=>T(o=>(new ld(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),ld=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),$l(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=dr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=dr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=dr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=dr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new od(i.value.snapshot))}),t.children.length&&this.forwardEvent(new nd(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if($l(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),$l(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},sa=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},pr=class{component;route;constructor(t,n){this.component=t,this.route=n}};function A_(e,t,n){let r=e._root,o=t?t._root:null;return mo(r,o,n,[r.value])}function O_(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Er(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!ah(e)?e:t.get(e):r}function mo(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=dr(t);return e.children.forEach(s=>{k_(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>vo(a,n.getContext(s),o)),o}function k_(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=F_(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new sa(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?mo(e,t,a?a.children:null,r,o):mo(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new pr(a.outlet.component,s))}else s&&vo(t,a,o),o.canActivateChecks.push(new sa(r)),i.component?mo(e,null,a?a.children:null,r,o):mo(e,null,n,r,o);return o}function F_(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Dn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Dn(e.url,t.url)||!ct(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!cd(e,t)||!ct(e.queryParams,t.queryParams);case"paramsChange":default:return!cd(e,t)}}function vo(e,t,n){let r=dr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?vo(s,t.children.getContext(i),n):vo(s,null,n):vo(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new pr(t.outlet.component,o)):n.canDeactivateChecks.push(new pr(null,o)):n.canDeactivateChecks.push(new pr(null,o))}function No(e){return typeof e=="function"}function P_(e){return typeof e=="boolean"}function L_(e){return e&&No(e.canLoad)}function j_(e){return e&&No(e.canActivate)}function V_(e){return e&&No(e.canActivateChild)}function U_(e){return e&&No(e.canDeactivate)}function B_(e){return e&&No(e.canMatch)}function ov(e){return e instanceof Ue||e?.name==="EmptyError"}var qs=Symbol("INITIAL_VALUE");function Dr(){return pe(e=>_r(e.map(t=>t.pipe($e(1),ja(qs)))).pipe(T(t=>{for(let n of t)if(n!==!0){if(n===qs)return qs;if(n===!1||$_(n))return n}return!0}),X(t=>t!==qs),$e(1)))}function $_(e){return En(e)||e instanceof bo}function H_(e,t){return G(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?M(j(D({},n),{guardsResult:!0})):z_(s,r,o,e).pipe(G(a=>a&&P_(a)?q_(r,i,e,t):M(a)),T(a=>j(D({},n),{guardsResult:a})))})}function z_(e,t,n,r){return q(e).pipe(G(o=>Q_(o.component,o.route,n,t,r)),dt(o=>o!==!0,!0))}function q_(e,t,n,r){return q(t).pipe(Ke(o=>kn(W_(o.route.parent,r),G_(o.route,r),Y_(e,o.path,n),Z_(e,o.route,n))),dt(o=>o!==!0,!0))}function G_(e,t){return e!==null&&t&&t(new rd(e)),M(!0)}function W_(e,t){return e!==null&&t&&t(new td(e)),M(!0)}function Z_(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return M(!0);let o=r.map(i=>Rr(()=>{let s=Ro(t)??n,a=Er(i,s),c=j_(a)?a.canActivate(t,e):Se(s,()=>a(t,e));return qt(c).pipe(dt())}));return M(o).pipe(Dr())}function Y_(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>O_(s)).filter(s=>s!==null).map(s=>Rr(()=>{let a=s.guards.map(c=>{let u=Ro(s.node)??n,l=Er(c,u),d=V_(l)?l.canActivateChild(r,e):Se(u,()=>l(r,e));return qt(d).pipe(dt())});return M(a).pipe(Dr())}));return M(i).pipe(Dr())}function Q_(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return M(!0);let s=i.map(a=>{let c=Ro(t)??o,u=Er(a,c),l=U_(u)?u.canDeactivate(e,t,n,r):Se(c,()=>u(e,t,n,r));return qt(l).pipe(dt())});return M(s).pipe(Dr())}function K_(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return M(!0);let i=o.map(s=>{let a=Er(s,e),c=L_(a)?a.canLoad(t,n):Se(e,()=>a(t,n));return qt(c)});return M(i).pipe(Dr(),iv(r))}function iv(e){return Ia(se(t=>{if(typeof t!="boolean")throw ia(e,t)}),T(t=>t===!0))}function J_(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return M(!0);let i=o.map(s=>{let a=Er(s,e),c=B_(a)?a.canMatch(t,n):Se(e,()=>a(t,n));return qt(c)});return M(i).pipe(Dr(),iv(r))}var Co=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},So=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function lr(e){return xt(new Co(e))}function X_(e){return xt(new E(4e3,!1))}function e0(e){return xt(nv(!1,xe.GuardRejected))}var dd=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return M(r);if(o.numberOfChildren>1||!o.children[N])return X_(`${t.redirectTo}`);o=o.children[N]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,params:f,data:p,title:v}=o,I=Se(i,()=>a({params:f,data:p,queryParams:c,fragment:u,routeConfig:l,url:d,outlet:h,title:v}));if(I instanceof Tt)throw new So(I);n=I}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new So(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Tt(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new V(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new E(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},fd={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function t0(e,t,n,r,o){let i=sv(e,t,n);return i.matched?(r=I_(t,r),J_(r,t,n,o).pipe(T(s=>s===!0?i:D({},fd)))):M(i)}function sv(e,t,n){if(t.path==="**")return n0(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?D({},fd):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||QT)(n,e,t);if(!o)return D({},fd);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?D(D({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function n0(e){return{matched:!0,parameters:e.length>0?Vy(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Oy(e,t,n,r){return n.length>0&&i0(e,n,r)?{segmentGroup:new V(t,o0(r,new V(n,e.children))),slicedSegments:[]}:n.length===0&&s0(e,n,r)?{segmentGroup:new V(e.segments,r0(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new V(e.segments,e.children),slicedSegments:n}}function r0(e,t,n,r){let o={};for(let i of n)if(ua(e,t,i)&&!r[We(i)]){let s=new V([],{});o[We(i)]=s}return D(D({},r),o)}function o0(e,t){let n={};n[N]=t;for(let r of e)if(r.path===""&&We(r)!==N){let o=new V([],{});n[We(r)]=o}return n}function i0(e,t,n){return n.some(r=>ua(e,t,r)&&We(r)!==N)}function s0(e,t,n){return n.some(r=>ua(e,t,r))}function ua(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function a0(e,t,n){return t.length===0&&!e.children[n]}var hd=class{};function c0(e,t,n,r,o,i,s="emptyOnly"){return new pd(e,t,n,r,o,s,i).recognize()}var u0=31,pd=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new dd(this.urlSerializer,this.urlTree)}noMatchError(t){return new E(4002,`'${t.segmentGroup}'`)}recognize(){let t=Oy(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(T(({children:n,rootSnapshot:r})=>{let o=new Re(r,n),i=new oa("",o),s=g_(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new hr([],Object.freeze({}),Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),N,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,N,n).pipe(T(r=>({children:r,rootSnapshot:n})),Be(r=>{if(r instanceof So)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Co?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(T(s=>s instanceof Re?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return q(i).pipe(Ke(s=>{let a=r.children[s],c=b_(n,s);return this.processSegmentGroup(t,c,a,s,o)}),xr((s,a)=>(s.push(...a),s)),At(null),Oa(),G(s=>{if(s===null)return lr(r);let a=av(s);return l0(a),M(a)}))}processSegment(t,n,r,o,i,s,a){return q(n).pipe(Ke(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(Be(u=>{if(u instanceof Co)return M(null);throw u}))),dt(c=>!!c),Be(c=>{if(ov(c))return a0(r,o,i)?M(new hd):lr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return We(r)!==s&&(s===N||!ua(o,i,r))?lr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):lr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=sv(n,o,i);if(!c)return lr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>u0&&(this.allowRedirects=!1));let f=new hr(i,u,Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,ky(o),We(o),o.component??o._loadedComponent??null,o,Fy(o)),p=ra(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(p.params),f.data=Object.freeze(p.data);let v=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(o,v).pipe(G(I=>this.processSegment(t,r,n,I.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=t0(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(pe(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(pe(({routes:u})=>{let l=r._loadedInjector??t,{parameters:d,consumedSegments:h,remainingSegments:f}=c,p=new hr(h,d,Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,ky(r),We(r),r.component??r._loadedComponent??null,r,Fy(r)),v=ra(p,s,this.paramsInheritanceStrategy);p.params=Object.freeze(v.params),p.data=Object.freeze(v.data);let{segmentGroup:I,slicedSegments:_}=Oy(n,h,f,u);if(_.length===0&&I.hasChildren())return this.processChildren(l,u,I,p).pipe(T(ne=>new Re(p,ne)));if(u.length===0&&_.length===0)return M(new Re(p,[]));let Z=We(r)===i;return this.processSegment(l,u,I,_,Z?N:i,!0,p).pipe(T(ne=>new Re(p,ne instanceof Re?[ne]:[])))}))):lr(n)))}getChildConfig(t,n,r){return n.children?M({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?M({routes:n._loadedRoutes,injector:n._loadedInjector}):K_(t,n,r,this.urlSerializer).pipe(G(o=>o?this.configLoader.loadChildren(t,n).pipe(se(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):e0(n))):M({routes:[],injector:t})}};function l0(e){e.sort((t,n)=>t.value.outlet===N?-1:n.value.outlet===N?1:t.value.outlet.localeCompare(n.value.outlet))}function d0(e){let t=e.value.routeConfig;return t&&t.path===""}function av(e){let t=[],n=new Set;for(let r of e){if(!d0(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=av(r.children);t.push(new Re(r.value,o))}return t.filter(r=>!n.has(r))}function ky(e){return e.data||{}}function Fy(e){return e.resolve||{}}function f0(e,t,n,r,o,i){return G(s=>c0(e,t,n,r,s.extractedUrl,o,i).pipe(T(({state:a,tree:c})=>j(D({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function h0(e,t){return G(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return M(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of cv(c))s.add(u);let a=0;return q(s).pipe(Ke(c=>i.has(c)?p0(c,r,e,t):(c.data=ra(c,c.parent,e).resolve,M(void 0))),se(()=>a++),Fn(1),G(c=>a===s.size?M(n):ie))})}function cv(e){let t=e.children.map(n=>cv(n)).flat();return[e,...t]}function p0(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!ev(o)&&(i[To]=o.title),g0(i,e,t,r).pipe(T(s=>(e._resolvedData=s,e.data=ra(e,e.parent,n).resolve,null)))}function g0(e,t,n,r){let o=ql(e);if(o.length===0)return M({});let i={};return q(o).pipe(G(s=>m0(e[s],t,n,r).pipe(dt(),se(a=>{if(a instanceof bo)throw ia(new mr,a);i[s]=a}))),Fn(1),T(()=>i),Be(s=>ov(s)?ie:xt(s)))}function m0(e,t,n,r){let o=Ro(t)??r,i=Er(e,o),s=i.resolve?i.resolve(t,n):Se(o,()=>i(t,n));return qt(s)}function Hl(e){return pe(t=>{let n=e(t);return n?q(n).pipe(T(()=>t)):M(t)})}var uv=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===N);return r}getResolvedTitleForRoute(n){return n.data[To]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(y0),providedIn:"root"})}return e})(),y0=(()=>{class e extends uv{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(y(Vm))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ao=new m("",{providedIn:"root",factory:()=>({})}),v0=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=_g({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Ku(0,"router-outlet")},dependencies:[M_],encapsulation:2})}return e})();function yd(e){let t=e.children&&e.children.map(yd),n=t?j(D({},e),{children:t}):D({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==N&&(n.component=v0),n}var Mo=new m(""),vd=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=g(ps);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return M(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=qt(n.loadComponent()).pipe(T(lv),se(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Ot(()=>{this.componentLoaders.delete(n)})),o=new Mn(r,()=>new U).pipe(Sn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return M({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=D0(r,this.compiler,n,this.onLoadEndListener).pipe(Ot(()=>{this.childrenLoaders.delete(r)})),s=new Mn(i,()=>new U).pipe(Sn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function D0(e,t,n,r){return qt(e.loadChildren()).pipe(T(lv),G(o=>o instanceof qu||Array.isArray(o)?M(o):q(t.compileModuleAsync(o))),T(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Mo,[],{optional:!0,self:!0}).flat()),{routes:s.map(yd),injector:i}}))}function E0(e){return e&&typeof e=="object"&&"default"in e}function lv(e){return E0(e)?e.default:e}var Dd=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(w0),providedIn:"root"})}return e})(),w0=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dv=new m(""),fv=new m("");function I0(e,t,n){let r=e.get(fv),o=e.get(le);return e.get(Q).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),b0(e))),{onViewTransitionCreated:c}=r;return c&&Se(e,()=>c({transition:a,from:t,to:n})),s})}function b0(e){return new Promise(t=>{Mu({read:()=>setTimeout(t)},{injector:e})})}var hv=new m(""),Ed=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new U;transitionAbortSubject=new U;configLoader=g(vd);environmentInjector=g(me);destroyRef=g(ot);urlSerializer=g(_o);rootContexts=g(xo);location=g(ar);inputBindingEnabled=g(ca,{optional:!0})!==null;titleStrategy=g(uv);options=g(Ao,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=g(Dd);createViewTransition=g(dv,{optional:!0});navigationErrorHandler=g(hv,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>M(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Xl(o)),r=o=>this.events.next(new ed(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(j(D({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new K(null),this.transitions.pipe(X(r=>r!==null),pe(r=>{let o=!1,i=!1;return M(r).pipe(pe(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",xe.SupersededByNewNavigation),ie;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?j(D({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new Ht(s.id,this.urlSerializer.serialize(s.rawUrl),u,Js.IgnoredSameUrlNavigation)),s.resolve(!1),ie}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return M(s).pipe(pe(u=>(this.events.next(new yr(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?ie:Promise.resolve(u))),f0(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),se(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=j(D({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new Xs(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:d,restoredState:h,extras:f}=s,p=new yr(u,this.urlSerializer.serialize(l),d,h);this.events.next(p);let v=Jy(this.rootComponentType).snapshot;return this.currentTransition=r=j(D({},s),{targetSnapshot:v,urlAfterRedirects:l,extras:j(D({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,M(r)}else{let u="";return this.events.next(new Ht(s.id,this.urlSerializer.serialize(s.extractedUrl),u,Js.IgnoredByUrlHandlingStrategy)),s.resolve(!1),ie}}),se(s=>{let a=new Yl(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),T(s=>(this.currentTransition=r=j(D({},s),{guards:A_(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),H_(this.environmentInjector,s=>this.events.next(s)),se(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw ia(this.urlSerializer,s.guardsResult);let a=new Ql(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),X(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",xe.GuardRejected),!1)),Hl(s=>{if(s.guards.canActivateChecks.length!==0)return M(s).pipe(se(a=>{let c=new Kl(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),pe(a=>{let c=!1;return M(a).pipe(h0(this.paramsInheritanceStrategy,this.environmentInjector),se({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",xe.NoDataFromResolver)}}))}),se(a=>{let c=new Jl(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Hl(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(se(l=>{c.component=l}),T(()=>{})));for(let l of c.children)u.push(...a(l));return u};return _r(a(s.targetSnapshot.root)).pipe(At(null),$e(1))}),Hl(()=>this.afterPreactivation()),pe(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?q(c).pipe(T(()=>r)):M(r)}),T(s=>{let a=T_(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=j(D({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),se(()=>{this.events.next(new wo)}),N_(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),$e(1),se({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ze(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),Nr(this.transitionAbortSubject.pipe(se(s=>{throw s}))),Ot(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",xe.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),Be(s=>{if(this.destroyed)return r.resolve(!1),ie;if(i=!0,rv(s))this.events.next(new Mt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),x_(s)?this.events.next(new vr(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Eo(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Se(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof bo){let{message:u,cancellationCode:l}=ia(this.urlSerializer,c);this.events.next(new Mt(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new vr(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return ie}))}))}cancelNavigationTransition(n,r,o){let i=new Mt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function C0(e){return e!==Ys}var S0=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(M0),providedIn:"root"})}return e})(),gd=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},M0=(()=>{class e extends gd{static \u0275fac=(()=>{let n;return function(o){return(n||(n=Kr(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),pv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>g(T0),providedIn:"root"})}return e})(),T0=(()=>{class e extends pv{location=g(ar);urlSerializer=g(_o);options=g(Ao,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";urlHandlingStrategy=g(Dd);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Tt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}routerState=Jy(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&n(r.url,r.state)})}handleRouterEvent(n,r){if(n instanceof yr)this.stateMemento=this.createStateMemento();else if(n instanceof Ht)this.rawUrlTree=r.initialUrl;else if(n instanceof Xs){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let o=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??o,r)}}else n instanceof wo?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):n instanceof Mt&&(n.code===xe.GuardRejected||n.code===xe.NoDataFromResolver)?this.restoreHistory(r):n instanceof Eo?this.restoreHistory(r,!0):n instanceof Ze&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,r){let o=n instanceof Tt?this.urlSerializer.serialize(n):n;if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){let i=this.browserPageId,s=D(D({},r.extras.state),this.generateNgRouterState(r.id,i));this.location.replaceState(o,"",s)}else{let i=D(D({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(o,"",i)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.currentUrlTree===n.finalUrl&&i===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=Kr(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function gv(e,t){e.events.pipe(X(n=>n instanceof Ze||n instanceof Mt||n instanceof Eo||n instanceof Ht),T(n=>n instanceof Ze||n instanceof Ht?0:(n instanceof Mt?n.code===xe.Redirect||n.code===xe.SupersededByNewNavigation:!1)?2:1),X(n=>n!==2),$e(1)).subscribe(()=>{t()})}var _0={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},R0={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},_t=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=g(Zu);stateManager=g(pv);options=g(Ao,{optional:!0})||{};pendingTasks=g(Lt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=g(Ed);urlSerializer=g(_o);location=g(ar);urlHandlingStrategy=g(Dd);_events=new U;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=g(S0);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=g(Mo,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!g(ca,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new Y;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Mt&&r.code!==xe.Redirect&&r.code!==xe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ze)this.navigated=!0;else if(r instanceof vr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=D({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||C0(o.source)},s);this.scheduleNavigation(a,Ys,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}N0(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ys,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",r)},0)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=D({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(yd),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=D(D({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=Zy(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Yy(d,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=En(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Ys,null,r)}navigate(n,r={skipLocationChange:!1}){return x0(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=D({},_0):r===!1?o=D({},R0):o=r,En(n))return _y(this.currentUrlTree,n,o);let i=this.parseUrl(n);return _y(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return gv(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function x0(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new E(4008,!1)}function N0(e){return!(e instanceof wo)&&!(e instanceof vr)}var Py=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new U;constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(u=>{u instanceof Ze&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(En(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:Up(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:En(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(H(_t),H(zt),yu("tabindex"),H(tr),H(it),H(St))};static \u0275dir=Ut({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&Ju("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Qu("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",no],skipLocationChange:[2,"skipLocationChange","skipLocationChange",no],replaceUrl:[2,"replaceUrl","replaceUrl",no],routerLink:"routerLink"},features:[Zr]})}return e})(),Wj=(()=>{class e{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new we;constructor(n,r,o,i,s){this.router=n,this.element=r,this.renderer=o,this.cdr=i,this.link=s,this.routerEventsSubscription=n.events.subscribe(a=>{a instanceof Ze&&this.update()})}ngAfterContentInit(){M(this.links.changes,M(null)).pipe(Qe()).subscribe(n=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let n=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=q(n).pipe(Qe()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(n){let r=Array.isArray(n)?n:n.split(" ");this.classes=r.filter(o=>!!o)}ngOnChanges(n){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let n=this.hasActiveLinks();this.classes.forEach(r=>{n?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),n&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==n&&(this._isActive=n,this.cdr.markForCheck(),this.isActiveChange.emit(n))})}isLinkActive(n){let r=A0(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return o=>{let i=o.urlTree;return i?n.isActive(i,r):!1}}hasActiveLinks(){let n=this.isLinkActive(this.router);return this.link&&n(this.link)||this.links.some(n)}static \u0275fac=function(r){return new(r||e)(H(_t),H(it),H(tr),H(gn),H(Py,8))};static \u0275dir=Ut({type:e,selectors:[["","routerLinkActive",""]],contentQueries:function(r,o,i){if(r&1&&Bg(i,Py,5),r&2){let s;$g(s=Hg())&&(o.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Zr]})}return e})();function A0(e){return!!e.paths}var aa=class{};var O0=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i,s){this.router=n,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(X(n=>n instanceof Ze),Ke(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=fs(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return q(o).pipe(Qe())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=M(null);let i=o.pipe(G(s=>s===null?M(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return q([i,s]).pipe(Qe())}else return i})}static \u0275fac=function(r){return new(r||e)(y(_t),y(ps),y(me),y(aa),y(vd))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mv=new m(""),k0=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof yr?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Ze?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Ht&&n.code===Js.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof ea&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new ea(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){vg()};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function Zj(e,...t){return hn([{provide:Mo,multi:!0,useValue:e},[],{provide:zt,useFactory:yv,deps:[_t]},{provide:to,multi:!0,useFactory:vv},t.map(n=>n.\u0275providers)])}function yv(e){return e.routerState.root}function Oo(e,t){return{\u0275kind:e,\u0275providers:t}}function vv(){let e=g(oe);return t=>{let n=e.get(vt);if(t!==n.components[0])return;let r=e.get(_t),o=e.get(Dv);e.get(wd)===1&&r.initialNavigation(),e.get(Ev,null,O.Optional)?.setUpPreloading(),e.get(mv,null,O.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Dv=new m("",{factory:()=>new U}),wd=new m("",{providedIn:"root",factory:()=>1});function F0(){return Oo(2,[{provide:wd,useValue:0},{provide:hs,multi:!0,deps:[oe],useFactory:t=>{let n=t.get(am,Promise.resolve());return()=>n.then(()=>new Promise(r=>{let o=t.get(_t),i=t.get(Dv);gv(o,()=>{r(!0)}),t.get(Ed).afterPreactivation=()=>(r(!0),i.closed?M(void 0):i),o.initialNavigation()}))}}])}function P0(){return Oo(3,[{provide:hs,multi:!0,useFactory:()=>{let t=g(_t);return()=>{t.setUpLocationChangeListener()}}},{provide:wd,useValue:2}])}var Ev=new m("");function L0(e){return Oo(0,[{provide:Ev,useExisting:O0},{provide:aa,useExisting:e}])}function j0(){return Oo(8,[Ay,{provide:ca,useExisting:Ay}])}function V0(e){let t=[{provide:dv,useValue:I0},{provide:fv,useValue:D({skipNextTransition:!!e?.skipInitialTransition},e)}];return Oo(9,t)}var U0=[ar,{provide:_o,useClass:mr},_t,xo,{provide:zt,useFactory:yv,deps:[_t]},vd,[]],Yj=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[U0,[],{provide:Mo,multi:!0,useValue:n},[],r?.errorHandler?{provide:hv,useValue:r.errorHandler}:[],{provide:Ao,useValue:r||{}},r?.useHash?$0():H0(),B0(),r?.preloadingStrategy?L0(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?z0(r):[],r?.bindToComponentInputs?j0().\u0275providers:[],r?.enableViewTransitions?V0().\u0275providers:[],q0()]}}static forChild(n){return{ngModule:e,providers:[{provide:Mo,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Te({type:e});static \u0275inj=Ce({})}return e})();function B0(){return{provide:mv,useFactory:()=>{let e=g(gm),t=g(Q),n=g(Ao),r=g(Ed),o=g(_o);return n.scrollOffset&&e.setOffset(n.scrollOffset),new k0(o,r,e,t,n)}}}function $0(){return{provide:St,useClass:um}}function H0(){return{provide:St,useClass:fl}}function z0(e){return[e.initialNavigation==="disabled"?P0().\u0275providers:[],e.initialNavigation==="enabledBlocking"?F0().\u0275providers:[]]}var Ly=new m("");function q0(){return[{provide:Ly,useFactory:vv},{provide:to,multi:!0,useExisting:Ly}]}export{Y as a,R as b,U as c,K as d,Tn as e,Sa as f,ie as g,Qt as h,q as i,M as j,xt as k,_a as l,Bv as m,Hv as n,T as o,G as p,Rr as q,Xv as r,Ra as s,ui as t,X as u,Be as v,Ke as w,sD as x,$e as y,Aa as z,xr as A,La as B,uD as C,lD as D,pe as E,Nr as F,se as G,Va as H,ih as I,w as J,Ce as K,m as L,y as M,g as N,hn as O,VD as P,Zr as Q,o1 as R,i1 as S,Kr as T,ot as U,we as V,Q as W,qe as X,gp as Y,it as Z,is as _,pn as $,at as aa,s1 as ba,tr as ca,H as da,_g as ea,Te as fa,Ut as ga,jb as ha,Yb as ia,nr as ja,uC as ka,Pg as la,lC as ma,m1 as na,jg as oa,Vg as pa,Ku as qa,y1 as ra,Ju as sa,v1 as ta,D1 as ua,E1 as va,w1 as wa,$g as xa,Hg as ya,I1 as za,OC as Aa,zg as Ba,FC as Ca,b1 as Da,PC as Ea,C1 as Fa,S1 as Ga,M1 as Ha,T1 as Ia,_1 as Ja,R1 as Ka,gn as La,no as Ma,rr as Na,ro as Oa,ir as Pa,le as Qa,tL as Ra,nL as Sa,rL as Ta,oL as Ua,iL as Va,jS as Wa,hl as Xa,YS as Ya,bL as Za,HL as _a,xM as $a,M_ as ab,Py as bb,Wj as cb,Zj as db,Yj as eb,OM as fb,Sl as gb,ty as hb,cj as ib,ny as jb,ur as kb,Ol as lb,fo as mb,js as nb,BM as ob,ho as pb,Us as qb,yn as rb,iT as sb,uj as tb,lj as ub,dj as vb,fj as wb,Ij as xb,bj as yb,Cj as zb,Mj as Ab};
