import{c as Ui}from"./chunk-MAVNWS4Y.js";import{g as Fn,i as zn}from"./chunk-MMJWBTO6.js";import{Aa as ee,Ab as In,Ba as vn,Ga as ji,Ha as ve,Ia as Me,J as Vt,Ja as Ke,K as Zt,Ka as Mn,M as ye,Q as mn,Sa as Sn,Ta as kn,Ua as wn,V as Hi,Va as Dn,W as bn,Wa as Cn,Z as xn,ba as Q,d as dn,da as Qt,ea as _n,eb as $i,fa as Jt,ga as yn,hb as qe,ia as te,ib as Yi,j as Ue,ka as wt,o as un,oa as U,p as fn,pa as X,qa as Xe,rb as Pn,sb as Ge,tb as On,ub as An,v as gn,vb as Ze,wb as Tn,xb as Ln,yb as Rn,z as pn,za as nt,zb as En}from"./chunk-5UJX3CQ5.js";import{a as St,b as kt}from"./chunk-GAL4ENT6.js";var ie=qe("[Dashboard] Load Dashboard Data"),Qe=qe("[Dashboard] Load Dashboard Data Success",Yi()),Je=qe("[Dashboard] Load Dashboard Data Failure",Yi());var Xi=On("dashboard"),Nn=Ge(Xi,e=>e.metrics),Vn=Ge(Xi,e=>e.loading),Wn=Ge(Xi,e=>e.error);function we(e){return e+.5|0}var Dt=(e,i,t)=>Math.max(Math.min(e,t),i);function Se(e){return Dt(we(e*2.55),0,255)}function Ct(e){return Dt(we(e*255),0,255)}function mt(e){return Dt(we(e/2.55)/100,0,1)}function Hn(e){return Dt(we(e*100),0,100)}var ot={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},qi=[..."0123456789ABCDEF"],cr=e=>qi[e&15],hr=e=>qi[(e&240)>>4]+qi[e&15],ti=e=>(e&240)>>4===(e&15),dr=e=>ti(e.r)&&ti(e.g)&&ti(e.b)&&ti(e.a);function ur(e){var i=e.length,t;return e[0]==="#"&&(i===4||i===5?t={r:255&ot[e[1]]*17,g:255&ot[e[2]]*17,b:255&ot[e[3]]*17,a:i===5?ot[e[4]]*17:255}:(i===7||i===9)&&(t={r:ot[e[1]]<<4|ot[e[2]],g:ot[e[3]]<<4|ot[e[4]],b:ot[e[5]]<<4|ot[e[6]],a:i===9?ot[e[7]]<<4|ot[e[8]]:255})),t}var fr=(e,i)=>e<255?i(e):"";function gr(e){var i=dr(e)?cr:hr;return e?"#"+i(e.r)+i(e.g)+i(e.b)+fr(e.a,i):void 0}var pr=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Un(e,i,t){let s=i*Math.min(t,1-t),n=(o,a=(o+e/30)%12)=>t-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function mr(e,i,t){let s=(n,o=(n+e/60)%6)=>t-t*i*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function br(e,i,t){let s=Un(e,1,.5),n;for(i+t>1&&(n=1/(i+t),i*=n,t*=n),n=0;n<3;n++)s[n]*=1-i-t,s[n]+=i;return s}function xr(e,i,t,s,n){return e===n?(i-t)/s+(i<t?6:0):i===n?(t-e)/s+2:(e-i)/s+4}function Gi(e){let t=e.r/255,s=e.g/255,n=e.b/255,o=Math.max(t,s,n),a=Math.min(t,s,n),r=(o+a)/2,l,c,h;return o!==a&&(h=o-a,c=r>.5?h/(2-o-a):h/(o+a),l=xr(t,s,n,h,o),l=l*60+.5),[l|0,c||0,r]}function Zi(e,i,t,s){return(Array.isArray(i)?e(i[0],i[1],i[2]):e(i,t,s)).map(Ct)}function Qi(e,i,t){return Zi(Un,e,i,t)}function _r(e,i,t){return Zi(br,e,i,t)}function yr(e,i,t){return Zi(mr,e,i,t)}function Xn(e){return(e%360+360)%360}function vr(e){let i=pr.exec(e),t=255,s;if(!i)return;i[5]!==s&&(t=i[6]?Se(+i[5]):Ct(+i[5]));let n=Xn(+i[2]),o=+i[3]/100,a=+i[4]/100;return i[1]==="hwb"?s=_r(n,o,a):i[1]==="hsv"?s=yr(n,o,a):s=Qi(n,o,a),{r:s[0],g:s[1],b:s[2],a:t}}function Mr(e,i){var t=Gi(e);t[0]=Xn(t[0]+i),t=Qi(t),e.r=t[0],e.g=t[1],e.b=t[2]}function Sr(e){if(!e)return;let i=Gi(e),t=i[0],s=Hn(i[1]),n=Hn(i[2]);return e.a<255?`hsla(${t}, ${s}%, ${n}%, ${mt(e.a)})`:`hsl(${t}, ${s}%, ${n}%)`}var jn={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},$n={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function kr(){let e={},i=Object.keys($n),t=Object.keys(jn),s,n,o,a,r;for(s=0;s<i.length;s++){for(a=r=i[s],n=0;n<t.length;n++)o=t[n],r=r.replace(o,jn[o]);o=parseInt($n[a],16),e[r]=[o>>16&255,o>>8&255,o&255]}return e}var ei;function wr(e){ei||(ei=kr(),ei.transparent=[0,0,0,0]);let i=ei[e.toLowerCase()];return i&&{r:i[0],g:i[1],b:i[2],a:i.length===4?i[3]:255}}var Dr=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Cr(e){let i=Dr.exec(e),t=255,s,n,o;if(i){if(i[7]!==s){let a=+i[7];t=i[8]?Se(a):Dt(a*255,0,255)}return s=+i[1],n=+i[3],o=+i[5],s=255&(i[2]?Se(s):Dt(s,0,255)),n=255&(i[4]?Se(n):Dt(n,0,255)),o=255&(i[6]?Se(o):Dt(o,0,255)),{r:s,g:n,b:o,a:t}}}function Pr(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${mt(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}var Ki=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,se=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Or(e,i,t){let s=se(mt(e.r)),n=se(mt(e.g)),o=se(mt(e.b));return{r:Ct(Ki(s+t*(se(mt(i.r))-s))),g:Ct(Ki(n+t*(se(mt(i.g))-n))),b:Ct(Ki(o+t*(se(mt(i.b))-o))),a:e.a+t*(i.a-e.a)}}function ii(e,i,t){if(e){let s=Gi(e);s[i]=Math.max(0,Math.min(s[i]+s[i]*t,i===0?360:1)),s=Qi(s),e.r=s[0],e.g=s[1],e.b=s[2]}}function Kn(e,i){return e&&Object.assign(i||{},e)}function Yn(e){var i={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(i={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(i.a=Ct(e[3]))):(i=Kn(e,{r:0,g:0,b:0,a:1}),i.a=Ct(i.a)),i}function Ar(e){return e.charAt(0)==="r"?Cr(e):vr(e)}var ke=class e{constructor(i){if(i instanceof e)return i;let t=typeof i,s;t==="object"?s=Yn(i):t==="string"&&(s=ur(i)||wr(i)||Ar(i)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var i=Kn(this._rgb);return i&&(i.a=mt(i.a)),i}set rgb(i){this._rgb=Yn(i)}rgbString(){return this._valid?Pr(this._rgb):void 0}hexString(){return this._valid?gr(this._rgb):void 0}hslString(){return this._valid?Sr(this._rgb):void 0}mix(i,t){if(i){let s=this.rgb,n=i.rgb,o,a=t===o?.5:t,r=2*a-1,l=s.a-n.a,c=((r*l===-1?r:(r+l)/(1+r*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(i,t){return i&&(this._rgb=Or(this._rgb,i._rgb,t)),this}clone(){return new e(this.rgb)}alpha(i){return this._rgb.a=Ct(i),this}clearer(i){let t=this._rgb;return t.a*=1-i,this}greyscale(){let i=this._rgb,t=we(i.r*.3+i.g*.59+i.b*.11);return i.r=i.g=i.b=t,this}opaquer(i){let t=this._rgb;return t.a*=1+i,this}negate(){let i=this._rgb;return i.r=255-i.r,i.g=255-i.g,i.b=255-i.b,this}lighten(i){return ii(this._rgb,2,i),this}darken(i){return ii(this._rgb,2,-i),this}saturate(i){return ii(this._rgb,1,i),this}desaturate(i){return ii(this._rgb,1,-i),this}rotate(i){return Mr(this._rgb,i),this}};function ut(){}var oo=(()=>{let e=0;return()=>e++})();function O(e){return e==null}function F(e){if(Array.isArray&&Array.isArray(e))return!0;let i=Object.prototype.toString.call(e);return i.slice(0,7)==="[object"&&i.slice(-6)==="Array]"}function A(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function V(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function tt(e,i){return V(e)?e:i}function D(e,i){return typeof e>"u"?i:e}var ao=(e,i)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/i,is=(e,i)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*i:+e;function E(e,i,t){if(e&&typeof e.call=="function")return e.apply(t,i)}function L(e,i,t,s){let n,o,a;if(F(e))if(o=e.length,s)for(n=o-1;n>=0;n--)i.call(t,e[n],n);else for(n=0;n<o;n++)i.call(t,e[n],n);else if(A(e))for(a=Object.keys(e),o=a.length,n=0;n<o;n++)i.call(t,e[a[n]],a[n])}function Pe(e,i){let t,s,n,o;if(!e||!i||e.length!==i.length)return!1;for(t=0,s=e.length;t<s;++t)if(n=e[t],o=i[t],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ri(e){if(F(e))return e.map(ri);if(A(e)){let i=Object.create(null),t=Object.keys(e),s=t.length,n=0;for(;n<s;++n)i[t[n]]=ri(e[t[n]]);return i}return e}function ro(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function Tr(e,i,t,s){if(!ro(e))return;let n=i[e],o=t[e];A(n)&&A(o)?oe(n,o,s):i[e]=ri(o)}function oe(e,i,t){let s=F(i)?i:[i],n=s.length;if(!A(e))return e;t=t||{};let o=t.merger||Tr,a;for(let r=0;r<n;++r){if(a=s[r],!A(a))continue;let l=Object.keys(a);for(let c=0,h=l.length;c<h;++c)o(l[c],e,a,t)}return e}function re(e,i){return oe(e,i,{merger:Lr})}function Lr(e,i,t){if(!ro(e))return;let s=i[e],n=t[e];A(s)&&A(n)?re(s,n):Object.prototype.hasOwnProperty.call(i,e)||(i[e]=ri(n))}var qn={"":e=>e,x:e=>e.x,y:e=>e.y};function Rr(e){let i=e.split("."),t=[],s="";for(let n of i)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(t.push(s),s="");return t}function Er(e){let i=Rr(e);return t=>{for(let s of i){if(s==="")break;t=t&&t[s]}return t}}function _t(e,i){return(qn[i]||(qn[i]=Er(i)))(e)}function di(e){return e.charAt(0).toUpperCase()+e.slice(1)}var le=e=>typeof e<"u",bt=e=>typeof e=="function",ss=(e,i)=>{if(e.size!==i.size)return!1;for(let t of e)if(!i.has(t))return!1;return!0};function lo(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}var z=Math.PI,B=2*z,Ir=B+z,li=Number.POSITIVE_INFINITY,Fr=z/180,W=z/2,Wt=z/4,Gn=z*2/3,xt=Math.log10,lt=Math.sign;function ce(e,i,t){return Math.abs(e-i)<t}function ns(e){let i=Math.round(e);e=ce(e,i,e/1e3)?i:e;let t=Math.pow(10,Math.floor(xt(e))),s=e/t;return(s<=1?1:s<=2?2:s<=5?5:10)*t}function co(e){let i=[],t=Math.sqrt(e),s;for(s=1;s<t;s++)e%s===0&&(i.push(s),i.push(e/s));return t===(t|0)&&i.push(t),i.sort((n,o)=>n-o).pop(),i}function zr(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function $t(e){return!zr(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function ho(e,i){let t=Math.round(e);return t-i<=e&&t+i>=e}function os(e,i,t){let s,n,o;for(s=0,n=e.length;s<n;s++)o=e[s][t],isNaN(o)||(i.min=Math.min(i.min,o),i.max=Math.max(i.max,o))}function at(e){return e*(z/180)}function ui(e){return e*(180/z)}function as(e){if(!V(e))return;let i=1,t=0;for(;Math.round(e*i)/i!==e;)i*=10,t++;return t}function rs(e,i){let t=i.x-e.x,s=i.y-e.y,n=Math.sqrt(t*t+s*s),o=Math.atan2(s,t);return o<-.5*z&&(o+=B),{angle:o,distance:n}}function ci(e,i){return Math.sqrt(Math.pow(i.x-e.x,2)+Math.pow(i.y-e.y,2))}function Br(e,i){return(e-i+Ir)%B-z}function J(e){return(e%B+B)%B}function he(e,i,t,s){let n=J(e),o=J(i),a=J(t),r=J(o-n),l=J(a-n),c=J(n-o),h=J(n-a);return n===o||n===a||s&&o===a||r>l&&c<h}function $(e,i,t){return Math.max(i,Math.min(t,e))}function uo(e){return $(e,-32768,32767)}function ft(e,i,t,s=1e-6){return e>=Math.min(i,t)-s&&e<=Math.max(i,t)+s}function fi(e,i,t){t=t||(a=>e[a]<i);let s=e.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,t(o)?n=o:s=o;return{lo:n,hi:s}}var ht=(e,i,t,s)=>fi(e,t,s?n=>{let o=e[n][i];return o<t||o===t&&e[n+1][i]===t}:n=>e[n][i]<t),fo=(e,i,t)=>fi(e,t,s=>e[s][i]>=t);function go(e,i,t){let s=0,n=e.length;for(;s<n&&e[s]<i;)s++;for(;n>s&&e[n-1]>t;)n--;return s>0||n<e.length?e.slice(s,n):e}var po=["push","pop","shift","splice","unshift"];function mo(e,i){if(e._chartjs){e._chartjs.listeners.push(i);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[i]}}),po.forEach(t=>{let s="_onData"+di(t),n=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value(...o){let a=n.apply(this,o);return e._chartjs.listeners.forEach(r=>{typeof r[s]=="function"&&r[s](...o)}),a}})})}function ls(e,i){let t=e._chartjs;if(!t)return;let s=t.listeners,n=s.indexOf(i);n!==-1&&s.splice(n,1),!(s.length>0)&&(po.forEach(o=>{delete e[o]}),delete e._chartjs)}function cs(e){let i=new Set(e);return i.size===e.length?e:Array.from(i)}var hs=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function ds(e,i){let t=[],s=!1;return function(...n){t=n,s||(s=!0,hs.call(window,()=>{s=!1,e.apply(i,t)}))}}function bo(e,i){let t;return function(...s){return i?(clearTimeout(t),t=setTimeout(e,i,s)):e.apply(this,s),i}}var gi=e=>e==="start"?"left":e==="end"?"right":"center",K=(e,i,t)=>e==="start"?i:e==="end"?t:(i+t)/2,xo=(e,i,t,s)=>e===(s?"left":"right")?t:e==="center"?(i+t)/2:i;function us(e,i,t){let s=i.length,n=0,o=s;if(e._sorted){let{iScale:a,vScale:r,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,h=a.axis,{min:d,max:u,minDefined:f,maxDefined:g}=a.getUserBounds();if(f){if(n=Math.min(ht(l,h,d).lo,t?s:ht(i,h,a.getPixelForValue(d)).lo),c){let p=l.slice(0,n+1).reverse().findIndex(m=>!O(m[r.axis]));n-=Math.max(0,p)}n=$(n,0,s-1)}if(g){let p=Math.max(ht(l,a.axis,u,!0).hi+1,t?0:ht(i,h,a.getPixelForValue(u),!0).hi+1);if(c){let m=l.slice(p-1).findIndex(b=>!O(b[r.axis]));p+=Math.max(0,m)}o=$(p,n,s)-n}else o=s-n}return{start:n,count:o}}function fs(e){let{xScale:i,yScale:t,_scaleRanges:s}=e,n={xmin:i.min,xmax:i.max,ymin:t.min,ymax:t.max};if(!s)return e._scaleRanges=n,!0;let o=s.xmin!==i.min||s.xmax!==i.max||s.ymin!==t.min||s.ymax!==t.max;return Object.assign(s,n),o}var si=e=>e===0||e===1,Zn=(e,i,t)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-i)*B/t)),Qn=(e,i,t)=>Math.pow(2,-10*e)*Math.sin((e-i)*B/t)+1,ne={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*W)+1,easeOutSine:e=>Math.sin(e*W),easeInOutSine:e=>-.5*(Math.cos(z*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>si(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>si(e)?e:Zn(e,.075,.3),easeOutElastic:e=>si(e)?e:Qn(e,.075,.3),easeInOutElastic(e){return si(e)?e:e<.5?.5*Zn(e*2,.1125,.45):.5+.5*Qn(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let i=1.70158;return(e/=.5)<1?.5*(e*e*(((i*=1.525)+1)*e-i)):.5*((e-=2)*e*(((i*=1.525)+1)*e+i)+2)},easeInBounce:e=>1-ne.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?ne.easeInBounce(e*2)*.5:ne.easeOutBounce(e*2-1)*.5+.5};function gs(e){if(e&&typeof e=="object"){let i=e.toString();return i==="[object CanvasPattern]"||i==="[object CanvasGradient]"}return!1}function ps(e){return gs(e)?e:new ke(e)}function Ji(e){return gs(e)?e:new ke(e).saturate(.5).darken(.1).hexString()}var Nr=["x","y","borderWidth","radius","tension"],Vr=["color","borderColor","backgroundColor"];function Wr(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:i=>i!=="onProgress"&&i!=="onComplete"&&i!=="fn"}),e.set("animations",{colors:{type:"color",properties:Vr},numbers:{type:"number",properties:Nr}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:i=>i|0}}}})}function Hr(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}var Jn=new Map;function jr(e,i){i=i||{};let t=e+JSON.stringify(i),s=Jn.get(t);return s||(s=new Intl.NumberFormat(e,i),Jn.set(t,s)),s}function de(e,i,t){return jr(i,t).format(e)}var _o={values(e){return F(e)?e:""+e},numeric(e,i,t){if(e===0)return"0";let s=this.chart.options.locale,n,o=e;if(t.length>1){let c=Math.max(Math.abs(t[0].value),Math.abs(t[t.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=$r(e,t)}let a=xt(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(l,this.options.ticks.format),de(e,s,l)},logarithmic(e,i,t){if(e===0)return"0";let s=t[i].significand||e/Math.pow(10,Math.floor(xt(e)));return[1,2,3,5,10,15].includes(s)||i>.8*t.length?_o.numeric.call(this,e,i,t):""}};function $r(e,i){let t=i.length>3?i[2].value-i[1].value:i[1].value-i[0].value;return Math.abs(t)>=1&&e!==Math.floor(e)&&(t=e-Math.floor(e)),t}var Oe={formatters:_o};function Yr(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(i,t)=>t.lineWidth,tickColor:(i,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Oe.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:i=>!i.startsWith("before")&&!i.startsWith("after")&&i!=="callback"&&i!=="parser",_indexable:i=>i!=="borderDash"&&i!=="tickBorderDash"&&i!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:i=>i!=="backdropPadding"&&i!=="callback",_indexable:i=>i!=="backdropPadding"})}var Ot=Object.create(null),pi=Object.create(null);function De(e,i){if(!i)return e;let t=i.split(".");for(let s=0,n=t.length;s<n;++s){let o=t[s];e=e[o]||(e[o]=Object.create(null))}return e}function ts(e,i,t){return typeof i=="string"?oe(De(e,i),t):oe(De(e,""),i)}var es=class{constructor(i,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Ji(n.backgroundColor),this.hoverBorderColor=(s,n)=>Ji(n.borderColor),this.hoverColor=(s,n)=>Ji(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(i),this.apply(t)}set(i,t){return ts(this,i,t)}get(i){return De(this,i)}describe(i,t){return ts(pi,i,t)}override(i,t){return ts(Ot,i,t)}route(i,t,s,n){let o=De(this,i),a=De(this,s),r="_"+t;Object.defineProperties(o,{[r]:{value:o[t],writable:!0},[t]:{enumerable:!0,get(){let l=this[r],c=a[n];return A(l)?Object.assign({},c,l):D(l,c)},set(l){this[r]=l}}})}apply(i){i.forEach(t=>t(this))}},I=new es({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Wr,Hr,Yr]);function Ur(e){return!e||O(e.size)||O(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function Ce(e,i,t,s,n){let o=i[n];return o||(o=i[n]=e.measureText(n).width,t.push(n)),o>s&&(s=o),s}function yo(e,i,t,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==i&&(n=s.data={},o=s.garbageCollect=[],s.font=i),e.save(),e.font=i;let a=0,r=t.length,l,c,h,d,u;for(l=0;l<r;l++)if(d=t[l],d!=null&&!F(d))a=Ce(e,n,o,a,d);else if(F(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!F(u)&&(a=Ce(e,n,o,a,u));e.restore();let f=o.length/2;if(f>t.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return a}function At(e,i,t){let s=e.currentDevicePixelRatio,n=t!==0?Math.max(t/2,.5):0;return Math.round((i-n)*s)/s+n}function ms(e,i){!i&&!e||(i=i||e.getContext("2d"),i.save(),i.resetTransform(),i.clearRect(0,0,e.width,e.height),i.restore())}function mi(e,i,t,s){bs(e,i,t,s,null)}function bs(e,i,t,s,n){let o,a,r,l,c,h,d,u,f=i.pointStyle,g=i.rotation,p=i.radius,m=(g||0)*Fr;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(t,s),e.rotate(m),e.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),e.restore();return}if(!(isNaN(p)||p<=0)){switch(e.beginPath(),f){default:n?e.ellipse(t,s,n/2,p,0,0,B):e.arc(t,s,p,0,B),e.closePath();break;case"triangle":h=n?n/2:p,e.moveTo(t+Math.sin(m)*h,s-Math.cos(m)*p),m+=Gn,e.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*p),m+=Gn,e.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*p),e.closePath();break;case"rectRounded":c=p*.516,l=p-c,a=Math.cos(m+Wt)*l,d=Math.cos(m+Wt)*(n?n/2-c:l),r=Math.sin(m+Wt)*l,u=Math.sin(m+Wt)*(n?n/2-c:l),e.arc(t-d,s-r,c,m-z,m-W),e.arc(t+u,s-a,c,m-W,m),e.arc(t+d,s+r,c,m,m+W),e.arc(t-u,s+a,c,m+W,m+z),e.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,e.rect(t-h,s-l,2*h,2*l);break}m+=Wt;case"rectRot":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),e.moveTo(t-d,s-r),e.lineTo(t+u,s-a),e.lineTo(t+d,s+r),e.lineTo(t-u,s+a),e.closePath();break;case"crossRot":m+=Wt;case"cross":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),e.moveTo(t-d,s-r),e.lineTo(t+d,s+r),e.moveTo(t+u,s-a),e.lineTo(t-u,s+a);break;case"star":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),e.moveTo(t-d,s-r),e.lineTo(t+d,s+r),e.moveTo(t+u,s-a),e.lineTo(t-u,s+a),m+=Wt,d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),e.moveTo(t-d,s-r),e.lineTo(t+d,s+r),e.moveTo(t+u,s-a),e.lineTo(t-u,s+a);break;case"line":a=n?n/2:Math.cos(m)*p,r=Math.sin(m)*p,e.moveTo(t-a,s-r),e.lineTo(t+a,s+r);break;case"dash":e.moveTo(t,s),e.lineTo(t+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:e.closePath();break}e.fill(),i.borderWidth>0&&e.stroke()}}function dt(e,i,t){return t=t||.5,!i||e&&e.x>i.left-t&&e.x<i.right+t&&e.y>i.top-t&&e.y<i.bottom+t}function Ae(e,i){e.save(),e.beginPath(),e.rect(i.left,i.top,i.right-i.left,i.bottom-i.top),e.clip()}function Te(e){e.restore()}function vo(e,i,t,s,n){if(!i)return e.lineTo(t.x,t.y);if(n==="middle"){let o=(i.x+t.x)/2;e.lineTo(o,i.y),e.lineTo(o,t.y)}else n==="after"!=!!s?e.lineTo(i.x,t.y):e.lineTo(t.x,i.y);e.lineTo(t.x,t.y)}function Mo(e,i,t,s){if(!i)return e.lineTo(t.x,t.y);e.bezierCurveTo(s?i.cp1x:i.cp2x,s?i.cp1y:i.cp2y,s?t.cp2x:t.cp1x,s?t.cp2y:t.cp1y,t.x,t.y)}function Xr(e,i){i.translation&&e.translate(i.translation[0],i.translation[1]),O(i.rotation)||e.rotate(i.rotation),i.color&&(e.fillStyle=i.color),i.textAlign&&(e.textAlign=i.textAlign),i.textBaseline&&(e.textBaseline=i.textBaseline)}function Kr(e,i,t,s,n){if(n.strikethrough||n.underline){let o=e.measureText(s),a=i-o.actualBoundingBoxLeft,r=i+o.actualBoundingBoxRight,l=t-o.actualBoundingBoxAscent,c=t+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=n.decorationWidth||2,e.moveTo(a,h),e.lineTo(r,h),e.stroke()}}function qr(e,i){let t=e.fillStyle;e.fillStyle=i.color,e.fillRect(i.left,i.top,i.width,i.height),e.fillStyle=t}function Tt(e,i,t,s,n,o={}){let a=F(i)?i:[i],r=o.strokeWidth>0&&o.strokeColor!=="",l,c;for(e.save(),e.font=n.string,Xr(e,o),l=0;l<a.length;++l)c=a[l],o.backdrop&&qr(e,o.backdrop),r&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),O(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,t,s,o.maxWidth)),e.fillText(c,t,s,o.maxWidth),Kr(e,t,s,c,o),s+=Number(n.lineHeight);e.restore()}function ue(e,i){let{x:t,y:s,w:n,h:o,radius:a}=i;e.arc(t+a.topLeft,s+a.topLeft,a.topLeft,1.5*z,z,!0),e.lineTo(t,s+o-a.bottomLeft),e.arc(t+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,z,W,!0),e.lineTo(t+n-a.bottomRight,s+o),e.arc(t+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,W,0,!0),e.lineTo(t+n,s+a.topRight),e.arc(t+n-a.topRight,s+a.topRight,a.topRight,0,-W,!0),e.lineTo(t+a.topLeft,s)}var Gr=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Zr=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Qr(e,i){let t=(""+e).match(Gr);if(!t||t[1]==="normal")return i*1.2;switch(e=+t[2],t[3]){case"px":return e;case"%":e/=100;break}return i*e}var Jr=e=>+e||0;function bi(e,i){let t={},s=A(i),n=s?Object.keys(i):i,o=A(e)?s?a=>D(e[a],e[i[a]]):a=>e[a]:()=>e;for(let a of n)t[a]=Jr(o(a));return t}function xs(e){return bi(e,{top:"y",right:"x",bottom:"y",left:"x"})}function Lt(e){return bi(e,["topLeft","topRight","bottomLeft","bottomRight"])}function q(e){let i=xs(e);return i.width=i.left+i.right,i.height=i.top+i.bottom,i}function j(e,i){e=e||{},i=i||I.font;let t=D(e.size,i.size);typeof t=="string"&&(t=parseInt(t,10));let s=D(e.style,i.style);s&&!(""+s).match(Zr)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let n={family:D(e.family,i.family),lineHeight:Qr(D(e.lineHeight,i.lineHeight),t),size:t,style:s,weight:D(e.weight,i.weight),string:""};return n.string=Ur(n),n}function fe(e,i,t,s){let n=!0,o,a,r;for(o=0,a=e.length;o<a;++o)if(r=e[o],r!==void 0&&(i!==void 0&&typeof r=="function"&&(r=r(i),n=!1),t!==void 0&&F(r)&&(r=r[t%r.length],n=!1),r!==void 0))return s&&!n&&(s.cacheable=!1),r}function So(e,i,t){let{min:s,max:n}=e,o=is(i,(n-s)/2),a=(r,l)=>t&&r===0?0:r+l;return{min:a(s,-Math.abs(o)),max:a(n,o)}}function yt(e,i){return Object.assign(Object.create(e),i)}function xi(e,i=[""],t,s,n=()=>e[0]){let o=t||e;typeof s>"u"&&(s=Do("_fallback",e));let a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>xi([r,...e],i,o,s)};return new Proxy(a,{deleteProperty(r,l){return delete r[l],delete r._keys,delete e[0][l],!0},get(r,l){return ko(r,l,()=>rl(l,i,e,r))},getOwnPropertyDescriptor(r,l){return Reflect.getOwnPropertyDescriptor(r._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(r,l){return eo(r).includes(l)},ownKeys(r){return eo(r)},set(r,l,c){let h=r._storage||(r._storage=n());return r[l]=h[l]=c,delete r._keys,!0}})}function jt(e,i,t,s){let n={_cacheable:!1,_proxy:e,_context:i,_subProxy:t,_stack:new Set,_descriptors:_s(e,s),setContext:o=>jt(e,o,t,s),override:o=>jt(e.override(o),i,t,s)};return new Proxy(n,{deleteProperty(o,a){return delete o[a],delete e[a],!0},get(o,a,r){return ko(o,a,()=>el(o,a,r))},getOwnPropertyDescriptor(o,a){return o._descriptors.allKeys?Reflect.has(e,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,a)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,a){return Reflect.has(e,a)},ownKeys(){return Reflect.ownKeys(e)},set(o,a,r){return e[a]=r,delete o[a],!0}})}function _s(e,i={scriptable:!0,indexable:!0}){let{_scriptable:t=i.scriptable,_indexable:s=i.indexable,_allKeys:n=i.allKeys}=e;return{allKeys:n,scriptable:t,indexable:s,isScriptable:bt(t)?t:()=>t,isIndexable:bt(s)?s:()=>s}}var tl=(e,i)=>e?e+di(i):i,ys=(e,i)=>A(i)&&e!=="adapters"&&(Object.getPrototypeOf(i)===null||i.constructor===Object);function ko(e,i,t){if(Object.prototype.hasOwnProperty.call(e,i)||i==="constructor")return e[i];let s=t();return e[i]=s,s}function el(e,i,t){let{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=e,r=s[i];return bt(r)&&a.isScriptable(i)&&(r=il(i,r,e,t)),F(r)&&r.length&&(r=sl(i,r,e,a.isIndexable)),ys(i,r)&&(r=jt(r,n,o&&o[i],a)),r}function il(e,i,t,s){let{_proxy:n,_context:o,_subProxy:a,_stack:r}=t;if(r.has(e))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+e);r.add(e);let l=i(o,a||s);return r.delete(e),ys(e,l)&&(l=vs(n._scopes,n,e,l)),l}function sl(e,i,t,s){let{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=t;if(typeof o.index<"u"&&s(e))return i[o.index%i.length];if(A(i[0])){let l=i,c=n._scopes.filter(h=>h!==l);i=[];for(let h of l){let d=vs(c,n,e,h);i.push(jt(d,o,a&&a[e],r))}}return i}function wo(e,i,t){return bt(e)?e(i,t):e}var nl=(e,i)=>e===!0?i:typeof e=="string"?_t(i,e):void 0;function ol(e,i,t,s,n){for(let o of i){let a=nl(t,o);if(a){e.add(a);let r=wo(a._fallback,t,n);if(typeof r<"u"&&r!==t&&r!==s)return r}else if(a===!1&&typeof s<"u"&&t!==s)return null}return!1}function vs(e,i,t,s){let n=i._rootScopes,o=wo(i._fallback,t,s),a=[...e,...n],r=new Set;r.add(s);let l=to(r,a,t,o||t,s);return l===null||typeof o<"u"&&o!==t&&(l=to(r,a,o,l,s),l===null)?!1:xi(Array.from(r),[""],n,o,()=>al(i,t,s))}function to(e,i,t,s,n){for(;t;)t=ol(e,i,t,s,n);return t}function al(e,i,t){let s=e._getTarget();i in s||(s[i]={});let n=s[i];return F(n)&&A(t)?t:n||{}}function rl(e,i,t,s){let n;for(let o of i)if(n=Do(tl(o,e),t),typeof n<"u")return ys(e,n)?vs(t,s,e,n):n}function Do(e,i){for(let t of i){if(!t)continue;let s=t[e];if(typeof s<"u")return s}}function eo(e){let i=e._keys;return i||(i=e._keys=ll(e._scopes)),i}function ll(e){let i=new Set;for(let t of e)for(let s of Object.keys(t).filter(n=>!n.startsWith("_")))i.add(s);return Array.from(i)}function Ms(e,i,t,s){let{iScale:n}=e,{key:o="r"}=this._parsing,a=new Array(s),r,l,c,h;for(r=0,l=s;r<l;++r)c=r+t,h=i[c],a[r]={r:n.parse(_t(h,o),c)};return a}var cl=Number.EPSILON||1e-14,ae=(e,i)=>i<e.length&&!e[i].skip&&e[i],Co=e=>e==="x"?"y":"x";function hl(e,i,t,s){let n=e.skip?i:e,o=i,a=t.skip?i:t,r=ci(o,n),l=ci(a,o),c=r/(r+l),h=l/(r+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;let d=s*c,u=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+u*(a.x-n.x),y:o.y+u*(a.y-n.y)}}}function dl(e,i,t){let s=e.length,n,o,a,r,l,c=ae(e,0);for(let h=0;h<s-1;++h)if(l=c,c=ae(e,h+1),!(!l||!c)){if(ce(i[h],0,cl)){t[h]=t[h+1]=0;continue}n=t[h]/i[h],o=t[h+1]/i[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),t[h]=n*a*i[h],t[h+1]=o*a*i[h])}}function ul(e,i,t="x"){let s=Co(t),n=e.length,o,a,r,l=ae(e,0);for(let c=0;c<n;++c){if(a=r,r=l,l=ae(e,c+1),!r)continue;let h=r[t],d=r[s];a&&(o=(h-a[t])/3,r[`cp1${t}`]=h-o,r[`cp1${s}`]=d-o*i[c]),l&&(o=(l[t]-h)/3,r[`cp2${t}`]=h+o,r[`cp2${s}`]=d+o*i[c])}}function fl(e,i="x"){let t=Co(i),s=e.length,n=Array(s).fill(0),o=Array(s),a,r,l,c=ae(e,0);for(a=0;a<s;++a)if(r=l,l=c,c=ae(e,a+1),!!l){if(c){let h=c[i]-l[i];n[a]=h!==0?(c[t]-l[t])/h:0}o[a]=r?c?lt(n[a-1])!==lt(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}dl(e,n,o),ul(e,o,i)}function ni(e,i,t){return Math.max(Math.min(e,t),i)}function gl(e,i){let t,s,n,o,a,r=dt(e[0],i);for(t=0,s=e.length;t<s;++t)a=o,o=r,r=t<s-1&&dt(e[t+1],i),o&&(n=e[t],a&&(n.cp1x=ni(n.cp1x,i.left,i.right),n.cp1y=ni(n.cp1y,i.top,i.bottom)),r&&(n.cp2x=ni(n.cp2x,i.left,i.right),n.cp2y=ni(n.cp2y,i.top,i.bottom)))}function Po(e,i,t,s,n){let o,a,r,l;if(i.spanGaps&&(e=e.filter(c=>!c.skip)),i.cubicInterpolationMode==="monotone")fl(e,n);else{let c=s?e[e.length-1]:e[0];for(o=0,a=e.length;o<a;++o)r=e[o],l=hl(c,r,e[Math.min(o+1,a-(s?0:1))%a],i.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,c=r}i.capBezierPoints&&gl(e,t)}function _i(){return typeof window<"u"&&typeof document<"u"}function yi(e){let i=e.parentNode;return i&&i.toString()==="[object ShadowRoot]"&&(i=i.host),i}function hi(e,i,t){let s;return typeof e=="string"?(s=parseInt(e,10),e.indexOf("%")!==-1&&(s=s/100*i.parentNode[t])):s=e,s}var vi=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function pl(e,i){return vi(e).getPropertyValue(i)}var ml=["top","right","bottom","left"];function Ht(e,i,t){let s={};t=t?"-"+t:"";for(let n=0;n<4;n++){let o=ml[n];s[o]=parseFloat(e[i+"-"+o+t])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}var bl=(e,i,t)=>(e>0||i>0)&&(!t||!t.shadowRoot);function xl(e,i){let t=e.touches,s=t&&t.length?t[0]:e,{offsetX:n,offsetY:o}=s,a=!1,r,l;if(bl(n,o,e.target))r=n,l=o;else{let c=i.getBoundingClientRect();r=s.clientX-c.left,l=s.clientY-c.top,a=!0}return{x:r,y:l,box:a}}function Rt(e,i){if("native"in e)return e;let{canvas:t,currentDevicePixelRatio:s}=i,n=vi(t),o=n.boxSizing==="border-box",a=Ht(n,"padding"),r=Ht(n,"border","width"),{x:l,y:c,box:h}=xl(e,t),d=a.left+(h&&r.left),u=a.top+(h&&r.top),{width:f,height:g}=i;return o&&(f-=a.width+r.width,g-=a.height+r.height),{x:Math.round((l-d)/f*t.width/s),y:Math.round((c-u)/g*t.height/s)}}function _l(e,i,t){let s,n;if(i===void 0||t===void 0){let o=e&&yi(e);if(!o)i=e.clientWidth,t=e.clientHeight;else{let a=o.getBoundingClientRect(),r=vi(o),l=Ht(r,"border","width"),c=Ht(r,"padding");i=a.width-c.width-l.width,t=a.height-c.height-l.height,s=hi(r.maxWidth,o,"clientWidth"),n=hi(r.maxHeight,o,"clientHeight")}}return{width:i,height:t,maxWidth:s||li,maxHeight:n||li}}var oi=e=>Math.round(e*10)/10;function Oo(e,i,t,s){let n=vi(e),o=Ht(n,"margin"),a=hi(n.maxWidth,e,"clientWidth")||li,r=hi(n.maxHeight,e,"clientHeight")||li,l=_l(e,i,t),{width:c,height:h}=l;if(n.boxSizing==="content-box"){let u=Ht(n,"border","width"),f=Ht(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=oi(Math.min(c,a,l.maxWidth)),h=oi(Math.min(h,r,l.maxHeight)),c&&!h&&(h=oi(c/2)),(i!==void 0||t!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=oi(Math.floor(h*s))),{width:c,height:h}}function Ss(e,i,t){let s=i||1,n=Math.floor(e.height*s),o=Math.floor(e.width*s);e.height=Math.floor(e.height),e.width=Math.floor(e.width);let a=e.canvas;return a.style&&(t||!a.style.height&&!a.style.width)&&(a.style.height=`${e.height}px`,a.style.width=`${e.width}px`),e.currentDevicePixelRatio!==s||a.height!==n||a.width!==o?(e.currentDevicePixelRatio=s,a.height=n,a.width=o,e.ctx.setTransform(s,0,0,s,0,0),!0):!1}var Ao=function(){let e=!1;try{let i={get passive(){return e=!0,!1}};_i()&&(window.addEventListener("test",null,i),window.removeEventListener("test",null,i))}catch{}return e}();function ks(e,i){let t=pl(e,i),s=t&&t.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Pt(e,i,t,s){return{x:e.x+t*(i.x-e.x),y:e.y+t*(i.y-e.y)}}function To(e,i,t,s){return{x:e.x+t*(i.x-e.x),y:s==="middle"?t<.5?e.y:i.y:s==="after"?t<1?e.y:i.y:t>0?i.y:e.y}}function Lo(e,i,t,s){let n={x:e.cp2x,y:e.cp2y},o={x:i.cp1x,y:i.cp1y},a=Pt(e,n,t),r=Pt(n,o,t),l=Pt(o,i,t),c=Pt(a,r,t),h=Pt(r,l,t);return Pt(c,h,t)}var yl=function(e,i){return{x(t){return e+e+i-t},setWidth(t){i=t},textAlign(t){return t==="center"?t:t==="right"?"left":"right"},xPlus(t,s){return t-s},leftForLtr(t,s){return t-s}}},vl=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,i){return e+i},leftForLtr(e,i){return e}}};function Yt(e,i,t){return e?yl(i,t):vl()}function ws(e,i){let t,s;(i==="ltr"||i==="rtl")&&(t=e.canvas.style,s=[t.getPropertyValue("direction"),t.getPropertyPriority("direction")],t.setProperty("direction",i,"important"),e.prevTextDirection=s)}function Ds(e,i){i!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",i[0],i[1]))}function Ro(e){return e==="angle"?{between:he,compare:Br,normalize:J}:{between:ft,compare:(i,t)=>i-t,normalize:i=>i}}function io({start:e,end:i,count:t,loop:s,style:n}){return{start:e%t,end:i%t,loop:s&&(i-e+1)%t===0,style:n}}function Ml(e,i,t){let{property:s,start:n,end:o}=t,{between:a,normalize:r}=Ro(s),l=i.length,{start:c,end:h,loop:d}=e,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&a(r(i[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:e.style}}function Cs(e,i,t){if(!t)return[e];let{property:s,start:n,end:o}=t,a=i.length,{compare:r,between:l,normalize:c}=Ro(s),{start:h,end:d,loop:u,style:f}=Ml(e,i,t),g=[],p=!1,m=null,b,x,y,M=()=>l(n,y,b)&&r(n,y)!==0,_=()=>r(o,b)===0||l(o,y,b),v=()=>p||M(),k=()=>!p||_();for(let S=h,w=h;S<=d;++S)x=i[S%a],!x.skip&&(b=c(x[s]),b!==y&&(p=l(b,n,o),m===null&&v()&&(m=r(b,n)===0?S:w),m!==null&&k()&&(g.push(io({start:m,end:S,loop:u,count:a,style:f})),m=null),w=S,y=b));return m!==null&&g.push(io({start:m,end:d,loop:u,count:a,style:f})),g}function Ps(e,i){let t=[],s=e.segments;for(let n=0;n<s.length;n++){let o=Cs(s[n],e.points,i);o.length&&t.push(...o)}return t}function Sl(e,i,t,s){let n=0,o=i-1;if(t&&!s)for(;n<i&&!e[n].skip;)n++;for(;n<i&&e[n].skip;)n++;for(n%=i,t&&(o+=n);o>n&&e[o%i].skip;)o--;return o%=i,{start:n,end:o}}function kl(e,i,t,s){let n=e.length,o=[],a=i,r=e[i],l;for(l=i+1;l<=t;++l){let c=e[l%n];c.skip||c.stop?r.skip||(s=!1,o.push({start:i%n,end:(l-1)%n,loop:s}),i=a=c.stop?l:null):(a=l,r.skip&&(i=l)),r=c}return a!==null&&o.push({start:i%n,end:a%n,loop:s}),o}function Eo(e,i){let t=e.points,s=e.options.spanGaps,n=t.length;if(!n)return[];let o=!!e._loop,{start:a,end:r}=Sl(t,n,o,s);if(s===!0)return so(e,[{start:a,end:r,loop:o}],t,i);let l=r<a?r+n:r,c=!!e._fullLoop&&a===0&&r===n-1;return so(e,kl(t,a,l,c),t,i)}function so(e,i,t,s){return!s||!s.setContext||!t?i:wl(e,i,t,s)}function wl(e,i,t,s){let n=e._chart.getContext(),o=no(e.options),{_datasetIndex:a,options:{spanGaps:r}}=e,l=t.length,c=[],h=o,d=i[0].start,u=d;function f(g,p,m,b){let x=r?-1:1;if(g!==p){for(g+=l;t[g%l].skip;)g-=x;for(;t[p%l].skip;)p+=x;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(let g of i){d=r?d:g.start;let p=t[d%l],m;for(u=d+1;u<=g.end;u++){let b=t[u%l];m=no(s.setContext(yt(n,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:a}))),Dl(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}function no(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function Dl(e,i){if(!i)return!1;let t=[],s=function(n,o){return gs(o)?(t.includes(o)||t.push(o),t.indexOf(o)):o};return JSON.stringify(e,s)!==JSON.stringify(i,s)}function ai(e,i,t){return e.options.clip?e[t]:i[t]}function Cl(e,i){let{xScale:t,yScale:s}=e;return t&&s?{left:ai(t,i,"left"),right:ai(t,i,"right"),top:ai(s,i,"top"),bottom:ai(s,i,"bottom")}:i}function Os(e,i){let t=i._clip;if(t.disabled)return!1;let s=Cl(i,e.chartArea);return{left:t.left===!1?0:s.left-(t.left===!0?0:t.left),right:t.right===!1?e.width:s.right+(t.right===!0?0:t.right),top:t.top===!1?0:s.top-(t.top===!0?0:t.top),bottom:t.bottom===!1?e.height:s.bottom+(t.bottom===!0?0:t.bottom)}}var Vs=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(i,t,s,n){let o=t.listeners[n],a=t.duration;o.forEach(r=>r({chart:i,initial:t.initial,numSteps:a,currentStep:Math.min(s-t.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=hs.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(i=Date.now()){let t=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;let o=s.items,a=o.length-1,r=!1,l;for(;a>=0;--a)l=o[a],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(i),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,i,"progress")),o.length||(s.running=!1,this._notify(n,s,i,"complete"),s.initial=!1),t+=o.length}),this._lastDate=i,t===0&&(this._running=!1)}_getAnims(i){let t=this._charts,s=t.get(i);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(i,s)),s}listen(i,t,s){this._getAnims(i).listeners[t].push(s)}add(i,t){!t||!t.length||this._getAnims(i).items.push(...t)}has(i){return this._getAnims(i).items.length>0}start(i){let t=this._charts.get(i);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(i){if(!this._running)return!1;let t=this._charts.get(i);return!(!t||!t.running||!t.items.length)}stop(i){let t=this._charts.get(i);if(!t||!t.items.length)return;let s=t.items,n=s.length-1;for(;n>=0;--n)s[n].cancel();t.items=[],this._notify(i,t,Date.now(),"complete")}remove(i){return this._charts.delete(i)}},vt=new Vs,Io="transparent",Pl={boolean(e,i,t){return t>.5?i:e},color(e,i,t){let s=ps(e||Io),n=s.valid&&ps(i||Io);return n&&n.valid?n.mix(s,t).hexString():i},number(e,i,t){return e+(i-e)*t}},Ws=class{constructor(i,t,s,n){let o=t[s];n=fe([i.to,n,o,i.from]);let a=fe([i.from,o,n]);this._active=!0,this._fn=i.fn||Pl[i.type||typeof a],this._easing=ne[i.easing]||ne.linear,this._start=Math.floor(Date.now()+(i.delay||0)),this._duration=this._total=Math.floor(i.duration),this._loop=!!i.loop,this._target=t,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(i,t,s){if(this._active){this._notify(!1);let n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,i.duration)),this._total+=o,this._loop=!!i.loop,this._to=fe([i.to,t,n,i.from]),this._from=fe([i.from,n,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(i){let t=i-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to,l;if(this._active=o!==r&&(a||t<s),!this._active){this._target[n]=r,this._notify(!0);return}if(t<0){this._target[n]=o;return}l=t/s%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,r,l)}wait(){let i=this._promises||(this._promises=[]);return new Promise((t,s)=>{i.push({res:t,rej:s})})}_notify(i){let t=i?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][t]()}},Ai=class{constructor(i,t){this._chart=i,this._properties=new Map,this.configure(t)}configure(i){if(!A(i))return;let t=Object.keys(I.animation),s=this._properties;Object.getOwnPropertyNames(i).forEach(n=>{let o=i[n];if(!A(o))return;let a={};for(let r of t)a[r]=o[r];(F(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(i,t){let s=t.options,n=Al(i,s);if(!n)return[];let o=this._createAnimations(n,s);return s.$shared&&Ol(i.options.$animations,s).then(()=>{i.options=s},()=>{}),o}_createAnimations(i,t){let s=this._properties,n=[],o=i.$animations||(i.$animations={}),a=Object.keys(t),r=Date.now(),l;for(l=a.length-1;l>=0;--l){let c=a[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(i,t));continue}let h=t[c],d=o[c],u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,r);continue}else d.cancel();if(!u||!u.duration){i[c]=h;continue}o[c]=d=new Ws(u,i,c,h),n.push(d)}return n}update(i,t){if(this._properties.size===0){Object.assign(i,t);return}let s=this._createAnimations(i,t);if(s.length)return vt.add(this._chart,s),!0}};function Ol(e,i){let t=[],s=Object.keys(i);for(let n=0;n<s.length;n++){let o=e[s[n]];o&&o.active()&&t.push(o.wait())}return Promise.all(t)}function Al(e,i){if(!i)return;let t=e.options;if(!t){e.options=i;return}return t.$shared&&(e.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t}function Fo(e,i){let t=e&&e.options||{},s=t.reverse,n=t.min===void 0?i:0,o=t.max===void 0?i:0;return{start:s?o:n,end:s?n:o}}function Tl(e,i,t){if(t===!1)return!1;let s=Fo(e,t),n=Fo(i,t);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Ll(e){let i,t,s,n;return A(e)?(i=e.top,t=e.right,s=e.bottom,n=e.left):i=t=s=n=e,{top:i,right:t,bottom:s,left:n,disabled:e===!1}}function Ia(e,i){let t=[],s=e._getSortedDatasetMetas(i),n,o;for(n=0,o=s.length;n<o;++n)t.push(s[n].index);return t}function zo(e,i,t,s={}){let n=e.keys,o=s.mode==="single",a,r,l,c;if(i===null)return;let h=!1;for(a=0,r=n.length;a<r;++a){if(l=+n[a],l===t){if(h=!0,s.all)continue;break}c=e.values[l],V(c)&&(o||i===0||lt(i)===lt(c))&&(i+=c)}return!h&&!s.all?0:i}function Rl(e,i){let{iScale:t,vScale:s}=i,n=t.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",a=Object.keys(e),r=new Array(a.length),l,c,h;for(l=0,c=a.length;l<c;++l)h=a[l],r[l]={[n]:h,[o]:e[h]};return r}function As(e,i){let t=e&&e.options.stacked;return t||t===void 0&&i.stack!==void 0}function El(e,i,t){return`${e.id}.${i.id}.${t.stack||t.type}`}function Il(e){let{min:i,max:t,minDefined:s,maxDefined:n}=e.getUserBounds();return{min:s?i:Number.NEGATIVE_INFINITY,max:n?t:Number.POSITIVE_INFINITY}}function Fl(e,i,t){let s=e[i]||(e[i]={});return s[t]||(s[t]={})}function Bo(e,i,t,s){for(let n of i.getMatchingVisibleMetas(s).reverse()){let o=e[n.index];if(t&&o>0||!t&&o<0)return n.index}return null}function No(e,i){let{chart:t,_cachedMeta:s}=e,n=t._stacks||(t._stacks={}),{iScale:o,vScale:a,index:r}=s,l=o.axis,c=a.axis,h=El(o,a,s),d=i.length,u;for(let f=0;f<d;++f){let g=i[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=Fl(n,h,p),u[r]=m,u._top=Bo(u,a,!0,s.type),u._bottom=Bo(u,a,!1,s.type);let x=u._visualValues||(u._visualValues={});x[r]=m}}function Ts(e,i){let t=e.scales;return Object.keys(t).filter(s=>t[s].axis===i).shift()}function zl(e,i){return yt(e,{active:!1,dataset:void 0,datasetIndex:i,index:i,mode:"default",type:"dataset"})}function Bl(e,i,t){return yt(e,{active:!1,dataIndex:i,parsed:void 0,raw:void 0,element:t,index:i,mode:"default",type:"data"})}function Le(e,i){let t=e.controller.index,s=e.vScale&&e.vScale.axis;if(s){i=i||e._parsed;for(let n of i){let o=n._stacks;if(!o||o[s]===void 0||o[s][t]===void 0)return;delete o[s][t],o[s]._visualValues!==void 0&&o[s]._visualValues[t]!==void 0&&delete o[s]._visualValues[t]}}}var Ls=e=>e==="reset"||e==="none",Vo=(e,i)=>i?e:Object.assign({},e),Nl=(e,i,t)=>e&&!i.hidden&&i._stacked&&{keys:Ia(t,!0),values:null},It=(()=>{class e{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,s){this.chart=t,this._ctx=t.ctx,this.index=s,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=As(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Le(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,s=this._cachedMeta,n=this.getDataset(),o=(u,f,g,p)=>u==="x"?f:u==="r"?p:g,a=s.xAxisID=D(n.xAxisID,Ts(t,"x")),r=s.yAxisID=D(n.yAxisID,Ts(t,"y")),l=s.rAxisID=D(n.rAxisID,Ts(t,"r")),c=s.indexAxis,h=s.iAxisID=o(c,a,r,l),d=s.vAxisID=o(c,r,a,l);s.xScale=this.getScaleForId(a),s.yScale=this.getScaleForId(r),s.rScale=this.getScaleForId(l),s.iScale=this.getScaleForId(h),s.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let s=this._cachedMeta;return t===s.iScale?s.vScale:s.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&ls(this._data,this),t._stacked&&Le(t)}_dataCheck(){let t=this.getDataset(),s=t.data||(t.data=[]),n=this._data;if(A(s)){let o=this._cachedMeta;this._data=Rl(s,o)}else if(n!==s){if(n){ls(n,this);let o=this._cachedMeta;Le(o),o._parsed=[]}s&&Object.isExtensible(s)&&mo(s,this),this._syncList=[],this._data=s}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let s=this._cachedMeta,n=this.getDataset(),o=!1;this._dataCheck();let a=s._stacked;s._stacked=As(s.vScale,s),s.stack!==n.stack&&(o=!0,Le(s),s.stack=n.stack),this._resyncElements(t),(o||a!==s._stacked)&&(No(this,s._parsed),s._stacked=As(s.vScale,s))}configure(){let t=this.chart.config,s=t.datasetScopeKeys(this._type),n=t.getOptionScopes(this.getDataset(),s,!0);this.options=t.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,s){let{_cachedMeta:n,_data:o}=this,{iScale:a,_stacked:r}=n,l=a.axis,c=t===0&&s===o.length?!0:n._sorted,h=t>0&&n._parsed[t-1],d,u,f;if(this._parsing===!1)n._parsed=o,n._sorted=!0,f=o;else{F(o[t])?f=this.parseArrayData(n,o,t,s):A(o[t])?f=this.parseObjectData(n,o,t,s):f=this.parsePrimitiveData(n,o,t,s);let g=()=>u[l]===null||h&&u[l]<h[l];for(d=0;d<s;++d)n._parsed[d+t]=u=f[d],c&&(g()&&(c=!1),h=u);n._sorted=c}r&&No(this,f)}parsePrimitiveData(t,s,n,o){let{iScale:a,vScale:r}=t,l=a.axis,c=r.axis,h=a.getLabels(),d=a===r,u=new Array(o),f,g,p;for(f=0,g=o;f<g;++f)p=f+n,u[f]={[l]:d||a.parse(h[p],p),[c]:r.parse(s[p],p)};return u}parseArrayData(t,s,n,o){let{xScale:a,yScale:r}=t,l=new Array(o),c,h,d,u;for(c=0,h=o;c<h;++c)d=c+n,u=s[d],l[c]={x:a.parse(u[0],d),y:r.parse(u[1],d)};return l}parseObjectData(t,s,n,o){let{xScale:a,yScale:r}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h=new Array(o),d,u,f,g;for(d=0,u=o;d<u;++d)f=d+n,g=s[f],h[d]={x:a.parse(_t(g,l),f),y:r.parse(_t(g,c),f)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,s,n){let o=this.chart,a=this._cachedMeta,r=s[t.axis],l={keys:Ia(o,!0),values:s._stacks[t.axis]._visualValues};return zo(l,r,a.index,{mode:n})}updateRangeFromParsed(t,s,n,o){let a=n[s.axis],r=a===null?NaN:a,l=o&&n._stacks[s.axis];o&&l&&(o.values=l,r=zo(o,a,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,s){let n=this._cachedMeta,o=n._parsed,a=n._sorted&&t===n.iScale,r=o.length,l=this._getOtherScale(t),c=Nl(s,n,this.chart),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:u}=Il(l),f,g;function p(){g=o[f];let m=g[l.axis];return!V(g[t.axis])||d>m||u<m}for(f=0;f<r&&!(!p()&&(this.updateRangeFromParsed(h,t,g,c),a));++f);if(a){for(f=r-1;f>=0;--f)if(!p()){this.updateRangeFromParsed(h,t,g,c);break}}return h}getAllParsedValues(t){let s=this._cachedMeta._parsed,n=[],o,a,r;for(o=0,a=s.length;o<a;++o)r=s[o][t.axis],V(r)&&n.push(r);return n}getMaxOverflow(){return!1}getLabelAndValue(t){let s=this._cachedMeta,n=s.iScale,o=s.vScale,a=this.getParsed(t);return{label:n?""+n.getLabelForValue(a[n.axis]):"",value:o?""+o.getLabelForValue(a[o.axis]):""}}_update(t){let s=this._cachedMeta;this.update(t||"default"),s._clip=Ll(D(this.options.clip,Tl(s.xScale,s.yScale,this.getMaxOverflow())))}update(t){}draw(){let t=this._ctx,s=this.chart,n=this._cachedMeta,o=n.data||[],a=s.chartArea,r=[],l=this._drawStart||0,c=this._drawCount||o.length-l,h=this.options.drawActiveElementsOnTop,d;for(n.dataset&&n.dataset.draw(t,a,l,c),d=l;d<l+c;++d){let u=o[d];u.hidden||(u.active&&h?r.push(u):u.draw(t,a))}for(d=0;d<r.length;++d)r[d].draw(t,a)}getStyle(t,s){let n=s?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(t||0,n)}getContext(t,s,n){let o=this.getDataset(),a;if(t>=0&&t<this._cachedMeta.data.length){let r=this._cachedMeta.data[t];a=r.$context||(r.$context=Bl(this.getContext(),t,r)),a.parsed=this.getParsed(t),a.raw=o.data[t],a.index=a.dataIndex=t}else a=this.$context||(this.$context=zl(this.chart.getContext(),this.index)),a.dataset=o,a.index=a.datasetIndex=this.index;return a.active=!!s,a.mode=n,a}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,s){return this._resolveElementOptions(this.dataElementType.id,s,t)}_resolveElementOptions(t,s="default",n){let o=s==="active",a=this._cachedDataOpts,r=t+"-"+s,l=a[r],c=this.enableOptionSharing&&le(n);if(l)return Vo(l,c);let h=this.chart.config,d=h.datasetElementScopeKeys(this._type,t),u=o?[`${t}Hover`,"hover",t,""]:[t,""],f=h.getOptionScopes(this.getDataset(),d),g=Object.keys(I.elements[t]),p=()=>this.getContext(n,o,s),m=h.resolveNamedOptions(f,g,p,u);return m.$shared&&(m.$shared=c,a[r]=Object.freeze(Vo(m,c))),m}_resolveAnimations(t,s,n){let o=this.chart,a=this._cachedDataOpts,r=`animation-${s}`,l=a[r];if(l)return l;let c;if(o.options.animation!==!1){let d=this.chart.config,u=d.datasetAnimationScopeKeys(this._type,s),f=d.getOptionScopes(this.getDataset(),u);c=d.createResolver(f,this.getContext(t,n,s))}let h=new Ai(o,c&&c.animations);return c&&c._cacheable&&(a[r]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,s){return!s||Ls(t)||this.chart._animationsDisabled}_getSharedOptions(t,s){let n=this.resolveDataElementOptions(t,s),o=this._sharedOptions,a=this.getSharedOptions(n),r=this.includeOptions(s,a)||a!==o;return this.updateSharedOptions(a,s,n),{sharedOptions:a,includeOptions:r}}updateElement(t,s,n,o){Ls(o)?Object.assign(t,n):this._resolveAnimations(s,o).update(t,n)}updateSharedOptions(t,s,n){t&&!Ls(s)&&this._resolveAnimations(void 0,s).update(t,n)}_setStyle(t,s,n,o){t.active=o;let a=this.getStyle(s,o);this._resolveAnimations(s,n,o).update(t,{options:!o&&this.getSharedOptions(a)||a})}removeHoverStyle(t,s,n){this._setStyle(t,n,"active",!1)}setHoverStyle(t,s,n){this._setStyle(t,n,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let s=this._data,n=this._cachedMeta.data;for(let[l,c,h]of this._syncList)this[l](c,h);this._syncList=[];let o=n.length,a=s.length,r=Math.min(a,o);r&&this.parse(0,r),a>o?this._insertElements(o,a-o,t):a<o&&this._removeElements(a,o-a)}_insertElements(t,s,n=!0){let o=this._cachedMeta,a=o.data,r=t+s,l,c=h=>{for(h.length+=s,l=h.length-1;l>=r;l--)h[l]=h[l-s]};for(c(a),l=t;l<r;++l)a[l]=new this.dataElementType;this._parsing&&c(o._parsed),this.parse(t,s),n&&this.updateElements(a,t,s,"reset")}updateElements(t,s,n,o){}_removeElements(t,s){let n=this._cachedMeta;if(this._parsing){let o=n._parsed.splice(t,s);n._stacked&&Le(n,o)}n.data.splice(t,s)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[s,n,o]=t;this[s](n,o)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,s){s&&this._sync(["_removeElements",t,s]);let n=arguments.length-2;n&&this._sync(["_insertElements",t,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}return e})();function Vl(e,i){if(!e._cache.$bar){let t=e.getMatchingVisibleMetas(i),s=[];for(let n=0,o=t.length;n<o;n++)s=s.concat(t[n].controller.getAllParsedValues(e));e._cache.$bar=cs(s.sort((n,o)=>n-o))}return e._cache.$bar}function Wl(e){let i=e.iScale,t=Vl(i,e.type),s=i._length,n,o,a,r,l=()=>{a===32767||a===-32768||(le(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=t.length;n<o;++n)a=i.getPixelForValue(t[n]),l();for(r=void 0,n=0,o=i.ticks.length;n<o;++n)a=i.getPixelForTick(n),l();return s}function Hl(e,i,t,s){let n=t.barThickness,o,a;return O(n)?(o=i.min*t.categoryPercentage,a=t.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:i.pixels[e]-o/2}}function jl(e,i,t,s){let n=i.pixels,o=n[e],a=e>0?n[e-1]:null,r=e<n.length-1?n[e+1]:null,l=t.categoryPercentage;a===null&&(a=o-(r===null?i.end-i.start:r-o)),r===null&&(r=o+o-a);let c=o-(o-Math.min(a,r))/2*l;return{chunk:Math.abs(r-a)/2*l/s,ratio:t.barPercentage,start:c}}function $l(e,i,t,s){let n=t.parse(e[0],s),o=t.parse(e[1],s),a=Math.min(n,o),r=Math.max(n,o),l=a,c=r;Math.abs(a)>Math.abs(r)&&(l=r,c=a),i[t.axis]=c,i._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}function Fa(e,i,t,s){return F(e)?$l(e,i,t,s):i[t.axis]=t.parse(e,s),i}function Wo(e,i,t,s){let n=e.iScale,o=e.vScale,a=n.getLabels(),r=n===o,l=[],c,h,d,u;for(c=t,h=t+s;c<h;++c)u=i[c],d={},d[n.axis]=r||n.parse(a[c],c),l.push(Fa(u,d,o,c));return l}function Rs(e){return e&&e.barStart!==void 0&&e.barEnd!==void 0}function Yl(e,i,t){return e!==0?lt(e):(i.isHorizontal()?1:-1)*(i.min>=t?1:-1)}function Ul(e){let i,t,s,n,o;return e.horizontal?(i=e.base>e.x,t="left",s="right"):(i=e.base<e.y,t="bottom",s="top"),i?(n="end",o="start"):(n="start",o="end"),{start:t,end:s,reverse:i,top:n,bottom:o}}function Xl(e,i,t,s){let n=i.borderSkipped,o={};if(!n){e.borderSkipped=o;return}if(n===!0){e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:a,end:r,reverse:l,top:c,bottom:h}=Ul(e);n==="middle"&&t&&(e.enableBorderRadius=!0,(t._top||0)===s?n=c:(t._bottom||0)===s?n=h:(o[Ho(h,a,r,l)]=!0,n=c)),o[Ho(n,a,r,l)]=!0,e.borderSkipped=o}function Ho(e,i,t,s){return s?(e=Kl(e,i,t),e=jo(e,t,i)):e=jo(e,i,t),e}function Kl(e,i,t){return e===i?t:e===t?i:e}function jo(e,i,t){return e==="start"?i:e==="end"?t:e}function ql(e,{inflateAmount:i},t){e.inflateAmount=i==="auto"?t===1?.33:0:i}var Gl=(()=>{class e extends It{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,s,n,o){return Wo(t,s,n,o)}parseArrayData(t,s,n,o){return Wo(t,s,n,o)}parseObjectData(t,s,n,o){let{iScale:a,vScale:r}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h=a.axis==="x"?l:c,d=r.axis==="x"?l:c,u=[],f,g,p,m;for(f=n,g=n+o;f<g;++f)m=s[f],p={},p[a.axis]=a.parse(_t(m,h),f),u.push(Fa(_t(m,d),p,r,f));return u}updateRangeFromParsed(t,s,n,o){super.updateRangeFromParsed(t,s,n,o);let a=n._custom;a&&s===this._cachedMeta.vScale&&(t.min=Math.min(t.min,a.min),t.max=Math.max(t.max,a.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let s=this._cachedMeta,{iScale:n,vScale:o}=s,a=this.getParsed(t),r=a._custom,l=Rs(r)?"["+r.start+", "+r.end+"]":""+o.getLabelForValue(a[o.axis]);return{label:""+n.getLabelForValue(a[n.axis]),value:l}}initialize(){this.enableOptionSharing=!0,super.initialize();let t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){let s=this._cachedMeta;this.updateElements(s.data,0,s.data.length,t)}updateElements(t,s,n,o){let a=o==="reset",{index:r,_cachedMeta:{vScale:l}}=this,c=l.getBasePixel(),h=l.isHorizontal(),d=this._getRuler(),{sharedOptions:u,includeOptions:f}=this._getSharedOptions(s,o);for(let g=s;g<s+n;g++){let p=this.getParsed(g),m=a||O(p[l.axis])?{base:c,head:c}:this._calculateBarValuePixels(g),b=this._calculateBarIndexPixels(g,d),x=(p._stacks||{})[l.axis],y={horizontal:h,base:m.base,enableBorderRadius:!x||Rs(p._custom)||r===x._top||r===x._bottom,x:h?m.head:b.center,y:h?b.center:m.head,height:h?b.size:Math.abs(m.size),width:h?Math.abs(m.size):b.size};f&&(y.options=u||this.resolveDataElementOptions(g,t[g].active?"active":o));let M=y.options||t[g].options;Xl(y,M,x,r),ql(y,M,d.ratio),this.updateElement(t[g],g,y,o)}}_getStacks(t,s){let{iScale:n}=this._cachedMeta,o=n.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),a=n.options.stacked,r=[],l=this._cachedMeta.controller.getParsed(s),c=l&&l[n.axis],h=d=>{let u=d._parsed.find(g=>g[n.axis]===c),f=u&&u[d.vScale.axis];if(O(f)||isNaN(f))return!0};for(let d of o)if(!(s!==void 0&&h(d))&&((a===!1||r.indexOf(d.stack)===-1||a===void 0&&d.stack===void 0)&&r.push(d.stack),d.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,s,n){let o=this._getStacks(t,n),a=s!==void 0?o.indexOf(s):-1;return a===-1?o.length-1:a}_getRuler(){let t=this.options,s=this._cachedMeta,n=s.iScale,o=[],a,r;for(a=0,r=s.data.length;a<r;++a)o.push(n.getPixelForValue(this.getParsed(a)[n.axis],a));let l=t.barThickness;return{min:l||Wl(s),pixels:o,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:t.grouped,ratio:l?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:s,_stacked:n,index:o},options:{base:a,minBarLength:r}}=this,l=a||0,c=this.getParsed(t),h=c._custom,d=Rs(h),u=c[s.axis],f=0,g=n?this.applyStack(s,c,n):u,p,m;g!==u&&(f=g-u,g=u),d&&(u=h.barStart,g=h.barEnd-h.barStart,u!==0&&lt(u)!==lt(h.barEnd)&&(f=0),f+=u);let b=!O(a)&&!d?a:f,x=s.getPixelForValue(b);if(this.chart.getDataVisibility(t)?p=s.getPixelForValue(f+g):p=x,m=p-x,Math.abs(m)<r){m=Yl(m,s,l)*r,u===l&&(x-=m/2);let y=s.getPixelForDecimal(0),M=s.getPixelForDecimal(1),_=Math.min(y,M),v=Math.max(y,M);x=Math.max(Math.min(x,v),_),p=x+m,n&&!d&&(c._stacks[s.axis]._visualValues[o]=s.getValueForPixel(p)-s.getValueForPixel(x))}if(x===s.getPixelForValue(l)){let y=lt(m)*s.getLineWidthForValue(l)/2;x+=y,m-=y}return{size:m,base:x,head:p,center:p+m/2}}_calculateBarIndexPixels(t,s){let n=s.scale,o=this.options,a=o.skipNull,r=D(o.maxBarThickness,1/0),l,c;if(s.grouped){let h=a?this._getStackCount(t):s.stackCount,d=o.barThickness==="flex"?jl(t,s,o,h):Hl(t,s,o,h),u=this._getStackIndex(this.index,this._cachedMeta.stack,a?t:void 0);l=d.start+d.chunk*u+d.chunk/2,c=Math.min(r,d.chunk*d.ratio)}else l=n.getPixelForValue(this.getParsed(t)[n.axis],t),c=Math.min(r,s.min*s.ratio);return{base:l-c/2,head:l+c/2,center:l,size:c}}draw(){let t=this._cachedMeta,s=t.vScale,n=t.data,o=n.length,a=0;for(;a<o;++a)this.getParsed(a)[s.axis]!==null&&!n[a].hidden&&n[a].draw(this._ctx)}}return e})(),Zl=(()=>{class e extends It{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,s,n,o){let a=super.parsePrimitiveData(t,s,n,o);for(let r=0;r<a.length;r++)a[r]._custom=this.resolveDataElementOptions(r+n).radius;return a}parseArrayData(t,s,n,o){let a=super.parseArrayData(t,s,n,o);for(let r=0;r<a.length;r++){let l=s[n+r];a[r]._custom=D(l[2],this.resolveDataElementOptions(r+n).radius)}return a}parseObjectData(t,s,n,o){let a=super.parseObjectData(t,s,n,o);for(let r=0;r<a.length;r++){let l=s[n+r];a[r]._custom=D(l&&l.r&&+l.r,this.resolveDataElementOptions(r+n).radius)}return a}getMaxOverflow(){let t=this._cachedMeta.data,s=0;for(let n=t.length-1;n>=0;--n)s=Math.max(s,t[n].size(this.resolveDataElementOptions(n))/2);return s>0&&s}getLabelAndValue(t){let s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:a}=s,r=this.getParsed(t),l=o.getLabelForValue(r.x),c=a.getLabelForValue(r.y),h=r._custom;return{label:n[t]||"",value:"("+l+", "+c+(h?", "+h:"")+")"}}update(t){let s=this._cachedMeta.data;this.updateElements(s,0,s.length,t)}updateElements(t,s,n,o){let a=o==="reset",{iScale:r,vScale:l}=this._cachedMeta,{sharedOptions:c,includeOptions:h}=this._getSharedOptions(s,o),d=r.axis,u=l.axis;for(let f=s;f<s+n;f++){let g=t[f],p=!a&&this.getParsed(f),m={},b=m[d]=a?r.getPixelForDecimal(.5):r.getPixelForValue(p[d]),x=m[u]=a?l.getBasePixel():l.getPixelForValue(p[u]);m.skip=isNaN(b)||isNaN(x),h&&(m.options=c||this.resolveDataElementOptions(f,g.active?"active":o),a&&(m.options.radius=0)),this.updateElement(g,f,m,o)}}resolveDataElementOptions(t,s){let n=this.getParsed(t),o=super.resolveDataElementOptions(t,s);o.$shared&&(o=Object.assign({},o,{$shared:!1}));let a=o.radius;return s!=="active"&&(o.radius=0),o.radius+=D(n&&n._custom,a),o}}return e})();function Ql(e,i,t){let s=1,n=1,o=0,a=0;if(i<B){let r=e,l=r+i,c=Math.cos(r),h=Math.sin(r),d=Math.cos(l),u=Math.sin(l),f=(y,M,_)=>he(y,r,l,!0)?1:Math.max(M,M*t,_,_*t),g=(y,M,_)=>he(y,r,l,!0)?-1:Math.min(M,M*t,_,_*t),p=f(0,c,d),m=f(W,h,u),b=g(z,c,d),x=g(z+W,h,u);s=(p-b)/2,n=(m-x)/2,o=-(p+b)/2,a=-(m+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}var ln=(()=>{class e extends It{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let s=t.data;if(s.labels.length&&s.datasets.length){let{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((a,r)=>{let c=t.getDatasetMeta(0).controller.getStyle(r);return{text:a,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}}};constructor(t,s){super(t,s),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,s){let n=this.getDataset().data,o=this._cachedMeta;if(this._parsing===!1)o._parsed=n;else{let a=c=>+n[c];if(A(n[t])){let{key:c="value"}=this._parsing;a=h=>+_t(n[h],c)}let r,l;for(r=t,l=t+s;r<l;++r)o._parsed[r]=a(r)}}_getRotation(){return at(this.options.rotation-90)}_getCircumference(){return at(this.options.circumference)}_getRotationExtents(){let t=B,s=-B;for(let n=0;n<this.chart.data.datasets.length;++n)if(this.chart.isDatasetVisible(n)&&this.chart.getDatasetMeta(n).type===this._type){let o=this.chart.getDatasetMeta(n).controller,a=o._getRotation(),r=o._getCircumference();t=Math.min(t,a),s=Math.max(s,a+r)}return{rotation:t,circumference:s-t}}update(t){let s=this.chart,{chartArea:n}=s,o=this._cachedMeta,a=o.data,r=this.getMaxBorderWidth()+this.getMaxOffset(a)+this.options.spacing,l=Math.max((Math.min(n.width,n.height)-r)/2,0),c=Math.min(ao(this.options.cutout,l),1),h=this._getRingWeight(this.index),{circumference:d,rotation:u}=this._getRotationExtents(),{ratioX:f,ratioY:g,offsetX:p,offsetY:m}=Ql(u,d,c),b=(n.width-r)/f,x=(n.height-r)/g,y=Math.max(Math.min(b,x)/2,0),M=is(this.options.radius,y),_=Math.max(M*c,0),v=(M-_)/this._getVisibleDatasetWeightTotal();this.offsetX=p*M,this.offsetY=m*M,o.total=this.calculateTotal(),this.outerRadius=M-v*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-v*h,0),this.updateElements(a,0,a.length,t)}_circumference(t,s){let n=this.options,o=this._cachedMeta,a=this._getCircumference();return s&&n.animation.animateRotate||!this.chart.getDataVisibility(t)||o._parsed[t]===null||o.data[t].hidden?0:this.calculateCircumference(o._parsed[t]*a/B)}updateElements(t,s,n,o){let a=o==="reset",r=this.chart,l=r.chartArea,h=r.options.animation,d=(l.left+l.right)/2,u=(l.top+l.bottom)/2,f=a&&h.animateScale,g=f?0:this.innerRadius,p=f?0:this.outerRadius,{sharedOptions:m,includeOptions:b}=this._getSharedOptions(s,o),x=this._getRotation(),y;for(y=0;y<s;++y)x+=this._circumference(y,a);for(y=s;y<s+n;++y){let M=this._circumference(y,a),_=t[y],v={x:d+this.offsetX,y:u+this.offsetY,startAngle:x,endAngle:x+M,circumference:M,outerRadius:p,innerRadius:g};b&&(v.options=m||this.resolveDataElementOptions(y,_.active?"active":o)),x+=M,this.updateElement(_,y,v,o)}}calculateTotal(){let t=this._cachedMeta,s=t.data,n=0,o;for(o=0;o<s.length;o++){let a=t._parsed[o];a!==null&&!isNaN(a)&&this.chart.getDataVisibility(o)&&!s[o].hidden&&(n+=Math.abs(a))}return n}calculateCircumference(t){let s=this._cachedMeta.total;return s>0&&!isNaN(t)?B*(Math.abs(t)/s):0}getLabelAndValue(t){let s=this._cachedMeta,n=this.chart,o=n.data.labels||[],a=de(s._parsed[t],n.options.locale);return{label:o[t]||"",value:a}}getMaxBorderWidth(t){let s=0,n=this.chart,o,a,r,l,c;if(!t){for(o=0,a=n.data.datasets.length;o<a;++o)if(n.isDatasetVisible(o)){r=n.getDatasetMeta(o),t=r.data,l=r.controller;break}}if(!t)return 0;for(o=0,a=t.length;o<a;++o)c=l.resolveDataElementOptions(o),c.borderAlign!=="inner"&&(s=Math.max(s,c.borderWidth||0,c.hoverBorderWidth||0));return s}getMaxOffset(t){let s=0;for(let n=0,o=t.length;n<o;++n){let a=this.resolveDataElementOptions(n);s=Math.max(s,a.offset||0,a.hoverOffset||0)}return s}_getRingWeightOffset(t){let s=0;for(let n=0;n<t;++n)this.chart.isDatasetVisible(n)&&(s+=this._getRingWeight(n));return s}_getRingWeight(t){return Math.max(D(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}return e})(),Jl=(()=>{class e extends It{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let s=this._cachedMeta,{dataset:n,data:o=[],_dataset:a}=s,r=this.chart._animationsDisabled,{start:l,count:c}=us(s,o,r);this._drawStart=l,this._drawCount=c,fs(s)&&(l=0,c=o.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!a._decimated,n.points=o;let h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(n,void 0,{animated:!r,options:h},t),this.updateElements(o,l,c,t)}updateElements(t,s,n,o){let a=o==="reset",{iScale:r,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,{sharedOptions:d,includeOptions:u}=this._getSharedOptions(s,o),f=r.axis,g=l.axis,{spanGaps:p,segment:m}=this.options,b=$t(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||a||o==="none",y=s+n,M=t.length,_=s>0&&this.getParsed(s-1);for(let v=0;v<M;++v){let k=t[v],S=x?k:{};if(v<s||v>=y){S.skip=!0;continue}let w=this.getParsed(v),P=O(w[g]),C=S[f]=r.getPixelForValue(w[f],v),T=S[g]=a||P?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,w,c):w[g],v);S.skip=isNaN(C)||isNaN(T)||P,S.stop=v>0&&Math.abs(w[f]-_[f])>b,m&&(S.parsed=w,S.raw=h.data[v]),u&&(S.options=d||this.resolveDataElementOptions(v,k.active?"active":o)),x||this.updateElement(k,v,S,o),_=w}}getMaxOverflow(){let t=this._cachedMeta,s=t.dataset,n=s.options&&s.options.borderWidth||0,o=t.data||[];if(!o.length)return n;let a=o[0].size(this.resolveDataElementOptions(0)),r=o[o.length-1].size(this.resolveDataElementOptions(o.length-1));return Math.max(n,a,r)/2}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}return e})(),za=(()=>{class e extends It{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let s=t.data;if(s.labels.length&&s.datasets.length){let{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((a,r)=>{let c=t.getDatasetMeta(0).controller.getStyle(r);return{text:a,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,s){super(t,s),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let s=this._cachedMeta,n=this.chart,o=n.data.labels||[],a=de(s._parsed[t].r,n.options.locale);return{label:o[t]||"",value:a}}parseObjectData(t,s,n,o){return Ms.bind(this)(t,s,n,o)}update(t){let s=this._cachedMeta.data;this._updateRadius(),this.updateElements(s,0,s.length,t)}getMinMax(){let t=this._cachedMeta,s={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((n,o)=>{let a=this.getParsed(o).r;!isNaN(a)&&this.chart.getDataVisibility(o)&&(a<s.min&&(s.min=a),a>s.max&&(s.max=a))}),s}_updateRadius(){let t=this.chart,s=t.chartArea,n=t.options,o=Math.min(s.right-s.left,s.bottom-s.top),a=Math.max(o/2,0),r=Math.max(n.cutoutPercentage?a/100*n.cutoutPercentage:1,0),l=(a-r)/t.getVisibleDatasetCount();this.outerRadius=a-l*this.index,this.innerRadius=this.outerRadius-l}updateElements(t,s,n,o){let a=o==="reset",r=this.chart,c=r.options.animation,h=this._cachedMeta.rScale,d=h.xCenter,u=h.yCenter,f=h.getIndexAngle(0)-.5*z,g=f,p,m=360/this.countVisibleElements();for(p=0;p<s;++p)g+=this._computeAngle(p,o,m);for(p=s;p<s+n;p++){let b=t[p],x=g,y=g+this._computeAngle(p,o,m),M=r.getDataVisibility(p)?h.getDistanceFromCenterForValue(this.getParsed(p).r):0;g=y,a&&(c.animateScale&&(M=0),c.animateRotate&&(x=y=f));let _={x:d,y:u,innerRadius:0,outerRadius:M,startAngle:x,endAngle:y,options:this.resolveDataElementOptions(p,b.active?"active":o)};this.updateElement(b,p,_,o)}}countVisibleElements(){let t=this._cachedMeta,s=0;return t.data.forEach((n,o)=>{!isNaN(this.getParsed(o).r)&&this.chart.getDataVisibility(o)&&s++}),s}_computeAngle(t,s,n){return this.chart.getDataVisibility(t)?at(this.resolveDataElementOptions(t,s).angle||n):0}}return e})(),tc=(()=>{class e extends ln{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}return e})(),ec=(()=>{class e extends It{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let s=this._cachedMeta.vScale,n=this.getParsed(t);return{label:s.getLabels()[t],value:""+s.getLabelForValue(n[s.axis])}}parseObjectData(t,s,n,o){return Ms.bind(this)(t,s,n,o)}update(t){let s=this._cachedMeta,n=s.dataset,o=s.data||[],a=s.iScale.getLabels();if(n.points=o,t!=="resize"){let r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);let l={_loop:!0,_fullLoop:a.length===o.length,options:r};this.updateElement(n,void 0,l,t)}this.updateElements(o,0,o.length,t)}updateElements(t,s,n,o){let a=this._cachedMeta.rScale,r=o==="reset";for(let l=s;l<s+n;l++){let c=t[l],h=this.resolveDataElementOptions(l,c.active?"active":o),d=a.getPointPositionForValue(l,this.getParsed(l).r),u=r?a.xCenter:d.x,f=r?a.yCenter:d.y,g={x:u,y:f,angle:d.angle,skip:isNaN(u)||isNaN(f),options:h};this.updateElement(c,l,g,o)}}}return e})(),ic=(()=>{class e extends It{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:a}=s,r=this.getParsed(t),l=o.getLabelForValue(r.x),c=a.getLabelForValue(r.y);return{label:n[t]||"",value:"("+l+", "+c+")"}}update(t){let s=this._cachedMeta,{data:n=[]}=s,o=this.chart._animationsDisabled,{start:a,count:r}=us(s,n,o);if(this._drawStart=a,this._drawCount=r,fs(s)&&(a=0,r=n.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:l,_dataset:c}=s;l._chart=this.chart,l._datasetIndex=this.index,l._decimated=!!c._decimated,l.points=n;let h=this.resolveDatasetElementOptions(t);h.segment=this.options.segment,this.updateElement(l,void 0,{animated:!o,options:h},t)}else this.datasetElementType&&(delete s.dataset,this.datasetElementType=!1);this.updateElements(n,a,r,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,s,n,o){let a=o==="reset",{iScale:r,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,d=this.resolveDataElementOptions(s,o),u=this.getSharedOptions(d),f=this.includeOptions(o,u),g=r.axis,p=l.axis,{spanGaps:m,segment:b}=this.options,x=$t(m)?m:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||a||o==="none",M=s>0&&this.getParsed(s-1);for(let _=s;_<s+n;++_){let v=t[_],k=this.getParsed(_),S=y?v:{},w=O(k[p]),P=S[g]=r.getPixelForValue(k[g],_),C=S[p]=a||w?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,k,c):k[p],_);S.skip=isNaN(P)||isNaN(C)||w,S.stop=_>0&&Math.abs(k[g]-M[g])>x,b&&(S.parsed=k,S.raw=h.data[_]),f&&(S.options=u||this.resolveDataElementOptions(_,v.active?"active":o)),y||this.updateElement(v,_,S,o),M=k}this.updateSharedOptions(u,o,d)}getMaxOverflow(){let t=this._cachedMeta,s=t.data||[];if(!this.options.showLine){let l=0;for(let c=s.length-1;c>=0;--c)l=Math.max(l,s[c].size(this.resolveDataElementOptions(c))/2);return l>0&&l}let n=t.dataset,o=n.options&&n.options.borderWidth||0;if(!s.length)return o;let a=s[0].size(this.resolveDataElementOptions(0)),r=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(o,a,r)/2}}return e})(),sc=Object.freeze({__proto__:null,BarController:Gl,BubbleController:Zl,DoughnutController:ln,LineController:Jl,PieController:tc,PolarAreaController:za,RadarController:ec,ScatterController:ic});function Ut(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var Hs=class e{static override(i){Object.assign(e.prototype,i)}options;constructor(i){this.options=i||{}}init(){}formats(){return Ut()}parse(){return Ut()}format(){return Ut()}add(){return Ut()}diff(){return Ut()}startOf(){return Ut()}endOf(){return Ut()}},nc={_date:Hs};function oc(e,i,t,s){let{controller:n,data:o,_sorted:a}=e,r=n._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(r&&i===r.axis&&i!=="r"&&a&&o.length){let c=r._reversePixels?fo:ht;if(s){if(n._sharedOptions){let h=o[0],d=typeof h.getRange=="function"&&h.getRange(i);if(d){let u=c(o,i,t-d),f=c(o,i,t+d);return{lo:u.lo,hi:f.hi}}}}else{let h=c(o,i,t);if(l){let{vScale:d}=n._cachedMeta,{_parsed:u}=e,f=u.slice(0,h.lo+1).reverse().findIndex(p=>!O(p[d.axis]));h.lo-=Math.max(0,f);let g=u.slice(h.hi).findIndex(p=>!O(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function He(e,i,t,s,n){let o=e.getSortedVisibleDatasetMetas(),a=t[i];for(let r=0,l=o.length;r<l;++r){let{index:c,data:h}=o[r],{lo:d,hi:u}=oc(o[r],i,a,n);for(let f=d;f<=u;++f){let g=h[f];g.skip||s(g,c,f)}}}function ac(e){let i=e.indexOf("x")!==-1,t=e.indexOf("y")!==-1;return function(s,n){let o=i?Math.abs(s.x-n.x):0,a=t?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}function Es(e,i,t,s,n){let o=[];return!n&&!e.isPointInArea(i)||He(e,t,i,function(r,l,c){!n&&!dt(r,e.chartArea,0)||r.inRange(i.x,i.y,s)&&o.push({element:r,datasetIndex:l,index:c})},!0),o}function rc(e,i,t,s){let n=[];function o(a,r,l){let{startAngle:c,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=rs(a,{x:i.x,y:i.y});he(d,c,h)&&n.push({element:a,datasetIndex:r,index:l})}return He(e,t,i,o),n}function lc(e,i,t,s,n,o){let a=[],r=ac(t),l=Number.POSITIVE_INFINITY;function c(h,d,u){let f=h.inRange(i.x,i.y,n);if(s&&!f)return;let g=h.getCenterPoint(n);if(!(!!o||e.isPointInArea(g))&&!f)return;let m=r(i,g);m<l?(a=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&a.push({element:h,datasetIndex:d,index:u})}return He(e,t,i,c),a}function Is(e,i,t,s,n,o){return!o&&!e.isPointInArea(i)?[]:t==="r"&&!s?rc(e,i,t,n):lc(e,i,t,s,n,o)}function $o(e,i,t,s,n){let o=[],a=t==="x"?"inXRange":"inYRange",r=!1;return He(e,t,i,(l,c,h)=>{l[a]&&l[a](i[t],n)&&(o.push({element:l,datasetIndex:c,index:h}),r=r||l.inRange(i.x,i.y,n))}),s&&!r?[]:o}var cc={evaluateInteractionItems:He,modes:{index(e,i,t,s){let n=Rt(i,e),o=t.axis||"x",a=t.includeInvisible||!1,r=t.intersect?Es(e,n,o,s,a):Is(e,n,o,!1,s,a),l=[];return r.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{let h=r[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(e,i,t,s){let n=Rt(i,e),o=t.axis||"xy",a=t.includeInvisible||!1,r=t.intersect?Es(e,n,o,s,a):Is(e,n,o,!1,s,a);if(r.length>0){let l=r[0].datasetIndex,c=e.getDatasetMeta(l).data;r=[];for(let h=0;h<c.length;++h)r.push({element:c[h],datasetIndex:l,index:h})}return r},point(e,i,t,s){let n=Rt(i,e),o=t.axis||"xy",a=t.includeInvisible||!1;return Es(e,n,o,s,a)},nearest(e,i,t,s){let n=Rt(i,e),o=t.axis||"xy",a=t.includeInvisible||!1;return Is(e,n,o,t.intersect,s,a)},x(e,i,t,s){let n=Rt(i,e);return $o(e,n,"x",t.intersect,s)},y(e,i,t,s){let n=Rt(i,e);return $o(e,n,"y",t.intersect,s)}}},Ba=["left","top","right","bottom"];function Re(e,i){return e.filter(t=>t.pos===i)}function Yo(e,i){return e.filter(t=>Ba.indexOf(t.pos)===-1&&t.box.axis===i)}function Ee(e,i){return e.sort((t,s)=>{let n=i?s:t,o=i?t:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function hc(e){let i=[],t,s,n,o,a,r;for(t=0,s=(e||[]).length;t<s;++t)n=e[t],{position:o,options:{stack:a,stackWeight:r=1}}=n,i.push({index:t,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return i}function dc(e){let i={};for(let t of e){let{stack:s,pos:n,stackWeight:o}=t;if(!s||!Ba.includes(n))continue;let a=i[s]||(i[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return i}function uc(e,i){let t=dc(e),{vBoxMaxWidth:s,hBoxMaxHeight:n}=i,o,a,r;for(o=0,a=e.length;o<a;++o){r=e[o];let{fullSize:l}=r.box,c=t[r.stack],h=c&&r.stackWeight/c.weight;r.horizontal?(r.width=h?h*s:l&&i.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:l&&i.availableHeight)}return t}function fc(e){let i=hc(e),t=Ee(i.filter(c=>c.box.fullSize),!0),s=Ee(Re(i,"left"),!0),n=Ee(Re(i,"right")),o=Ee(Re(i,"top"),!0),a=Ee(Re(i,"bottom")),r=Yo(i,"x"),l=Yo(i,"y");return{fullSize:t,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:Re(i,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}function Uo(e,i,t,s){return Math.max(e[t],i[t])+Math.max(e[s],i[s])}function Na(e,i){e.top=Math.max(e.top,i.top),e.left=Math.max(e.left,i.left),e.bottom=Math.max(e.bottom,i.bottom),e.right=Math.max(e.right,i.right)}function gc(e,i,t,s){let{pos:n,box:o}=t,a=e.maxPadding;if(!A(n)){t.size&&(e[n]-=t.size);let d=s[t.stack]||{size:0,count:1};d.size=Math.max(d.size,t.horizontal?o.height:o.width),t.size=d.size/d.count,e[n]+=t.size}o.getPadding&&Na(a,o.getPadding());let r=Math.max(0,i.outerWidth-Uo(a,e,"left","right")),l=Math.max(0,i.outerHeight-Uo(a,e,"top","bottom")),c=r!==e.w,h=l!==e.h;return e.w=r,e.h=l,t.horizontal?{same:c,other:h}:{same:h,other:c}}function pc(e){let i=e.maxPadding;function t(s){let n=Math.max(i[s]-e[s],0);return e[s]+=n,n}e.y+=t("top"),e.x+=t("left"),t("right"),t("bottom")}function mc(e,i){let t=i.maxPadding;function s(n){let o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(i[a],t[a])}),o}return s(e?["left","right"]:["top","bottom"])}function ze(e,i,t,s){let n=[],o,a,r,l,c,h;for(o=0,a=e.length,c=0;o<a;++o){r=e[o],l=r.box,l.update(r.width||i.w,r.height||i.h,mc(r.horizontal,i));let{same:d,other:u}=gc(i,t,r,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(r)}return c&&ze(n,i,t,s)||h}function Mi(e,i,t,s,n){e.top=t,e.left=i,e.right=i+s,e.bottom=t+n,e.width=s,e.height=n}function Xo(e,i,t,s){let n=t.padding,{x:o,y:a}=i;for(let r of e){let l=r.box,c=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/c.weight||1;if(r.horizontal){let d=i.w*h,u=c.size||l.height;le(c.start)&&(a=c.start),l.fullSize?Mi(l,n.left,a,t.outerWidth-n.right-n.left,u):Mi(l,i.left+c.placed,a,d,u),c.start=a,c.placed+=d,a=l.bottom}else{let d=i.h*h,u=c.size||l.width;le(c.start)&&(o=c.start),l.fullSize?Mi(l,o,n.top,u,t.outerHeight-n.bottom-n.top):Mi(l,o,i.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}i.x=o,i.y=a}var Z={addBox(e,i){e.boxes||(e.boxes=[]),i.fullSize=i.fullSize||!1,i.position=i.position||"top",i.weight=i.weight||0,i._layers=i._layers||function(){return[{z:0,draw(t){i.draw(t)}}]},e.boxes.push(i)},removeBox(e,i){let t=e.boxes?e.boxes.indexOf(i):-1;t!==-1&&e.boxes.splice(t,1)},configure(e,i,t){i.fullSize=t.fullSize,i.position=t.position,i.weight=t.weight},update(e,i,t,s){if(!e)return;let n=q(e.options.layout.padding),o=Math.max(i-n.width,0),a=Math.max(t-n.height,0),r=fc(e.boxes),l=r.vertical,c=r.horizontal;L(e.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});let h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:i,outerHeight:t,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),u=Object.assign({},n);Na(u,q(s));let f=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n),g=uc(l.concat(c),d);ze(r.fullSize,f,d,g),ze(l,f,d,g),ze(c,f,d,g)&&ze(l,f,d,g),pc(f),Xo(r.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Xo(r.rightAndBottom,f,d,g),e.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},L(r.chartArea,p=>{let m=p.box;Object.assign(m,e.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}},Ti=class{acquireContext(i,t){}releaseContext(i){return!1}addEventListener(i,t,s){}removeEventListener(i,t,s){}getDevicePixelRatio(){return 1}getMaximumSize(i,t,s,n){return t=Math.max(0,t||i.width),s=s||i.height,{width:t,height:Math.max(0,n?Math.floor(t/n):s)}}isAttached(i){return!0}updateConfig(i){}},js=class extends Ti{acquireContext(i){return i&&i.getContext&&i.getContext("2d")||null}updateConfig(i){i.options.animation=!1}},Pi="$chartjs",bc={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Ko=e=>e===null||e==="";function xc(e,i){let t=e.style,s=e.getAttribute("height"),n=e.getAttribute("width");if(e[Pi]={initial:{height:s,width:n,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",Ko(n)){let o=ks(e,"width");o!==void 0&&(e.width=o)}if(Ko(s))if(e.style.height==="")e.height=e.width/(i||2);else{let o=ks(e,"height");o!==void 0&&(e.height=o)}return e}var Va=Ao?{passive:!0}:!1;function _c(e,i,t){e&&e.addEventListener(i,t,Va)}function yc(e,i,t){e&&e.canvas&&e.canvas.removeEventListener(i,t,Va)}function vc(e,i){let t=bc[e.type]||e.type,{x:s,y:n}=Rt(e,i);return{type:t,chart:i,native:e,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Li(e,i){for(let t of e)if(t===i||t.contains(i))return!0}function Mc(e,i,t){let s=e.canvas,n=new MutationObserver(o=>{let a=!1;for(let r of o)a=a||Li(r.addedNodes,s),a=a&&!Li(r.removedNodes,s);a&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}function Sc(e,i,t){let s=e.canvas,n=new MutationObserver(o=>{let a=!1;for(let r of o)a=a||Li(r.removedNodes,s),a=a&&!Li(r.addedNodes,s);a&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}var Ne=new Map,qo=0;function Wa(){let e=window.devicePixelRatio;e!==qo&&(qo=e,Ne.forEach((i,t)=>{t.currentDevicePixelRatio!==e&&i()}))}function kc(e,i){Ne.size||window.addEventListener("resize",Wa),Ne.set(e,i)}function wc(e){Ne.delete(e),Ne.size||window.removeEventListener("resize",Wa)}function Dc(e,i,t){let s=e.canvas,n=s&&yi(s);if(!n)return;let o=ds((r,l)=>{let c=n.clientWidth;t(r,l),c<n.clientWidth&&t()},window),a=new ResizeObserver(r=>{let l=r[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return a.observe(n),kc(e,o),a}function Fs(e,i,t){t&&t.disconnect(),i==="resize"&&wc(e)}function Cc(e,i,t){let s=e.canvas,n=ds(o=>{e.ctx!==null&&t(vc(o,e))},e);return _c(s,i,n),n}var $s=class extends Ti{acquireContext(i,t){let s=i&&i.getContext&&i.getContext("2d");return s&&s.canvas===i?(xc(i,t),s):null}releaseContext(i){let t=i.canvas;if(!t[Pi])return!1;let s=t[Pi].initial;["height","width"].forEach(o=>{let a=s[o];O(a)?t.removeAttribute(o):t.setAttribute(o,a)});let n=s.style||{};return Object.keys(n).forEach(o=>{t.style[o]=n[o]}),t.width=t.width,delete t[Pi],!0}addEventListener(i,t,s){this.removeEventListener(i,t);let n=i.$proxies||(i.$proxies={}),a={attach:Mc,detach:Sc,resize:Dc}[t]||Cc;n[t]=a(i,t,s)}removeEventListener(i,t){let s=i.$proxies||(i.$proxies={}),n=s[t];if(!n)return;({attach:Fs,detach:Fs,resize:Fs}[t]||yc)(i,t,n),s[t]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(i,t,s,n){return Oo(i,t,s,n)}isAttached(i){let t=i&&yi(i);return!!(t&&t.isConnected)}};function Pc(e){return!_i()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?js:$s}var ct=class{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(i){let{x:t,y:s}=this.getProps(["x","y"],i);return{x:t,y:s}}hasValue(){return $t(this.x)&&$t(this.y)}getProps(i,t){let s=this.$animations;if(!t||!s)return this;let n={};return i.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}};function Oc(e,i){let t=e.options.ticks,s=Ac(e),n=Math.min(t.maxTicksLimit||s,s),o=t.major.enabled?Lc(i):[],a=o.length,r=o[0],l=o[a-1],c=[];if(a>n)return Rc(i,c,o,a/n),c;let h=Tc(o,i,n);if(a>0){let d,u,f=a>1?Math.round((l-r)/(a-1)):null;for(Si(i,c,h,O(f)?0:r-f,r),d=0,u=a-1;d<u;d++)Si(i,c,h,o[d],o[d+1]);return Si(i,c,h,l,O(f)?i.length:l+f),c}return Si(i,c,h),c}function Ac(e){let i=e.options.offset,t=e._tickSize(),s=e._length/t+(i?0:1),n=e._maxLength/t;return Math.floor(Math.min(s,n))}function Tc(e,i,t){let s=Ec(e),n=i.length/t;if(!s)return Math.max(n,1);let o=co(s);for(let a=0,r=o.length-1;a<r;a++){let l=o[a];if(l>n)return l}return Math.max(n,1)}function Lc(e){let i=[],t,s;for(t=0,s=e.length;t<s;t++)e[t].major&&i.push(t);return i}function Rc(e,i,t,s){let n=0,o=t[0],a;for(s=Math.ceil(s),a=0;a<e.length;a++)a===o&&(i.push(e[a]),n++,o=t[n*s])}function Si(e,i,t,s,n){let o=D(s,0),a=Math.min(D(n,e.length),e.length),r=0,l,c,h;for(t=Math.ceil(t),n&&(l=n-s,t=l/Math.floor(l/t)),h=o;h<0;)r++,h=Math.round(o+r*t);for(c=Math.max(o,0);c<a;c++)c===h&&(i.push(e[c]),r++,h=Math.round(o+r*t))}function Ec(e){let i=e.length,t,s;if(i<2)return!1;for(s=e[0],t=1;t<i;++t)if(e[t]-e[t-1]!==s)return!1;return s}var Ic=e=>e==="left"?"right":e==="right"?"left":e,Go=(e,i,t)=>i==="top"||i==="left"?e[i]+t:e[i]-t,Zo=(e,i)=>Math.min(i||e,e);function Qo(e,i){let t=[],s=e.length/i,n=e.length,o=0;for(;o<n;o+=s)t.push(e[Math.floor(o)]);return t}function Fc(e,i,t){let s=e.ticks.length,n=Math.min(i,s-1),o=e._startPixel,a=e._endPixel,r=1e-6,l=e.getPixelForTick(n),c;if(!(t&&(s===1?c=Math.max(l-o,a-l):i===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(n-1))/2,l+=n<i?c:-c,l<o-r||l>a+r)))return l}function zc(e,i){L(e,t=>{let s=t.gc,n=s.length/2,o;if(n>i){for(o=0;o<n;++o)delete t.data[s[o]];s.splice(0,n)}})}function Ie(e){return e.drawTicks?e.tickLength:0}function Jo(e,i){if(!e.display)return 0;let t=j(e.font,i),s=q(e.padding);return(F(e.text)?e.text.length:1)*t.lineHeight+s.height}function Bc(e,i){return yt(e,{scale:i,type:"scale"})}function Nc(e,i,t){return yt(e,{tick:t,index:i,type:"tick"})}function Vc(e,i,t){let s=gi(e);return(t&&i!=="right"||!t&&i==="right")&&(s=Ic(s)),s}function Wc(e,i,t,s){let{top:n,left:o,bottom:a,right:r,chart:l}=e,{chartArea:c,scales:h}=l,d=0,u,f,g,p=a-n,m=r-o;if(e.isHorizontal()){if(f=K(s,o,r),A(t)){let b=Object.keys(t)[0],x=t[b];g=h[b].getPixelForValue(x)+p-i}else t==="center"?g=(c.bottom+c.top)/2+p-i:g=Go(e,t,i);u=r-o}else{if(A(t)){let b=Object.keys(t)[0],x=t[b];f=h[b].getPixelForValue(x)-m+i}else t==="center"?f=(c.left+c.right)/2-m+i:f=Go(e,t,i);g=K(s,a,n),d=t==="left"?-W:W}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}var Kt=class e extends ct{constructor(i){super(),this.id=i.id,this.type=i.type,this.options=void 0,this.ctx=i.ctx,this.chart=i.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(i){this.options=i.setContext(this.getContext()),this.axis=i.axis,this._userMin=this.parse(i.min),this._userMax=this.parse(i.max),this._suggestedMin=this.parse(i.suggestedMin),this._suggestedMax=this.parse(i.suggestedMax)}parse(i,t){return i}getUserBounds(){let{_userMin:i,_userMax:t,_suggestedMin:s,_suggestedMax:n}=this;return i=tt(i,Number.POSITIVE_INFINITY),t=tt(t,Number.NEGATIVE_INFINITY),s=tt(s,Number.POSITIVE_INFINITY),n=tt(n,Number.NEGATIVE_INFINITY),{min:tt(i,s),max:tt(t,n),minDefined:V(i),maxDefined:V(t)}}getMinMax(i){let{min:t,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),a;if(n&&o)return{min:t,max:s};let r=this.getMatchingVisibleMetas();for(let l=0,c=r.length;l<c;++l)a=r[l].controller.getMinMax(this,i),n||(t=Math.min(t,a.min)),o||(s=Math.max(s,a.max));return t=o&&t>s?s:t,s=n&&t>s?t:s,{min:tt(t,tt(s,t)),max:tt(s,tt(t,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let i=this.chart.data;return this.options.labels||(this.isHorizontal()?i.xLabels:i.yLabels)||i.labels||[]}getLabelItems(i=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(i))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){E(this.options.beforeUpdate,[this])}update(i,t,s){let{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=i,this.maxHeight=t,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=So(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=r<this.ticks.length;this._convertTicksToLabels(l?Qo(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=Oc(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let i=this.options.reverse,t,s;this.isHorizontal()?(t=this.left,s=this.right):(t=this.top,s=this.bottom,i=!i),this._startPixel=t,this._endPixel=s,this._reversePixels=i,this._length=s-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){E(this.options.afterUpdate,[this])}beforeSetDimensions(){E(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){E(this.options.afterSetDimensions,[this])}_callHooks(i){this.chart.notifyPlugins(i,this.getContext()),E(this.options[i],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){E(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(i){let t=this.options.ticks,s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s],o.label=E(t.callback,[o.value,s,i],this)}afterTickToLabelConversion(){E(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){E(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let i=this.options,t=i.ticks,s=Zo(this.ticks.length,i.ticks.maxTicksLimit),n=t.minRotation||0,o=t.maxRotation,a=n,r,l,c;if(!this._isVisible()||!t.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=$(this.chart.width-d,0,this.maxWidth);r=i.offset?this.maxWidth/s:f/(s-1),d+6>r&&(r=f/(s-(i.offset?.5:1)),l=this.maxHeight-Ie(i.grid)-t.padding-Jo(i.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),a=ui(Math.min(Math.asin($((h.highest.height+6)/r,-1,1)),Math.asin($(l/c,-1,1))-Math.asin($(u/c,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){E(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){E(this.options.beforeFit,[this])}fit(){let i={width:0,height:0},{chart:t,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){let l=Jo(n,t.options.font);if(r?(i.width=this.maxWidth,i.height=Ie(o)+l):(i.height=this.maxHeight,i.width=Ie(o)+l),s.display&&this.ticks.length){let{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=at(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(r){let b=s.mirror?0:m*d.width+p*u.height;i.height=Math.min(this.maxHeight,i.height+b+f)}else{let b=s.mirror?0:p*d.width+m*u.height;i.width=Math.min(this.maxWidth,i.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),r?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=i.height):(this.width=i.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(i,t,s,n){let{ticks:{align:o,padding:a},position:r}=this.options,l=this.labelRotation!==0,c=r!=="top"&&this.axis==="x";if(this.isHorizontal()){let h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1),u=0,f=0;l?c?(u=n*i.width,f=s*t.height):(u=s*i.height,f=n*t.width):o==="start"?f=t.width:o==="end"?u=i.width:o!=="inner"&&(u=i.width/2,f=t.width/2),this.paddingLeft=Math.max((u-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+a)*this.width/(this.width-d),0)}else{let h=t.height/2,d=i.height/2;o==="start"?(h=0,d=i.height):o==="end"&&(h=t.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){E(this.options.afterFit,[this])}isHorizontal(){let{axis:i,position:t}=this.options;return t==="top"||t==="bottom"||i==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(i){this.beforeTickToLabelConversion(),this.generateTickLabels(i);let t,s;for(t=0,s=i.length;t<s;t++)O(i[t].label)&&(i.splice(t,1),s--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let i=this._labelSizes;if(!i){let t=this.options.ticks.sampleSize,s=this.ticks;t<s.length&&(s=Qo(s,t)),this._labelSizes=i=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return i}_computeLabelSizes(i,t,s){let{ctx:n,_longestTextCache:o}=this,a=[],r=[],l=Math.floor(t/Zo(t,s)),c=0,h=0,d,u,f,g,p,m,b,x,y,M,_;for(d=0;d<t;d+=l){if(g=i[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},x=p.lineHeight,y=M=0,!O(g)&&!F(g))y=Ce(n,b.data,b.gc,y,g),M=x;else if(F(g))for(u=0,f=g.length;u<f;++u)_=g[u],!O(_)&&!F(_)&&(y=Ce(n,b.data,b.gc,y,_),M+=x);a.push(y),r.push(M),c=Math.max(y,c),h=Math.max(M,h)}zc(o,t);let v=a.indexOf(c),k=r.indexOf(h),S=w=>({width:a[w]||0,height:r[w]||0});return{first:S(0),last:S(t-1),widest:S(v),highest:S(k),widths:a,heights:r}}getLabelForValue(i){return i}getPixelForValue(i,t){return NaN}getValueForPixel(i){}getPixelForTick(i){let t=this.ticks;return i<0||i>t.length-1?null:this.getPixelForValue(t[i].value)}getPixelForDecimal(i){this._reversePixels&&(i=1-i);let t=this._startPixel+i*this._length;return uo(this._alignToPixels?At(this.chart,t,0):t)}getDecimalForPixel(i){let t=(i-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:i,max:t}=this;return i<0&&t<0?t:i>0&&t>0?i:0}getContext(i){let t=this.ticks||[];if(i>=0&&i<t.length){let s=t[i];return s.$context||(s.$context=Nc(this.getContext(),i,s))}return this.$context||(this.$context=Bc(this.chart.getContext(),this))}_tickSize(){let i=this.options.ticks,t=at(this.labelRotation),s=Math.abs(Math.cos(t)),n=Math.abs(Math.sin(t)),o=this._getLabelSizes(),a=i.autoSkipPadding||0,r=o?o.widest.width+a:0,l=o?o.highest.height+a:0;return this.isHorizontal()?l*s>r*n?r/s:l/n:l*n<r*s?l/s:r/n}_isVisible(){let i=this.options.display;return i!=="auto"?!!i:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(i){let t=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=Ie(o),f=[],g=r.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(N){return At(s,N,p)},x,y,M,_,v,k,S,w,P,C,T,Y;if(a==="top")x=b(this.bottom),k=this.bottom-u,w=x-m,C=b(i.top)+m,Y=i.bottom;else if(a==="bottom")x=b(this.top),C=i.top,Y=b(i.bottom)-m,k=x+m,w=this.top+u;else if(a==="left")x=b(this.right),v=this.right-u,S=x-m,P=b(i.left)+m,T=i.right;else if(a==="right")x=b(this.left),P=i.left,T=b(i.right)-m,v=x+m,S=this.left+u;else if(t==="x"){if(a==="center")x=b((i.top+i.bottom)/2+.5);else if(A(a)){let N=Object.keys(a)[0],H=a[N];x=b(this.chart.scales[N].getPixelForValue(H))}C=i.top,Y=i.bottom,k=x+m,w=k+u}else if(t==="y"){if(a==="center")x=b((i.left+i.right)/2);else if(A(a)){let N=Object.keys(a)[0],H=a[N];x=b(this.chart.scales[N].getPixelForValue(H))}v=x-m,S=v-u,P=i.left,T=i.right}let st=D(n.ticks.maxTicksLimit,d),R=Math.max(1,Math.ceil(d/st));for(y=0;y<d;y+=R){let N=this.getContext(y),H=o.setContext(N),rt=r.setContext(N),G=H.lineWidth,qt=H.color,Ye=rt.dash||[],Gt=rt.dashOffset,xe=H.tickWidth,Bt=H.tickColor,_e=H.tickBorderDash||[],Nt=H.tickBorderDashOffset;M=Fc(this,y,l),M!==void 0&&(_=At(s,M,G),c?v=S=P=T=_:k=w=C=Y=_,f.push({tx1:v,ty1:k,tx2:S,ty2:w,x1:P,y1:C,x2:T,y2:Y,width:G,color:qt,borderDash:Ye,borderDashOffset:Gt,tickWidth:xe,tickColor:Bt,tickBorderDash:_e,tickBorderDashOffset:Nt}))}return this._ticksLength=d,this._borderValue=x,f}_computeLabelItems(i){let t=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=Ie(s.grid),f=u+h,g=d?-h:f,p=-at(this.labelRotation),m=[],b,x,y,M,_,v,k,S,w,P,C,T,Y="middle";if(n==="top")v=this.bottom-g,k=this._getXAxisLabelAlignment();else if(n==="bottom")v=this.top+g,k=this._getXAxisLabelAlignment();else if(n==="left"){let R=this._getYAxisLabelAlignment(u);k=R.textAlign,_=R.x}else if(n==="right"){let R=this._getYAxisLabelAlignment(u);k=R.textAlign,_=R.x}else if(t==="x"){if(n==="center")v=(i.top+i.bottom)/2+f;else if(A(n)){let R=Object.keys(n)[0],N=n[R];v=this.chart.scales[R].getPixelForValue(N)+f}k=this._getXAxisLabelAlignment()}else if(t==="y"){if(n==="center")_=(i.left+i.right)/2-f;else if(A(n)){let R=Object.keys(n)[0],N=n[R];_=this.chart.scales[R].getPixelForValue(N)}k=this._getYAxisLabelAlignment(u).textAlign}t==="y"&&(l==="start"?Y="top":l==="end"&&(Y="bottom"));let st=this._getLabelSizes();for(b=0,x=r.length;b<x;++b){y=r[b],M=y.label;let R=o.setContext(this.getContext(b));S=this.getPixelForTick(b)+o.labelOffset,w=this._resolveTickFontOptions(b),P=w.lineHeight,C=F(M)?M.length:1;let N=C/2,H=R.color,rt=R.textStrokeColor,G=R.textStrokeWidth,qt=k;a?(_=S,k==="inner"&&(b===x-1?qt=this.options.reverse?"left":"right":b===0?qt=this.options.reverse?"right":"left":qt="center"),n==="top"?c==="near"||p!==0?T=-C*P+P/2:c==="center"?T=-st.highest.height/2-N*P+P:T=-st.highest.height+P/2:c==="near"||p!==0?T=P/2:c==="center"?T=st.highest.height/2-N*P:T=st.highest.height-C*P,d&&(T*=-1),p!==0&&!R.showLabelBackdrop&&(_+=P/2*Math.sin(p))):(v=S,T=(1-C)*P/2);let Ye;if(R.showLabelBackdrop){let Gt=q(R.backdropPadding),xe=st.heights[b],Bt=st.widths[b],_e=T-Gt.top,Nt=0-Gt.left;switch(Y){case"middle":_e-=xe/2;break;case"bottom":_e-=xe;break}switch(k){case"center":Nt-=Bt/2;break;case"right":Nt-=Bt;break;case"inner":b===x-1?Nt-=Bt:b>0&&(Nt-=Bt/2);break}Ye={left:Nt,top:_e,width:Bt+Gt.width,height:xe+Gt.height,color:R.backdropColor}}m.push({label:M,font:w,textOffset:T,options:{rotation:p,color:H,strokeColor:rt,strokeWidth:G,textAlign:qt,textBaseline:Y,translation:[_,v],backdrop:Ye}})}return m}_getXAxisLabelAlignment(){let{position:i,ticks:t}=this.options;if(-at(this.labelRotation))return i==="top"?"left":"right";let n="center";return t.align==="start"?n="left":t.align==="end"?n="right":t.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(i){let{position:t,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,a=this._getLabelSizes(),r=i+o,l=a.widest.width,c,h;return t==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-r,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):t==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+r,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;let i=this.chart,t=this.options.position;if(t==="left"||t==="right")return{top:0,left:this.left,bottom:i.height,right:this.right};if(t==="top"||t==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:i.width}}drawBackground(){let{ctx:i,options:{backgroundColor:t},left:s,top:n,width:o,height:a}=this;t&&(i.save(),i.fillStyle=t,i.fillRect(s,n,o,a),i.restore())}getLineWidthForValue(i){let t=this.options.grid;if(!this._isVisible()||!t.display)return 0;let n=this.ticks.findIndex(o=>o.value===i);return n>=0?t.setContext(this.getContext(n)).lineWidth:0}drawGrid(i){let t=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(i)),o,a,r=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(t.display)for(o=0,a=n.length;o<a;++o){let l=n[o];t.drawOnChartArea&&r({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),t.drawTicks&&r({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){let{chart:i,ctx:t,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;let r=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue,c,h,d,u;this.isHorizontal()?(c=At(i,this.left,a)-a/2,h=At(i,this.right,r)+r/2,d=u=l):(d=At(i,this.top,a)-a/2,u=At(i,this.bottom,r)+r/2,c=h=l),t.save(),t.lineWidth=o.width,t.strokeStyle=o.color,t.beginPath(),t.moveTo(c,d),t.lineTo(h,u),t.stroke(),t.restore()}drawLabels(i){if(!this.options.ticks.display)return;let s=this.ctx,n=this._computeLabelArea();n&&Ae(s,n);let o=this.getLabelItems(i);for(let a of o){let r=a.options,l=a.font,c=a.label,h=a.textOffset;Tt(s,c,0,h,l,r)}n&&Te(s)}drawTitle(){let{ctx:i,options:{position:t,title:s,reverse:n}}=this;if(!s.display)return;let o=j(s.font),a=q(s.padding),r=s.align,l=o.lineHeight/2;t==="bottom"||t==="center"||A(t)?(l+=a.bottom,F(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=a.top;let{titleX:c,titleY:h,maxWidth:d,rotation:u}=Wc(this,l,t,r);Tt(i,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:Vc(r,t,n),textBaseline:"middle",translation:[c,h]})}draw(i){this._isVisible()&&(this.drawBackground(),this.drawGrid(i),this.drawBorder(),this.drawTitle(),this.drawLabels(i))}_layers(){let i=this.options,t=i.ticks&&i.ticks.z||0,s=D(i.grid&&i.grid.z,-1),n=D(i.border&&i.border.z,0);return!this._isVisible()||this.draw!==e.prototype.draw?[{z:t,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:t,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(i){let t=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[],o,a;for(o=0,a=t.length;o<a;++o){let r=t[o];r[s]===this.id&&(!i||r.type===i)&&n.push(r)}return n}_resolveTickFontOptions(i){let t=this.options.ticks.setContext(this.getContext(i));return j(t.font)}_maxDigits(){let i=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/i}},pe=class{constructor(i,t,s){this.type=i,this.scope=t,this.override=s,this.items=Object.create(null)}isForType(i){return Object.prototype.isPrototypeOf.call(this.type.prototype,i.prototype)}register(i){let t=Object.getPrototypeOf(i),s;$c(t)&&(s=this.register(t));let n=this.items,o=i.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+i);return o in n||(n[o]=i,Hc(i,a,s),this.override&&I.override(i.id,i.overrides)),a}get(i){return this.items[i]}unregister(i){let t=this.items,s=i.id,n=this.scope;s in t&&delete t[s],n&&s in I[n]&&(delete I[n][s],this.override&&delete Ot[s])}};function Hc(e,i,t){let s=oe(Object.create(null),[t?I.get(t):{},I.get(i),e.defaults]);I.set(i,s),e.defaultRoutes&&jc(i,e.defaultRoutes),e.descriptors&&I.describe(i,e.descriptors)}function jc(e,i){Object.keys(i).forEach(t=>{let s=t.split("."),n=s.pop(),o=[e].concat(s).join("."),a=i[t].split("."),r=a.pop(),l=a.join(".");I.route(o,n,l,r)})}function $c(e){return"id"in e&&"defaults"in e}var Ys=class{constructor(){this.controllers=new pe(It,"datasets",!0),this.elements=new pe(ct,"elements"),this.plugins=new pe(Object,"plugins"),this.scales=new pe(Kt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...i){this._each("register",i)}remove(...i){this._each("unregister",i)}addControllers(...i){this._each("register",i,this.controllers)}addElements(...i){this._each("register",i,this.elements)}addPlugins(...i){this._each("register",i,this.plugins)}addScales(...i){this._each("register",i,this.scales)}getController(i){return this._get(i,this.controllers,"controller")}getElement(i){return this._get(i,this.elements,"element")}getPlugin(i){return this._get(i,this.plugins,"plugin")}getScale(i){return this._get(i,this.scales,"scale")}removeControllers(...i){this._each("unregister",i,this.controllers)}removeElements(...i){this._each("unregister",i,this.elements)}removePlugins(...i){this._each("unregister",i,this.plugins)}removeScales(...i){this._each("unregister",i,this.scales)}_each(i,t,s){[...t].forEach(n=>{let o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(i,o,n):L(n,a=>{let r=s||this._getRegistryForType(a);this._exec(i,r,a)})})}_exec(i,t,s){let n=di(i);E(s["before"+n],[],s),t[i](s),E(s["after"+n],[],s)}_getRegistryForType(i){for(let t=0;t<this._typedRegistries.length;t++){let s=this._typedRegistries[t];if(s.isForType(i))return s}return this.plugins}_get(i,t,s){let n=t.get(i);if(n===void 0)throw new Error('"'+i+'" is not a registered '+s+".");return n}},pt=new Ys,Us=class{constructor(){this._init=[]}notify(i,t,s,n){t==="beforeInit"&&(this._init=this._createDescriptors(i,!0),this._notify(this._init,i,"install"));let o=n?this._descriptors(i).filter(n):this._descriptors(i),a=this._notify(o,i,t,s);return t==="afterDestroy"&&(this._notify(o,i,"stop"),this._notify(this._init,i,"uninstall")),a}_notify(i,t,s,n){n=n||{};for(let o of i){let a=o.plugin,r=a[s],l=[t,n,o.options];if(E(r,l,a)===!1&&n.cancelable)return!1}return!0}invalidate(){O(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(i){if(this._cache)return this._cache;let t=this._cache=this._createDescriptors(i);return this._notifyStateChanges(i),t}_createDescriptors(i,t){let s=i&&i.config,n=D(s.options&&s.options.plugins,{}),o=Yc(s);return n===!1&&!t?[]:Xc(i,o,n,t)}_notifyStateChanges(i){let t=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(l=>r.plugin.id===l.plugin.id));this._notify(n(t,s),i,"stop"),this._notify(n(s,t),i,"start")}};function Yc(e){let i={},t=[],s=Object.keys(pt.plugins.items);for(let o=0;o<s.length;o++)t.push(pt.getPlugin(s[o]));let n=e.plugins||[];for(let o=0;o<n.length;o++){let a=n[o];t.indexOf(a)===-1&&(t.push(a),i[a.id]=!0)}return{plugins:t,localIds:i}}function Uc(e,i){return!i&&e===!1?null:e===!0?{}:e}function Xc(e,{plugins:i,localIds:t},s,n){let o=[],a=e.getContext();for(let r of i){let l=r.id,c=Uc(s[l],n);c!==null&&o.push({plugin:r,options:Kc(e.config,{plugin:r,local:t[l]},c,a)})}return o}function Kc(e,{plugin:i,local:t},s,n){let o=e.pluginScopeKeys(i),a=e.getOptionScopes(s,o);return t&&i.defaults&&a.push(i.defaults),e.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Xs(e,i){let t=I.datasets[e]||{};return((i.datasets||{})[e]||{}).indexAxis||i.indexAxis||t.indexAxis||"x"}function qc(e,i){let t=e;return e==="_index_"?t=i:e==="_value_"&&(t=i==="x"?"y":"x"),t}function Gc(e,i){return e===i?"_index_":"_value_"}function ta(e){if(e==="x"||e==="y"||e==="r")return e}function Zc(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function Ks(e,...i){if(ta(e))return e;for(let t of i){let s=t.axis||Zc(t.position)||e.length>1&&ta(e[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function ea(e,i,t){if(t[i+"AxisID"]===e)return{axis:i}}function Qc(e,i){if(i.data&&i.data.datasets){let t=i.data.datasets.filter(s=>s.xAxisID===e||s.yAxisID===e);if(t.length)return ea(e,"x",t[0])||ea(e,"y",t[0])}return{}}function Jc(e,i){let t=Ot[e.type]||{scales:{}},s=i.scales||{},n=Xs(e.type,i),o=Object.create(null);return Object.keys(s).forEach(a=>{let r=s[a];if(!A(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);let l=Ks(a,r,Qc(a,e),I.scales[r.type]),c=Gc(l,n),h=t.scales||{};o[a]=re(Object.create(null),[{axis:l},r,h[l],h[c]])}),e.data.datasets.forEach(a=>{let r=a.type||e.type,l=a.indexAxis||Xs(r,i),h=(Ot[r]||{}).scales||{};Object.keys(h).forEach(d=>{let u=qc(d,l),f=a[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),re(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(a=>{let r=o[a];re(r,[I.scales[r.type],I.scale])}),o}function Ha(e){let i=e.options||(e.options={});i.plugins=D(i.plugins,{}),i.scales=Jc(e,i)}function ja(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function th(e){return e=e||{},e.data=ja(e.data),Ha(e),e}var ia=new Map,$a=new Set;function ki(e,i){let t=ia.get(e);return t||(t=i(),ia.set(e,t),$a.add(t)),t}var Fe=(e,i,t)=>{let s=_t(i,t);s!==void 0&&e.add(s)},qs=class{constructor(i){this._config=th(i),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(i){this._config.type=i}get data(){return this._config.data}set data(i){this._config.data=ja(i)}get options(){return this._config.options}set options(i){this._config.options=i}get plugins(){return this._config.plugins}update(){let i=this._config;this.clearCache(),Ha(i)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(i){return ki(i,()=>[[`datasets.${i}`,""]])}datasetAnimationScopeKeys(i,t){return ki(`${i}.transition.${t}`,()=>[[`datasets.${i}.transitions.${t}`,`transitions.${t}`],[`datasets.${i}`,""]])}datasetElementScopeKeys(i,t){return ki(`${i}-${t}`,()=>[[`datasets.${i}.elements.${t}`,`datasets.${i}`,`elements.${t}`,""]])}pluginScopeKeys(i){let t=i.id,s=this.type;return ki(`${s}-plugin-${t}`,()=>[[`plugins.${t}`,...i.additionalOptionScopes||[]]])}_cachedScopes(i,t){let s=this._scopeCache,n=s.get(i);return(!n||t)&&(n=new Map,s.set(i,n)),n}getOptionScopes(i,t,s){let{options:n,type:o}=this,a=this._cachedScopes(i,s),r=a.get(t);if(r)return r;let l=new Set;t.forEach(h=>{i&&(l.add(i),h.forEach(d=>Fe(l,i,d))),h.forEach(d=>Fe(l,n,d)),h.forEach(d=>Fe(l,Ot[o]||{},d)),h.forEach(d=>Fe(l,I,d)),h.forEach(d=>Fe(l,pi,d))});let c=Array.from(l);return c.length===0&&c.push(Object.create(null)),$a.has(t)&&a.set(t,c),c}chartOptionScopes(){let{options:i,type:t}=this;return[i,Ot[t]||{},I.datasets[t]||{},{type:t},I,pi]}resolveNamedOptions(i,t,s,n=[""]){let o={$shared:!0},{resolver:a,subPrefixes:r}=sa(this._resolverCache,i,n),l=a;if(ih(a,t)){o.$shared=!1,s=bt(s)?s():s;let c=this.createResolver(i,s,r);l=jt(a,s,c)}for(let c of t)o[c]=l[c];return o}createResolver(i,t,s=[""],n){let{resolver:o}=sa(this._resolverCache,i,s);return A(t)?jt(o,t,void 0,n):o}};function sa(e,i,t){let s=e.get(i);s||(s=new Map,e.set(i,s));let n=t.join(),o=s.get(n);return o||(o={resolver:xi(i,t),subPrefixes:t.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}var eh=e=>A(e)&&Object.getOwnPropertyNames(e).some(i=>bt(e[i]));function ih(e,i){let{isScriptable:t,isIndexable:s}=_s(e);for(let n of i){let o=t(n),a=s(n),r=(a||o)&&e[n];if(o&&(bt(r)||eh(r))||a&&F(r))return!0}return!1}var sh="4.4.9",nh=["top","bottom","left","right","chartArea"];function na(e,i){return e==="top"||e==="bottom"||nh.indexOf(e)===-1&&i==="x"}function oa(e,i){return function(t,s){return t[e]===s[e]?t[i]-s[i]:t[e]-s[e]}}function aa(e){let i=e.chart,t=i.options.animation;i.notifyPlugins("afterRender"),E(t&&t.onComplete,[e],i)}function oh(e){let i=e.chart,t=i.options.animation;E(t&&t.onProgress,[e],i)}function Ya(e){return _i()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}var Oi={},ra=e=>{let i=Ya(e);return Object.values(Oi).filter(t=>t.canvas===i).pop()};function ah(e,i,t){let s=Object.keys(e);for(let n of s){let o=+n;if(o>=i){let a=e[n];delete e[n],(t>0||o>i)&&(e[o+t]=a)}}}function rh(e,i,t,s){return!t||e.type==="mouseout"?null:s?i:e}var je=(()=>{class e{static defaults=I;static instances=Oi;static overrides=Ot;static registry=pt;static version=sh;static getChart=ra;static register(...t){pt.add(...t),la()}static unregister(...t){pt.remove(...t),la()}constructor(t,s){let n=this.config=new qs(s),o=Ya(t),a=ra(o);if(a)throw new Error("Canvas is already in use. Chart with ID '"+a.id+"' must be destroyed before the canvas with ID '"+a.canvas.id+"' can be reused.");let r=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||Pc(o)),this.platform.updateConfig(n);let l=this.platform.acquireContext(o,r.aspectRatio),c=l&&l.canvas,h=c&&c.height,d=c&&c.width;if(this.id=oo(),this.ctx=l,this.canvas=c,this.width=d,this.height=h,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Us,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=bo(u=>this.update(u),r.resizeDelay||0),this._dataChanges=[],Oi[this.id]=this,!l||!c){console.error("Failed to create chart: can't acquire context from the given item");return}vt.listen(this,"complete",aa),vt.listen(this,"progress",oh),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:s},width:n,height:o,_aspectRatio:a}=this;return O(t)?s&&a?a:o?n/o:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return pt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Ss(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return ms(this.canvas,this.ctx),this}stop(){return vt.stop(this),this}resize(t,s){vt.running(this)?this._resizeBeforeDraw={width:t,height:s}:this._resize(t,s)}_resize(t,s){let n=this.options,o=this.canvas,a=n.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(o,t,s,a),l=n.devicePixelRatio||this.platform.getDevicePixelRatio(),c=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Ss(this,l,!0)&&(this.notifyPlugins("resize",{size:r}),E(n.onResize,[this,r],this),this.attached&&this._doResize(c)&&this.render())}ensureScalesHaveIDs(){let s=this.options.scales||{};L(s,(n,o)=>{n.id=o})}buildOrUpdateScales(){let t=this.options,s=t.scales,n=this.scales,o=Object.keys(n).reduce((r,l)=>(r[l]=!1,r),{}),a=[];s&&(a=a.concat(Object.keys(s).map(r=>{let l=s[r],c=Ks(r,l),h=c==="r",d=c==="x";return{options:l,dposition:h?"chartArea":d?"bottom":"left",dtype:h?"radialLinear":d?"category":"linear"}}))),L(a,r=>{let l=r.options,c=l.id,h=Ks(c,l),d=D(l.type,r.dtype);(l.position===void 0||na(l.position,h)!==na(r.dposition))&&(l.position=r.dposition),o[c]=!0;let u=null;if(c in n&&n[c].type===d)u=n[c];else{let f=pt.getScale(d);u=new f({id:c,type:d,ctx:this.ctx,chart:this}),n[u.id]=u}u.init(l,t)}),L(o,(r,l)=>{r||delete n[l]}),L(n,r=>{Z.configure(this,r,r.options),Z.addBox(this,r)})}_updateMetasets(){let t=this._metasets,s=this.data.datasets.length,n=t.length;if(t.sort((o,a)=>o.index-a.index),n>s){for(let o=s;o<n;++o)this._destroyDatasetMeta(o);t.splice(s,n-s)}this._sortedMetasets=t.slice(0).sort(oa("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:s}}=this;t.length>s.length&&delete this._stacks,t.forEach((n,o)=>{s.filter(a=>a===n._dataset).length===0&&this._destroyDatasetMeta(o)})}buildOrUpdateControllers(){let t=[],s=this.data.datasets,n,o;for(this._removeUnreferencedMetasets(),n=0,o=s.length;n<o;n++){let a=s[n],r=this.getDatasetMeta(n),l=a.type||this.config.type;if(r.type&&r.type!==l&&(this._destroyDatasetMeta(n),r=this.getDatasetMeta(n)),r.type=l,r.indexAxis=a.indexAxis||Xs(l,this.options),r.order=a.order||0,r.index=n,r.label=""+a.label,r.visible=this.isDatasetVisible(n),r.controller)r.controller.updateIndex(n),r.controller.linkScales();else{let c=pt.getController(l),{datasetElementType:h,dataElementType:d}=I.datasets[l];Object.assign(c,{dataElementType:pt.getElement(d),datasetElementType:h&&pt.getElement(h)}),r.controller=new c(this,n),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){L(this.data.datasets,(t,s)=>{this.getDatasetMeta(s).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let s=this.config;s.update();let n=this._options=s.createResolver(s.chartOptionScopes(),this.getContext()),o=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;let a=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let h=0,d=this.data.datasets.length;h<d;h++){let{controller:u}=this.getDatasetMeta(h),f=!o&&a.indexOf(u)===-1;u.buildOrUpdateElements(f),r=Math.max(+u.getMaxOverflow(),r)}r=this._minPadding=n.layout.autoPadding?r:0,this._updateLayout(r),o||L(a,h=>{h.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(oa("z","_idx"));let{_active:l,_lastEvent:c}=this;c?this._eventHandler(c,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){L(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,s=new Set(Object.keys(this._listeners)),n=new Set(t.events);(!ss(s,n)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this,s=this._getUniformDataChanges()||[];for(let{method:n,start:o,count:a}of s){let r=n==="_removeElements"?-a:a;ah(t,o,r)}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let s=this.data.datasets.length,n=a=>new Set(t.filter(r=>r[0]===a).map((r,l)=>l+","+r.splice(1).join(","))),o=n(0);for(let a=1;a<s;a++)if(!ss(o,n(a)))return;return Array.from(o).map(a=>a.split(",")).map(a=>({method:a[1],start:+a[2],count:+a[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,t);let s=this.chartArea,n=s.width<=0||s.height<=0;this._layers=[],L(this.boxes,o=>{n&&o.position==="chartArea"||(o.configure&&o.configure(),this._layers.push(...o._layers()))},this),this._layers.forEach((o,a)=>{o._idx=a}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let s=0,n=this.data.datasets.length;s<n;++s)this.getDatasetMeta(s).controller.configure();for(let s=0,n=this.data.datasets.length;s<n;++s)this._updateDataset(s,bt(t)?t({datasetIndex:s}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,s){let n=this.getDatasetMeta(t),o={meta:n,index:t,mode:s,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",o)!==!1&&(n.controller._update(s),o.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",o))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(vt.has(this)?this.attached&&!vt.running(this)&&vt.start(this):(this.draw(),aa({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:n,height:o}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(n,o)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;let s=this._layers;for(t=0;t<s.length&&s[t].z<=0;++t)s[t].draw(this.chartArea);for(this._drawDatasets();t<s.length;++t)s[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let s=this._sortedMetasets,n=[],o,a;for(o=0,a=s.length;o<a;++o){let r=s[o];(!t||r.visible)&&n.push(r)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;let t=this.getSortedVisibleDatasetMetas();for(let s=t.length-1;s>=0;--s)this._drawDataset(t[s]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let s=this.ctx,n={meta:t,index:t.index,cancelable:!0},o=Os(this,t);this.notifyPlugins("beforeDatasetDraw",n)!==!1&&(o&&Ae(s,o),t.controller.draw(),o&&Te(s),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(t){return dt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,s,n,o){let a=cc.modes[s];return typeof a=="function"?a(this,t,n,o):[]}getDatasetMeta(t){let s=this.data.datasets[t],n=this._metasets,o=n.filter(a=>a&&a._dataset===s).pop();return o||(o={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:s&&s.order||0,index:t,_dataset:s,_parsed:[],_sorted:!1},n.push(o)),o}getContext(){return this.$context||(this.$context=yt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let s=this.data.datasets[t];if(!s)return!1;let n=this.getDatasetMeta(t);return typeof n.hidden=="boolean"?!n.hidden:!s.hidden}setDatasetVisibility(t,s){let n=this.getDatasetMeta(t);n.hidden=!s}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,s,n){let o=n?"show":"hide",a=this.getDatasetMeta(t),r=a.controller._resolveAnimations(void 0,o);le(s)?(a.data[s].hidden=!n,this.update()):(this.setDatasetVisibility(t,n),r.update(a,{visible:n}),this.update(l=>l.datasetIndex===t?o:void 0))}hide(t,s){this._updateVisibility(t,s,!1)}show(t,s){this._updateVisibility(t,s,!0)}_destroyDatasetMeta(t){let s=this._metasets[t];s&&s.controller&&s.controller._destroy(),delete this._metasets[t]}_stop(){let t,s;for(this.stop(),vt.remove(this),t=0,s=this.data.datasets.length;t<s;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:s}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),ms(t,s),this.platform.releaseContext(s),this.canvas=null,this.ctx=null),delete Oi[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,s=this.platform,n=(a,r)=>{s.addEventListener(this,a,r),t[a]=r},o=(a,r,l)=>{a.offsetX=r,a.offsetY=l,this._eventHandler(a)};L(this.options.events,a=>n(a,o))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let t=this._responsiveListeners,s=this.platform,n=(c,h)=>{s.addEventListener(this,c,h),t[c]=h},o=(c,h)=>{t[c]&&(s.removeEventListener(this,c,h),delete t[c])},a=(c,h)=>{this.canvas&&this.resize(c,h)},r,l=()=>{o("attach",l),this.attached=!0,this.resize(),n("resize",a),n("detach",r)};r=()=>{this.attached=!1,o("resize",a),this._stop(),this._resize(0,0),n("attach",l)},s.isAttached(this.canvas)?l():r()}unbindEvents(){L(this._listeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._listeners={},L(this._responsiveListeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,s,n){let o=n?"set":"remove",a,r,l,c;for(s==="dataset"&&(a=this.getDatasetMeta(t[0].datasetIndex),a.controller["_"+o+"DatasetHoverStyle"]()),l=0,c=t.length;l<c;++l){r=t[l];let h=r&&this.getDatasetMeta(r.datasetIndex).controller;h&&h[o+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let s=this._active||[],n=t.map(({datasetIndex:a,index:r})=>{let l=this.getDatasetMeta(a);if(!l)throw new Error("No dataset found at index "+a);return{datasetIndex:a,element:l.data[r],index:r}});!Pe(n,s)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,s))}notifyPlugins(t,s,n){return this._plugins.notify(this,t,s,n)}isPluginEnabled(t){return this._plugins._cache.filter(s=>s.plugin.id===t).length===1}_updateHoverStyles(t,s,n){let o=this.options.hover,a=(c,h)=>c.filter(d=>!h.some(u=>d.datasetIndex===u.datasetIndex&&d.index===u.index)),r=a(s,t),l=n?t:a(t,s);r.length&&this.updateHoverStyle(r,o.mode,!1),l.length&&o.mode&&this.updateHoverStyle(l,o.mode,!0)}_eventHandler(t,s){let n={event:t,replay:s,cancelable:!0,inChartArea:this.isPointInArea(t)},o=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",n,o)===!1)return;let a=this._handleEvent(t,s,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,o),(a||n.changed)&&this.render(),this}_handleEvent(t,s,n){let{_active:o=[],options:a}=this,r=s,l=this._getActiveElements(t,o,n,r),c=lo(t),h=rh(t,this._lastEvent,n,c);n&&(this._lastEvent=null,E(a.onHover,[t,l,this],this),c&&E(a.onClick,[t,l,this],this));let d=!Pe(l,o);return(d||s)&&(this._active=l,this._updateHoverStyles(l,o,s)),this._lastEvent=h,d}_getActiveElements(t,s,n,o){if(t.type==="mouseout")return[];if(!n)return s;let a=this.options.hover;return this.getElementsAtEventForMode(t,a.mode,a,o)}}return e})();function la(){return L(je.instances,e=>e._plugins.invalidate())}function lh(e,i,t){let{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=i,c=n/r;e.beginPath(),e.arc(o,a,r,s-c,t+c),l>n?(c=n/l,e.arc(o,a,l,t+c,s-c,!0)):e.arc(o,a,n,t+W,s-W),e.closePath(),e.clip()}function ch(e){return bi(e,["outerStart","outerEnd","innerStart","innerEnd"])}function hh(e,i,t,s){let n=ch(e.options.borderRadius),o=(t-i)/2,a=Math.min(o,s*i/2),r=l=>{let c=(t-Math.min(o,l))*s/2;return $(l,0,Math.min(o,c))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:$(n.innerStart,0,a),innerEnd:$(n.innerEnd,0,a)}}function ge(e,i,t,s){return{x:t+e*Math.cos(i),y:s+e*Math.sin(i)}}function Ri(e,i,t,s,n,o){let{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=i,d=Math.max(i.outerRadius+s+t-c,0),u=h>0?h+s+t+c:0,f=0,g=n-l;if(s){let R=h>0?h-s:0,N=d>0?d-s:0,H=(R+N)/2,rt=H!==0?g*H/(H+s):g;f=(g-rt)/2}let p=Math.max(.001,g*d-t/z)/d,m=(g-p)/2,b=l+m+f,x=n-m-f,{outerStart:y,outerEnd:M,innerStart:_,innerEnd:v}=hh(i,u,d,x-b),k=d-y,S=d-M,w=b+y/k,P=x-M/S,C=u+_,T=u+v,Y=b+_/C,st=x-v/T;if(e.beginPath(),o){let R=(w+P)/2;if(e.arc(a,r,d,w,R),e.arc(a,r,d,R,P),M>0){let G=ge(S,P,a,r);e.arc(G.x,G.y,M,P,x+W)}let N=ge(T,x,a,r);if(e.lineTo(N.x,N.y),v>0){let G=ge(T,st,a,r);e.arc(G.x,G.y,v,x+W,st+Math.PI)}let H=(x-v/u+(b+_/u))/2;if(e.arc(a,r,u,x-v/u,H,!0),e.arc(a,r,u,H,b+_/u,!0),_>0){let G=ge(C,Y,a,r);e.arc(G.x,G.y,_,Y+Math.PI,b-W)}let rt=ge(k,b,a,r);if(e.lineTo(rt.x,rt.y),y>0){let G=ge(k,w,a,r);e.arc(G.x,G.y,y,b-W,w)}}else{e.moveTo(a,r);let R=Math.cos(w)*d+a,N=Math.sin(w)*d+r;e.lineTo(R,N);let H=Math.cos(P)*d+a,rt=Math.sin(P)*d+r;e.lineTo(H,rt)}e.closePath()}function dh(e,i,t,s,n){let{fullCircles:o,startAngle:a,circumference:r}=i,l=i.endAngle;if(o){Ri(e,i,t,s,l,n);for(let c=0;c<o;++c)e.fill();isNaN(r)||(l=a+(r%B||B))}return Ri(e,i,t,s,l,n),e.fill(),l}function uh(e,i,t,s,n){let{fullCircles:o,startAngle:a,circumference:r,options:l}=i,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f=l.borderAlign==="inner";if(!c)return;e.setLineDash(d||[]),e.lineDashOffset=u,f?(e.lineWidth=c*2,e.lineJoin=h||"round"):(e.lineWidth=c,e.lineJoin=h||"bevel");let g=i.endAngle;if(o){Ri(e,i,t,s,g,n);for(let p=0;p<o;++p)e.stroke();isNaN(r)||(g=a+(r%B||B))}f&&lh(e,i,g),o||(Ri(e,i,t,s,g,n),e.stroke())}var Gs=class extends ct{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:i=>i!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(i){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,i&&Object.assign(this,i)}inRange(i,t,s){let n=this.getProps(["x","y"],s),{angle:o,distance:a}=rs(n,{x:i,y:t}),{startAngle:r,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),u=(this.options.spacing+this.options.borderWidth)/2,f=D(d,l-r),g=he(o,r,l)&&r!==l,p=f>=B||g,m=ft(a,c+u,h+u);return p&&m}getCenterPoint(i){let{x:t,y:s,startAngle:n,endAngle:o,innerRadius:a,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],i),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(a+r+c+l)/2;return{x:t+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(i){return this.getCenterPoint(i)}draw(i){let{options:t,circumference:s}=this,n=(t.offset||0)/4,o=(t.spacing||0)/2,a=t.circular;if(this.pixelMargin=t.borderAlign==="inner"?.33:0,this.fullCircles=s>B?Math.floor(s/B):0,s===0||this.innerRadius<0||this.outerRadius<0)return;i.save();let r=(this.startAngle+this.endAngle)/2;i.translate(Math.cos(r)*n,Math.sin(r)*n);let l=1-Math.sin(Math.min(z,s||0)),c=n*l;i.fillStyle=t.backgroundColor,i.strokeStyle=t.borderColor,dh(i,this,c,o,a),uh(i,this,c,o,a),i.restore()}};function Ua(e,i,t=i){e.lineCap=D(t.borderCapStyle,i.borderCapStyle),e.setLineDash(D(t.borderDash,i.borderDash)),e.lineDashOffset=D(t.borderDashOffset,i.borderDashOffset),e.lineJoin=D(t.borderJoinStyle,i.borderJoinStyle),e.lineWidth=D(t.borderWidth,i.borderWidth),e.strokeStyle=D(t.borderColor,i.borderColor)}function fh(e,i,t){e.lineTo(t.x,t.y)}function gh(e){return e.stepped?vo:e.tension||e.cubicInterpolationMode==="monotone"?Mo:fh}function Xa(e,i,t={}){let s=e.length,{start:n=0,end:o=s-1}=t,{start:a,end:r}=i,l=Math.max(n,a),c=Math.min(o,r),h=n<a&&o<a||n>r&&o>r;return{count:s,start:l,loop:i.loop,ilen:c<l&&!h?s+c-l:c-l}}function ph(e,i,t,s){let{points:n,options:o}=i,{count:a,start:r,loop:l,ilen:c}=Xa(n,t,s),h=gh(o),{move:d=!0,reverse:u}=s||{},f,g,p;for(f=0;f<=c;++f)g=n[(r+(u?c-f:f))%a],!g.skip&&(d?(e.moveTo(g.x,g.y),d=!1):h(e,p,g,u,o.stepped),p=g);return l&&(g=n[(r+(u?c:0))%a],h(e,p,g,u,o.stepped)),!!l}function mh(e,i,t,s){let n=i.points,{count:o,start:a,ilen:r}=Xa(n,t,s),{move:l=!0,reverse:c}=s||{},h=0,d=0,u,f,g,p,m,b,x=M=>(a+(c?r-M:M))%o,y=()=>{p!==m&&(e.lineTo(h,m),e.lineTo(h,p),e.lineTo(h,b))};for(l&&(f=n[x(0)],e.moveTo(f.x,f.y)),u=0;u<=r;++u){if(f=n[x(u)],f.skip)continue;let M=f.x,_=f.y,v=M|0;v===g?(_<p?p=_:_>m&&(m=_),h=(d*h+M)/++d):(y(),e.lineTo(M,_),g=v,d=0,p=m=_),b=_}y()}function Zs(e){let i=e.options,t=i.borderDash&&i.borderDash.length;return!e._decimated&&!e._loop&&!i.tension&&i.cubicInterpolationMode!=="monotone"&&!i.stepped&&!t?mh:ph}function bh(e){return e.stepped?To:e.tension||e.cubicInterpolationMode==="monotone"?Lo:Pt}function xh(e,i,t,s){let n=i._path;n||(n=i._path=new Path2D,i.path(n,t,s)&&n.closePath()),Ua(e,i.options),e.stroke(n)}function _h(e,i,t,s){let{segments:n,options:o}=i,a=Zs(i);for(let r of n)Ua(e,o,r.style),e.beginPath(),a(e,i,r,{start:t,end:t+s-1})&&e.closePath(),e.stroke()}var yh=typeof Path2D=="function";function vh(e,i,t,s){yh&&!i.options.segment?xh(e,i,t,s):_h(e,i,t,s)}var Fi=(()=>{class e extends ct{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,s){let n=this.options;if((n.tension||n.cubicInterpolationMode==="monotone")&&!n.stepped&&!this._pointsUpdated){let o=n.spanGaps?this._loop:this._fullLoop;Po(this._points,n,t,o,s),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Eo(this,this.options.segment))}first(){let t=this.segments,s=this.points;return t.length&&s[t[0].start]}last(){let t=this.segments,s=this.points,n=t.length;return n&&s[t[n-1].end]}interpolate(t,s){let n=this.options,o=t[s],a=this.points,r=Ps(this,{property:s,start:o,end:o});if(!r.length)return;let l=[],c=bh(n),h,d;for(h=0,d=r.length;h<d;++h){let{start:u,end:f}=r[h],g=a[u],p=a[f];if(g===p){l.push(g);continue}let m=Math.abs((o-g[s])/(p[s]-g[s])),b=c(g,p,m,n.stepped);b[s]=t[s],l.push(b)}return l.length===1?l[0]:l}pathSegment(t,s,n){return Zs(this)(t,this,s,n)}path(t,s,n){let o=this.segments,a=Zs(this),r=this._loop;s=s||0,n=n||this.points.length-s;for(let l of o)r&=a(t,this,l,{start:s,end:s+n-1});return!!r}draw(t,s,n,o){let a=this.options||{};(this.points||[]).length&&a.borderWidth&&(t.save(),vh(t,this,n,o),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}return e})();function ca(e,i,t,s){let n=e.options,{[t]:o}=e.getProps([t],s);return Math.abs(i-o)<n.radius+n.hitRadius}var Mh=(()=>{class e extends ct{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,s,n){let o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(t-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(t,s){return ca(this,t,"x",s)}inYRange(t,s){return ca(this,t,"y",s)}getCenterPoint(t){let{x:s,y:n}=this.getProps(["x","y"],t);return{x:s,y:n}}size(t){t=t||this.options||{};let s=t.radius||0;s=Math.max(s,s&&t.hoverRadius||0);let n=s&&t.borderWidth||0;return(s+n)*2}draw(t,s){let n=this.options;this.skip||n.radius<.1||!dt(this,s,this.size(n)/2)||(t.strokeStyle=n.borderColor,t.lineWidth=n.borderWidth,t.fillStyle=n.backgroundColor,mi(t,n,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}return e})();function Ka(e,i){let{x:t,y:s,base:n,width:o,height:a}=e.getProps(["x","y","base","width","height"],i),r,l,c,h,d;return e.horizontal?(d=a/2,r=Math.min(t,n),l=Math.max(t,n),c=s-d,h=s+d):(d=o/2,r=t-d,l=t+d,c=Math.min(s,n),h=Math.max(s,n)),{left:r,top:c,right:l,bottom:h}}function Et(e,i,t,s){return e?0:$(i,t,s)}function Sh(e,i,t){let s=e.options.borderWidth,n=e.borderSkipped,o=xs(s);return{t:Et(n.top,o.top,0,t),r:Et(n.right,o.right,0,i),b:Et(n.bottom,o.bottom,0,t),l:Et(n.left,o.left,0,i)}}function kh(e,i,t){let{enableBorderRadius:s}=e.getProps(["enableBorderRadius"]),n=e.options.borderRadius,o=Lt(n),a=Math.min(i,t),r=e.borderSkipped,l=s||A(n);return{topLeft:Et(!l||r.top||r.left,o.topLeft,0,a),topRight:Et(!l||r.top||r.right,o.topRight,0,a),bottomLeft:Et(!l||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:Et(!l||r.bottom||r.right,o.bottomRight,0,a)}}function wh(e){let i=Ka(e),t=i.right-i.left,s=i.bottom-i.top,n=Sh(e,t/2,s/2),o=kh(e,t/2,s/2);return{outer:{x:i.left,y:i.top,w:t,h:s,radius:o},inner:{x:i.left+n.l,y:i.top+n.t,w:t-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function zs(e,i,t,s){let n=i===null,o=t===null,r=e&&!(n&&o)&&Ka(e,s);return r&&(n||ft(i,r.left,r.right))&&(o||ft(t,r.top,r.bottom))}function Dh(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}function Ch(e,i){e.rect(i.x,i.y,i.w,i.h)}function Bs(e,i,t={}){let s=e.x!==t.x?-i:0,n=e.y!==t.y?-i:0,o=(e.x+e.w!==t.x+t.w?i:0)-s,a=(e.y+e.h!==t.y+t.h?i:0)-n;return{x:e.x+s,y:e.y+n,w:e.w+o,h:e.h+a,radius:e.radius}}var Qs=class extends ct{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(i){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,i&&Object.assign(this,i)}draw(i){let{inflateAmount:t,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=wh(this),r=Dh(a.radius)?ue:Ch;i.save(),(a.w!==o.w||a.h!==o.h)&&(i.beginPath(),r(i,Bs(a,t,o)),i.clip(),r(i,Bs(o,-t,a)),i.fillStyle=s,i.fill("evenodd")),i.beginPath(),r(i,Bs(o,t)),i.fillStyle=n,i.fill(),i.restore()}inRange(i,t,s){return zs(this,i,t,s)}inXRange(i,t){return zs(this,i,null,t)}inYRange(i,t){return zs(this,null,i,t)}getCenterPoint(i){let{x:t,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],i);return{x:o?(t+n)/2:t,y:o?s:(s+n)/2}}getRange(i){return i==="x"?this.width/2:this.height/2}},Ph=Object.freeze({__proto__:null,ArcElement:Gs,BarElement:Qs,LineElement:Fi,PointElement:Mh}),Js=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],ha=Js.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function qa(e){return Js[e%Js.length]}function Ga(e){return ha[e%ha.length]}function Oh(e,i){return e.borderColor=qa(i),e.backgroundColor=Ga(i),++i}function Ah(e,i){return e.backgroundColor=e.data.map(()=>qa(i++)),i}function Th(e,i){return e.backgroundColor=e.data.map(()=>Ga(i++)),i}function Lh(e){let i=0;return(t,s)=>{let n=e.getDatasetMeta(s).controller;n instanceof ln?i=Ah(t,i):n instanceof za?i=Th(t,i):n&&(i=Oh(t,i))}}function da(e){let i;for(i in e)if(e[i].borderColor||e[i].backgroundColor)return!0;return!1}function Rh(e){return e&&(e.borderColor||e.backgroundColor)}function Eh(){return I.borderColor!=="rgba(0,0,0,0.1)"||I.backgroundColor!=="rgba(0,0,0,0.1)"}var Ih={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,i,t){if(!t.enabled)return;let{data:{datasets:s},options:n}=e.config,{elements:o}=n,a=da(s)||Rh(n)||o&&da(o)||Eh();if(!t.forceOverride&&a)return;let r=Lh(e);s.forEach(r)}};function Fh(e,i,t,s,n){let o=n.samples||s;if(o>=t)return e.slice(i,i+t);let a=[],r=(t-2)/(o-2),l=0,c=i+t-1,h=i,d,u,f,g,p;for(a[l++]=e[h],d=0;d<o-2;d++){let m=0,b=0,x,y=Math.floor((d+1)*r)+1+i,M=Math.min(Math.floor((d+2)*r)+1,t)+i,_=M-y;for(x=y;x<M;x++)m+=e[x].x,b+=e[x].y;m/=_,b/=_;let v=Math.floor(d*r)+1+i,k=Math.min(Math.floor((d+1)*r)+1,t)+i,{x:S,y:w}=e[h];for(f=g=-1,x=v;x<k;x++)g=.5*Math.abs((S-m)*(e[x].y-w)-(S-e[x].x)*(b-w)),g>f&&(f=g,u=e[x],p=x);a[l++]=u,h=p}return a[l++]=e[c],a}function zh(e,i,t,s){let n=0,o=0,a,r,l,c,h,d,u,f,g,p,m=[],b=i+t-1,x=e[i].x,M=e[b].x-x;for(a=i;a<i+t;++a){r=e[a],l=(r.x-x)/M*s,c=r.y;let _=l|0;if(_===h)c<g?(g=c,d=a):c>p&&(p=c,u=a),n=(o*n+r.x)/++o;else{let v=a-1;if(!O(d)&&!O(u)){let k=Math.min(d,u),S=Math.max(d,u);k!==f&&k!==v&&m.push(kt(St({},e[k]),{x:n})),S!==f&&S!==v&&m.push(kt(St({},e[S]),{x:n}))}a>0&&v!==f&&m.push(e[v]),m.push(r),h=_,o=0,g=p=c,d=u=f=a}}return m}function Za(e){if(e._decimated){let i=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:i})}}function ua(e){e.data.datasets.forEach(i=>{Za(i)})}function Bh(e,i){let t=i.length,s=0,n,{iScale:o}=e,{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=$(ht(i,o.axis,a).lo,0,t-1)),c?n=$(ht(i,o.axis,r).hi+1,s,t)-s:n=t-s,{start:s,count:n}}var Nh={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,i,t)=>{if(!t.enabled){ua(e);return}let s=e.width;e.data.datasets.forEach((n,o)=>{let{_data:a,indexAxis:r}=n,l=e.getDatasetMeta(o),c=a||n.data;if(fe([r,e.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;let h=e.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||e.options.parsing)return;let{start:d,count:u}=Bh(l,c),f=t.threshold||4*s;if(u<=f){Za(n);return}O(a)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}}));let g;switch(t.algorithm){case"lttb":g=Fh(c,d,u,s,t);break;case"min-max":g=zh(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${t.algorithm}'`)}n._decimated=g})},destroy(e){ua(e)}};function Vh(e,i,t){let s=e.segments,n=e.points,o=i.points,a=[];for(let r of s){let{start:l,end:c}=r;c=cn(l,c,n);let h=tn(t,n[l],n[c],r.loop);if(!i.segments){a.push({source:r,target:h,start:n[l],end:n[c]});continue}let d=Ps(i,h);for(let u of d){let f=tn(t,o[u.start],o[u.end],u.loop),g=Cs(r,n,f);for(let p of g)a.push({source:p,target:u,start:{[t]:fa(h,f,"start",Math.max)},end:{[t]:fa(h,f,"end",Math.min)}})}}return a}function tn(e,i,t,s){if(s)return;let n=i[e],o=t[e];return e==="angle"&&(n=J(n),o=J(o)),{property:e,start:n,end:o}}function Wh(e,i){let{x:t=null,y:s=null}=e||{},n=i.points,o=[];return i.segments.forEach(({start:a,end:r})=>{r=cn(a,r,n);let l=n[a],c=n[r];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):t!==null&&(o.push({x:t,y:l.y}),o.push({x:t,y:c.y}))}),o}function cn(e,i,t){for(;i>e;i--){let s=t[i];if(!isNaN(s.x)&&!isNaN(s.y))break}return i}function fa(e,i,t,s){return e&&i?s(e[t],i[t]):e?e[t]:i?i[t]:0}function Qa(e,i){let t=[],s=!1;return F(e)?(s=!0,t=e):t=Wh(e,i),t.length?new Fi({points:t,options:{tension:0},_loop:s,_fullLoop:s}):null}function ga(e){return e&&e.fill!==!1}function Hh(e,i,t){let n=e[i].fill,o=[i],a;if(!t)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!V(n))return n;if(a=e[n],!a)return!1;if(a.visible)return n;o.push(n),n=a.fill}return!1}function jh(e,i,t){let s=Xh(e);if(A(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return V(n)&&Math.floor(n)===n?$h(s[0],i,n,t):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function $h(e,i,t,s){return(e==="-"||e==="+")&&(t=i+t),t===i||t<0||t>=s?!1:t}function Yh(e,i){let t=null;return e==="start"?t=i.bottom:e==="end"?t=i.top:A(e)?t=i.getPixelForValue(e.value):i.getBasePixel&&(t=i.getBasePixel()),t}function Uh(e,i,t){let s;return e==="start"?s=t:e==="end"?s=i.options.reverse?i.min:i.max:A(e)?s=e.value:s=i.getBaseValue(),s}function Xh(e){let i=e.options,t=i.fill,s=D(t&&t.target,t);return s===void 0&&(s=!!i.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Kh(e){let{scale:i,index:t,line:s}=e,n=[],o=s.segments,a=s.points,r=qh(i,t);r.push(Qa({x:null,y:i.bottom},s));for(let l=0;l<o.length;l++){let c=o[l];for(let h=c.start;h<=c.end;h++)Gh(n,a[h],r)}return new Fi({points:n,options:{}})}function qh(e,i){let t=[],s=e.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){let o=s[n];if(o.index===i)break;o.hidden||t.unshift(o.dataset)}return t}function Gh(e,i,t){let s=[];for(let n=0;n<t.length;n++){let o=t[n],{first:a,last:r,point:l}=Zh(o,i,"x");if(!(!l||a&&r)){if(a)s.unshift(l);else if(e.push(l),!r)break}}e.push(...s)}function Zh(e,i,t){let s=e.interpolate(i,t);if(!s)return{};let n=s[t],o=e.segments,a=e.points,r=!1,l=!1;for(let c=0;c<o.length;c++){let h=o[c],d=a[h.start][t],u=a[h.end][t];if(ft(n,d,u)){r=n===d,l=n===u;break}}return{first:r,last:l,point:s}}var Ei=class{constructor(i){this.x=i.x,this.y=i.y,this.radius=i.radius}pathSegment(i,t,s){let{x:n,y:o,radius:a}=this;return t=t||{start:0,end:B},i.arc(n,o,a,t.end,t.start,!0),!s.bounds}interpolate(i){let{x:t,y:s,radius:n}=this,o=i.angle;return{x:t+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}};function Qh(e){let{chart:i,fill:t,line:s}=e;if(V(t))return Jh(i,t);if(t==="stack")return Kh(e);if(t==="shape")return!0;let n=td(e);return n instanceof Ei?n:Qa(n,s)}function Jh(e,i){let t=e.getDatasetMeta(i);return t&&e.isDatasetVisible(i)?t.dataset:null}function td(e){return(e.scale||{}).getPointPositionForValue?id(e):ed(e)}function ed(e){let{scale:i={},fill:t}=e,s=Yh(t,i);if(V(s)){let n=i.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function id(e){let{scale:i,fill:t}=e,s=i.options,n=i.getLabels().length,o=s.reverse?i.max:i.min,a=Uh(t,i,o),r=[];if(s.grid.circular){let l=i.getPointPositionForValue(0,o);return new Ei({x:l.x,y:l.y,radius:i.getDistanceFromCenterForValue(a)})}for(let l=0;l<n;++l)r.push(i.getPointPositionForValue(l,a));return r}function Ns(e,i,t){let s=Qh(i),{chart:n,index:o,line:a,scale:r,axis:l}=i,c=a.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},g=n.getDatasetMeta(o),p=Os(n,g);s&&a.points.length&&(Ae(e,t),sd(e,{line:a,target:s,above:u,below:f,area:t,scale:r,axis:l,clip:p}),Te(e))}function sd(e,i){let{line:t,target:s,above:n,below:o,area:a,scale:r,clip:l}=i,c=t._loop?"angle":i.axis;e.save(),c==="x"&&o!==n&&(pa(e,s,a.top),ma(e,{line:t,target:s,color:n,scale:r,property:c,clip:l}),e.restore(),e.save(),pa(e,s,a.bottom)),ma(e,{line:t,target:s,color:o,scale:r,property:c,clip:l}),e.restore()}function pa(e,i,t){let{segments:s,points:n}=i,o=!0,a=!1;e.beginPath();for(let r of s){let{start:l,end:c}=r,h=n[l],d=n[cn(l,c,n)];o?(e.moveTo(h.x,h.y),o=!1):(e.lineTo(h.x,t),e.lineTo(h.x,h.y)),a=!!i.pathSegment(e,r,{move:a}),a?e.closePath():e.lineTo(d.x,t)}e.lineTo(i.first().x,t),e.closePath(),e.clip()}function ma(e,i){let{line:t,target:s,property:n,color:o,scale:a,clip:r}=i,l=Vh(t,s,n);for(let{source:c,target:h,start:d,end:u}of l){let{style:{backgroundColor:f=o}={}}=c,g=s!==!0;e.save(),e.fillStyle=f,nd(e,a,r,g&&tn(n,d,u)),e.beginPath();let p=!!t.pathSegment(e,c),m;if(g){p?e.closePath():ba(e,s,u,n);let b=!!s.pathSegment(e,h,{move:p,reverse:!0});m=p&&b,m||ba(e,s,d,n)}e.closePath(),e.fill(m?"evenodd":"nonzero"),e.restore()}}function nd(e,i,t,s){let n=i.chart.chartArea,{property:o,start:a,end:r}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=a,c=n.top,h=r,d=n.bottom):(l=n.left,c=a,h=n.right,d=r),e.beginPath(),t&&(l=Math.max(l,t.left),h=Math.min(h,t.right),c=Math.max(c,t.top),d=Math.min(d,t.bottom)),e.rect(l,c,h-l,d-c),e.clip()}}function ba(e,i,t,s){let n=i.interpolate(t,s);n&&e.lineTo(n.x,n.y)}var od={id:"filler",afterDatasetsUpdate(e,i,t){let s=(e.data.datasets||[]).length,n=[],o,a,r,l;for(a=0;a<s;++a)o=e.getDatasetMeta(a),r=o.dataset,l=null,r&&r.options&&r instanceof Fi&&(l={visible:e.isDatasetVisible(a),index:a,fill:jh(r,a,s),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:r}),o.$filler=l,n.push(l);for(a=0;a<s;++a)l=n[a],!(!l||l.fill===!1)&&(l.fill=Hh(n,a,t.propagate))},beforeDraw(e,i,t){let s=t.drawTime==="beforeDraw",n=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let a=n.length-1;a>=0;--a){let r=n[a].$filler;r&&(r.line.updateControlPoints(o,r.axis),s&&r.fill&&Ns(e.ctx,r,o))}},beforeDatasetsDraw(e,i,t){if(t.drawTime!=="beforeDatasetsDraw")return;let s=e.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){let o=s[n].$filler;ga(o)&&Ns(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,i,t){let s=i.meta.$filler;!ga(s)||t.drawTime!=="beforeDatasetDraw"||Ns(e.ctx,s,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},xa=(e,i)=>{let{boxHeight:t=i,boxWidth:s=i}=e;return e.usePointStyle&&(t=Math.min(t,i),s=e.pointStyleWidth||Math.min(s,i)),{boxWidth:s,boxHeight:t,itemHeight:Math.max(i,t)}},ad=(e,i)=>e!==null&&i!==null&&e.datasetIndex===i.datasetIndex&&e.index===i.index,Ii=class extends ct{constructor(i){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=i.chart,this.options=i.options,this.ctx=i.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(i,t,s){this.maxWidth=i,this.maxHeight=t,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let i=this.options.labels||{},t=E(i.generateLabels,[this.chart],this)||[];i.filter&&(t=t.filter(s=>i.filter(s,this.chart.data))),i.sort&&(t=t.sort((s,n)=>i.sort(s,n,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){let{options:i,ctx:t}=this;if(!i.display){this.width=this.height=0;return}let s=i.labels,n=j(s.font),o=n.size,a=this._computeTitleHeight(),{boxWidth:r,itemHeight:l}=xa(s,o),c,h;t.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(a,o,r,l)+10):(h=this.maxHeight,c=this._fitCols(a,n,r,l)+10),this.width=Math.min(c,i.maxWidth||this.maxWidth),this.height=Math.min(h,i.maxHeight||this.maxHeight)}_fitRows(i,t,s,n){let{ctx:o,maxWidth:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+r,d=i;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{let m=s+t/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*r>a)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+r}),d}_fitCols(i,t,s,n){let{ctx:o,maxHeight:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=a-i,d=r,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{let{itemWidth:x,itemHeight:y}=rd(s,t,o,m,n);b>0&&f+y+2*r>h&&(d+=u+r,c.push({width:u,height:f}),g+=u+r,p++,u=f=0),l[b]={left:g,top:f,col:p,width:x,height:y},u=Math.max(u,x),f+=y+r}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;let i=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:s,labels:{padding:n},rtl:o}}=this,a=Yt(o,this.left,this.width);if(this.isHorizontal()){let r=0,l=K(s,this.left+n,this.right-this.lineWidths[r]);for(let c of t)r!==c.row&&(r=c.row,l=K(s,this.left+n,this.right-this.lineWidths[r])),c.top+=this.top+i+n,c.left=a.leftForLtr(a.x(l),c.width),l+=c.width+n}else{let r=0,l=K(s,this.top+i+n,this.bottom-this.columnSizes[r].height);for(let c of t)c.col!==r&&(r=c.col,l=K(s,this.top+i+n,this.bottom-this.columnSizes[r].height)),c.top=l,c.left+=this.left+n,c.left=a.leftForLtr(a.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){let i=this.ctx;Ae(i,this),this._draw(),Te(i)}}_draw(){let{options:i,columnSizes:t,lineWidths:s,ctx:n}=this,{align:o,labels:a}=i,r=I.color,l=Yt(i.rtl,this.left,this.width),c=j(a.font),{padding:h}=a,d=c.size,u=d/2,f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;let{boxWidth:g,boxHeight:p,itemHeight:m}=xa(a,d),b=function(v,k,S){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();let w=D(S.lineWidth,1);if(n.fillStyle=D(S.fillStyle,r),n.lineCap=D(S.lineCap,"butt"),n.lineDashOffset=D(S.lineDashOffset,0),n.lineJoin=D(S.lineJoin,"miter"),n.lineWidth=w,n.strokeStyle=D(S.strokeStyle,r),n.setLineDash(D(S.lineDash,[])),a.usePointStyle){let P={radius:p*Math.SQRT2/2,pointStyle:S.pointStyle,rotation:S.rotation,borderWidth:w},C=l.xPlus(v,g/2),T=k+u;bs(n,P,C,T,a.pointStyleWidth&&g)}else{let P=k+Math.max((d-p)/2,0),C=l.leftForLtr(v,g),T=Lt(S.borderRadius);n.beginPath(),Object.values(T).some(Y=>Y!==0)?ue(n,{x:C,y:P,w:g,h:p,radius:T}):n.rect(C,P,g,p),n.fill(),w!==0&&n.stroke()}n.restore()},x=function(v,k,S){Tt(n,S.text,v,k+m/2,c,{strikethrough:S.hidden,textAlign:l.textAlign(S.textAlign)})},y=this.isHorizontal(),M=this._computeTitleHeight();y?f={x:K(o,this.left+h,this.right-s[0]),y:this.top+h+M,line:0}:f={x:this.left+h,y:K(o,this.top+M+h,this.bottom-t[0].height),line:0},ws(this.ctx,i.textDirection);let _=m+h;this.legendItems.forEach((v,k)=>{n.strokeStyle=v.fontColor,n.fillStyle=v.fontColor;let S=n.measureText(v.text).width,w=l.textAlign(v.textAlign||(v.textAlign=a.textAlign)),P=g+u+S,C=f.x,T=f.y;l.setWidth(this.width),y?k>0&&C+P+h>this.right&&(T=f.y+=_,f.line++,C=f.x=K(o,this.left+h,this.right-s[f.line])):k>0&&T+_>this.bottom&&(C=f.x=C+t[f.line].width+h,f.line++,T=f.y=K(o,this.top+M+h,this.bottom-t[f.line].height));let Y=l.x(C);if(b(Y,T,v),C=xo(w,C+g+u,y?C+P:this.right,i.rtl),x(l.x(C),T,v),y)f.x+=P+h;else if(typeof v.text!="string"){let st=c.lineHeight;f.y+=Ja(v,st)+h}else f.y+=_}),Ds(this.ctx,i.textDirection)}drawTitle(){let i=this.options,t=i.title,s=j(t.font),n=q(t.padding);if(!t.display)return;let o=Yt(i.rtl,this.left,this.width),a=this.ctx,r=t.position,l=s.size/2,c=n.top+l,h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=K(i.align,d,this.right-u);else{let g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+K(i.align,this.top,this.bottom-g-i.labels.padding-this._computeTitleHeight())}let f=K(r,d,d+u);a.textAlign=o.textAlign(gi(r)),a.textBaseline="middle",a.strokeStyle=t.color,a.fillStyle=t.color,a.font=s.string,Tt(a,t.text,f,h,s)}_computeTitleHeight(){let i=this.options.title,t=j(i.font),s=q(i.padding);return i.display?t.lineHeight+s.height:0}_getLegendItemAt(i,t){let s,n,o;if(ft(i,this.left,this.right)&&ft(t,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],ft(i,n.left,n.left+n.width)&&ft(t,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(i){let t=this.options;if(!hd(i.type,t))return;let s=this._getLegendItemAt(i.x,i.y);if(i.type==="mousemove"||i.type==="mouseout"){let n=this._hoveredItem,o=ad(n,s);n&&!o&&E(t.onLeave,[i,n,this],this),this._hoveredItem=s,s&&!o&&E(t.onHover,[i,s,this],this)}else s&&E(t.onClick,[i,s,this],this)}};function rd(e,i,t,s,n){let o=ld(s,e,i,t),a=cd(n,s,i.lineHeight);return{itemWidth:o,itemHeight:a}}function ld(e,i,t,s){let n=e.text;return n&&typeof n!="string"&&(n=n.reduce((o,a)=>o.length>a.length?o:a)),i+t.size/2+s.measureText(n).width}function cd(e,i,t){let s=e;return typeof i.text!="string"&&(s=Ja(i,t)),s}function Ja(e,i){let t=e.text?e.text.length:0;return i*t}function hd(e,i){return!!((e==="mousemove"||e==="mouseout")&&(i.onHover||i.onLeave)||i.onClick&&(e==="click"||e==="mouseup"))}var dd={id:"legend",_element:Ii,start(e,i,t){let s=e.legend=new Ii({ctx:e.ctx,options:t,chart:e});Z.configure(e,s,t),Z.addBox(e,s)},stop(e){Z.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,i,t){let s=e.legend;Z.configure(e,s,t),s.options=t},afterUpdate(e){let i=e.legend;i.buildLabels(),i.adjustHitBoxes()},afterEvent(e,i){i.replay||e.legend.handleEvent(i.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,i,t){let s=i.datasetIndex,n=t.chart;n.isDatasetVisible(s)?(n.hide(s),i.hidden=!0):(n.show(s),i.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){let i=e.data.datasets,{labels:{usePointStyle:t,pointStyle:s,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{let c=l.controller.getStyle(t?0:void 0),h=q(c.borderWidth);return{text:i[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:a&&(r||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}},Ve=class extends ct{constructor(i){super(),this.chart=i.chart,this.options=i.options,this.ctx=i.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(i,t){let s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=i,this.height=this.bottom=t;let n=F(s.text)?s.text.length:1;this._padding=q(s.padding);let o=n*j(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){let i=this.options.position;return i==="top"||i==="bottom"}_drawArgs(i){let{top:t,left:s,bottom:n,right:o,options:a}=this,r=a.align,l=0,c,h,d;return this.isHorizontal()?(h=K(r,s,o),d=t+i,c=o-s):(a.position==="left"?(h=s+i,d=K(r,n,t),l=z*-.5):(h=o-i,d=K(r,t,n),l=z*.5),c=n-t),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){let i=this.ctx,t=this.options;if(!t.display)return;let s=j(t.font),o=s.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:l,rotation:c}=this._drawArgs(o);Tt(i,t.text,0,0,s,{color:t.color,maxWidth:l,rotation:c,textAlign:gi(t.align),textBaseline:"middle",translation:[a,r]})}};function ud(e,i){let t=new Ve({ctx:e.ctx,options:i,chart:e});Z.configure(e,t,i),Z.addBox(e,t),e.titleBlock=t}var fd={id:"title",_element:Ve,start(e,i,t){ud(e,t)},stop(e){let i=e.titleBlock;Z.removeBox(e,i),delete e.titleBlock},beforeUpdate(e,i,t){let s=e.titleBlock;Z.configure(e,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},wi=new WeakMap,gd={id:"subtitle",start(e,i,t){let s=new Ve({ctx:e.ctx,options:t,chart:e});Z.configure(e,s,t),Z.addBox(e,s),wi.set(e,s)},stop(e){Z.removeBox(e,wi.get(e)),wi.delete(e)},beforeUpdate(e,i,t){let s=wi.get(e);Z.configure(e,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Be={average(e){if(!e.length)return!1;let i,t,s=new Set,n=0,o=0;for(i=0,t=e.length;i<t;++i){let r=e[i].element;if(r&&r.hasValue()){let l=r.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((r,l)=>r+l)/s.size,y:n/o}},nearest(e,i){if(!e.length)return!1;let t=i.x,s=i.y,n=Number.POSITIVE_INFINITY,o,a,r;for(o=0,a=e.length;o<a;++o){let l=e[o].element;if(l&&l.hasValue()){let c=l.getCenterPoint(),h=ci(i,c);h<n&&(n=h,r=l)}}if(r){let l=r.tooltipPosition();t=l.x,s=l.y}return{x:t,y:s}}};function gt(e,i){return i&&(F(i)?Array.prototype.push.apply(e,i):e.push(i)),e}function Mt(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function pd(e,i){let{element:t,datasetIndex:s,index:n}=i,o=e.getDatasetMeta(s).controller,{label:a,value:r}=o.getLabelAndValue(n);return{chart:e,label:a,parsed:o.getParsed(n),raw:e.data.datasets[s].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:t}}function _a(e,i){let t=e.chart.ctx,{body:s,footer:n,title:o}=e,{boxWidth:a,boxHeight:r}=i,l=j(i.bodyFont),c=j(i.titleFont),h=j(i.footerFont),d=o.length,u=n.length,f=s.length,g=q(i.padding),p=g.height,m=0,b=s.reduce((M,_)=>M+_.before.length+_.lines.length+_.after.length,0);if(b+=e.beforeBody.length+e.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*i.titleSpacing+i.titleMarginBottom),b){let M=i.displayColors?Math.max(r,l.lineHeight):l.lineHeight;p+=f*M+(b-f)*l.lineHeight+(b-1)*i.bodySpacing}u&&(p+=i.footerMarginTop+u*h.lineHeight+(u-1)*i.footerSpacing);let x=0,y=function(M){m=Math.max(m,t.measureText(M).width+x)};return t.save(),t.font=c.string,L(e.title,y),t.font=l.string,L(e.beforeBody.concat(e.afterBody),y),x=i.displayColors?a+2+i.boxPadding:0,L(s,M=>{L(M.before,y),L(M.lines,y),L(M.after,y)}),x=0,t.font=h.string,L(e.footer,y),t.restore(),m+=g.width,{width:m,height:p}}function md(e,i){let{y:t,height:s}=i;return t<s/2?"top":t>e.height-s/2?"bottom":"center"}function bd(e,i,t,s){let{x:n,width:o}=s,a=t.caretSize+t.caretPadding;if(e==="left"&&n+o+a>i.width||e==="right"&&n-o-a<0)return!0}function xd(e,i,t,s){let{x:n,width:o}=t,{width:a,chartArea:{left:r,right:l}}=e,c="center";return s==="center"?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right"),bd(c,e,i,t)&&(c="center"),c}function ya(e,i,t){let s=t.yAlign||i.yAlign||md(e,t);return{xAlign:t.xAlign||i.xAlign||xd(e,i,t,s),yAlign:s}}function _d(e,i){let{x:t,width:s}=e;return i==="right"?t-=s:i==="center"&&(t-=s/2),t}function yd(e,i,t){let{y:s,height:n}=e;return i==="top"?s+=t:i==="bottom"?s-=n+t:s-=n/2,s}function va(e,i,t,s){let{caretSize:n,caretPadding:o,cornerRadius:a}=e,{xAlign:r,yAlign:l}=t,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Lt(a),g=_d(i,r),p=yd(i,l,c);return l==="center"?r==="left"?g+=c:r==="right"&&(g-=c):r==="left"?g-=Math.max(h,u)+n:r==="right"&&(g+=Math.max(d,f)+n),{x:$(g,0,s.width-i.width),y:$(p,0,s.height-i.height)}}function Di(e,i,t){let s=q(t.padding);return i==="center"?e.x+e.width/2:i==="right"?e.x+e.width-s.right:e.x+s.left}function Ma(e){return gt([],Mt(e))}function vd(e,i,t){return yt(e,{tooltip:i,tooltipItems:t,type:"tooltip"})}function Sa(e,i){let t=i&&i.dataset&&i.dataset.tooltip&&i.dataset.tooltip.callbacks;return t?e.override(t):e}var tr={beforeTitle:ut,title(e){if(e.length>0){let i=e[0],t=i.chart.data.labels,s=t?t.length:0;if(this&&this.options&&this.options.mode==="dataset")return i.dataset.label||"";if(i.label)return i.label;if(s>0&&i.dataIndex<s)return t[i.dataIndex]}return""},afterTitle:ut,beforeBody:ut,beforeLabel:ut,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let i=e.dataset.label||"";i&&(i+=": ");let t=e.formattedValue;return O(t)||(i+=t),i},labelColor(e){let t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){let t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:ut,afterBody:ut,beforeFooter:ut,footer:ut,afterFooter:ut};function et(e,i,t,s){let n=e[i].call(t,s);return typeof n>"u"?tr[i].call(t,s):n}var ka=(()=>{class e extends ct{static positioners=Be;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let s=this.chart,n=this.options.setContext(this.getContext()),o=n.enabled&&s.options.animation&&n.animations,a=new Ai(this.chart,o);return o._cacheable&&(this._cachedAnimations=Object.freeze(a)),a}getContext(){return this.$context||(this.$context=vd(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,s){let{callbacks:n}=s,o=et(n,"beforeTitle",this,t),a=et(n,"title",this,t),r=et(n,"afterTitle",this,t),l=[];return l=gt(l,Mt(o)),l=gt(l,Mt(a)),l=gt(l,Mt(r)),l}getBeforeBody(t,s){return Ma(et(s.callbacks,"beforeBody",this,t))}getBody(t,s){let{callbacks:n}=s,o=[];return L(t,a=>{let r={before:[],lines:[],after:[]},l=Sa(n,a);gt(r.before,Mt(et(l,"beforeLabel",this,a))),gt(r.lines,et(l,"label",this,a)),gt(r.after,Mt(et(l,"afterLabel",this,a))),o.push(r)}),o}getAfterBody(t,s){return Ma(et(s.callbacks,"afterBody",this,t))}getFooter(t,s){let{callbacks:n}=s,o=et(n,"beforeFooter",this,t),a=et(n,"footer",this,t),r=et(n,"afterFooter",this,t),l=[];return l=gt(l,Mt(o)),l=gt(l,Mt(a)),l=gt(l,Mt(r)),l}_createItems(t){let s=this._active,n=this.chart.data,o=[],a=[],r=[],l=[],c,h;for(c=0,h=s.length;c<h;++c)l.push(pd(this.chart,s[c]));return t.filter&&(l=l.filter((d,u,f)=>t.filter(d,u,f,n))),t.itemSort&&(l=l.sort((d,u)=>t.itemSort(d,u,n))),L(l,d=>{let u=Sa(t.callbacks,d);o.push(et(u,"labelColor",this,d)),a.push(et(u,"labelPointStyle",this,d)),r.push(et(u,"labelTextColor",this,d))}),this.labelColors=o,this.labelPointStyles=a,this.labelTextColors=r,this.dataPoints=l,l}update(t,s){let n=this.options.setContext(this.getContext()),o=this._active,a,r=[];if(!o.length)this.opacity!==0&&(a={opacity:0});else{let l=Be[n.position].call(this,o,this._eventPosition);r=this._createItems(n),this.title=this.getTitle(r,n),this.beforeBody=this.getBeforeBody(r,n),this.body=this.getBody(r,n),this.afterBody=this.getAfterBody(r,n),this.footer=this.getFooter(r,n);let c=this._size=_a(this,n),h=Object.assign({},l,c),d=ya(this.chart,n,h),u=va(n,h,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,a={opacity:1,x:u.x,y:u.y,width:c.width,height:c.height,caretX:l.x,caretY:l.y}}this._tooltipItems=r,this.$context=void 0,a&&this._resolveAnimations().update(this,a),t&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:s})}drawCaret(t,s,n,o){let a=this.getCaretPosition(t,n,o);s.lineTo(a.x1,a.y1),s.lineTo(a.x2,a.y2),s.lineTo(a.x3,a.y3)}getCaretPosition(t,s,n){let{xAlign:o,yAlign:a}=this,{caretSize:r,cornerRadius:l}=n,{topLeft:c,topRight:h,bottomLeft:d,bottomRight:u}=Lt(l),{x:f,y:g}=t,{width:p,height:m}=s,b,x,y,M,_,v;return a==="center"?(_=g+m/2,o==="left"?(b=f,x=b-r,M=_+r,v=_-r):(b=f+p,x=b+r,M=_-r,v=_+r),y=b):(o==="left"?x=f+Math.max(c,d)+r:o==="right"?x=f+p-Math.max(h,u)-r:x=this.caretX,a==="top"?(M=g,_=M-r,b=x-r,y=x+r):(M=g+m,_=M+r,b=x+r,y=x-r),v=M),{x1:b,x2:x,x3:y,y1:M,y2:_,y3:v}}drawTitle(t,s,n){let o=this.title,a=o.length,r,l,c;if(a){let h=Yt(n.rtl,this.x,this.width);for(t.x=Di(this,n.titleAlign,n),s.textAlign=h.textAlign(n.titleAlign),s.textBaseline="middle",r=j(n.titleFont),l=n.titleSpacing,s.fillStyle=n.titleColor,s.font=r.string,c=0;c<a;++c)s.fillText(o[c],h.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+l,c+1===a&&(t.y+=n.titleMarginBottom-l)}}_drawColorBox(t,s,n,o,a){let r=this.labelColors[n],l=this.labelPointStyles[n],{boxHeight:c,boxWidth:h}=a,d=j(a.bodyFont),u=Di(this,"left",a),f=o.x(u),g=c<d.lineHeight?(d.lineHeight-c)/2:0,p=s.y+g;if(a.usePointStyle){let m={radius:Math.min(h,c)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},b=o.leftForLtr(f,h)+h/2,x=p+c/2;t.strokeStyle=a.multiKeyBackground,t.fillStyle=a.multiKeyBackground,mi(t,m,b,x),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,mi(t,m,b,x)}else{t.lineWidth=A(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;let m=o.leftForLtr(f,h),b=o.leftForLtr(o.xPlus(f,1),h-2),x=Lt(r.borderRadius);Object.values(x).some(y=>y!==0)?(t.beginPath(),t.fillStyle=a.multiKeyBackground,ue(t,{x:m,y:p,w:h,h:c,radius:x}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ue(t,{x:b,y:p+1,w:h-2,h:c-2,radius:x}),t.fill()):(t.fillStyle=a.multiKeyBackground,t.fillRect(m,p,h,c),t.strokeRect(m,p,h,c),t.fillStyle=r.backgroundColor,t.fillRect(b,p+1,h-2,c-2))}t.fillStyle=this.labelTextColors[n]}drawBody(t,s,n){let{body:o}=this,{bodySpacing:a,bodyAlign:r,displayColors:l,boxHeight:c,boxWidth:h,boxPadding:d}=n,u=j(n.bodyFont),f=u.lineHeight,g=0,p=Yt(n.rtl,this.x,this.width),m=function(w){s.fillText(w,p.x(t.x+g),t.y+f/2),t.y+=f+a},b=p.textAlign(r),x,y,M,_,v,k,S;for(s.textAlign=r,s.textBaseline="middle",s.font=u.string,t.x=Di(this,b,n),s.fillStyle=n.bodyColor,L(this.beforeBody,m),g=l&&b!=="right"?r==="center"?h/2+d:h+2+d:0,_=0,k=o.length;_<k;++_){for(x=o[_],y=this.labelTextColors[_],s.fillStyle=y,L(x.before,m),M=x.lines,l&&M.length&&(this._drawColorBox(s,t,_,p,n),f=Math.max(u.lineHeight,c)),v=0,S=M.length;v<S;++v)m(M[v]),f=u.lineHeight;L(x.after,m)}g=0,f=u.lineHeight,L(this.afterBody,m),t.y-=a}drawFooter(t,s,n){let o=this.footer,a=o.length,r,l;if(a){let c=Yt(n.rtl,this.x,this.width);for(t.x=Di(this,n.footerAlign,n),t.y+=n.footerMarginTop,s.textAlign=c.textAlign(n.footerAlign),s.textBaseline="middle",r=j(n.footerFont),s.fillStyle=n.footerColor,s.font=r.string,l=0;l<a;++l)s.fillText(o[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+n.footerSpacing}}drawBackground(t,s,n,o){let{xAlign:a,yAlign:r}=this,{x:l,y:c}=t,{width:h,height:d}=n,{topLeft:u,topRight:f,bottomLeft:g,bottomRight:p}=Lt(o.cornerRadius);s.fillStyle=o.backgroundColor,s.strokeStyle=o.borderColor,s.lineWidth=o.borderWidth,s.beginPath(),s.moveTo(l+u,c),r==="top"&&this.drawCaret(t,s,n,o),s.lineTo(l+h-f,c),s.quadraticCurveTo(l+h,c,l+h,c+f),r==="center"&&a==="right"&&this.drawCaret(t,s,n,o),s.lineTo(l+h,c+d-p),s.quadraticCurveTo(l+h,c+d,l+h-p,c+d),r==="bottom"&&this.drawCaret(t,s,n,o),s.lineTo(l+g,c+d),s.quadraticCurveTo(l,c+d,l,c+d-g),r==="center"&&a==="left"&&this.drawCaret(t,s,n,o),s.lineTo(l,c+u),s.quadraticCurveTo(l,c,l+u,c),s.closePath(),s.fill(),o.borderWidth>0&&s.stroke()}_updateAnimationTarget(t){let s=this.chart,n=this.$animations,o=n&&n.x,a=n&&n.y;if(o||a){let r=Be[t.position].call(this,this._active,this._eventPosition);if(!r)return;let l=this._size=_a(this,t),c=Object.assign({},r,this._size),h=ya(s,t,c),d=va(t,c,h,s);(o._to!==d.x||a._to!==d.y)&&(this.xAlign=h.xAlign,this.yAlign=h.yAlign,this.width=l.width,this.height=l.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(t){let s=this.options.setContext(this.getContext()),n=this.opacity;if(!n)return;this._updateAnimationTarget(s);let o={width:this.width,height:this.height},a={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;let r=q(s.padding),l=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;s.enabled&&l&&(t.save(),t.globalAlpha=n,this.drawBackground(a,t,o,s),ws(t,s.textDirection),a.y+=r.top,this.drawTitle(a,t,s),this.drawBody(a,t,s),this.drawFooter(a,t,s),Ds(t,s.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,s){let n=this._active,o=t.map(({datasetIndex:l,index:c})=>{let h=this.chart.getDatasetMeta(l);if(!h)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:h.data[c],index:c}}),a=!Pe(n,o),r=this._positionChanged(o,s);(a||r)&&(this._active=o,this._eventPosition=s,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,s,n=!0){if(s&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let o=this.options,a=this._active||[],r=this._getActiveElements(t,a,s,n),l=this._positionChanged(r,t),c=s||!Pe(r,a)||l;return c&&(this._active=r,(o.enabled||o.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,s))),c}_getActiveElements(t,s,n,o){let a=this.options;if(t.type==="mouseout")return[];if(!o)return s.filter(l=>this.chart.data.datasets[l.datasetIndex]&&this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index)!==void 0);let r=this.chart.getElementsAtEventForMode(t,a.mode,a,n);return a.reverse&&r.reverse(),r}_positionChanged(t,s){let{caretX:n,caretY:o,options:a}=this,r=Be[a.position].call(this,t,s);return r!==!1&&(n!==r.x||o!==r.y)}}return e})(),Md={id:"tooltip",_element:ka,positioners:Be,afterInit(e,i,t){t&&(e.tooltip=new ka({chart:e,options:t}))},beforeUpdate(e,i,t){e.tooltip&&e.tooltip.initialize(t)},reset(e,i,t){e.tooltip&&e.tooltip.initialize(t)},afterDraw(e){let i=e.tooltip;if(i&&i._willRender()){let t={tooltip:i};if(e.notifyPlugins("beforeTooltipDraw",kt(St({},t),{cancelable:!0}))===!1)return;i.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",t)}},afterEvent(e,i){if(e.tooltip){let t=i.replay;e.tooltip.handleEvent(i.event,t,i.inChartArea)&&(i.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,i)=>i.bodyFont.size,boxWidth:(e,i)=>i.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:tr},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Sd=Object.freeze({__proto__:null,Colors:Ih,Decimation:Nh,Filler:od,Legend:dd,SubTitle:gd,Title:fd,Tooltip:Md}),kd=(e,i,t,s)=>(typeof i=="string"?(t=e.push(i)-1,s.unshift({index:t,label:i})):isNaN(i)&&(t=null),t);function wd(e,i,t,s){let n=e.indexOf(i);if(n===-1)return kd(e,i,t,s);let o=e.lastIndexOf(i);return n!==o?t:n}var Dd=(e,i)=>e===null?null:$(Math.round(e),0,i);function wa(e){let i=this.getLabels();return e>=0&&e<i.length?i[e]:e}var Cd=(()=>{class e extends Kt{static id="category";static defaults={ticks:{callback:wa}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let s=this._addedLabels;if(s.length){let n=this.getLabels();for(let{index:o,label:a}of s)n[o]===a&&n.splice(o,1);this._addedLabels=[]}super.init(t)}parse(t,s){if(O(t))return null;let n=this.getLabels();return s=isFinite(s)&&n[s]===t?s:wd(n,t,D(s,t),this._addedLabels),Dd(s,n.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:s}=this.getUserBounds(),{min:n,max:o}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(n=0),s||(o=this.getLabels().length-1)),this.min=n,this.max=o}buildTicks(){let t=this.min,s=this.max,n=this.options.offset,o=[],a=this.getLabels();a=t===0&&s===a.length-1?a:a.slice(t,s+1),this._valueRange=Math.max(a.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let r=t;r<=s;r++)o.push({value:r});return o}getLabelForValue(t){return wa.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let s=this.ticks;return t<0||t>s.length-1?null:this.getPixelForValue(s[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}return e})();function Pd(e,i){let t=[],{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=e,f=o||1,g=h-1,{min:p,max:m}=i,b=!O(a),x=!O(r),y=!O(c),M=(m-p)/(d+1),_=ns((m-p)/g/f)*f,v,k,S,w;if(_<1e-14&&!b&&!x)return[{value:p},{value:m}];w=Math.ceil(m/_)-Math.floor(p/_),w>g&&(_=ns(w*_/g/f)*f),O(l)||(v=Math.pow(10,l),_=Math.ceil(_*v)/v),n==="ticks"?(k=Math.floor(p/_)*_,S=Math.ceil(m/_)*_):(k=p,S=m),b&&x&&o&&ho((r-a)/o,_/1e3)?(w=Math.round(Math.min((r-a)/_,h)),_=(r-a)/w,k=a,S=r):y?(k=b?a:k,S=x?r:S,w=c-1,_=(S-k)/w):(w=(S-k)/_,ce(w,Math.round(w),_/1e3)?w=Math.round(w):w=Math.ceil(w));let P=Math.max(as(_),as(k));v=Math.pow(10,O(l)?P:l),k=Math.round(k*v)/v,S=Math.round(S*v)/v;let C=0;for(b&&(u&&k!==a?(t.push({value:a}),k<a&&C++,ce(Math.round((k+C*_)*v)/v,a,Da(a,M,e))&&C++):k<a&&C++);C<w;++C){let T=Math.round((k+C*_)*v)/v;if(x&&T>r)break;t.push({value:T})}return x&&u&&S!==r?t.length&&ce(t[t.length-1].value,r,Da(r,M,e))?t[t.length-1].value=r:t.push({value:r}):(!x||S===r)&&t.push({value:S}),t}function Da(e,i,{horizontal:t,minRotation:s}){let n=at(s),o=(t?Math.sin(n):Math.cos(n))||.001,a=.75*i*(""+e).length;return Math.min(i/o,a)}var me=class extends Kt{constructor(i){super(i),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(i,t){return O(i)||(typeof i=="number"||i instanceof Number)&&!isFinite(+i)?null:+i}handleTickRangeOptions(){let{beginAtZero:i}=this.options,{minDefined:t,maxDefined:s}=this.getUserBounds(),{min:n,max:o}=this,a=l=>n=t?n:l,r=l=>o=s?o:l;if(i){let l=lt(n),c=lt(o);l<0&&c<0?r(0):l>0&&c>0&&a(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);r(o+l),i||a(n-l)}this.min=n,this.max=o}getTickLimit(){let i=this.options.ticks,{maxTicksLimit:t,stepSize:s}=i,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),t=t||11),t&&(n=Math.min(t,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let i=this.options,t=i.ticks,s=this.getTickLimit();s=Math.max(2,s);let n={maxTicks:s,bounds:i.bounds,min:i.min,max:i.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:t.includeBounds!==!1},o=this._range||this,a=Pd(n,o);return i.bounds==="ticks"&&os(a,this,"value"),i.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){let i=this.ticks,t=this.min,s=this.max;if(super.configure(),this.options.offset&&i.length){let n=(s-t)/Math.max(i.length-1,1)/2;t-=n,s+=n}this._startValue=t,this._endValue=s,this._valueRange=s-t}getLabelForValue(i){return de(i,this.chart.options.locale,this.options.ticks.format)}},en=class extends me{static id="linear";static defaults={ticks:{callback:Oe.formatters.numeric}};determineDataLimits(){let{min:i,max:t}=this.getMinMax(!0);this.min=V(i)?i:0,this.max=V(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){let i=this.isHorizontal(),t=i?this.width:this.height,s=at(this.options.ticks.minRotation),n=(i?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,o.lineHeight/n))}getPixelForValue(i){return i===null?NaN:this.getPixelForDecimal((i-this._startValue)/this._valueRange)}getValueForPixel(i){return this._startValue+this.getDecimalForPixel(i)*this._valueRange}},We=e=>Math.floor(xt(e)),Xt=(e,i)=>Math.pow(10,We(e)+i);function Ca(e){return e/Math.pow(10,We(e))===1}function Pa(e,i,t){let s=Math.pow(10,t),n=Math.floor(e/s);return Math.ceil(i/s)-n}function Od(e,i){let t=i-e,s=We(t);for(;Pa(e,i,s)>10;)s++;for(;Pa(e,i,s)<10;)s--;return Math.min(s,We(e))}function Ad(e,{min:i,max:t}){i=tt(e.min,i);let s=[],n=We(i),o=Od(i,t),a=o<0?Math.pow(10,Math.abs(o)):1,r=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((i-l)*a)/a,h=Math.floor((i-l)/r/10)*r*10,d=Math.floor((c-h)/Math.pow(10,o)),u=tt(e.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);for(;u<t;)s.push({value:u,major:Ca(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),u=Math.round((l+h+d*Math.pow(10,o))*a)/a;let f=tt(e.max,u);return s.push({value:f,major:Ca(f),significand:d}),s}var sn=class extends Kt{static id="logarithmic";static defaults={ticks:{callback:Oe.formatters.logarithmic,major:{enabled:!0}}};constructor(i){super(i),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(i,t){let s=me.prototype.parse.apply(this,[i,t]);if(s===0){this._zero=!0;return}return V(s)&&s>0?s:null}determineDataLimits(){let{min:i,max:t}=this.getMinMax(!0);this.min=V(i)?Math.max(0,i):null,this.max=V(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!V(this._userMin)&&(this.min=i===Xt(this.min,0)?Xt(this.min,-1):Xt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:i,maxDefined:t}=this.getUserBounds(),s=this.min,n=this.max,o=r=>s=i?s:r,a=r=>n=t?n:r;s===n&&(s<=0?(o(1),a(10)):(o(Xt(s,-1)),a(Xt(n,1)))),s<=0&&o(Xt(n,-1)),n<=0&&a(Xt(s,1)),this.min=s,this.max=n}buildTicks(){let i=this.options,t={min:this._userMin,max:this._userMax},s=Ad(t,this);return i.bounds==="ticks"&&os(s,this,"value"),i.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(i){return i===void 0?"0":de(i,this.chart.options.locale,this.options.ticks.format)}configure(){let i=this.min;super.configure(),this._startValue=xt(i),this._valueRange=xt(this.max)-xt(i)}getPixelForValue(i){return(i===void 0||i===0)&&(i=this.min),i===null||isNaN(i)?NaN:this.getPixelForDecimal(i===this.min?0:(xt(i)-this._startValue)/this._valueRange)}getValueForPixel(i){let t=this.getDecimalForPixel(i);return Math.pow(10,this._startValue+t*this._valueRange)}};function nn(e){let i=e.ticks;if(i.display&&e.display){let t=q(i.backdropPadding);return D(i.font&&i.font.size,I.font.size)+t.height}return 0}function Td(e,i,t){return t=F(t)?t:[t],{w:yo(e,i.string,t),h:t.length*i.lineHeight}}function Oa(e,i,t,s,n){return e===s||e===n?{start:i-t/2,end:i+t/2}:e<s||e>n?{start:i-t,end:i}:{start:i,end:i+t}}function Ld(e){let i={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},t=Object.assign({},i),s=[],n=[],o=e._pointLabels.length,a=e.options.pointLabels,r=a.centerPointLabels?z/o:0;for(let l=0;l<o;l++){let c=a.setContext(e.getPointLabelContext(l));n[l]=c.padding;let h=e.getPointPosition(l,e.drawingArea+n[l],r),d=j(c.font),u=Td(e.ctx,d,e._pointLabels[l]);s[l]=u;let f=J(e.getIndexAngle(l)+r),g=Math.round(ui(f)),p=Oa(g,h.x,u.w,0,180),m=Oa(g,h.y,u.h,90,270);Rd(t,i,f,p,m)}e.setCenterPoint(i.l-t.l,t.r-i.r,i.t-t.t,t.b-i.b),e._pointLabelItems=Fd(e,s,n)}function Rd(e,i,t,s,n){let o=Math.abs(Math.sin(t)),a=Math.abs(Math.cos(t)),r=0,l=0;s.start<i.l?(r=(i.l-s.start)/o,e.l=Math.min(e.l,i.l-r)):s.end>i.r&&(r=(s.end-i.r)/o,e.r=Math.max(e.r,i.r+r)),n.start<i.t?(l=(i.t-n.start)/a,e.t=Math.min(e.t,i.t-l)):n.end>i.b&&(l=(n.end-i.b)/a,e.b=Math.max(e.b,i.b+l))}function Ed(e,i,t){let s=e.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=t,l=e.getPointPosition(i,s+n+a,o),c=Math.round(ui(J(l.angle+W))),h=Nd(l.y,r.h,c),d=zd(c),u=Bd(l.x,r.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function Id(e,i){if(!i)return!0;let{left:t,top:s,right:n,bottom:o}=e;return!(dt({x:t,y:s},i)||dt({x:t,y:o},i)||dt({x:n,y:s},i)||dt({x:n,y:o},i))}function Fd(e,i,t){let s=[],n=e._pointLabels.length,o=e.options,{centerPointLabels:a,display:r}=o.pointLabels,l={extra:nn(o)/2,additionalAngle:a?z/n:0},c;for(let h=0;h<n;h++){l.padding=t[h],l.size=i[h];let d=Ed(e,h,l);s.push(d),r==="auto"&&(d.visible=Id(d,c),d.visible&&(c=d))}return s}function zd(e){return e===0||e===180?"center":e<180?"left":"right"}function Bd(e,i,t){return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function Nd(e,i,t){return t===90||t===270?e-=i/2:(t>270||t<90)&&(e-=i),e}function Vd(e,i,t){let{left:s,top:n,right:o,bottom:a}=t,{backdropColor:r}=i;if(!O(r)){let l=Lt(i.borderRadius),c=q(i.backdropPadding);e.fillStyle=r;let h=s-c.left,d=n-c.top,u=o-s+c.width,f=a-n+c.height;Object.values(l).some(g=>g!==0)?(e.beginPath(),ue(e,{x:h,y:d,w:u,h:f,radius:l}),e.fill()):e.fillRect(h,d,u,f)}}function Wd(e,i){let{ctx:t,options:{pointLabels:s}}=e;for(let n=i-1;n>=0;n--){let o=e._pointLabelItems[n];if(!o.visible)continue;let a=s.setContext(e.getPointLabelContext(n));Vd(t,a,o);let r=j(a.font),{x:l,y:c,textAlign:h}=o;Tt(t,e._pointLabels[n],l,c+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}function er(e,i,t,s){let{ctx:n}=e;if(t)n.arc(e.xCenter,e.yCenter,i,0,B);else{let o=e.getPointPosition(0,i);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=e.getPointPosition(a,i),n.lineTo(o.x,o.y)}}function Hd(e,i,t,s,n){let o=e.ctx,a=i.circular,{color:r,lineWidth:l}=i;!a&&!s||!r||!l||t<0||(o.save(),o.strokeStyle=r,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),er(e,t,a,s),o.closePath(),o.stroke(),o.restore())}function jd(e,i,t){return yt(e,{label:t,index:i,type:"pointLabel"})}var on=class extends me{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Oe.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(i){return i},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(i){super(i),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let i=this._padding=q(nn(this.options)/2),t=this.width=this.maxWidth-i.width,s=this.height=this.maxHeight-i.height;this.xCenter=Math.floor(this.left+t/2+i.left),this.yCenter=Math.floor(this.top+s/2+i.top),this.drawingArea=Math.floor(Math.min(t,s)/2)}determineDataLimits(){let{min:i,max:t}=this.getMinMax(!1);this.min=V(i)&&!isNaN(i)?i:0,this.max=V(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/nn(this.options))}generateTickLabels(i){me.prototype.generateTickLabels.call(this,i),this._pointLabels=this.getLabels().map((t,s)=>{let n=E(this.options.pointLabels.callback,[t,s],this);return n||n===0?n:""}).filter((t,s)=>this.chart.getDataVisibility(s))}fit(){let i=this.options;i.display&&i.pointLabels.display?Ld(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(i,t,s,n){this.xCenter+=Math.floor((i-t)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(i,t,s,n))}getIndexAngle(i){let t=B/(this._pointLabels.length||1),s=this.options.startAngle||0;return J(i*t+at(s))}getDistanceFromCenterForValue(i){if(O(i))return NaN;let t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-i)*t:(i-this.min)*t}getValueForDistanceFromCenter(i){if(O(i))return NaN;let t=i/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(i){let t=this._pointLabels||[];if(i>=0&&i<t.length){let s=t[i];return jd(this.getContext(),i,s)}}getPointPosition(i,t,s=0){let n=this.getIndexAngle(i)-W+s;return{x:Math.cos(n)*t+this.xCenter,y:Math.sin(n)*t+this.yCenter,angle:n}}getPointPositionForValue(i,t){return this.getPointPosition(i,this.getDistanceFromCenterForValue(t))}getBasePosition(i){return this.getPointPositionForValue(i||0,this.getBaseValue())}getPointLabelPosition(i){let{left:t,top:s,right:n,bottom:o}=this._pointLabelItems[i];return{left:t,top:s,right:n,bottom:o}}drawBackground(){let{backgroundColor:i,grid:{circular:t}}=this.options;if(i){let s=this.ctx;s.save(),s.beginPath(),er(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),s.closePath(),s.fillStyle=i,s.fill(),s.restore()}}drawGrid(){let i=this.ctx,t=this.options,{angleLines:s,grid:n,border:o}=t,a=this._pointLabels.length,r,l,c;if(t.pointLabels.display&&Wd(this,a),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);let u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);Hd(this,f,l,a,g)}}),s.display){for(i.save(),r=a-1;r>=0;r--){let h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:u}=h;!u||!d||(i.lineWidth=u,i.strokeStyle=d,i.setLineDash(h.borderDash),i.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),c=this.getPointPosition(r,l),i.beginPath(),i.moveTo(this.xCenter,this.yCenter),i.lineTo(c.x,c.y),i.stroke())}i.restore()}}drawBorder(){}drawLabels(){let i=this.ctx,t=this.options,s=t.ticks;if(!s.display)return;let n=this.getIndexAngle(0),o,a;i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((r,l)=>{if(l===0&&this.min>=0&&!t.reverse)return;let c=s.setContext(this.getContext(l)),h=j(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){i.font=h.string,a=i.measureText(r.label).width,i.fillStyle=c.backdropColor;let d=q(c.backdropPadding);i.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}Tt(i,r.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),i.restore()}drawTitle(){}},zi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},it=Object.keys(zi);function Aa(e,i){return e-i}function Ta(e,i){if(O(i))return null;let t=e._adapter,{parser:s,round:n,isoWeekday:o}=e._parseOpts,a=i;return typeof s=="function"&&(a=s(a)),V(a)||(a=typeof s=="string"?t.parse(a,s):t.parse(a)),a===null?null:(n&&(a=n==="week"&&($t(o)||o===!0)?t.startOf(a,"isoWeek",o):t.startOf(a,n)),+a)}function La(e,i,t,s){let n=it.length;for(let o=it.indexOf(e);o<n-1;++o){let a=zi[it[o]],r=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((t-i)/(r*a.size))<=s)return it[o]}return it[n-1]}function $d(e,i,t,s,n){for(let o=it.length-1;o>=it.indexOf(t);o--){let a=it[o];if(zi[a].common&&e._adapter.diff(n,s,a)>=i-1)return a}return it[t?it.indexOf(t):0]}function Yd(e){for(let i=it.indexOf(e)+1,t=it.length;i<t;++i)if(zi[it[i]].common)return it[i]}function Ra(e,i,t){if(!t)e[i]=!0;else if(t.length){let{lo:s,hi:n}=fi(t,i),o=t[s]>=i?t[s]:t[n];e[o]=!0}}function Ud(e,i,t,s){let n=e._adapter,o=+n.startOf(i[0].value,s),a=i[i.length-1].value,r,l;for(r=o;r<=a;r=+n.add(r,1,s))l=t[r],l>=0&&(i[l].major=!0);return i}function Ea(e,i,t){let s=[],n={},o=i.length,a,r;for(a=0;a<o;++a)r=i[a],n[r]=a,s.push({value:r,major:!1});return o===0||!t?s:Ud(e,s,n,t)}var an=(()=>{class e extends Kt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,s={}){let n=t.time||(t.time={}),o=this._adapter=new nc._date(t.adapters.date);o.init(s),re(n.displayFormats,o.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(t),this._normalized=s.normalized}parse(t,s){return t===void 0?null:Ta(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,s=this._adapter,n=t.time.unit||"day",{min:o,max:a,minDefined:r,maxDefined:l}=this.getUserBounds();function c(h){!r&&!isNaN(h.min)&&(o=Math.min(o,h.min)),!l&&!isNaN(h.max)&&(a=Math.max(a,h.max))}(!r||!l)&&(c(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&c(this.getMinMax(!1))),o=V(o)&&!isNaN(o)?o:+s.startOf(Date.now(),n),a=V(a)&&!isNaN(a)?a:+s.endOf(Date.now(),n)+1,this.min=Math.min(o,a-1),this.max=Math.max(o+1,a)}_getLabelBounds(){let t=this.getLabelTimestamps(),s=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return t.length&&(s=t[0],n=t[t.length-1]),{min:s,max:n}}buildTicks(){let t=this.options,s=t.time,n=t.ticks,o=n.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&o.length&&(this.min=this._userMin||o[0],this.max=this._userMax||o[o.length-1]);let a=this.min,r=this.max,l=go(o,a,r);return this._unit=s.unit||(n.autoSkip?La(s.minUnit,this.min,this.max,this._getLabelCapacity(a)):$d(this,l.length,s.minUnit,this.min,this.max)),this._majorUnit=!n.major.enabled||this._unit==="year"?void 0:Yd(this._unit),this.initOffsets(o),t.reverse&&l.reverse(),Ea(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let s=0,n=0,o,a;this.options.offset&&t.length&&(o=this.getDecimalForValue(t[0]),t.length===1?s=1-o:s=(this.getDecimalForValue(t[1])-o)/2,a=this.getDecimalForValue(t[t.length-1]),t.length===1?n=a:n=(a-this.getDecimalForValue(t[t.length-2]))/2);let r=t.length<3?.5:.25;s=$(s,0,r),n=$(n,0,r),this._offsets={start:s,end:n,factor:1/(s+1+n)}}_generate(){let t=this._adapter,s=this.min,n=this.max,o=this.options,a=o.time,r=a.unit||La(a.minUnit,s,n,this._getLabelCapacity(s)),l=D(o.ticks.stepSize,1),c=r==="week"?a.isoWeekday:!1,h=$t(c)||c===!0,d={},u=s,f,g;if(h&&(u=+t.startOf(u,"isoWeek",c)),u=+t.startOf(u,h?"day":r),t.diff(n,s,r)>1e5*l)throw new Error(s+" and "+n+" are too far apart with stepSize of "+l+" "+r);let p=o.ticks.source==="data"&&this.getDataTimestamps();for(f=u,g=0;f<n;f=+t.add(f,l,r),g++)Ra(d,f,p);return(f===n||o.bounds==="ticks"||g===1)&&Ra(d,f,p),Object.keys(d).sort(Aa).map(m=>+m)}getLabelForValue(t){let s=this._adapter,n=this.options.time;return n.tooltipFormat?s.format(t,n.tooltipFormat):s.format(t,n.displayFormats.datetime)}format(t,s){let o=this.options.time.displayFormats,a=this._unit,r=s||o[a];return this._adapter.format(t,r)}_tickFormatFunction(t,s,n,o){let a=this.options,r=a.ticks.callback;if(r)return E(r,[t,s,n],this);let l=a.time.displayFormats,c=this._unit,h=this._majorUnit,d=c&&l[c],u=h&&l[h],f=n[s],g=h&&u&&f&&f.major;return this._adapter.format(t,o||(g?u:d))}generateTickLabels(t){let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],o.label=this._tickFormatFunction(o.value,s,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let s=this._offsets,n=this.getDecimalForValue(t);return this.getPixelForDecimal((s.start+n)*s.factor)}getValueForPixel(t){let s=this._offsets,n=this.getDecimalForPixel(t)/s.factor-s.end;return this.min+n*(this.max-this.min)}_getLabelSize(t){let s=this.options.ticks,n=this.ctx.measureText(t).width,o=at(this.isHorizontal()?s.maxRotation:s.minRotation),a=Math.cos(o),r=Math.sin(o),l=this._resolveTickFontOptions(0).size;return{w:n*a+l*r,h:n*r+l*a}}_getLabelCapacity(t){let s=this.options.time,n=s.displayFormats,o=n[s.unit]||n.millisecond,a=this._tickFormatFunction(t,0,Ea(this,[t],this._majorUnit),o),r=this._getLabelSize(a),l=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return l>0?l:1}getDataTimestamps(){let t=this._cache.data||[],s,n;if(t.length)return t;let o=this.getMatchingVisibleMetas();if(this._normalized&&o.length)return this._cache.data=o[0].controller.getAllParsedValues(this);for(s=0,n=o.length;s<n;++s)t=t.concat(o[s].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){let t=this._cache.labels||[],s,n;if(t.length)return t;let o=this.getLabels();for(s=0,n=o.length;s<n;++s)t.push(Ta(this,o[s]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return cs(t.sort(Aa))}}return e})();function Ci(e,i,t){let s=0,n=e.length-1,o,a,r,l;t?(i>=e[s].pos&&i<=e[n].pos&&({lo:s,hi:n}=ht(e,"pos",i)),{pos:o,time:r}=e[s],{pos:a,time:l}=e[n]):(i>=e[s].time&&i<=e[n].time&&({lo:s,hi:n}=ht(e,"time",i)),{time:o,pos:r}=e[s],{time:a,pos:l}=e[n]);let c=a-o;return c?r+(l-r)*(i-o)/c:r}var rn=class extends an{static id="timeseries";static defaults=an.defaults;constructor(i){super(i),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let i=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(i);this._minPos=Ci(t,this.min),this._tableRange=Ci(t,this.max)-this._minPos,super.initOffsets(i)}buildLookupTable(i){let{min:t,max:s}=this,n=[],o=[],a,r,l,c,h;for(a=0,r=i.length;a<r;++a)c=i[a],c>=t&&c<=s&&n.push(c);if(n.length<2)return[{time:t,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],l=n[a-1],c=n[a],Math.round((h+l)/2)!==c&&o.push({time:c,pos:a/(r-1)});return o}_generate(){let i=this.min,t=this.max,s=super.getDataTimestamps();return(!s.includes(i)||!s.length)&&s.splice(0,0,i),(!s.includes(t)||s.length===1)&&s.push(t),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let i=this._cache.all||[];if(i.length)return i;let t=this.getDataTimestamps(),s=this.getLabelTimestamps();return t.length&&s.length?i=this.normalize(t.concat(s)):i=t.length?t:s,i=this._cache.all=i,i}getDecimalForValue(i){return(Ci(this._table,i)-this._minPos)/this._tableRange}getValueForPixel(i){let t=this._offsets,s=this.getDecimalForPixel(i)/t.factor-t.end;return Ci(this._table,s*this._tableRange+this._minPos,!0)}},Xd=Object.freeze({__proto__:null,CategoryScale:Cd,LinearScale:en,LogarithmicScale:sn,RadialLinearScale:on,TimeScale:an,TimeSeriesScale:rn}),ir=[sc,Ph,Sd,Xd];var Kd=(()=>{class e{constructor(){this.colorschemesOptions=new dn(void 0)}setColorschemesOptions(t){this.pColorschemesOptions=t,this.colorschemesOptions.next(t)}getColorschemesOptions(){return this.pColorschemesOptions}static{this.\u0275fac=function(s){return new(s||e)}}static{this.\u0275prov=Vt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),nr=(()=>{class e{constructor(t,s,n){this.zone=s,this.themeService=n,this.type="bar",this.plugins=[],this.chartClick=new Hi,this.chartHover=new Hi,this.subs=[],this.themeOverrides={},this.ctx=t.nativeElement.getContext("2d"),this.subs.push(this.themeService.colorschemesOptions.pipe(pn()).subscribe(o=>this.themeChanged(o)))}ngOnChanges(t){let s=["type"],n=Object.getOwnPropertyNames(t);if(n.some(o=>s.includes(o))||n.every(o=>t[o].isFirstChange()))this.render();else{let o=this.getChartConfiguration();this.chart&&(Object.assign(this.chart.config.data,o.data),this.chart.config.plugins&&Object.assign(this.chart.config.plugins,o.plugins),this.chart.config.options&&Object.assign(this.chart.config.options,o.options)),this.update()}}ngOnDestroy(){this.chart&&(this.chart.destroy(),this.chart=void 0),this.subs.forEach(t=>t.unsubscribe())}render(){return this.chart&&this.chart.destroy(),this.zone.runOutsideAngular(()=>this.chart=new je(this.ctx,this.getChartConfiguration()))}update(t){this.chart&&this.zone.runOutsideAngular(()=>this.chart?.update(t))}hideDataset(t,s){this.chart&&(this.chart.getDatasetMeta(t).hidden=s,this.update())}isDatasetHidden(t){return this.chart?.getDatasetMeta(t)?.hidden}toBase64Image(){return this.chart?.toBase64Image()}themeChanged(t){this.themeOverrides=t,this.chart&&(this.chart.config.options&&Object.assign(this.chart.config.options,this.getChartOptions()),this.update())}getChartOptions(){return Ui({onHover:(t,s)=>{!this.chartHover.observed&&!this.chartHover.observers?.length||this.zone.run(()=>this.chartHover.emit({event:t,active:s}))},onClick:(t,s)=>{!this.chartClick.observed&&!this.chartClick.observers?.length||this.zone.run(()=>this.chartClick.emit({event:t,active:s}))}},this.themeOverrides,this.options,{plugins:{legend:{display:this.legend}}})}getChartConfiguration(){return{type:this.type,data:this.getChartData(),options:this.getChartOptions(),plugins:this.plugins}}getChartData(){return this.data?this.data:{labels:this.labels||[],datasets:this.datasets||[]}}static{this.\u0275fac=function(s){return new(s||e)(Qt(xn),Qt(bn),Qt(Kd))}}static{this.\u0275dir=yn({type:e,selectors:[["canvas","baseChart",""]],inputs:{type:"type",legend:"legend",data:"data",options:"options",plugins:"plugins",labels:"labels",datasets:"datasets"},outputs:{chartClick:"chartClick",chartHover:"chartHover"},exportAs:["base-chart"],standalone:!1,features:[mn]})}}return e})(),qd=[[255,99,132],[54,162,235],[255,206,86],[231,233,237],[75,192,192],[151,187,205],[220,220,220],[247,70,74],[70,191,189],[253,180,92],[148,159,177],[77,83,96]],Gd={plugins:{colors:{enabled:!1}},datasets:{line:{backgroundColor:e=>Ft(zt(e.datasetIndex),.4),borderColor:e=>Ft(zt(e.datasetIndex),1),pointBackgroundColor:e=>Ft(zt(e.datasetIndex),1),pointBorderColor:"#fff"},bar:{backgroundColor:e=>Ft(zt(e.datasetIndex),.6),borderColor:e=>Ft(zt(e.datasetIndex),1)},get radar(){return this.line},doughnut:{backgroundColor:e=>Ft(zt(e.dataIndex),.6),borderColor:"#fff"},get pie(){return this.doughnut},polarArea:{backgroundColor:e=>Ft(zt(e.dataIndex),.6),borderColor:e=>Ft(zt(e.dataIndex),1)},get bubble(){return this.doughnut},get scatter(){return this.doughnut},get area(){return this.polarArea}}};function Ft(e,i){return"rgba("+e.concat(i).join(",")+")"}function hn(e,i){return Math.floor(Math.random()*(i-e+1))+e}function Zd(){return[hn(0,255),hn(0,255),hn(0,255)]}function zt(e=0){return qd[e]||Zd()}var sr=(()=>{class e{constructor(){this.generateColors=!0}static{this.\u0275fac=function(s){return new(s||e)}}static{this.\u0275prov=Vt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();je.register(...ir);var Bi=(()=>{class e{constructor(t){t?.plugins&&je.register(...t.plugins);let s=Ui(t?.generateColors?Gd:{},t?.defaults||{});I.set(s)}static forRoot(t){return{ngModule:e,providers:[{provide:sr,useValue:t}]}}static{this.\u0275fac=function(s){return new(s||e)(ye(sr,8))}}static{this.\u0275mod=Jt({type:e})}static{this.\u0275inj=Zt({})}}return e})();var Jd=e=>({data:e,label:"Questions Answered"}),or=e=>[e],ar=()=>({responsive:!0,maintainAspectRatio:!1}),tu=e=>({data:e,label:"Documents"});function eu(e,i){e&1&&(U(0,"div",5),Xe(1,"div",6),U(2,"p",7),nt(3,"Loading dashboard data..."),X()())}function iu(e,i){if(e&1&&(U(0,"div",8),nt(1),X()),e&2){let t=i.ngIf;Q(),vn(" Error loading dashboard: ",t," ")}}function su(e,i){if(e&1&&(U(0,"div",22)(1,"div",23)(2,"div",24)(3,"h2",25),nt(4),X(),U(5,"p",26),nt(6),X()()()()),e&2){let t=i.$implicit;Q(4),ee(t.value),Q(2),ee(t.label)}}function nu(e,i){if(e&1&&(U(0,"li",27)(1,"div")(2,"span",28),nt(3),X(),nt(4," - "),U(5,"span",7),nt(6),X()(),U(7,"small",7),nt(8),Me(9,"date"),X()()),e&2){let t=i.$implicit;Q(3),ee(t.type),Q(3),ee(t.details),Q(2),ee(Mn(9,3,t.timestamp,"short"))}}function ou(e,i){if(e&1&&(U(0,"div")(1,"div",9),te(2,su,7,2,"div",10),X(),U(3,"div",11)(4,"div",12)(5,"div",13)(6,"div",14),nt(7,"Questions Answered Over Time"),X(),U(8,"div",15),Xe(9,"canvas",16),X()()(),U(10,"div",12)(11,"div",13)(12,"div",14),nt(13,"Documents Indexed by Type"),X(),U(14,"div",15),Xe(15,"canvas",17),X()()()(),U(16,"div",18)(17,"div",14),nt(18," Recent Activities "),X(),U(19,"div",19)(20,"ul",20),te(21,nu,10,6,"li",21),X()()()()),e&2){let t=i.ngIf;Q(2),wt("ngForOf",t.cards),Q(7),wt("datasets",ve(14,or,ve(12,Jd,t.questionsOverTime.data)))("labels",t.questionsOverTime.labels)("options",ji(16,ar))("legend",!0)("height",300),Q(6),wt("datasets",ve(19,or,ve(17,tu,t.documentsByType.data)))("labels",t.documentsByType.labels)("options",ji(21,ar))("legend",!0)("height",300),Q(6),wt("ngForOf",t.recentActivities)}}var be=class e{constructor(i){this.store=i}metrics$;loading$;error$;ngOnInit(){this.store.dispatch(ie()),this.metrics$=this.store.select(Nn),this.loading$=this.store.select(Vn),this.error$=this.store.select(Wn)}static \u0275fac=function(t){return new(t||e)(Qt(Pn))};static \u0275cmp=_n({type:e,selectors:[["app-dashboard"]],decls:9,vars:9,consts:[[1,"container-fluid","py-4"],[1,"mb-4","fw-bold","text-primary"],["class","text-center my-5",4,"ngIf"],["class","alert alert-danger",4,"ngIf"],[4,"ngIf"],[1,"text-center","my-5"],[1,"spinner-border","text-primary"],[1,"text-muted"],[1,"alert","alert-danger"],[1,"row","g-3"],["class","col-md-3",4,"ngFor","ngForOf"],[1,"row","g-4","mt-4"],[1,"col-md-6"],[1,"card","shadow-sm","border-0","rounded-4"],[1,"card-header","bg-white","border-0","fw-bold","text-primary"],[1,"card-body"],["baseChart","","chartType","line",3,"datasets","labels","options","legend","height"],["baseChart","","chartType","doughnut",3,"datasets","labels","options","legend","height"],[1,"card","mt-5","shadow-sm","border-0","rounded-4"],[1,"card-body","p-0"],[1,"list-group","list-group-flush"],["class","list-group-item d-flex justify-content-between align-items-center",4,"ngFor","ngForOf"],[1,"col-md-3"],[1,"card","h-100","shadow-sm","border-0","rounded-4","text-center"],[1,"card-body","d-flex","flex-column","justify-content-center","align-items-center"],[1,"fw-bold","text-primary"],[1,"text-muted","mb-0"],[1,"list-group-item","d-flex","justify-content-between","align-items-center"],[1,"fw-bold"]],template:function(t,s){t&1&&(U(0,"div",0)(1,"h1",1),nt(2,"ChatAI Dashboard"),X(),te(3,eu,4,0,"div",2),Me(4,"async"),te(5,iu,2,1,"div",3),Me(6,"async"),te(7,ou,22,22,"div",4),Me(8,"async"),X()),t&2&&(Q(3),wt("ngIf",Ke(4,3,s.loading$)),Q(2),wt("ngIf",Ke(6,5,s.error$)),Q(2),wt("ngIf",Ke(8,7,s.metrics$)))},dependencies:[Bi,nr,Dn,Sn,kn,wn],styles:["h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:#007bff}.card[_ngcontent-%COMP%]{border-radius:1rem}.card-header[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.shadow-sm[_ngcontent-%COMP%]{box-shadow:0 4px 10px #0000001a}.list-group-item[_ngcontent-%COMP%]{padding:1rem}.list-group-item[_ngcontent-%COMP%]:not(:last-child){border-bottom:1px solid #eee}.text-primary[_ngcontent-%COMP%]{color:#007bff!important}"]})};var au=[{path:"",component:be}],Ni=class e{static \u0275fac=function(t){return new(t||e)};static \u0275mod=Jt({type:e});static \u0275inj=Zt({imports:[$i.forChild(au),$i]})};var ru={metrics:null,loading:!1,error:null},rr=Tn(ru,Ze(ie,e=>kt(St({},e),{loading:!0,error:null})),Ze(Qe,(e,{metrics:i})=>kt(St({},e),{metrics:i,loading:!1})),Ze(Je,(e,{error:i})=>kt(St({},e),{error:i,loading:!1})));var Vi=class e{getDashboardMetrics(){return Ue({cards:[{label:"Prompts Created",value:120},{label:"Projects Used",value:45},{label:"Questions Answered",value:780},{label:"Documents Indexed",value:320}],questionsOverTime:{labels:["Jan","Feb","Mar","Apr","May"],data:[150,200,250,400,450]},documentsByType:{labels:["PDF","Word","Text"],data:[200,100,20]},recentActivities:[{type:"Prompt Created",details:"Created a welcome prompt",timestamp:new Date},{type:"Document Indexed",details:"Indexed GDPR policy",timestamp:new Date}]})}static \u0275fac=function(t){return new(t||e)};static \u0275prov=Vt({token:e,factory:e.\u0275fac,providedIn:"root"})};var Wi=class e{constructor(i,t){this.actions$=i;this.dashboardService=t;this.loadDashboardData$=Ln(()=>this.actions$.pipe(En(ie),fn(()=>this.dashboardService.getDashboardMetrics().pipe(un(s=>Qe({metrics:s})),gn(s=>Ue(Je({error:s})))))))}loadDashboardData$;static \u0275fac=function(t){return new(t||e)(ye(Rn),ye(Vi))};static \u0275prov=Vt({token:e,factory:e.\u0275fac})};var lr=class e{static \u0275fac=function(t){return new(t||e)};static \u0275mod=Jt({type:e});static \u0275inj=Zt({imports:[Cn,Fn,Bi,zn.forRoot(),Ni,An.forFeature("dashboard",rr),In.forFeature([Wi]),be]})};export{lr as DashboardModule};
