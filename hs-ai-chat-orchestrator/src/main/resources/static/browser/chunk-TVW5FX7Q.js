import{a as ft,b as Qt,c as tt}from"./chunk-MAVNWS4Y.js";import{a as T,b as At,c as Rr,d as wt,e as fo,f as J}from"./chunk-GAL4ENT6.js";var Bn=wt((td,Rn)=>{"use strict";var et=-1,z=1,M=0;function ve(r,e,t,s,n){if(r===e)return r?[[M,r]]:[];if(t!=null){var i=Ro(r,e,t);if(i)return i}var l=Kr(r,e),o=r.substring(0,l);r=r.substring(l),e=e.substring(l),l=$e(r,e);var a=r.substring(r.length-l);r=r.substring(0,r.length-l),e=e.substring(0,e.length-l);var c=Lo(r,e);return o&&c.unshift([M,o]),a&&c.push([M,a]),Gr(c,n),s&&ko(c),c}function Lo(r,e){var t;if(!r)return[[z,e]];if(!e)return[[et,r]];var s=r.length>e.length?r:e,n=r.length>e.length?e:r,i=s.indexOf(n);if(i!==-1)return t=[[z,s.substring(0,i)],[M,n],[z,s.substring(i+n.length)]],r.length>e.length&&(t[0][0]=t[2][0]=et),t;if(n.length===1)return[[et,r],[z,e]];var l=So(r,e);if(l){var o=l[0],a=l[1],c=l[2],u=l[3],h=l[4],d=ve(o,c),m=ve(a,u);return d.concat([[M,h]],m)}return qo(r,e)}function qo(r,e){for(var t=r.length,s=e.length,n=Math.ceil((t+s)/2),i=n,l=2*n,o=new Array(l),a=new Array(l),c=0;c<l;c++)o[c]=-1,a[c]=-1;o[i+1]=0,a[i+1]=0;for(var u=t-s,h=u%2!==0,d=0,m=0,g=0,v=0,E=0;E<n;E++){for(var y=-E+d;y<=E-m;y+=2){var N=i+y,x;y===-E||y!==E&&o[N-1]<o[N+1]?x=o[N+1]:x=o[N-1]+1;for(var A=x-y;x<t&&A<s&&r.charAt(x)===e.charAt(A);)x++,A++;if(o[N]=x,x>t)m+=2;else if(A>s)d+=2;else if(h){var q=i+u-y;if(q>=0&&q<l&&a[q]!==-1){var S=t-a[q];if(x>=S)return Tn(r,e,x,A)}}}for(var G=-E+g;G<=E-v;G+=2){var q=i+G,S;G===-E||G!==E&&a[q-1]<a[q+1]?S=a[q+1]:S=a[q-1]+1;for(var ot=S-G;S<t&&ot<s&&r.charAt(t-S-1)===e.charAt(s-ot-1);)S++,ot++;if(a[q]=S,S>t)v+=2;else if(ot>s)g+=2;else if(!h){var N=i+u-G;if(N>=0&&N<l&&o[N]!==-1){var x=o[N],A=i+x-N;if(S=t-S,x>=S)return Tn(r,e,x,A)}}}}return[[et,r],[z,e]]}function Tn(r,e,t,s){var n=r.substring(0,t),i=e.substring(0,s),l=r.substring(t),o=e.substring(s),a=ve(n,i),c=ve(l,o);return a.concat(c)}function Kr(r,e){if(!r||!e||r.charAt(0)!==e.charAt(0))return 0;for(var t=0,s=Math.min(r.length,e.length),n=s,i=0;t<n;)r.substring(i,n)==e.substring(i,n)?(t=n,i=t):s=n,n=Math.floor((s-t)/2+t);return Cn(r.charCodeAt(n-1))&&n--,n}function Ln(r,e){var t=r.length,s=e.length;if(t==0||s==0)return 0;t>s?r=r.substring(t-s):t<s&&(e=e.substring(0,t));var n=Math.min(t,s);if(r==e)return n;for(var i=0,l=1;;){var o=r.substring(n-l),a=e.indexOf(o);if(a==-1)return i;l+=a,(a==0||r.substring(n-l)==e.substring(0,l))&&(i=l,l++)}}function $e(r,e){if(!r||!e||r.slice(-1)!==e.slice(-1))return 0;for(var t=0,s=Math.min(r.length,e.length),n=s,i=0;t<n;)r.substring(r.length-n,r.length-i)==e.substring(e.length-n,e.length-i)?(t=n,i=t):s=n,n=Math.floor((s-t)/2+t);return On(r.charCodeAt(r.length-n))&&n--,n}function So(r,e){var t=r.length>e.length?r:e,s=r.length>e.length?e:r;if(t.length<4||s.length*2<t.length)return null;function n(m,g,v){for(var E=m.substring(v,v+Math.floor(m.length/4)),y=-1,N="",x,A,q,S;(y=g.indexOf(E,y+1))!==-1;){var G=Kr(m.substring(v),g.substring(y)),ot=$e(m.substring(0,v),g.substring(0,y));N.length<ot+G&&(N=g.substring(y-ot,y)+g.substring(y,y+G),x=m.substring(0,v-ot),A=m.substring(v+G),q=g.substring(0,y-ot),S=g.substring(y+G))}return N.length*2>=m.length?[x,A,q,S,N]:null}var i=n(t,s,Math.ceil(t.length/4)),l=n(t,s,Math.ceil(t.length/2)),o;if(!i&&!l)return null;l?i?o=i[4].length>l[4].length?i:l:o=l:o=i;var a,c,u,h;r.length>e.length?(a=o[0],c=o[1],u=o[2],h=o[3]):(u=o[0],h=o[1],a=o[2],c=o[3]);var d=o[4];return[a,c,u,h,d]}function ko(r){for(var e=!1,t=[],s=0,n=null,i=0,l=0,o=0,a=0,c=0;i<r.length;)r[i][0]==M?(t[s++]=i,l=a,o=c,a=0,c=0,n=r[i][1]):(r[i][0]==z?a+=r[i][1].length:c+=r[i][1].length,n&&n.length<=Math.max(l,o)&&n.length<=Math.max(a,c)&&(r.splice(t[s-1],0,[et,n]),r[t[s-1]+1][0]=z,s--,s--,i=s>0?t[s-1]:-1,l=0,o=0,a=0,c=0,n=null,e=!0)),i++;for(e&&Gr(r),_o(r),i=1;i<r.length;){if(r[i-1][0]==et&&r[i][0]==z){var u=r[i-1][1],h=r[i][1],d=Ln(u,h),m=Ln(h,u);d>=m?(d>=u.length/2||d>=h.length/2)&&(r.splice(i,0,[M,h.substring(0,d)]),r[i-1][1]=u.substring(0,u.length-d),r[i+1][1]=h.substring(d),i++):(m>=u.length/2||m>=h.length/2)&&(r.splice(i,0,[M,u.substring(0,m)]),r[i-1][0]=z,r[i-1][1]=h.substring(0,h.length-m),r[i+1][0]=et,r[i+1][1]=u.substring(m),i++),i++}i++}}var qn=/[^a-zA-Z0-9]/,Sn=/\s/,kn=/[\r\n]/,Co=/\n\r?\n$/,Oo=/^\r?\n\r?\n/;function _o(r){function e(m,g){if(!m||!g)return 6;var v=m.charAt(m.length-1),E=g.charAt(0),y=v.match(qn),N=E.match(qn),x=y&&v.match(Sn),A=N&&E.match(Sn),q=x&&v.match(kn),S=A&&E.match(kn),G=q&&m.match(Co),ot=S&&g.match(Oo);return G||ot?5:q||S?4:y&&!x&&A?3:x||A?2:y||N?1:0}for(var t=1;t<r.length-1;){if(r[t-1][0]==M&&r[t+1][0]==M){var s=r[t-1][1],n=r[t][1],i=r[t+1][1],l=$e(s,n);if(l){var o=n.substring(n.length-l);s=s.substring(0,s.length-l),n=o+n.substring(0,n.length-l),i=o+i}for(var a=s,c=n,u=i,h=e(s,n)+e(n,i);n.charAt(0)===i.charAt(0);){s+=n.charAt(0),n=n.substring(1)+i.charAt(0),i=i.substring(1);var d=e(s,n)+e(n,i);d>=h&&(h=d,a=s,c=n,u=i)}r[t-1][1]!=a&&(a?r[t-1][1]=a:(r.splice(t-1,1),t--),r[t][1]=c,u?r[t+1][1]=u:(r.splice(t+1,1),t--))}t++}}function Gr(r,e){r.push([M,""]);for(var t=0,s=0,n=0,i="",l="",o;t<r.length;){if(t<r.length-1&&!r[t][1]){r.splice(t,1);continue}switch(r[t][0]){case z:n++,l+=r[t][1],t++;break;case et:s++,i+=r[t][1],t++;break;case M:var a=t-n-s-1;if(e){if(a>=0&&In(r[a][1])){var c=r[a][1].slice(-1);if(r[a][1]=r[a][1].slice(0,-1),i=c+i,l=c+l,!r[a][1]){r.splice(a,1),t--;var u=a-1;r[u]&&r[u][0]===z&&(n++,l=r[u][1]+l,u--),r[u]&&r[u][0]===et&&(s++,i=r[u][1]+i,u--),a=u}}if(_n(r[t][1])){var c=r[t][1].charAt(0);r[t][1]=r[t][1].slice(1),i+=c,l+=c}}if(t<r.length-1&&!r[t][1]){r.splice(t,1);break}if(i.length>0||l.length>0){i.length>0&&l.length>0&&(o=Kr(l,i),o!==0&&(a>=0?r[a][1]+=l.substring(0,o):(r.splice(0,0,[M,l.substring(0,o)]),t++),l=l.substring(o),i=i.substring(o)),o=$e(l,i),o!==0&&(r[t][1]=l.substring(l.length-o)+r[t][1],l=l.substring(0,l.length-o),i=i.substring(0,i.length-o)));var h=n+s;i.length===0&&l.length===0?(r.splice(t-h,h),t=t-h):i.length===0?(r.splice(t-h,h,[z,l]),t=t-h+1):l.length===0?(r.splice(t-h,h,[et,i]),t=t-h+1):(r.splice(t-h,h,[et,i],[z,l]),t=t-h+2)}t!==0&&r[t-1][0]===M?(r[t-1][1]+=r[t][1],r.splice(t,1)):t++,n=0,s=0,i="",l="";break}}r[r.length-1][1]===""&&r.pop();var d=!1;for(t=1;t<r.length-1;)r[t-1][0]===M&&r[t+1][0]===M&&(r[t][1].substring(r[t][1].length-r[t-1][1].length)===r[t-1][1]?(r[t][1]=r[t-1][1]+r[t][1].substring(0,r[t][1].length-r[t-1][1].length),r[t+1][1]=r[t-1][1]+r[t+1][1],r.splice(t-1,1),d=!0):r[t][1].substring(0,r[t+1][1].length)==r[t+1][1]&&(r[t-1][1]+=r[t+1][1],r[t][1]=r[t][1].substring(r[t+1][1].length)+r[t+1][1],r.splice(t+1,1),d=!0)),t++;d&&Gr(r,e)}function Cn(r){return r>=55296&&r<=56319}function On(r){return r>=56320&&r<=57343}function _n(r){return On(r.charCodeAt(0))}function In(r){return Cn(r.charCodeAt(r.length-1))}function Io(r){for(var e=[],t=0;t<r.length;t++)r[t][1].length>0&&e.push(r[t]);return e}function Vr(r,e,t,s){return In(r)||_n(s)?null:Io([[M,r],[et,e],[z,t],[M,s]])}function Ro(r,e,t){var s=typeof t=="number"?{index:t,length:0}:t.oldRange,n=typeof t=="number"?null:t.newRange,i=r.length,l=e.length;if(s.length===0&&(n===null||n.length===0)){var o=s.index,a=r.slice(0,o),c=r.slice(o),u=n?n.index:null;t:{var h=o+l-i;if(u!==null&&u!==h||h<0||h>l)break t;var d=e.slice(0,h),m=e.slice(h);if(m!==c)break t;var g=Math.min(o,h),v=a.slice(0,g),E=d.slice(0,g);if(v!==E)break t;var y=a.slice(g),N=d.slice(g);return Vr(v,y,N,c)}t:{if(u!==null&&u!==o)break t;var x=o,d=e.slice(0,x),m=e.slice(x);if(d!==a)break t;var A=Math.min(i-x,l-x),q=c.slice(c.length-A),S=m.slice(m.length-A);if(q!==S)break t;var y=c.slice(0,c.length-A),N=m.slice(0,m.length-A);return Vr(a,y,N,q)}}if(s.length>0&&n&&n.length===0)t:{var v=r.slice(0,s.index),q=r.slice(s.index+s.length),g=v.length,A=q.length;if(l<g+A)break t;var E=e.slice(0,g),S=e.slice(l-A);if(v!==E||q!==S)break t;var y=r.slice(g,i-A),N=e.slice(g,l-A);return Vr(v,y,N,q)}return null}function ze(r,e,t,s){return ve(r,e,t,s,!0)}ze.INSERT=z;ze.DELETE=et;ze.EQUAL=M;Rn.exports=ze});var as=wt((Ee,se)=>{"use strict";var Bo=200,Gn="__lodash_hash_undefined__",Wn=9007199254740991,es="[object Arguments]",Mo="[object Array]",Zn="[object Boolean]",Qn="[object Date]",Do="[object Error]",rs="[object Function]",Xn="[object GeneratorFunction]",Ve="[object Map]",Yn="[object Number]",ss="[object Object]",Mn="[object Promise]",Jn="[object RegExp]",Ke="[object Set]",ti="[object String]",ei="[object Symbol]",Zr="[object WeakMap]",ri="[object ArrayBuffer]",Ge="[object DataView]",si="[object Float32Array]",ni="[object Float64Array]",ii="[object Int8Array]",li="[object Int16Array]",oi="[object Int32Array]",ai="[object Uint8Array]",ci="[object Uint8ClampedArray]",ui="[object Uint16Array]",hi="[object Uint32Array]",Uo=/[\\^$.*+?()[\]{}|]/g,Po=/\w*$/,jo=/^\[object .+?Constructor\]$/,Ho=/^(?:0|[1-9]\d*)$/,w={};w[es]=w[Mo]=w[ri]=w[Ge]=w[Zn]=w[Qn]=w[si]=w[ni]=w[ii]=w[li]=w[oi]=w[Ve]=w[Yn]=w[ss]=w[Jn]=w[Ke]=w[ti]=w[ei]=w[ai]=w[ci]=w[ui]=w[hi]=!0;w[Do]=w[rs]=w[Zr]=!1;var Fo=typeof global=="object"&&global&&global.Object===Object&&global,$o=typeof self=="object"&&self&&self.Object===Object&&self,dt=Fo||$o||Function("return this")(),fi=typeof Ee=="object"&&Ee&&!Ee.nodeType&&Ee,Dn=fi&&typeof se=="object"&&se&&!se.nodeType&&se,zo=Dn&&Dn.exports===fi;function Vo(r,e){return r.set(e[0],e[1]),r}function Ko(r,e){return r.add(e),r}function Go(r,e){for(var t=-1,s=r?r.length:0;++t<s&&e(r[t],t,r)!==!1;);return r}function Wo(r,e){for(var t=-1,s=e.length,n=r.length;++t<s;)r[n+t]=e[t];return r}function di(r,e,t,s){var n=-1,i=r?r.length:0;for(s&&i&&(t=r[++n]);++n<i;)t=e(t,r[n],n,r);return t}function Zo(r,e){for(var t=-1,s=Array(r);++t<r;)s[t]=e(t);return s}function Qo(r,e){return r?.[e]}function mi(r){var e=!1;if(r!=null&&typeof r.toString!="function")try{e=!!(r+"")}catch{}return e}function Un(r){var e=-1,t=Array(r.size);return r.forEach(function(s,n){t[++e]=[n,s]}),t}function ns(r,e){return function(t){return r(e(t))}}function Pn(r){var e=-1,t=Array(r.size);return r.forEach(function(s){t[++e]=s}),t}var Xo=Array.prototype,Yo=Function.prototype,We=Object.prototype,Wr=dt["__core-js_shared__"],jn=function(){var r=/[^.]+$/.exec(Wr&&Wr.keys&&Wr.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),pi=Yo.toString,St=We.hasOwnProperty,Ze=We.toString,Jo=RegExp("^"+pi.call(St).replace(Uo,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Hn=zo?dt.Buffer:void 0,Fn=dt.Symbol,$n=dt.Uint8Array,ta=ns(Object.getPrototypeOf,Object),ea=Object.create,ra=We.propertyIsEnumerable,sa=Xo.splice,zn=Object.getOwnPropertySymbols,na=Hn?Hn.isBuffer:void 0,ia=ns(Object.keys,Object),Qr=le(dt,"DataView"),Ne=le(dt,"Map"),Xr=le(dt,"Promise"),Yr=le(dt,"Set"),Jr=le(dt,"WeakMap"),xe=le(Object,"create"),la=Ht(Qr),oa=Ht(Ne),aa=Ht(Xr),ca=Ht(Yr),ua=Ht(Jr),Vn=Fn?Fn.prototype:void 0,Kn=Vn?Vn.valueOf:void 0;function jt(r){var e=-1,t=r?r.length:0;for(this.clear();++e<t;){var s=r[e];this.set(s[0],s[1])}}function ha(){this.__data__=xe?xe(null):{}}function fa(r){return this.has(r)&&delete this.__data__[r]}function da(r){var e=this.__data__;if(xe){var t=e[r];return t===Gn?void 0:t}return St.call(e,r)?e[r]:void 0}function ma(r){var e=this.__data__;return xe?e[r]!==void 0:St.call(e,r)}function pa(r,e){var t=this.__data__;return t[r]=xe&&e===void 0?Gn:e,this}jt.prototype.clear=ha;jt.prototype.delete=fa;jt.prototype.get=da;jt.prototype.has=ma;jt.prototype.set=pa;function mt(r){var e=-1,t=r?r.length:0;for(this.clear();++e<t;){var s=r[e];this.set(s[0],s[1])}}function ga(){this.__data__=[]}function ba(r){var e=this.__data__,t=Qe(e,r);if(t<0)return!1;var s=e.length-1;return t==s?e.pop():sa.call(e,t,1),!0}function ya(r){var e=this.__data__,t=Qe(e,r);return t<0?void 0:e[t][1]}function va(r){return Qe(this.__data__,r)>-1}function Ea(r,e){var t=this.__data__,s=Qe(t,r);return s<0?t.push([r,e]):t[s][1]=e,this}mt.prototype.clear=ga;mt.prototype.delete=ba;mt.prototype.get=ya;mt.prototype.has=va;mt.prototype.set=Ea;function ne(r){var e=-1,t=r?r.length:0;for(this.clear();++e<t;){var s=r[e];this.set(s[0],s[1])}}function Na(){this.__data__={hash:new jt,map:new(Ne||mt),string:new jt}}function xa(r){return Xe(this,r).delete(r)}function Aa(r){return Xe(this,r).get(r)}function wa(r){return Xe(this,r).has(r)}function Ta(r,e){return Xe(this,r).set(r,e),this}ne.prototype.clear=Na;ne.prototype.delete=xa;ne.prototype.get=Aa;ne.prototype.has=wa;ne.prototype.set=Ta;function ie(r){this.__data__=new mt(r)}function La(){this.__data__=new mt}function qa(r){return this.__data__.delete(r)}function Sa(r){return this.__data__.get(r)}function ka(r){return this.__data__.has(r)}function Ca(r,e){var t=this.__data__;if(t instanceof mt){var s=t.__data__;if(!Ne||s.length<Bo-1)return s.push([r,e]),this;t=this.__data__=new ne(s)}return t.set(r,e),this}ie.prototype.clear=La;ie.prototype.delete=qa;ie.prototype.get=Sa;ie.prototype.has=ka;ie.prototype.set=Ca;function Oa(r,e){var t=ls(r)||ec(r)?Zo(r.length,String):[],s=t.length,n=!!s;for(var i in r)(e||St.call(r,i))&&!(n&&(i=="length"||Xa(i,s)))&&t.push(i);return t}function gi(r,e,t){var s=r[e];(!(St.call(r,e)&&Ei(s,t))||t===void 0&&!(e in r))&&(r[e]=t)}function Qe(r,e){for(var t=r.length;t--;)if(Ei(r[t][0],e))return t;return-1}function _a(r,e){return r&&bi(e,os(e),r)}function ts(r,e,t,s,n,i,l){var o;if(s&&(o=i?s(r,n,i,l):s(r)),o!==void 0)return o;if(!Ye(r))return r;var a=ls(r);if(a){if(o=Wa(r),!e)return Va(r,o)}else{var c=Pt(r),u=c==rs||c==Xn;if(sc(r))return Ua(r,e);if(c==ss||c==es||u&&!i){if(mi(r))return i?r:{};if(o=Za(u?{}:r),!e)return Ka(r,_a(o,r))}else{if(!w[c])return i?r:{};o=Qa(r,c,ts,e)}}l||(l=new ie);var h=l.get(r);if(h)return h;if(l.set(r,o),!a)var d=t?Ga(r):os(r);return Go(d||r,function(m,g){d&&(g=m,m=r[g]),gi(o,g,ts(m,e,t,s,g,r,l))}),o}function Ia(r){return Ye(r)?ea(r):{}}function Ra(r,e,t){var s=e(r);return ls(r)?s:Wo(s,t(r))}function Ba(r){return Ze.call(r)}function Ma(r){if(!Ye(r)||Ja(r))return!1;var e=xi(r)||mi(r)?Jo:jo;return e.test(Ht(r))}function Da(r){if(!vi(r))return ia(r);var e=[];for(var t in Object(r))St.call(r,t)&&t!="constructor"&&e.push(t);return e}function Ua(r,e){if(e)return r.slice();var t=new r.constructor(r.length);return r.copy(t),t}function is(r){var e=new r.constructor(r.byteLength);return new $n(e).set(new $n(r)),e}function Pa(r,e){var t=e?is(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.byteLength)}function ja(r,e,t){var s=e?t(Un(r),!0):Un(r);return di(s,Vo,new r.constructor)}function Ha(r){var e=new r.constructor(r.source,Po.exec(r));return e.lastIndex=r.lastIndex,e}function Fa(r,e,t){var s=e?t(Pn(r),!0):Pn(r);return di(s,Ko,new r.constructor)}function $a(r){return Kn?Object(Kn.call(r)):{}}function za(r,e){var t=e?is(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.length)}function Va(r,e){var t=-1,s=r.length;for(e||(e=Array(s));++t<s;)e[t]=r[t];return e}function bi(r,e,t,s){t||(t={});for(var n=-1,i=e.length;++n<i;){var l=e[n],o=s?s(t[l],r[l],l,t,r):void 0;gi(t,l,o===void 0?r[l]:o)}return t}function Ka(r,e){return bi(r,yi(r),e)}function Ga(r){return Ra(r,os,yi)}function Xe(r,e){var t=r.__data__;return Ya(e)?t[typeof e=="string"?"string":"hash"]:t.map}function le(r,e){var t=Qo(r,e);return Ma(t)?t:void 0}var yi=zn?ns(zn,Object):lc,Pt=Ba;(Qr&&Pt(new Qr(new ArrayBuffer(1)))!=Ge||Ne&&Pt(new Ne)!=Ve||Xr&&Pt(Xr.resolve())!=Mn||Yr&&Pt(new Yr)!=Ke||Jr&&Pt(new Jr)!=Zr)&&(Pt=function(r){var e=Ze.call(r),t=e==ss?r.constructor:void 0,s=t?Ht(t):void 0;if(s)switch(s){case la:return Ge;case oa:return Ve;case aa:return Mn;case ca:return Ke;case ua:return Zr}return e});function Wa(r){var e=r.length,t=r.constructor(e);return e&&typeof r[0]=="string"&&St.call(r,"index")&&(t.index=r.index,t.input=r.input),t}function Za(r){return typeof r.constructor=="function"&&!vi(r)?Ia(ta(r)):{}}function Qa(r,e,t,s){var n=r.constructor;switch(e){case ri:return is(r);case Zn:case Qn:return new n(+r);case Ge:return Pa(r,s);case si:case ni:case ii:case li:case oi:case ai:case ci:case ui:case hi:return za(r,s);case Ve:return ja(r,s,t);case Yn:case ti:return new n(r);case Jn:return Ha(r);case Ke:return Fa(r,s,t);case ei:return $a(r)}}function Xa(r,e){return e=e??Wn,!!e&&(typeof r=="number"||Ho.test(r))&&r>-1&&r%1==0&&r<e}function Ya(r){var e=typeof r;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?r!=="__proto__":r===null}function Ja(r){return!!jn&&jn in r}function vi(r){var e=r&&r.constructor,t=typeof e=="function"&&e.prototype||We;return r===t}function Ht(r){if(r!=null){try{return pi.call(r)}catch{}try{return r+""}catch{}}return""}function tc(r){return ts(r,!0,!0)}function Ei(r,e){return r===e||r!==r&&e!==e}function ec(r){return rc(r)&&St.call(r,"callee")&&(!ra.call(r,"callee")||Ze.call(r)==es)}var ls=Array.isArray;function Ni(r){return r!=null&&nc(r.length)&&!xi(r)}function rc(r){return ic(r)&&Ni(r)}var sc=na||oc;function xi(r){var e=Ye(r)?Ze.call(r):"";return e==rs||e==Xn}function nc(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Wn}function Ye(r){var e=typeof r;return!!r&&(e=="object"||e=="function")}function ic(r){return!!r&&typeof r=="object"}function os(r){return Ni(r)?Oa(r):Da(r)}function lc(){return[]}function oc(){return!1}se.exports=tc});var Es=wt((Ae,ae)=>{"use strict";var ac=200,vs="__lodash_hash_undefined__",lr=1,Ri=2,Bi=9007199254740991,Je="[object Arguments]",fs="[object Array]",cc="[object AsyncFunction]",Mi="[object Boolean]",Di="[object Date]",Ui="[object Error]",Pi="[object Function]",uc="[object GeneratorFunction]",tr="[object Map]",ji="[object Number]",hc="[object Null]",oe="[object Object]",Ai="[object Promise]",fc="[object Proxy]",Hi="[object RegExp]",er="[object Set]",Fi="[object String]",dc="[object Symbol]",mc="[object Undefined]",ds="[object WeakMap]",$i="[object ArrayBuffer]",rr="[object DataView]",pc="[object Float32Array]",gc="[object Float64Array]",bc="[object Int8Array]",yc="[object Int16Array]",vc="[object Int32Array]",Ec="[object Uint8Array]",Nc="[object Uint8ClampedArray]",xc="[object Uint16Array]",Ac="[object Uint32Array]",wc=/[\\^$.*+?()[\]{}|]/g,Tc=/^\[object .+?Constructor\]$/,Lc=/^(?:0|[1-9]\d*)$/,L={};L[pc]=L[gc]=L[bc]=L[yc]=L[vc]=L[Ec]=L[Nc]=L[xc]=L[Ac]=!0;L[Je]=L[fs]=L[$i]=L[Mi]=L[rr]=L[Di]=L[Ui]=L[Pi]=L[tr]=L[ji]=L[oe]=L[Hi]=L[er]=L[Fi]=L[ds]=!1;var zi=typeof global=="object"&&global&&global.Object===Object&&global,qc=typeof self=="object"&&self&&self.Object===Object&&self,pt=zi||qc||Function("return this")(),Vi=typeof Ae=="object"&&Ae&&!Ae.nodeType&&Ae,wi=Vi&&typeof ae=="object"&&ae&&!ae.nodeType&&ae,Ki=wi&&wi.exports===Vi,cs=Ki&&zi.process,Ti=function(){try{return cs&&cs.binding&&cs.binding("util")}catch{}}(),Li=Ti&&Ti.isTypedArray;function Sc(r,e){for(var t=-1,s=r==null?0:r.length,n=0,i=[];++t<s;){var l=r[t];e(l,t,r)&&(i[n++]=l)}return i}function kc(r,e){for(var t=-1,s=e.length,n=r.length;++t<s;)r[n+t]=e[t];return r}function Cc(r,e){for(var t=-1,s=r==null?0:r.length;++t<s;)if(e(r[t],t,r))return!0;return!1}function Oc(r,e){for(var t=-1,s=Array(r);++t<r;)s[t]=e(t);return s}function _c(r){return function(e){return r(e)}}function Ic(r,e){return r.has(e)}function Rc(r,e){return r?.[e]}function Bc(r){var e=-1,t=Array(r.size);return r.forEach(function(s,n){t[++e]=[n,s]}),t}function Mc(r,e){return function(t){return r(e(t))}}function Dc(r){var e=-1,t=Array(r.size);return r.forEach(function(s){t[++e]=s}),t}var Uc=Array.prototype,Pc=Function.prototype,or=Object.prototype,us=pt["__core-js_shared__"],Gi=Pc.toString,ct=or.hasOwnProperty,qi=function(){var r=/[^.]+$/.exec(us&&us.keys&&us.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),Wi=or.toString,jc=RegExp("^"+Gi.call(ct).replace(wc,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Si=Ki?pt.Buffer:void 0,sr=pt.Symbol,ki=pt.Uint8Array,Zi=or.propertyIsEnumerable,Hc=Uc.splice,Ft=sr?sr.toStringTag:void 0,Ci=Object.getOwnPropertySymbols,Fc=Si?Si.isBuffer:void 0,$c=Mc(Object.keys,Object),ms=ce(pt,"DataView"),we=ce(pt,"Map"),ps=ce(pt,"Promise"),gs=ce(pt,"Set"),bs=ce(pt,"WeakMap"),Te=ce(Object,"create"),zc=Vt(ms),Vc=Vt(we),Kc=Vt(ps),Gc=Vt(gs),Wc=Vt(bs),Oi=sr?sr.prototype:void 0,hs=Oi?Oi.valueOf:void 0;function $t(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var s=r[e];this.set(s[0],s[1])}}function Zc(){this.__data__=Te?Te(null):{},this.size=0}function Qc(r){var e=this.has(r)&&delete this.__data__[r];return this.size-=e?1:0,e}function Xc(r){var e=this.__data__;if(Te){var t=e[r];return t===vs?void 0:t}return ct.call(e,r)?e[r]:void 0}function Yc(r){var e=this.__data__;return Te?e[r]!==void 0:ct.call(e,r)}function Jc(r,e){var t=this.__data__;return this.size+=this.has(r)?0:1,t[r]=Te&&e===void 0?vs:e,this}$t.prototype.clear=Zc;$t.prototype.delete=Qc;$t.prototype.get=Xc;$t.prototype.has=Yc;$t.prototype.set=Jc;function gt(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var s=r[e];this.set(s[0],s[1])}}function tu(){this.__data__=[],this.size=0}function eu(r){var e=this.__data__,t=ar(e,r);if(t<0)return!1;var s=e.length-1;return t==s?e.pop():Hc.call(e,t,1),--this.size,!0}function ru(r){var e=this.__data__,t=ar(e,r);return t<0?void 0:e[t][1]}function su(r){return ar(this.__data__,r)>-1}function nu(r,e){var t=this.__data__,s=ar(t,r);return s<0?(++this.size,t.push([r,e])):t[s][1]=e,this}gt.prototype.clear=tu;gt.prototype.delete=eu;gt.prototype.get=ru;gt.prototype.has=su;gt.prototype.set=nu;function zt(r){var e=-1,t=r==null?0:r.length;for(this.clear();++e<t;){var s=r[e];this.set(s[0],s[1])}}function iu(){this.size=0,this.__data__={hash:new $t,map:new(we||gt),string:new $t}}function lu(r){var e=cr(this,r).delete(r);return this.size-=e?1:0,e}function ou(r){return cr(this,r).get(r)}function au(r){return cr(this,r).has(r)}function cu(r,e){var t=cr(this,r),s=t.size;return t.set(r,e),this.size+=t.size==s?0:1,this}zt.prototype.clear=iu;zt.prototype.delete=lu;zt.prototype.get=ou;zt.prototype.has=au;zt.prototype.set=cu;function nr(r){var e=-1,t=r==null?0:r.length;for(this.__data__=new zt;++e<t;)this.add(r[e])}function uu(r){return this.__data__.set(r,vs),this}function hu(r){return this.__data__.has(r)}nr.prototype.add=nr.prototype.push=uu;nr.prototype.has=hu;function Ct(r){var e=this.__data__=new gt(r);this.size=e.size}function fu(){this.__data__=new gt,this.size=0}function du(r){var e=this.__data__,t=e.delete(r);return this.size=e.size,t}function mu(r){return this.__data__.get(r)}function pu(r){return this.__data__.has(r)}function gu(r,e){var t=this.__data__;if(t instanceof gt){var s=t.__data__;if(!we||s.length<ac-1)return s.push([r,e]),this.size=++t.size,this;t=this.__data__=new zt(s)}return t.set(r,e),this.size=t.size,this}Ct.prototype.clear=fu;Ct.prototype.delete=du;Ct.prototype.get=mu;Ct.prototype.has=pu;Ct.prototype.set=gu;function bu(r,e){var t=ir(r),s=!t&&_u(r),n=!t&&!s&&ys(r),i=!t&&!s&&!n&&rl(r),l=t||s||n||i,o=l?Oc(r.length,String):[],a=o.length;for(var c in r)(e||ct.call(r,c))&&!(l&&(c=="length"||n&&(c=="offset"||c=="parent")||i&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||qu(c,a)))&&o.push(c);return o}function ar(r,e){for(var t=r.length;t--;)if(Yi(r[t][0],e))return t;return-1}function yu(r,e,t){var s=e(r);return ir(r)?s:kc(s,t(r))}function qe(r){return r==null?r===void 0?mc:hc:Ft&&Ft in Object(r)?Tu(r):Ou(r)}function _i(r){return Le(r)&&qe(r)==Je}function Qi(r,e,t,s,n){return r===e?!0:r==null||e==null||!Le(r)&&!Le(e)?r!==r&&e!==e:vu(r,e,t,s,Qi,n)}function vu(r,e,t,s,n,i){var l=ir(r),o=ir(e),a=l?fs:kt(r),c=o?fs:kt(e);a=a==Je?oe:a,c=c==Je?oe:c;var u=a==oe,h=c==oe,d=a==c;if(d&&ys(r)){if(!ys(e))return!1;l=!0,u=!1}if(d&&!u)return i||(i=new Ct),l||rl(r)?Xi(r,e,t,s,n,i):Au(r,e,a,t,s,n,i);if(!(t&lr)){var m=u&&ct.call(r,"__wrapped__"),g=h&&ct.call(e,"__wrapped__");if(m||g){var v=m?r.value():r,E=g?e.value():e;return i||(i=new Ct),n(v,E,t,s,i)}}return d?(i||(i=new Ct),wu(r,e,t,s,n,i)):!1}function Eu(r){if(!el(r)||ku(r))return!1;var e=Ji(r)?jc:Tc;return e.test(Vt(r))}function Nu(r){return Le(r)&&tl(r.length)&&!!L[qe(r)]}function xu(r){if(!Cu(r))return $c(r);var e=[];for(var t in Object(r))ct.call(r,t)&&t!="constructor"&&e.push(t);return e}function Xi(r,e,t,s,n,i){var l=t&lr,o=r.length,a=e.length;if(o!=a&&!(l&&a>o))return!1;var c=i.get(r);if(c&&i.get(e))return c==e;var u=-1,h=!0,d=t&Ri?new nr:void 0;for(i.set(r,e),i.set(e,r);++u<o;){var m=r[u],g=e[u];if(s)var v=l?s(g,m,u,e,r,i):s(m,g,u,r,e,i);if(v!==void 0){if(v)continue;h=!1;break}if(d){if(!Cc(e,function(E,y){if(!Ic(d,y)&&(m===E||n(m,E,t,s,i)))return d.push(y)})){h=!1;break}}else if(!(m===g||n(m,g,t,s,i))){h=!1;break}}return i.delete(r),i.delete(e),h}function Au(r,e,t,s,n,i,l){switch(t){case rr:if(r.byteLength!=e.byteLength||r.byteOffset!=e.byteOffset)return!1;r=r.buffer,e=e.buffer;case $i:return!(r.byteLength!=e.byteLength||!i(new ki(r),new ki(e)));case Mi:case Di:case ji:return Yi(+r,+e);case Ui:return r.name==e.name&&r.message==e.message;case Hi:case Fi:return r==e+"";case tr:var o=Bc;case er:var a=s&lr;if(o||(o=Dc),r.size!=e.size&&!a)return!1;var c=l.get(r);if(c)return c==e;s|=Ri,l.set(r,e);var u=Xi(o(r),o(e),s,n,i,l);return l.delete(r),u;case dc:if(hs)return hs.call(r)==hs.call(e)}return!1}function wu(r,e,t,s,n,i){var l=t&lr,o=Ii(r),a=o.length,c=Ii(e),u=c.length;if(a!=u&&!l)return!1;for(var h=a;h--;){var d=o[h];if(!(l?d in e:ct.call(e,d)))return!1}var m=i.get(r);if(m&&i.get(e))return m==e;var g=!0;i.set(r,e),i.set(e,r);for(var v=l;++h<a;){d=o[h];var E=r[d],y=e[d];if(s)var N=l?s(y,E,d,e,r,i):s(E,y,d,r,e,i);if(!(N===void 0?E===y||n(E,y,t,s,i):N)){g=!1;break}v||(v=d=="constructor")}if(g&&!v){var x=r.constructor,A=e.constructor;x!=A&&"constructor"in r&&"constructor"in e&&!(typeof x=="function"&&x instanceof x&&typeof A=="function"&&A instanceof A)&&(g=!1)}return i.delete(r),i.delete(e),g}function Ii(r){return yu(r,Bu,Lu)}function cr(r,e){var t=r.__data__;return Su(e)?t[typeof e=="string"?"string":"hash"]:t.map}function ce(r,e){var t=Rc(r,e);return Eu(t)?t:void 0}function Tu(r){var e=ct.call(r,Ft),t=r[Ft];try{r[Ft]=void 0;var s=!0}catch{}var n=Wi.call(r);return s&&(e?r[Ft]=t:delete r[Ft]),n}var Lu=Ci?function(r){return r==null?[]:(r=Object(r),Sc(Ci(r),function(e){return Zi.call(r,e)}))}:Mu,kt=qe;(ms&&kt(new ms(new ArrayBuffer(1)))!=rr||we&&kt(new we)!=tr||ps&&kt(ps.resolve())!=Ai||gs&&kt(new gs)!=er||bs&&kt(new bs)!=ds)&&(kt=function(r){var e=qe(r),t=e==oe?r.constructor:void 0,s=t?Vt(t):"";if(s)switch(s){case zc:return rr;case Vc:return tr;case Kc:return Ai;case Gc:return er;case Wc:return ds}return e});function qu(r,e){return e=e??Bi,!!e&&(typeof r=="number"||Lc.test(r))&&r>-1&&r%1==0&&r<e}function Su(r){var e=typeof r;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?r!=="__proto__":r===null}function ku(r){return!!qi&&qi in r}function Cu(r){var e=r&&r.constructor,t=typeof e=="function"&&e.prototype||or;return r===t}function Ou(r){return Wi.call(r)}function Vt(r){if(r!=null){try{return Gi.call(r)}catch{}try{return r+""}catch{}}return""}function Yi(r,e){return r===e||r!==r&&e!==e}var _u=_i(function(){return arguments}())?_i:function(r){return Le(r)&&ct.call(r,"callee")&&!Zi.call(r,"callee")},ir=Array.isArray;function Iu(r){return r!=null&&tl(r.length)&&!Ji(r)}var ys=Fc||Du;function Ru(r,e){return Qi(r,e)}function Ji(r){if(!el(r))return!1;var e=qe(r);return e==Pi||e==uc||e==cc||e==fc}function tl(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Bi}function el(r){var e=typeof r;return r!=null&&(e=="object"||e=="function")}function Le(r){return r!=null&&typeof r=="object"}var rl=Li?_c(Li):Nu;function Bu(r){return Iu(r)?bu(r):xu(r)}function Mu(){return[]}function Du(){return!1}ae.exports=Ru});var sl=wt(xs=>{"use strict";Object.defineProperty(xs,"__esModule",{value:!0});var Uu=as(),Pu=Es(),Ns;(function(r){function e(i={},l={},o=!1){typeof i!="object"&&(i={}),typeof l!="object"&&(l={});let a=Uu(l);o||(a=Object.keys(a).reduce((c,u)=>(a[u]!=null&&(c[u]=a[u]),c),{}));for(let c in i)i[c]!==void 0&&l[c]===void 0&&(a[c]=i[c]);return Object.keys(a).length>0?a:void 0}r.compose=e;function t(i={},l={}){typeof i!="object"&&(i={}),typeof l!="object"&&(l={});let o=Object.keys(i).concat(Object.keys(l)).reduce((a,c)=>(Pu(i[c],l[c])||(a[c]=l[c]===void 0?null:l[c]),a),{});return Object.keys(o).length>0?o:void 0}r.diff=t;function s(i={},l={}){i=i||{};let o=Object.keys(l).reduce((a,c)=>(l[c]!==i[c]&&i[c]!==void 0&&(a[c]=l[c]),a),{});return Object.keys(i).reduce((a,c)=>(i[c]!==l[c]&&l[c]===void 0&&(a[c]=null),a),o)}r.invert=s;function n(i,l,o=!1){if(typeof i!="object")return l;if(typeof l!="object")return;if(!o)return l;let a=Object.keys(l).reduce((c,u)=>(i[u]===void 0&&(c[u]=l[u]),c),{});return Object.keys(a).length>0?a:void 0}r.transform=n})(Ns||(Ns={}));xs.default=Ns});var Ts=wt(ws=>{"use strict";Object.defineProperty(ws,"__esModule",{value:!0});var As;(function(r){function e(t){return typeof t.delete=="number"?t.delete:typeof t.retain=="number"?t.retain:typeof t.retain=="object"&&t.retain!==null?1:typeof t.insert=="string"?t.insert.length:1}r.length=e})(As||(As={}));ws.default=As});var il=wt(qs=>{"use strict";Object.defineProperty(qs,"__esModule",{value:!0});var nl=Ts(),Ls=class{constructor(e){this.ops=e,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(e){e||(e=1/0);let t=this.ops[this.index];if(t){let s=this.offset,n=nl.default.length(t);if(e>=n-s?(e=n-s,this.index+=1,this.offset=0):this.offset+=e,typeof t.delete=="number")return{delete:e};{let i={};return t.attributes&&(i.attributes=t.attributes),typeof t.retain=="number"?i.retain=e:typeof t.retain=="object"&&t.retain!==null?i.retain=t.retain:typeof t.insert=="string"?i.insert=t.insert.substr(s,e):i.insert=t.insert,i}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?nl.default.length(this.ops[this.index])-this.offset:1/0}peekType(){let e=this.ops[this.index];return e?typeof e.delete=="number"?"delete":typeof e.retain=="number"||typeof e.retain=="object"&&e.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{let e=this.offset,t=this.index,s=this.next(),n=this.ops.slice(this.index);return this.offset=e,this.index=t,[s].concat(n)}}else return[]}};qs.default=Ls});var rt=wt((yt,hr)=>{"use strict";Object.defineProperty(yt,"__esModule",{value:!0});yt.AttributeMap=yt.OpIterator=yt.Op=void 0;var ur=Bn(),ju=as(),Ss=Es(),Kt=sl();yt.AttributeMap=Kt.default;var bt=Ts();yt.Op=bt.default;var nt=il();yt.OpIterator=nt.default;var Hu="\0",ll=(r,e)=>{if(typeof r!="object"||r===null)throw new Error(`cannot retain a ${typeof r}`);if(typeof e!="object"||e===null)throw new Error(`cannot retain a ${typeof e}`);let t=Object.keys(r)[0];if(!t||t!==Object.keys(e)[0])throw new Error(`embed types not matched: ${t} != ${Object.keys(e)[0]}`);return[t,r[t],e[t]]},vt=class r{constructor(e){Array.isArray(e)?this.ops=e:e!=null&&Array.isArray(e.ops)?this.ops=e.ops:this.ops=[]}static registerEmbed(e,t){this.handlers[e]=t}static unregisterEmbed(e){delete this.handlers[e]}static getHandler(e){let t=this.handlers[e];if(!t)throw new Error(`no handlers for embed type "${e}"`);return t}insert(e,t){let s={};return typeof e=="string"&&e.length===0?this:(s.insert=e,t!=null&&typeof t=="object"&&Object.keys(t).length>0&&(s.attributes=t),this.push(s))}delete(e){return e<=0?this:this.push({delete:e})}retain(e,t){if(typeof e=="number"&&e<=0)return this;let s={retain:e};return t!=null&&typeof t=="object"&&Object.keys(t).length>0&&(s.attributes=t),this.push(s)}push(e){let t=this.ops.length,s=this.ops[t-1];if(e=ju(e),typeof s=="object"){if(typeof e.delete=="number"&&typeof s.delete=="number")return this.ops[t-1]={delete:s.delete+e.delete},this;if(typeof s.delete=="number"&&e.insert!=null&&(t-=1,s=this.ops[t-1],typeof s!="object"))return this.ops.unshift(e),this;if(Ss(e.attributes,s.attributes)){if(typeof e.insert=="string"&&typeof s.insert=="string")return this.ops[t-1]={insert:s.insert+e.insert},typeof e.attributes=="object"&&(this.ops[t-1].attributes=e.attributes),this;if(typeof e.retain=="number"&&typeof s.retain=="number")return this.ops[t-1]={retain:s.retain+e.retain},typeof e.attributes=="object"&&(this.ops[t-1].attributes=e.attributes),this}}return t===this.ops.length?this.ops.push(e):this.ops.splice(t,0,e),this}chop(){let e=this.ops[this.ops.length-1];return e&&typeof e.retain=="number"&&!e.attributes&&this.ops.pop(),this}filter(e){return this.ops.filter(e)}forEach(e){this.ops.forEach(e)}map(e){return this.ops.map(e)}partition(e){let t=[],s=[];return this.forEach(n=>{(e(n)?t:s).push(n)}),[t,s]}reduce(e,t){return this.ops.reduce(e,t)}changeLength(){return this.reduce((e,t)=>t.insert?e+bt.default.length(t):t.delete?e-t.delete:e,0)}length(){return this.reduce((e,t)=>e+bt.default.length(t),0)}slice(e=0,t=1/0){let s=[],n=new nt.default(this.ops),i=0;for(;i<t&&n.hasNext();){let l;i<e?l=n.next(e-i):(l=n.next(t-i),s.push(l)),i+=bt.default.length(l)}return new r(s)}compose(e){let t=new nt.default(this.ops),s=new nt.default(e.ops),n=[],i=s.peek();if(i!=null&&typeof i.retain=="number"&&i.attributes==null){let o=i.retain;for(;t.peekType()==="insert"&&t.peekLength()<=o;)o-=t.peekLength(),n.push(t.next());i.retain-o>0&&s.next(i.retain-o)}let l=new r(n);for(;t.hasNext()||s.hasNext();)if(s.peekType()==="insert")l.push(s.next());else if(t.peekType()==="delete")l.push(t.next());else{let o=Math.min(t.peekLength(),s.peekLength()),a=t.next(o),c=s.next(o);if(c.retain){let u={};if(typeof a.retain=="number")u.retain=typeof c.retain=="number"?o:c.retain;else if(typeof c.retain=="number")a.retain==null?u.insert=a.insert:u.retain=a.retain;else{let d=a.retain==null?"insert":"retain",[m,g,v]=ll(a[d],c.retain),E=r.getHandler(m);u[d]={[m]:E.compose(g,v,d==="retain")}}let h=Kt.default.compose(a.attributes,c.attributes,typeof a.retain=="number");if(h&&(u.attributes=h),l.push(u),!s.hasNext()&&Ss(l.ops[l.ops.length-1],u)){let d=new r(t.rest());return l.concat(d).chop()}}else typeof c.delete=="number"&&(typeof a.retain=="number"||typeof a.retain=="object"&&a.retain!==null)&&l.push(c)}return l.chop()}concat(e){let t=new r(this.ops.slice());return e.ops.length>0&&(t.push(e.ops[0]),t.ops=t.ops.concat(e.ops.slice(1))),t}diff(e,t){if(this.ops===e.ops)return new r;let s=[this,e].map(a=>a.map(c=>{if(c.insert!=null)return typeof c.insert=="string"?c.insert:Hu;let u=a===e?"on":"with";throw new Error("diff() called "+u+" non-document")}).join("")),n=new r,i=ur(s[0],s[1],t,!0),l=new nt.default(this.ops),o=new nt.default(e.ops);return i.forEach(a=>{let c=a[1].length;for(;c>0;){let u=0;switch(a[0]){case ur.INSERT:u=Math.min(o.peekLength(),c),n.push(o.next(u));break;case ur.DELETE:u=Math.min(c,l.peekLength()),l.next(u),n.delete(u);break;case ur.EQUAL:u=Math.min(l.peekLength(),o.peekLength(),c);let h=l.next(u),d=o.next(u);Ss(h.insert,d.insert)?n.retain(u,Kt.default.diff(h.attributes,d.attributes)):n.push(d).delete(u);break}c-=u}}),n.chop()}eachLine(e,t=`
`){let s=new nt.default(this.ops),n=new r,i=0;for(;s.hasNext();){if(s.peekType()!=="insert")return;let l=s.peek(),o=bt.default.length(l)-s.peekLength(),a=typeof l.insert=="string"?l.insert.indexOf(t,o)-o:-1;if(a<0)n.push(s.next());else if(a>0)n.push(s.next(a));else{if(e(n,s.next(1).attributes||{},i)===!1)return;i+=1,n=new r}}n.length()>0&&e(n,{},i)}invert(e){let t=new r;return this.reduce((s,n)=>{if(n.insert)t.delete(bt.default.length(n));else{if(typeof n.retain=="number"&&n.attributes==null)return t.retain(n.retain),s+n.retain;if(n.delete||typeof n.retain=="number"){let i=n.delete||n.retain;return e.slice(s,s+i).forEach(o=>{n.delete?t.push(o):n.retain&&n.attributes&&t.retain(bt.default.length(o),Kt.default.invert(n.attributes,o.attributes))}),s+i}else if(typeof n.retain=="object"&&n.retain!==null){let i=e.slice(s,s+1),l=new nt.default(i.ops).next(),[o,a,c]=ll(n.retain,l.insert),u=r.getHandler(o);return t.retain({[o]:u.invert(a,c)},Kt.default.invert(n.attributes,l.attributes)),s+1}}return s},0),t.chop()}transform(e,t=!1){if(t=!!t,typeof e=="number")return this.transformPosition(e,t);let s=e,n=new nt.default(this.ops),i=new nt.default(s.ops),l=new r;for(;n.hasNext()||i.hasNext();)if(n.peekType()==="insert"&&(t||i.peekType()!=="insert"))l.retain(bt.default.length(n.next()));else if(i.peekType()==="insert")l.push(i.next());else{let o=Math.min(n.peekLength(),i.peekLength()),a=n.next(o),c=i.next(o);if(a.delete)continue;if(c.delete)l.push(c);else{let u=a.retain,h=c.retain,d=typeof h=="object"&&h!==null?h:o;if(typeof u=="object"&&u!==null&&typeof h=="object"&&h!==null){let m=Object.keys(u)[0];if(m===Object.keys(h)[0]){let g=r.getHandler(m);g&&(d={[m]:g.transform(u[m],h[m],t)})}}l.retain(d,Kt.default.transform(a.attributes,c.attributes,t))}}return l.chop()}transformPosition(e,t=!1){t=!!t;let s=new nt.default(this.ops),n=0;for(;s.hasNext()&&n<=e;){let i=s.peekLength(),l=s.peekType();if(s.next(),l==="delete"){e-=Math.min(i,e-n);continue}else l==="insert"&&(n<e||!t)&&(e+=i);n+=i}return e}};vt.Op=bt.default;vt.OpIterator=nt.default;vt.AttributeMap=Kt.default;vt.handlers={};yt.default=vt;typeof hr=="object"&&(hr.exports=vt,hr.exports.default=vt)});var cl=wt((Nd,Os)=>{"use strict";var Ku=Object.prototype.hasOwnProperty,V="~";function Se(){}Object.create&&(Se.prototype=Object.create(null),new Se().__proto__||(V=!1));function Gu(r,e,t){this.fn=r,this.context=e,this.once=t||!1}function al(r,e,t,s,n){if(typeof t!="function")throw new TypeError("The listener must be a function");var i=new Gu(t,s||r,n),l=V?V+e:e;return r._events[l]?r._events[l].fn?r._events[l]=[r._events[l],i]:r._events[l].push(i):(r._events[l]=i,r._eventsCount++),r}function fr(r,e){--r._eventsCount===0?r._events=new Se:delete r._events[e]}function H(){this._events=new Se,this._eventsCount=0}H.prototype.eventNames=function(){var e=[],t,s;if(this._eventsCount===0)return e;for(s in t=this._events)Ku.call(t,s)&&e.push(V?s.slice(1):s);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e};H.prototype.listeners=function(e){var t=V?V+e:e,s=this._events[t];if(!s)return[];if(s.fn)return[s.fn];for(var n=0,i=s.length,l=new Array(i);n<i;n++)l[n]=s[n].fn;return l};H.prototype.listenerCount=function(e){var t=V?V+e:e,s=this._events[t];return s?s.fn?1:s.length:0};H.prototype.emit=function(e,t,s,n,i,l){var o=V?V+e:e;if(!this._events[o])return!1;var a=this._events[o],c=arguments.length,u,h;if(a.fn){switch(a.once&&this.removeListener(e,a.fn,void 0,!0),c){case 1:return a.fn.call(a.context),!0;case 2:return a.fn.call(a.context,t),!0;case 3:return a.fn.call(a.context,t,s),!0;case 4:return a.fn.call(a.context,t,s,n),!0;case 5:return a.fn.call(a.context,t,s,n,i),!0;case 6:return a.fn.call(a.context,t,s,n,i,l),!0}for(h=1,u=new Array(c-1);h<c;h++)u[h-1]=arguments[h];a.fn.apply(a.context,u)}else{var d=a.length,m;for(h=0;h<d;h++)switch(a[h].once&&this.removeListener(e,a[h].fn,void 0,!0),c){case 1:a[h].fn.call(a[h].context);break;case 2:a[h].fn.call(a[h].context,t);break;case 3:a[h].fn.call(a[h].context,t,s);break;case 4:a[h].fn.call(a[h].context,t,s,n);break;default:if(!u)for(m=1,u=new Array(c-1);m<c;m++)u[m-1]=arguments[m];a[h].fn.apply(a[h].context,u)}}return!0};H.prototype.on=function(e,t,s){return al(this,e,t,s,!1)};H.prototype.once=function(e,t,s){return al(this,e,t,s,!0)};H.prototype.removeListener=function(e,t,s,n){var i=V?V+e:e;if(!this._events[i])return this;if(!t)return fr(this,i),this;var l=this._events[i];if(l.fn)l.fn===t&&(!n||l.once)&&(!s||l.context===s)&&fr(this,i);else{for(var o=0,a=[],c=l.length;o<c;o++)(l[o].fn!==t||n&&!l[o].once||s&&l[o].context!==s)&&a.push(l[o]);a.length?this._events[i]=a.length===1?a[0]:a:fr(this,i)}return this};H.prototype.removeAllListeners=function(e){var t;return e?(t=V?V+e:e,this._events[t]&&fr(this,t)):(this._events=new Se,this._eventsCount=0),this};H.prototype.off=H.prototype.removeListener;H.prototype.addListener=H.prototype.on;H.prefixed=V;H.EventEmitter=H;typeof Os<"u"&&(Os.exports=H)});var ye={};fo(ye,{Attributor:()=>$,AttributorStore:()=>ge,BlockBlot:()=>Ut,ClassAttributor:()=>R,ContainerBlot:()=>ee,EmbedBlot:()=>k,InlineBlot:()=>Fe,LeafBlot:()=>B,ParentBlot:()=>W,Registry:()=>qt,Scope:()=>p,ScrollBlot:()=>be,StyleAttributor:()=>Z,TextBlot:()=>re});var p=(r=>(r[r.TYPE=3]="TYPE",r[r.LEVEL=12]="LEVEL",r[r.ATTRIBUTE=13]="ATTRIBUTE",r[r.BLOT=14]="BLOT",r[r.INLINE=7]="INLINE",r[r.BLOCK=11]="BLOCK",r[r.BLOCK_BLOT=10]="BLOCK_BLOT",r[r.INLINE_BLOT=6]="INLINE_BLOT",r[r.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",r[r.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",r[r.ANY=15]="ANY",r))(p||{}),$=class{constructor(e,t,s={}){this.attrName=e,this.keyName=t;let n=p.TYPE&p.ATTRIBUTE;this.scope=s.scope!=null?s.scope&p.LEVEL|n:p.ATTRIBUTE,s.whitelist!=null&&(this.whitelist=s.whitelist)}static keys(e){return Array.from(e.attributes).map(t=>t.name)}add(e,t){return this.canAdd(e,t)?(e.setAttribute(this.keyName,t),!0):!1}canAdd(e,t){return this.whitelist==null?!0:typeof t=="string"?this.whitelist.indexOf(t.replace(/["']/g,""))>-1:this.whitelist.indexOf(t)>-1}remove(e){e.removeAttribute(this.keyName)}value(e){let t=e.getAttribute(this.keyName);return this.canAdd(e,t)&&t?t:""}},Lt=class extends Error{constructor(e){e="[Parchment] "+e,super(e),this.message=e,this.name=this.constructor.name}},mo=(()=>{let r=class Mr{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,s=!1){if(t==null)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(s){let n=null;try{n=t.parentNode}catch{return null}return this.find(n,s)}return null}create(t,s,n){let i=this.query(s);if(i==null)throw new Lt(`Unable to create ${s} blot`);let l=i,o=s instanceof Node||s.nodeType===Node.TEXT_NODE?s:l.create(n),a=new l(t,o,n);return Mr.blots.set(a.domNode,a),a}find(t,s=!1){return Mr.find(t,s)}query(t,s=p.ANY){let n;return typeof t=="string"?n=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?n=this.types.text:typeof t=="number"?t&p.LEVEL&p.BLOCK?n=this.types.block:t&p.LEVEL&p.INLINE&&(n=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(i=>(n=this.classes[i],!!n)),n=n||this.tags[t.tagName]),n==null?null:"scope"in n&&s&p.LEVEL&n.scope&&s&p.TYPE&n.scope?n:null}register(...t){return t.map(s=>{let n="blotName"in s,i="attrName"in s;if(!n&&!i)throw new Lt("Invalid definition");if(n&&s.blotName==="abstract")throw new Lt("Cannot register abstract class");let l=n?s.blotName:i?s.attrName:void 0;return this.types[l]=s,i?typeof s.keyName=="string"&&(this.attributes[s.keyName]=s):n&&(s.className&&(this.classes[s.className]=s),s.tagName&&(Array.isArray(s.tagName)?s.tagName=s.tagName.map(o=>o.toUpperCase()):s.tagName=s.tagName.toUpperCase(),(Array.isArray(s.tagName)?s.tagName:[s.tagName]).forEach(o=>{(this.tags[o]==null||s.className==null)&&(this.tags[o]=s)}))),s})}};return r.blots=new WeakMap,r})(),qt=mo;function vn(r,e){return(r.getAttribute("class")||"").split(/\s+/).filter(t=>t.indexOf(`${e}-`)===0)}var Dr=class extends ${static keys(e){return(e.getAttribute("class")||"").split(/\s+/).map(t=>t.split("-").slice(0,-1).join("-"))}add(e,t){return this.canAdd(e,t)?(this.remove(e),e.classList.add(`${this.keyName}-${t}`),!0):!1}remove(e){vn(e,this.keyName).forEach(t=>{e.classList.remove(t)}),e.classList.length===0&&e.removeAttribute("class")}value(e){let t=(vn(e,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(e,t)?t:""}},R=Dr;function Br(r){let e=r.split("-"),t=e.slice(1).map(s=>s[0].toUpperCase()+s.slice(1)).join("");return e[0]+t}var Ur=class extends ${static keys(e){return(e.getAttribute("style")||"").split(";").map(t=>t.split(":")[0].trim())}add(e,t){return this.canAdd(e,t)?(e.style[Br(this.keyName)]=t,!0):!1}remove(e){e.style[Br(this.keyName)]="",e.getAttribute("style")||e.removeAttribute("style")}value(e){let t=e.style[Br(this.keyName)];return this.canAdd(e,t)?t:""}},Z=Ur,Pr=class{constructor(e){this.attributes={},this.domNode=e,this.build()}attribute(e,t){t?e.add(this.domNode,t)&&(e.value(this.domNode)!=null?this.attributes[e.attrName]=e:delete this.attributes[e.attrName]):(e.remove(this.domNode),delete this.attributes[e.attrName])}build(){this.attributes={};let e=qt.find(this.domNode);if(e==null)return;let t=$.keys(this.domNode),s=R.keys(this.domNode),n=Z.keys(this.domNode);t.concat(s).concat(n).forEach(i=>{let l=e.scroll.query(i,p.ATTRIBUTE);l instanceof $&&(this.attributes[l.attrName]=l)})}copy(e){Object.keys(this.attributes).forEach(t=>{let s=this.attributes[t].value(this.domNode);e.format(t,s)})}move(e){this.copy(e),Object.keys(this.attributes).forEach(t=>{this.attributes[t].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((e,t)=>(e[t]=this.attributes[t].value(this.domNode),e),{})}},ge=Pr,Nn=class{constructor(e,t){this.scroll=e,this.domNode=t,qt.blots.set(t,this),this.prev=null,this.next=null}static create(e){if(this.tagName==null)throw new Lt("Blot definition missing tagName");let t,s;return Array.isArray(this.tagName)?(typeof e=="string"?(s=e.toUpperCase(),parseInt(s,10).toString()===s&&(s=parseInt(s,10))):typeof e=="number"&&(s=e),typeof s=="number"?t=document.createElement(this.tagName[s-1]):s&&this.tagName.indexOf(s)>-1?t=document.createElement(s):t=document.createElement(this.tagName[0])):t=document.createElement(this.tagName),this.className&&t.classList.add(this.className),t}get statics(){return this.constructor}attach(){}clone(){let e=this.domNode.cloneNode(!1);return this.scroll.create(e)}detach(){this.parent!=null&&this.parent.removeChild(this),qt.blots.delete(this.domNode)}deleteAt(e,t){this.isolate(e,t).remove()}formatAt(e,t,s,n){let i=this.isolate(e,t);if(this.scroll.query(s,p.BLOT)!=null&&n)i.wrap(s,n);else if(this.scroll.query(s,p.ATTRIBUTE)!=null){let l=this.scroll.create(this.statics.scope);i.wrap(l),l.format(s,n)}}insertAt(e,t,s){let n=s==null?this.scroll.create("text",t):this.scroll.create(t,s),i=this.split(e);this.parent.insertBefore(n,i||void 0)}isolate(e,t){let s=this.split(e);if(s==null)throw new Error("Attempt to isolate at end");return s.split(t),s}length(){return 1}offset(e=this.parent){return this.parent==null||this===e?0:this.parent.children.offset(this)+this.parent.offset(e)}optimize(e){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(e,t){let s=typeof e=="string"?this.scroll.create(e,t):e;return this.parent!=null&&(this.parent.insertBefore(s,this.next||void 0),this.remove()),s}split(e,t){return e===0?this:this.next}update(e,t){}wrap(e,t){let s=typeof e=="string"?this.scroll.create(e,t):e;if(this.parent!=null&&this.parent.insertBefore(s,this.next||void 0),typeof s.appendChild!="function")throw new Lt(`Cannot wrap ${e}`);return s.appendChild(this),s}};Nn.blotName="abstract";var xn=Nn,An=class extends xn{static value(e){return!0}index(e,t){return this.domNode===e||this.domNode.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(t,1):-1}position(e,t){let s=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return e>0&&(s+=1),[this.parent.domNode,s]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};An.scope=p.INLINE_BLOT;var po=An,B=po,jr=class{constructor(){this.head=null,this.tail=null,this.length=0}append(...e){if(this.insertBefore(e[0],null),e.length>1){let t=e.slice(1);this.append(...t)}}at(e){let t=this.iterator(),s=t();for(;s&&e>0;)e-=1,s=t();return s}contains(e){let t=this.iterator(),s=t();for(;s;){if(s===e)return!0;s=t()}return!1}indexOf(e){let t=this.iterator(),s=t(),n=0;for(;s;){if(s===e)return n;n+=1,s=t()}return-1}insertBefore(e,t){e!=null&&(this.remove(e),e.next=t,t!=null?(e.prev=t.prev,t.prev!=null&&(t.prev.next=e),t.prev=e,t===this.head&&(this.head=e)):this.tail!=null?(this.tail.next=e,e.prev=this.tail,this.tail=e):(e.prev=null,this.head=this.tail=e),this.length+=1)}offset(e){let t=0,s=this.head;for(;s!=null;){if(s===e)return t;t+=s.length(),s=s.next}return-1}remove(e){this.contains(e)&&(e.prev!=null&&(e.prev.next=e.next),e.next!=null&&(e.next.prev=e.prev),e===this.head&&(this.head=e.next),e===this.tail&&(this.tail=e.prev),this.length-=1)}iterator(e=this.head){return()=>{let t=e;return e!=null&&(e=e.next),t}}find(e,t=!1){let s=this.iterator(),n=s();for(;n;){let i=n.length();if(e<i||t&&e===i&&(n.next==null||n.next.length()!==0))return[n,e];e-=i,n=s()}return[null,0]}forEach(e){let t=this.iterator(),s=t();for(;s;)e(s),s=t()}forEachAt(e,t,s){if(t<=0)return;let[n,i]=this.find(e),l=e-i,o=this.iterator(n),a=o();for(;a&&l<e+t;){let c=a.length();e>l?s(a,e-l,Math.min(t,l+c-e)):s(a,0,Math.min(c,e+t-l)),l+=c,a=o()}}map(e){return this.reduce((t,s)=>(t.push(e(s)),t),[])}reduce(e,t){let s=this.iterator(),n=s();for(;n;)t=e(t,n),n=s();return t}};function En(r,e){let t=e.find(r);if(t)return t;try{return e.create(r)}catch{let s=e.create(p.INLINE);return Array.from(r.childNodes).forEach(n=>{s.domNode.appendChild(n)}),r.parentNode&&r.parentNode.replaceChild(s.domNode,r),s.attach(),s}}var go=(()=>{let r=class Tt extends xn{constructor(t,s){super(t,s),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=t,Tt.uiClass&&this.uiNode.classList.add(Tt.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new jr,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{let s=En(t,this.scroll);this.insertBefore(s,this.children.head||void 0)}catch(s){if(s instanceof Lt)return;throw s}})}deleteAt(t,s){if(t===0&&s===this.length())return this.remove();this.children.forEachAt(t,s,(n,i,l)=>{n.deleteAt(i,l)})}descendant(t,s=0){let[n,i]=this.children.find(s);return t.blotName==null&&t(n)||t.blotName!=null&&n instanceof t?[n,i]:n instanceof Tt?n.descendant(t,i):[null,-1]}descendants(t,s=0,n=Number.MAX_VALUE){let i=[],l=n;return this.children.forEachAt(s,n,(o,a,c)=>{(t.blotName==null&&t(o)||t.blotName!=null&&o instanceof t)&&i.push(o),o instanceof Tt&&(i=i.concat(o.descendants(t,a,l))),l-=c}),i}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(s=>{t||this.statics.allowedChildren.some(n=>s instanceof n)||(s.statics.scope===p.BLOCK_BLOT?(s.next!=null&&this.splitAfter(s),s.prev!=null&&this.splitAfter(s.prev),s.parent.unwrap(),t=!0):s instanceof Tt?s.unwrap():s.remove())})}formatAt(t,s,n,i){this.children.forEachAt(t,s,(l,o,a)=>{l.formatAt(o,a,n,i)})}insertAt(t,s,n){let[i,l]=this.children.find(t);if(i)i.insertAt(l,s,n);else{let o=n==null?this.scroll.create("text",s):this.scroll.create(s,n);this.appendChild(o)}}insertBefore(t,s){t.parent!=null&&t.parent.children.remove(t);let n=null;this.children.insertBefore(t,s||null),t.parent=this,s!=null&&(n=s.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==n)&&this.domNode.insertBefore(t.domNode,n),t.attach()}length(){return this.children.reduce((t,s)=>t+s.length(),0)}moveChildren(t,s){this.children.forEach(n=>{t.insertBefore(n,s)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){let s=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(s)}else this.remove()}path(t,s=!1){let[n,i]=this.children.find(t,s),l=[[this,t]];return n instanceof Tt?l.concat(n.path(i,s)):(n!=null&&l.push([n,i]),l)}removeChild(t){this.children.remove(t)}replaceWith(t,s){let n=typeof t=="string"?this.scroll.create(t,s):t;return n instanceof Tt&&this.moveChildren(n),super.replaceWith(n)}split(t,s=!1){if(!s){if(t===0)return this;if(t===this.length())return this.next}let n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(t,this.length(),(i,l,o)=>{let a=i.split(l,s);a!=null&&n.appendChild(a)}),n}splitAfter(t){let s=this.clone();for(;t.next!=null;)s.appendChild(t.next);return this.parent&&this.parent.insertBefore(s,this.next||void 0),s}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,s){let n=[],i=[];t.forEach(l=>{l.target===this.domNode&&l.type==="childList"&&(n.push(...l.addedNodes),i.push(...l.removedNodes))}),i.forEach(l=>{if(l.parentNode!=null&&l.tagName!=="IFRAME"&&document.body.compareDocumentPosition(l)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;let o=this.scroll.find(l);o!=null&&(o.domNode.parentNode==null||o.domNode.parentNode===this.domNode)&&o.detach()}),n.filter(l=>l.parentNode===this.domNode&&l!==this.uiNode).sort((l,o)=>l===o?0:l.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(l=>{let o=null;l.nextSibling!=null&&(o=this.scroll.find(l.nextSibling));let a=En(l,this.scroll);(a.next!==o||a.next==null)&&(a.parent!=null&&a.parent.removeChild(this),this.insertBefore(a,o||void 0))}),this.enforceAllowedChildren()}};return r.uiClass="",r})(),bo=go,W=bo;function yo(r,e){if(Object.keys(r).length!==Object.keys(e).length)return!1;for(let t in r)if(r[t]!==e[t])return!1;return!0}var Xt=class Yt extends W{static create(e){return super.create(e)}static formats(e,t){let s=t.query(Yt.blotName);if(!(s!=null&&e.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(e,t){super(e,t),this.attributes=new ge(this.domNode)}format(e,t){if(e===this.statics.blotName&&!t)this.children.forEach(s=>{s instanceof Yt||(s=s.wrap(Yt.blotName,!0)),this.attributes.copy(s)}),this.unwrap();else{let s=this.scroll.query(e,p.INLINE);if(s==null)return;s instanceof $?this.attributes.attribute(s,t):t&&(e!==this.statics.blotName||this.formats()[e]!==t)&&this.replaceWith(e,t)}}formats(){let e=this.attributes.values(),t=this.statics.formats(this.domNode,this.scroll);return t!=null&&(e[this.statics.blotName]=t),e}formatAt(e,t,s,n){this.formats()[s]!=null||this.scroll.query(s,p.ATTRIBUTE)?this.isolate(e,t).format(s,n):super.formatAt(e,t,s,n)}optimize(e){super.optimize(e);let t=this.formats();if(Object.keys(t).length===0)return this.unwrap();let s=this.next;s instanceof Yt&&s.prev===this&&yo(t,s.formats())&&(s.moveChildren(this),s.remove())}replaceWith(e,t){let s=super.replaceWith(e,t);return this.attributes.copy(s),s}update(e,t){super.update(e,t),e.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}wrap(e,t){let s=super.wrap(e,t);return s instanceof Yt&&this.attributes.move(s),s}};Xt.allowedChildren=[Xt,B],Xt.blotName="inline",Xt.scope=p.INLINE_BLOT,Xt.tagName="SPAN";var vo=Xt,Fe=vo,Jt=class Hr extends W{static create(e){return super.create(e)}static formats(e,t){let s=t.query(Hr.blotName);if(!(s!=null&&e.tagName===s.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(e,t){super(e,t),this.attributes=new ge(this.domNode)}format(e,t){let s=this.scroll.query(e,p.BLOCK);s!=null&&(s instanceof $?this.attributes.attribute(s,t):e===this.statics.blotName&&!t?this.replaceWith(Hr.blotName):t&&(e!==this.statics.blotName||this.formats()[e]!==t)&&this.replaceWith(e,t))}formats(){let e=this.attributes.values(),t=this.statics.formats(this.domNode,this.scroll);return t!=null&&(e[this.statics.blotName]=t),e}formatAt(e,t,s,n){this.scroll.query(s,p.BLOCK)!=null?this.format(s,n):super.formatAt(e,t,s,n)}insertAt(e,t,s){if(s==null||this.scroll.query(t,p.INLINE)!=null)super.insertAt(e,t,s);else{let n=this.split(e);if(n!=null){let i=this.scroll.create(t,s);n.parent.insertBefore(i,n)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(e,t){let s=super.replaceWith(e,t);return this.attributes.copy(s),s}update(e,t){super.update(e,t),e.some(s=>s.target===this.domNode&&s.type==="attributes")&&this.attributes.build()}};Jt.blotName="block",Jt.scope=p.BLOCK_BLOT,Jt.tagName="P",Jt.allowedChildren=[Fe,Jt,B];var Eo=Jt,Ut=Eo,Fr=class extends W{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(e,t){super.deleteAt(e,t),this.enforceAllowedChildren()}formatAt(e,t,s,n){super.formatAt(e,t,s,n),this.enforceAllowedChildren()}insertAt(e,t,s){super.insertAt(e,t,s),this.enforceAllowedChildren()}optimize(e){super.optimize(e),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};Fr.blotName="container",Fr.scope=p.BLOCK_BLOT;var No=Fr,ee=No,$r=class extends B{static formats(e,t){}format(e,t){super.formatAt(0,this.length(),e,t)}formatAt(e,t,s,n){e===0&&t===this.length()?this.format(s,n):super.formatAt(e,t,s,n)}formats(){return this.statics.formats(this.domNode,this.scroll)}},k=$r,xo={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Ao=100,te=class extends W{constructor(e,t){super(null,t),this.registry=e,this.scroll=this,this.build(),this.observer=new MutationObserver(s=>{this.update(s)}),this.observer.observe(this.domNode,xo),this.attach()}create(e,t){return this.registry.create(this,e,t)}find(e,t=!1){let s=this.registry.find(e,t);return s?s.scroll===this?s:t?this.find(s.scroll.domNode.parentNode,!0):null:null}query(e,t=p.ANY){return this.registry.query(e,t)}register(...e){return this.registry.register(...e)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(e,t){this.update(),e===0&&t===this.length()?this.children.forEach(s=>{s.remove()}):super.deleteAt(e,t)}formatAt(e,t,s,n){this.update(),super.formatAt(e,t,s,n)}insertAt(e,t,s){this.update(),super.insertAt(e,t,s)}optimize(e=[],t={}){super.optimize(t);let s=t.mutationsMap||new WeakMap,n=Array.from(this.observer.takeRecords());for(;n.length>0;)e.push(n.pop());let i=(a,c=!0)=>{a==null||a===this||a.domNode.parentNode!=null&&(s.has(a.domNode)||s.set(a.domNode,[]),c&&i(a.parent))},l=a=>{s.has(a.domNode)&&(a instanceof W&&a.children.forEach(l),s.delete(a.domNode),a.optimize(t))},o=e;for(let a=0;o.length>0;a+=1){if(a>=Ao)throw new Error("[Parchment] Maximum optimize iterations reached");for(o.forEach(c=>{let u=this.find(c.target,!0);u!=null&&(u.domNode===c.target&&(c.type==="childList"?(i(this.find(c.previousSibling,!1)),Array.from(c.addedNodes).forEach(h=>{let d=this.find(h,!1);i(d,!1),d instanceof W&&d.children.forEach(m=>{i(m,!1)})})):c.type==="attributes"&&i(u.prev)),i(u))}),this.children.forEach(l),o=Array.from(this.observer.takeRecords()),n=o.slice();n.length>0;)e.push(n.pop())}}update(e,t={}){e=e||this.observer.takeRecords();let s=new WeakMap;e.map(n=>{let i=this.find(n.target,!0);return i==null?null:s.has(i.domNode)?(s.get(i.domNode).push(n),null):(s.set(i.domNode,[n]),i)}).forEach(n=>{n!=null&&n!==this&&s.has(n.domNode)&&n.update(s.get(n.domNode)||[],t)}),t.mutationsMap=s,s.has(this.domNode)&&super.update(s.get(this.domNode),t),this.optimize(e,t)}};te.blotName="scroll",te.defaultChild=Ut,te.allowedChildren=[Ut,ee],te.scope=p.BLOCK_BLOT,te.tagName="DIV";var wo=te,be=wo,zr=class wn extends B{static create(e){return document.createTextNode(e)}static value(e){return e.data}constructor(e,t){super(e,t),this.text=this.statics.value(this.domNode)}deleteAt(e,t){this.domNode.data=this.text=this.text.slice(0,e)+this.text.slice(e+t)}index(e,t){return this.domNode===e?t:-1}insertAt(e,t,s){s==null?(this.text=this.text.slice(0,e)+t+this.text.slice(e),this.domNode.data=this.text):super.insertAt(e,t,s)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof wn&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(e,t=!1){return[this.domNode,e]}split(e,t=!1){if(!t){if(e===0)return this;if(e===this.length())return this.next}let s=this.scroll.create(this.domNode.splitText(e));return this.parent.insertBefore(s,this.next||void 0),this.text=this.statics.value(this.domNode),s}update(e,t){e.some(s=>s.type==="characterData"&&s.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};zr.blotName="text",zr.scope=p.INLINE_BLOT;var To=zr,re=To;var _t=J(rt(),1);var C=J(rt(),1);var ks=J(rt(),1);var Fu=(()=>{class r extends k{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}return r.blotName="break",r.tagName="BR",r})(),P=Fu;var _=class extends re{},$u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Gt(r){return r.replace(/[&<>"']/g,e=>$u[e])}var zu=(()=>{class r extends Fe{static allowedChildren=[r,P,k,_];static order=["cursor","inline","link","underline","strike","italic","bold","script","code"];static compare(t,s){let n=r.order.indexOf(t),i=r.order.indexOf(s);return n>=0||i>=0?n-i:t===s?0:t<s?-1:1}formatAt(t,s,n,i){if(r.compare(this.statics.blotName,n)<0&&this.scroll.query(n,p.BLOT)){let l=this.isolate(t,s);i&&l.wrap(n,i)}else super.formatAt(t,s,n,i)}optimize(t){if(super.optimize(t),this.parent instanceof r&&r.compare(this.statics.blotName,this.parent.statics.blotName)>0){let s=this.parent.isolate(this.offset(),this.length());this.moveChildren(s),s.wrap(this)}}}return r})(),j=zu;var ol=1,I=(()=>{class r extends Ut{cache={};delta(){return this.cache.delta==null&&(this.cache.delta=Cs(this)),this.cache.delta}deleteAt(t,s){super.deleteAt(t,s),this.cache={}}formatAt(t,s,n,i){s<=0||(this.scroll.query(n,p.BLOCK)?t+s===this.length()&&this.format(n,i):super.formatAt(t,Math.min(s,this.length()-t-1),n,i),this.cache={})}insertAt(t,s,n){if(n!=null){super.insertAt(t,s,n),this.cache={};return}if(s.length===0)return;let i=s.split(`
`),l=i.shift();l.length>0&&(t<this.length()-1||this.children.tail==null?super.insertAt(Math.min(t,this.length()-1),l):this.children.tail.insertAt(this.children.tail.length(),l),this.cache={});let o=this;i.reduce((a,c)=>(o=o.split(a,!0),o.insertAt(0,c),c.length),t+l.length)}insertBefore(t,s){let{head:n}=this.children;super.insertBefore(t,s),n instanceof P&&n.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+ol),this.cache.length}moveChildren(t,s){super.moveChildren(t,s),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(s&&(t===0||t>=this.length()-ol)){let i=this.clone();return t===0?(this.parent.insertBefore(i,this),this):(this.parent.insertBefore(i,this.next),i)}let n=super.split(t,s);return this.cache={},n}}return r.blotName="block",r.tagName="P",r.defaultChild=P,r.allowedChildren=[P,j,k,_],r})(),D=class extends k{attach(){super.attach(),this.attributes=new ge(this.domNode)}delta(){return new ks.default().insert(this.value(),T(T({},this.formats()),this.attributes.values()))}format(e,t){let s=this.scroll.query(e,p.BLOCK_ATTRIBUTE);s!=null&&this.attributes.attribute(s,t)}formatAt(e,t,s,n){this.format(s,n)}insertAt(e,t,s){if(s!=null){super.insertAt(e,t,s);return}let n=t.split(`
`),i=n.pop(),l=n.map(a=>{let c=this.scroll.create(I.blotName);return c.insertAt(0,a),c}),o=this.split(e);l.forEach(a=>{this.parent.insertBefore(a,o)}),i&&this.parent.insertBefore(this.scroll.create("text",i),o)}};D.scope=p.BLOCK_BLOT;function Cs(r){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return r.descendants(B).reduce((t,s)=>s.length()===0?t:t.insert(s.value(),Q(s,{},e)),new ks.default).insert(`
`,Q(r))}function Q(r){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return r==null||("formats"in r&&typeof r.formats=="function"&&(e=T(T({},e),r.formats()),t&&delete e["code-token"]),r.parent==null||r.parent.statics.blotName==="scroll"||r.parent.statics.scope!==r.statics.scope)?e:Q(r.parent,e,t)}var Vu=(()=>{class r extends k{static blotName="cursor";static className="ql-cursor";static tagName="span";static CONTENTS="\uFEFF";static value(){}constructor(t,s,n){super(t,s),this.selection=n,this.textNode=document.createTextNode(r.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,s){if(this.savedLength!==0){super.format(t,s);return}let n=this,i=0;for(;n!=null&&n.statics.scope!==p.BLOCK_BLOT;)i+=n.offset(n.parent),n=n.parent;n!=null&&(this.savedLength=r.CONTENTS.length,n.optimize(),n.formatAt(i,r.CONTENTS.length,t,s),this.savedLength=0)}index(t,s){return t===this.textNode?0:super.index(t,s)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;let t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);let s=this.prev instanceof _?this.prev:null,n=s?s.length():0,i=this.next instanceof _?this.next:null,l=i?i.text:"",{textNode:o}=this,a=o.data.split(r.CONTENTS).join("");o.data=r.CONTENTS;let c;if(s)c=s,(a||i)&&(s.insertAt(s.length(),a+l),i&&i.remove());else if(i)c=i,i.insertAt(0,a);else{let u=document.createTextNode(a);c=this.scroll.create(u),this.parent.insertBefore(c,this)}if(this.remove(),t){let u=(m,g)=>s&&m===s.domNode?g:m===o?n+g-1:i&&m===i.domNode?n+a.length+g:null,h=u(t.start.node,t.start.offset),d=u(t.end.node,t.end.offset);if(h!==null&&d!==null)return{startNode:c.domNode,startOffset:h,endNode:c.domNode,endOffset:d}}return null}update(t,s){if(t.some(n=>n.type==="characterData"&&n.target===this.textNode)){let n=this.restore();n&&(s.range=n)}}optimize(t){super.optimize(t);let{parent:s}=this;for(;s;){if(s.domNode.tagName==="A"){this.savedLength=r.CONTENTS.length,s.isolate(this.offset(s),this.length()).unwrap(),this.savedLength=0;break}s=s.parent}}value(){return""}}return r})(),Ot=Vu;var _s=J(cl(),1);var ke=new WeakMap;var Is=["error","warn","log","info"],Rs="warn";function ul(r){if(Rs&&Is.indexOf(r)<=Is.indexOf(Rs)){for(var e=arguments.length,t=new Array(e>1?e-1:0),s=1;s<e;s++)t[s-1]=arguments[s];console[r](...t)}}function Bs(r){return Is.reduce((e,t)=>(e[t]=ul.bind(console,t,r),e),{})}Bs.level=r=>{Rs=r};ul.level=Bs.level;var st=Bs;var Ms=st("quill:events"),Wu=["selectionchange","mousedown","mouseup","click"];Wu.forEach(r=>{document.addEventListener(r,function(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];Array.from(document.querySelectorAll(".ql-container")).forEach(n=>{let i=ke.get(n);i&&i.emitter&&i.emitter.handleDOM(...t)})})});var Zu=(()=>{class r extends _s.default{static events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"};static sources={API:"api",SILENT:"silent",USER:"user"};constructor(){super(),this.domListeners={},this.on("error",Ms.error)}emit(){for(var t=arguments.length,s=new Array(t),n=0;n<t;n++)s[n]=arguments[n];return Ms.log.call(Ms,...s),super.emit(...s)}handleDOM(t){for(var s=arguments.length,n=new Array(s>1?s-1:0),i=1;i<s;i++)n[i-1]=arguments[i];(this.domListeners[t.type]||[]).forEach(l=>{let{node:o,handler:a}=l;(t.target===o||o.contains(t.target))&&a(t,...n)})}listenDOM(t,s,n){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:s,handler:n})}}return r})(),b=Zu;var Ds=st("quill:selection"),K=class{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=e,this.length=t}},Ps=class{constructor(e,t){this.emitter=t,this.scroll=e,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new K(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,b.sources.USER),1)}),this.emitter.on(b.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;let s=this.getNativeRange();s!=null&&s.start.node!==this.cursor.textNode&&this.emitter.once(b.events.SCROLL_UPDATE,(n,i)=>{try{this.root.contains(s.start.node)&&this.root.contains(s.end.node)&&this.setNativeRange(s.start.node,s.start.offset,s.end.node,s.end.offset);let l=i.some(o=>o.type==="characterData"||o.type==="childList"||o.type==="attributes"&&o.target===this.root);this.update(l?b.sources.SILENT:n)}catch{}})}),this.emitter.on(b.events.SCROLL_OPTIMIZE,(s,n)=>{if(n.range){let{startNode:i,startOffset:l,endNode:o,endOffset:a}=n.range;this.setNativeRange(i,l,o,a),this.update(b.sources.SILENT)}}),this.update(b.sources.SILENT)}handleComposition(){this.emitter.on(b.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(b.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){let e=this.cursor.restore();if(!e)return;setTimeout(()=>{this.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(b.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(e,t){this.scroll.update();let s=this.getNativeRange();if(!(s==null||!s.native.collapsed||this.scroll.query(e,p.BLOCK))){if(s.start.node!==this.cursor.textNode){let n=this.scroll.find(s.start.node,!1);if(n==null)return;if(n instanceof B){let i=n.split(s.start.offset);n.parent.insertBefore(this.cursor,i)}else n.insertBefore(this.cursor,s.start.node);this.cursor.attach()}this.cursor.format(e,t),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=this.scroll.length();e=Math.min(e,s-1),t=Math.min(e+t,s-1)-e;let n,[i,l]=this.scroll.leaf(e);if(i==null)return null;if(t>0&&l===i.length()){let[u]=this.scroll.leaf(e+1);if(u){let[h]=this.scroll.line(e),[d]=this.scroll.line(e+1);h===d&&(i=u,l=0)}}[n,l]=i.position(l,!0);let o=document.createRange();if(t>0)return o.setStart(n,l),[i,l]=this.scroll.leaf(e+t),i==null?null:([n,l]=i.position(l,!0),o.setEnd(n,l),o.getBoundingClientRect());let a="left",c;if(n instanceof Text){if(!n.data.length)return null;l<n.data.length?(o.setStart(n,l),o.setEnd(n,l+1)):(o.setStart(n,l-1),o.setEnd(n,l),a="right"),c=o.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;c=i.domNode.getBoundingClientRect(),l>0&&(a="right")}return{bottom:c.top+c.height,height:c.height,left:c[a],right:c[a],top:c.top,width:0}}getNativeRange(){let e=document.getSelection();if(e==null||e.rangeCount<=0)return null;let t=e.getRangeAt(0);if(t==null)return null;let s=this.normalizeNative(t);return Ds.info("getNativeRange",s),s}getRange(){let e=this.scroll.domNode;if("isConnected"in e&&!e.isConnected)return[null,null];let t=this.getNativeRange();return t==null?[null,null]:[this.normalizedToRange(t),t]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&Us(this.root,document.activeElement)}normalizedToRange(e){let t=[[e.start.node,e.start.offset]];e.native.collapsed||t.push([e.end.node,e.end.offset]);let s=t.map(l=>{let[o,a]=l,c=this.scroll.find(o,!0),u=c.offset(this.scroll);return a===0?u:c instanceof B?u+c.index(o,a):u+c.length()}),n=Math.min(Math.max(...s),this.scroll.length()-1),i=Math.min(n,...s);return new K(i,n-i)}normalizeNative(e){if(!Us(this.root,e.startContainer)||!e.collapsed&&!Us(this.root,e.endContainer))return null;let t={start:{node:e.startContainer,offset:e.startOffset},end:{node:e.endContainer,offset:e.endOffset},native:e};return[t.start,t.end].forEach(s=>{let{node:n,offset:i}=s;for(;!(n instanceof Text)&&n.childNodes.length>0;)if(n.childNodes.length>i)n=n.childNodes[i],i=0;else if(n.childNodes.length===i)n=n.lastChild,n instanceof Text?i=n.data.length:n.childNodes.length>0?i=n.childNodes.length:i=n.childNodes.length+1;else break;s.node=n,s.offset=i}),t}rangeToNative(e){let t=this.scroll.length(),s=(n,i)=>{n=Math.min(t-1,n);let[l,o]=this.scroll.leaf(n);return l?l.position(o,i):[null,-1]};return[...s(e.index,!1),...s(e.index+e.length,!0)]}setNativeRange(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:t,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(Ds.info("setNativeRange",e,t,s,n),e!=null&&(this.root.parentNode==null||e.parentNode==null||s.parentNode==null))return;let l=document.getSelection();if(l!=null)if(e!=null){this.hasFocus()||this.root.focus({preventScroll:!0});let{native:o}=this.getNativeRange()||{};if(o==null||i||e!==o.startContainer||t!==o.startOffset||s!==o.endContainer||n!==o.endOffset){e instanceof Element&&e.tagName==="BR"&&(t=Array.from(e.parentNode.childNodes).indexOf(e),e=e.parentNode),s instanceof Element&&s.tagName==="BR"&&(n=Array.from(s.parentNode.childNodes).indexOf(s),s=s.parentNode);let a=document.createRange();a.setStart(e,t),a.setEnd(s,n),l.removeAllRanges(),l.addRange(a)}}else l.removeAllRanges(),this.root.blur()}setRange(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:b.sources.API;if(typeof t=="string"&&(s=t,t=!1),Ds.info("setRange",e),e!=null){let n=this.rangeToNative(e);this.setNativeRange(...n,t)}else this.setNativeRange(null);this.update(s)}update(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:b.sources.USER,t=this.lastRange,[s,n]=this.getRange();if(this.lastRange=s,this.lastNative=n,this.lastRange!=null&&(this.savedRange=this.lastRange),!Qt(t,this.lastRange)){if(!this.composing&&n!=null&&n.native.collapsed&&n.start.node!==this.cursor.textNode){let l=this.cursor.restore();l&&this.setNativeRange(l.startNode,l.startOffset,l.endNode,l.endOffset)}let i=[b.events.SELECTION_CHANGE,ft(this.lastRange),ft(t),e];this.emitter.emit(b.events.EDITOR_CHANGE,...i),e!==b.sources.SILENT&&this.emitter.emit(...i)}}};function Us(r,e){try{e.parentNode}catch{return!1}return r.contains(e)}var hl=Ps;var Qu=/^[ -~]*$/,Hs=class{constructor(e){this.scroll=e,this.delta=this.getDelta()}applyDelta(e){this.scroll.update();let t=this.scroll.length();this.scroll.batchStart();let s=fl(e),n=new C.default;return Yu(s.ops.slice()).reduce((l,o)=>{let a=C.Op.length(o),c=o.attributes||{},u=!1,h=!1;if(o.insert!=null){if(n.retain(a),typeof o.insert=="string"){let g=o.insert;h=!g.endsWith(`
`)&&(t<=l||!!this.scroll.descendant(D,l)[0]),this.scroll.insertAt(l,g);let[v,E]=this.scroll.line(l),y=tt({},Q(v));if(v instanceof I){let[N]=v.descendant(B,E);N&&(y=tt(y,Q(N)))}c=C.AttributeMap.diff(y,c)||{}}else if(typeof o.insert=="object"){let g=Object.keys(o.insert)[0];if(g==null)return l;let v=this.scroll.query(g,p.INLINE)!=null;if(v)(t<=l||this.scroll.descendant(D,l)[0])&&(h=!0);else if(l>0){let[E,y]=this.scroll.descendant(B,l-1);E instanceof _?E.value()[y]!==`
`&&(u=!0):E instanceof k&&E.statics.scope===p.INLINE_BLOT&&(u=!0)}if(this.scroll.insertAt(l,g,o.insert[g]),v){let[E]=this.scroll.descendant(B,l);if(E){let y=tt({},Q(E));c=C.AttributeMap.diff(y,c)||{}}}}t+=a}else if(n.push(o),o.retain!==null&&typeof o.retain=="object"){let g=Object.keys(o.retain)[0];if(g==null)return l;this.scroll.updateEmbedAt(l,g,o.retain[g])}Object.keys(c).forEach(g=>{this.scroll.formatAt(l,a,g,c[g])});let d=u?1:0,m=h?1:0;return t+=d+m,n.retain(d),n.delete(m),l+a+d+m},0),n.reduce((l,o)=>typeof o.delete=="number"?(this.scroll.deleteAt(l,o.delete),l):l+C.Op.length(o),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(s)}deleteText(e,t){return this.scroll.deleteAt(e,t),this.update(new C.default().retain(e).delete(t))}formatLine(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(s).forEach(i=>{this.scroll.lines(e,Math.max(t,1)).forEach(l=>{l.format(i,s[i])})}),this.scroll.optimize();let n=new C.default().retain(e).retain(t,ft(s));return this.update(n)}formatText(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(s).forEach(i=>{this.scroll.formatAt(e,t,i,s[i])});let n=new C.default().retain(e).retain(t,ft(s));return this.update(n)}getContents(e,t){return this.delta.slice(e,e+t)}getDelta(){return this.scroll.lines().reduce((e,t)=>e.concat(t.delta()),new C.default)}getFormat(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=[],n=[];t===0?this.scroll.path(e).forEach(o=>{let[a]=o;a instanceof I?s.push(a):a instanceof B&&n.push(a)}):(s=this.scroll.lines(e,t),n=this.scroll.descendants(B,e,t));let[i,l]=[s,n].map(o=>{let a=o.shift();if(a==null)return{};let c=Q(a);for(;Object.keys(c).length>0;){let u=o.shift();if(u==null)return c;c=Xu(Q(u),c)}return c});return T(T({},i),l)}getHTML(e,t){let[s,n]=this.scroll.line(e);if(s){let i=s.length();return s.length()>=n+t&&!(n===0&&t===i)?Ce(s,n,t,!0):Ce(this.scroll,e,t,!0)}return""}getText(e,t){return this.getContents(e,t).filter(s=>typeof s.insert=="string").map(s=>s.insert).join("")}insertContents(e,t){let s=fl(t),n=new C.default().retain(e).concat(s);return this.scroll.insertContents(e,s),this.update(n)}insertEmbed(e,t,s){return this.scroll.insertAt(e,t,s),this.update(new C.default().retain(e).insert({[t]:s}))}insertText(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return t=t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(e,t),Object.keys(s).forEach(n=>{this.scroll.formatAt(e,t.length,n,s[n])}),this.update(new C.default().retain(e).insert(t,ft(s)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;let e=this.scroll.children.head;if(e?.statics.blotName!==I.blotName)return!1;let t=e;return t.children.length>1?!1:t.children.head instanceof P}removeFormat(e,t){let s=this.getText(e,t),[n,i]=this.scroll.line(e+t),l=0,o=new C.default;n!=null&&(l=n.length()-i,o=n.delta().slice(i,i+l-1).insert(`
`));let c=this.getContents(e,t+l).diff(new C.default().insert(s).concat(o)),u=new C.default().retain(e).concat(c);return this.applyDelta(u)}update(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,n=this.delta;if(t.length===1&&t[0].type==="characterData"&&t[0].target.data.match(Qu)&&this.scroll.find(t[0].target)){let i=this.scroll.find(t[0].target),l=Q(i),o=i.offset(this.scroll),a=t[0].oldValue.replace(Ot.CONTENTS,""),c=new C.default().insert(a),u=new C.default().insert(i.value()),h=s&&{oldRange:dl(s.oldRange,-o),newRange:dl(s.newRange,-o)};e=new C.default().retain(o).concat(c.diff(u,h)).reduce((m,g)=>g.insert?m.insert(g.insert,l):m.push(g),new C.default),this.delta=n.compose(e)}else this.delta=this.getDelta(),(!e||!Qt(n.compose(e),this.delta))&&(e=n.diff(this.delta,s));return e}};function ue(r,e,t){if(r.length===0){let[m]=js(t.pop());return e<=0?`</li></${m}>`:`</li></${m}>${ue([],e-1,t)}`}let[{child:s,offset:n,length:i,indent:l,type:o},...a]=r,[c,u]=js(o);if(l>e)return t.push(o),l===e+1?`<${c}><li${u}>${Ce(s,n,i)}${ue(a,l,t)}`:`<${c}><li>${ue(r,e+1,t)}`;let h=t[t.length-1];if(l===e&&o===h)return`</li><li${u}>${Ce(s,n,i)}${ue(a,l,t)}`;let[d]=js(t.pop());return`</li></${d}>${ue(r,e-1,t)}`}function Ce(r,e,t){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in r&&typeof r.html=="function")return r.html(e,t);if(r instanceof _)return Gt(r.value().slice(e,e+t)).replaceAll(" ","&nbsp;");if(r instanceof W){if(r.statics.blotName==="list-container"){let c=[];return r.children.forEachAt(e,t,(u,h,d)=>{let m="formats"in u&&typeof u.formats=="function"?u.formats():{};c.push({child:u,offset:h,length:d,indent:m.indent||0,type:m.list})}),ue(c,-1,[])}let n=[];if(r.children.forEachAt(e,t,(c,u,h)=>{n.push(Ce(c,u,h))}),s||r.statics.blotName==="list")return n.join("");let{outerHTML:i,innerHTML:l}=r.domNode,[o,a]=i.split(`>${l}<`);return o==="<table"?`<table style="border: 1px solid #000;">${n.join("")}<${a}`:`${o}>${n.join("")}<${a}`}return r.domNode instanceof Element?r.domNode.outerHTML:""}function Xu(r,e){return Object.keys(e).reduce((t,s)=>{if(r[s]==null)return t;let n=e[s];return n===r[s]?t[s]=n:Array.isArray(n)?n.indexOf(r[s])<0?t[s]=n.concat([r[s]]):t[s]=n:t[s]=[n,r[s]],t},{})}function js(r){let e=r==="ordered"?"ol":"ul";switch(r){case"checked":return[e,' data-list="checked"'];case"unchecked":return[e,' data-list="unchecked"'];default:return[e,""]}}function fl(r){return r.reduce((e,t)=>{if(typeof t.insert=="string"){let s=t.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return e.insert(s,t.attributes)}return e.push(t)},new C.default)}function dl(r,e){let{index:t,length:s}=r;return new K(t+e,s)}function Yu(r){let e=[];return r.forEach(t=>{typeof t.insert=="string"?t.insert.split(`
`).forEach((n,i)=>{i&&e.push({insert:`
`,attributes:t.attributes}),n&&e.push({insert:n,attributes:t.attributes})}):e.push(t)}),e}var ml=Hs;var Ju=(()=>{class r{static DEFAULTS={};constructor(t){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=s}}return r})(),O=Ju;var dr="\uFEFF",Fs=class extends k{constructor(e,t){super(e,t),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(s=>{this.contentNode.appendChild(s)}),this.leftGuard=document.createTextNode(dr),this.rightGuard=document.createTextNode(dr),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(e,t){return e===this.leftGuard?0:e===this.rightGuard?1:super.index(e,t)}restore(e){let t=null,s,n=e.data.split(dr).join("");if(e===this.leftGuard)if(this.prev instanceof _){let i=this.prev.length();this.prev.insertAt(i,n),t={startNode:this.prev.domNode,startOffset:i+n.length}}else s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this),t={startNode:s,startOffset:n.length};else e===this.rightGuard&&(this.next instanceof _?(this.next.insertAt(0,n),t={startNode:this.next.domNode,startOffset:n.length}):(s=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(s),this.next),t={startNode:s,startOffset:n.length}));return e.data=dr,t}update(e,t){e.forEach(s=>{if(s.type==="characterData"&&(s.target===this.leftGuard||s.target===this.rightGuard)){let n=this.restore(s.target);n&&(t.range=n)}})}},he=Fs;var $s=class{isComposing=!1;constructor(e,t){this.scroll=e,this.emitter=t,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",e=>{this.isComposing||this.handleCompositionStart(e)}),this.scroll.domNode.addEventListener("compositionend",e=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(e)})})}handleCompositionStart(e){let t=e.target instanceof Node?this.scroll.find(e.target,!0):null;t&&!(t instanceof he)&&(this.emitter.emit(b.events.COMPOSITION_BEFORE_START,e),this.scroll.batchStart(),this.emitter.emit(b.events.COMPOSITION_START,e),this.isComposing=!0)}handleCompositionEnd(e){this.emitter.emit(b.events.COMPOSITION_BEFORE_END,e),this.scroll.batchEnd(),this.emitter.emit(b.events.COMPOSITION_END,e),this.isComposing=!1}},pl=$s;var th=(()=>{class r{static DEFAULTS={modules:{}};static themes={default:r};modules={};constructor(t,s){this.quill=t,this.options=s}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){let s=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new s(this.quill,this.options.modules[t]||{}),this.modules[t]}}return r})(),fe=th;var eh=r=>r.parentElement||r.getRootNode().host||null,rh=r=>{let e=r.getBoundingClientRect(),t="offsetWidth"in r&&Math.abs(e.width)/r.offsetWidth||1,s="offsetHeight"in r&&Math.abs(e.height)/r.offsetHeight||1;return{top:e.top,right:e.left+r.clientWidth*t,bottom:e.top+r.clientHeight*s,left:e.left}},mr=r=>{let e=parseInt(r,10);return Number.isNaN(e)?0:e},gl=(r,e,t,s,n,i)=>r<t&&e>s?0:r<t?-(t-r+n):e>s?e-r>s-t?r+n-t:e-s+i:0,sh=(r,e)=>{let t=r.ownerDocument,s=e,n=r;for(;n;){let i=n===t.body,l=i?{top:0,right:window.visualViewport?.width??t.documentElement.clientWidth,bottom:window.visualViewport?.height??t.documentElement.clientHeight,left:0}:rh(n),o=getComputedStyle(n),a=gl(s.left,s.right,l.left,l.right,mr(o.scrollPaddingLeft),mr(o.scrollPaddingRight)),c=gl(s.top,s.bottom,l.top,l.bottom,mr(o.scrollPaddingTop),mr(o.scrollPaddingBottom));if(a||c)if(i)t.defaultView?.scrollBy(a,c);else{let{scrollLeft:u,scrollTop:h}=n;c&&(n.scrollTop+=c),a&&(n.scrollLeft+=a);let d=n.scrollLeft-u,m=n.scrollTop-h;s={left:s.left-d,top:s.top-m,right:s.right-d,bottom:s.bottom-m}}n=i||o.position==="fixed"?null:eh(n)}},bl=sh;var nh=100,ih=["block","break","cursor","inline","scroll","text"],lh=(r,e,t)=>{let s=new qt;return ih.forEach(n=>{let i=e.query(n);i&&s.register(i)}),r.forEach(n=>{let i=e.query(n);i||t.error(`Cannot register "${n}" specified in "formats" config. Are you sure it was registered?`);let l=0;for(;i;)if(s.register(i),i="blotName"in i?i.requiredContainer??null:null,l+=1,l>nh){t.error(`Cycle detected in registering blot requiredContainer: "${n}"`);break}}),s},yl=lh;var de=st("quill"),pr=new qt;W.uiClass="ql-ui";var f=class r{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:pr,theme:"default"};static events=b.events;static sources=b.sources;static version="2.0.3";static imports={delta:_t.default,parchment:ye,"core/module":O,"core/theme":fe};static debug(e){e===!0&&(e="log"),st.level(e)}static find(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return ke.get(e)||pr.find(e,t)}static import(e){return this.imports[e]==null&&de.error(`Cannot import ${e}. Are you sure it was registered?`),this.imports[e]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){let e=arguments.length<=0?void 0:arguments[0],t=!!(!(arguments.length<=1)&&arguments[1]),s="attrName"in e?e.attrName:e.blotName;typeof s=="string"?this.register(`formats/${s}`,e,t):Object.keys(e).forEach(n=>{this.register(n,e[n],t)})}else{let e=arguments.length<=0?void 0:arguments[0],t=arguments.length<=1?void 0:arguments[1],s=!!(!(arguments.length<=2)&&arguments[2]);this.imports[e]!=null&&!s&&de.warn(`Overwriting ${e} with`,t),this.imports[e]=t,(e.startsWith("blots/")||e.startsWith("formats/"))&&t&&typeof t!="boolean"&&t.blotName!=="abstract"&&pr.register(t),typeof t.register=="function"&&t.register(pr)}}constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=oh(e,t),this.container=this.options.container,this.container==null){de.error("Invalid Quill container",e);return}this.options.debug&&r.debug(this.options.debug);let s=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",ke.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new b;let n=be.blotName,i=this.options.registry.query(n);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${n}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new ml(this.scroll),this.selection=new hl(this.scroll,this.emitter),this.composition=new pl(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(b.events.EDITOR_CHANGE,l=>{l===b.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(b.events.SCROLL_UPDATE,(l,o)=>{let a=this.selection.lastRange,[c]=this.selection.getRange(),u=a&&c?{oldRange:a,newRange:c}:void 0;at.call(this,()=>this.editor.update(null,o,u),l)}),this.emitter.on(b.events.SCROLL_EMBED_UPDATE,(l,o)=>{let a=this.selection.lastRange,[c]=this.selection.getRange(),u=a&&c?{oldRange:a,newRange:c}:void 0;at.call(this,()=>{let h=new _t.default().retain(l.offset(this)).retain({[l.statics.blotName]:o});return this.editor.update(h,[],u)},r.sources.USER)}),s){let l=this.clipboard.convert({html:`${s}<p><br></p>`,text:`
`});this.setContents(l)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof e=="string"){let s=e;e=document.createElement("div"),e.classList.add(s)}return this.container.insertBefore(e,t),e}blur(){this.selection.setRange(null)}deleteText(e,t,s){return[e,t,,s]=Et(e,t,s),at.call(this,()=>this.editor.deleteText(e,t),s,e,-1*t)}disable(){this.enable(!1)}editReadOnly(e){this.allowReadOnlyEdits=!0;let t=e();return this.allowReadOnlyEdits=!1,t}enable(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(e),this.container.classList.toggle("ql-disabled",!e)}focus(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),e.preventScroll||this.scrollSelectionIntoView()}format(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:b.sources.API;return at.call(this,()=>{let n=this.getSelection(!0),i=new _t.default;if(n==null)return i;if(this.scroll.query(e,p.BLOCK))i=this.editor.formatLine(n.index,n.length,{[e]:t});else{if(n.length===0)return this.selection.format(e,t),i;i=this.editor.formatText(n.index,n.length,{[e]:t})}return this.setSelection(n,b.sources.SILENT),i},s)}formatLine(e,t,s,n,i){let l;return[e,t,l,i]=Et(e,t,s,n,i),at.call(this,()=>this.editor.formatLine(e,t,l),i,e,0)}formatText(e,t,s,n,i){let l;return[e,t,l,i]=Et(e,t,s,n,i),at.call(this,()=>this.editor.formatText(e,t,l),i,e,0)}getBounds(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=null;if(typeof e=="number"?s=this.selection.getBounds(e,t):s=this.selection.getBounds(e.index,e.length),!s)return null;let n=this.container.getBoundingClientRect();return{bottom:s.bottom-n.top,height:s.height,left:s.left-n.left,right:s.right-n.left,top:s.top-n.top,width:s.width}}getContents(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-e;return[e,t]=Et(e,t),this.editor.getContents(e,t)}getFormat(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof e=="number"?this.editor.getFormat(e,t):this.editor.getFormat(e.index,e.length)}getIndex(e){return e.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(e){return this.scroll.leaf(e)}getLine(e){return this.scroll.line(e)}getLines(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof e!="number"?this.scroll.lines(e.index,e.length):this.scroll.lines(e,t)}getModule(e){return this.theme.modules[e]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;return typeof e=="number"&&(t=t??this.getLength()-e),[e,t]=Et(e,t),this.editor.getHTML(e,t)}getText(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;return typeof e=="number"&&(t=t??this.getLength()-e),[e,t]=Et(e,t),this.editor.getText(e,t)}hasFocus(){return this.selection.hasFocus()}insertEmbed(e,t,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:r.sources.API;return at.call(this,()=>this.editor.insertEmbed(e,t,s),n,e)}insertText(e,t,s,n,i){let l;return[e,,l,i]=Et(e,0,s,n,i),at.call(this,()=>this.editor.insertText(e,t,l),i,e,t.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(e,t,s){return[e,t,,s]=Et(e,t,s),at.call(this,()=>this.editor.removeFormat(e,t),s,e)}scrollRectIntoView(e){bl(this.root,e)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){let e=this.selection.lastRange,t=e&&this.selection.getBounds(e.index,e.length);t&&this.scrollRectIntoView(t)}setContents(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:b.sources.API;return at.call(this,()=>{e=new _t.default(e);let s=this.getLength(),n=this.editor.deleteText(0,s),i=this.editor.insertContents(0,e),l=this.editor.deleteText(this.getLength()-1,1);return n.compose(i).compose(l)},t)}setSelection(e,t,s){e==null?this.selection.setRange(null,t||r.sources.API):([e,t,,s]=Et(e,t,s),this.selection.setRange(new K(Math.max(0,e),t),s),s!==b.sources.SILENT&&this.scrollSelectionIntoView())}setText(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:b.sources.API,s=new _t.default().insert(e);return this.setContents(s,t)}update(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:b.sources.USER,t=this.scroll.update(e);return this.selection.update(e),t}updateContents(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:b.sources.API;return at.call(this,()=>(e=new _t.default(e),this.editor.applyDelta(e)),t,!0)}};function vl(r){return typeof r=="string"?document.querySelector(r):r}function zs(r){return Object.entries(r??{}).reduce((e,t)=>{let[s,n]=t;return At(T({},e),{[s]:n===!0?{}:n})},{})}function El(r){return Object.fromEntries(Object.entries(r).filter(e=>e[1]!==void 0))}function oh(r,e){let t=vl(r);if(!t)throw new Error("Invalid Quill container");let n=!e.theme||e.theme===f.DEFAULTS.theme?fe:f.import(`themes/${e.theme}`);if(!n)throw new Error(`Invalid theme ${e.theme}. Did you register it?`);let m=f.DEFAULTS,{modules:i}=m,l=Rr(m,["modules"]),g=n.DEFAULTS,{modules:o}=g,a=Rr(g,["modules"]),c=zs(e.modules);c!=null&&c.toolbar&&c.toolbar.constructor!==Object&&(c=At(T({},c),{toolbar:{container:c.toolbar}}));let u=tt({},zs(i),zs(o),c),h=T(T(T({},l),El(a)),El(e)),d=e.registry;return d?e.formats&&de.warn('Ignoring "formats" option because "registry" is specified'):d=e.formats?yl(e.formats,h.registry,de):h.registry,At(T({},h),{registry:d,container:t,theme:n,modules:Object.entries(u).reduce((v,E)=>{let[y,N]=E;if(!N)return v;let x=f.import(`modules/${y}`);return x==null?(de.error(`Cannot load ${y} module. Are you sure you registered it?`),v):At(T({},v),{[y]:tt({},x.DEFAULTS||{},N)})},{}),bounds:vl(h.bounds)})}function at(r,e,t,s){if(!this.isEnabled()&&e===b.sources.USER&&!this.allowReadOnlyEdits)return new _t.default;let n=t==null?null:this.getSelection(),i=this.editor.delta,l=r();if(n!=null&&(t===!0&&(t=n.index),s==null?n=Nl(n,l,e):s!==0&&(n=Nl(n,t,s,e)),this.setSelection(n,b.sources.SILENT)),l.length()>0){let o=[b.events.TEXT_CHANGE,l,i,e];this.emitter.emit(b.events.EDITOR_CHANGE,...o),e!==b.sources.SILENT&&this.emitter.emit(...o)}return l}function Et(r,e,t,s,n){let i={};return typeof r.index=="number"&&typeof r.length=="number"?typeof e!="number"?(n=s,s=t,t=e,e=r.length,r=r.index):(e=r.length,r=r.index):typeof e!="number"&&(n=s,s=t,t=e,e=0),typeof t=="object"?(i=t,n=s):typeof t=="string"&&(s!=null?i[t]=s:n=t),n=n||b.sources.API,[r,e,i,n]}function Nl(r,e,t,s){let n=typeof t=="number"?t:0;if(r==null)return null;let i,l;return e&&typeof e.transformPosition=="function"?[i,l]=[r.index,r.index+r.length].map(o=>e.transformPosition(o,s!==b.sources.USER)):[i,l]=[r.index,r.index+r.length].map(o=>o<e||o===e&&s===b.sources.USER?o:n>=0?o+n:Math.max(e,o+n)),new K(i,l-i)}var Vs=class extends ee{},it=Vs;var lt=J(rt(),1);function xl(r){return r instanceof I||r instanceof D}function Al(r){return typeof r.updateContent=="function"}var ah=(()=>{class r extends be{static blotName="scroll";static className="ql-editor";static tagName="DIV";static defaultChild=I;static allowedChildren=[I,D,it];constructor(t,s,n){let{emitter:i}=n;super(t,s),this.emitter=i,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",l=>this.handleDragStart(l))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;let t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(b.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(b.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,s){this.emitter.emit(b.events.SCROLL_EMBED_UPDATE,t,s)}deleteAt(t,s){let[n,i]=this.line(t),[l]=this.line(t+s);if(super.deleteAt(t,s),l!=null&&n!==l&&i>0){if(n instanceof D||l instanceof D){this.optimize();return}let o=l.children.head instanceof P?null:l.children.head;n.moveChildren(l,o),n.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,s,n,i){super.formatAt(t,s,n,i),this.optimize()}insertAt(t,s,n){if(t>=this.length())if(n==null||this.scroll.query(s,p.BLOCK)==null){let i=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(i),n==null&&s.endsWith(`
`)?i.insertAt(0,s.slice(0,-1),n):i.insertAt(0,s,n)}else{let i=this.scroll.create(s,n);this.appendChild(i)}else super.insertAt(t,s,n);this.optimize()}insertBefore(t,s){if(t.statics.scope===p.INLINE_BLOT){let n=this.scroll.create(this.statics.defaultChild.blotName);n.appendChild(t),super.insertBefore(n,s)}else super.insertBefore(t,s)}insertContents(t,s){let n=this.deltaToRenderBlocks(s.concat(new lt.default().insert(`
`))),i=n.pop();if(i==null)return;this.batchStart();let l=n.shift();if(l){let c=l.type==="block"&&(l.delta.length()===0||!this.descendant(D,t)[0]&&t<this.length()),u=l.type==="block"?l.delta:new lt.default().insert({[l.key]:l.value});Ks(this,t,u);let h=l.type==="block"?1:0,d=t+u.length()+h;c&&this.insertAt(d-1,`
`);let m=Q(this.line(t)[0]),g=lt.AttributeMap.diff(m,l.attributes)||{};Object.keys(g).forEach(v=>{this.formatAt(d-1,1,v,g[v])}),t=d}let[o,a]=this.children.find(t);if(n.length&&(o&&(o=o.split(a),a=0),n.forEach(c=>{if(c.type==="block"){let u=this.createBlock(c.attributes,o||void 0);Ks(u,0,c.delta)}else{let u=this.create(c.key,c.value);this.insertBefore(u,o||void 0),Object.keys(c.attributes).forEach(h=>{u.format(h,c.attributes[h])})}})),i.type==="block"&&i.delta.length()){let c=o?o.offset(o.scroll)+a:this.length();Ks(this,c,i.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){let s=this.path(t).pop();if(!s)return[null,-1];let[n,i]=s;return n instanceof B?[n,i]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(xl,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,n=(i,l,o)=>{let a=[],c=o;return i.children.forEachAt(l,o,(u,h,d)=>{xl(u)?a.push(u):u instanceof ee&&(a=a.concat(n(u,h,c))),c-=d}),a};return n(this,t,s)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,s),t.length>0&&this.emitter.emit(b.events.SCROLL_OPTIMIZE,t,s))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let s=b.sources.USER;typeof t=="string"&&(s=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(n=>{let{target:i}=n,l=this.find(i,!0);return l&&!Al(l)}),t.length>0&&this.emitter.emit(b.events.SCROLL_BEFORE_UPDATE,s,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(b.events.SCROLL_UPDATE,s,t)}updateEmbedAt(t,s,n){let[i]=this.descendant(l=>l instanceof D,t);i&&i.statics.blotName===s&&Al(i)&&i.updateContent(n)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){let s=[],n=new lt.default;return t.forEach(i=>{let l=i?.insert;if(l)if(typeof l=="string"){let o=l.split(`
`);o.slice(0,-1).forEach(c=>{n.insert(c,i.attributes),s.push({type:"block",delta:n,attributes:i.attributes??{}}),n=new lt.default});let a=o[o.length-1];a&&n.insert(a,i.attributes)}else{let o=Object.keys(l)[0];if(!o)return;this.query(o,p.INLINE)?n.push(i):(n.length()&&s.push({type:"block",delta:n,attributes:{}}),n=new lt.default,s.push({type:"blockEmbed",key:o,value:l[o],attributes:i.attributes??{}}))}}),n.length()&&s.push({type:"block",delta:n,attributes:{}}),s}createBlock(t,s){let n,i={};Object.entries(t).forEach(a=>{let[c,u]=a;this.query(c,p.BLOCK&p.BLOT)!=null?n=c:i[c]=u});let l=this.create(n||this.statics.defaultChild.blotName,n?t[n]:void 0);this.insertBefore(l,s||void 0);let o=l.length();return Object.entries(i).forEach(a=>{let[c,u]=a;l.formatAt(0,o,c,u)}),l}}return r})();function Ks(r,e,t){t.reduce((s,n)=>{let i=lt.Op.length(n),l=n.attributes||{};if(n.insert!=null){if(typeof n.insert=="string"){let o=n.insert;r.insertAt(s,o);let[a]=r.descendant(B,s),c=Q(a);l=lt.AttributeMap.diff(c,l)||{}}else if(typeof n.insert=="object"){let o=Object.keys(n.insert)[0];if(o==null)return s;if(r.insertAt(s,o,n.insert[o]),r.scroll.query(o,p.INLINE)!=null){let[c]=r.descendant(B,s),u=Q(c);l=lt.AttributeMap.diff(u,l)||{}}}}return Object.keys(l).forEach(o=>{r.formatAt(s,i,o,l[o])}),s+i},e)}var wl=ah;var X=J(rt(),1);var Gs={scope:p.BLOCK,whitelist:["right","center","justify"]},Tl=new $("align","align",Gs),Ws=new R("align","ql-align",Gs),gr=new Z("align","text-align",Gs);var Oe=class extends Z{value(e){let t=super.value(e);return t.startsWith("rgb(")?(t=t.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${t.split(",").map(n=>`00${parseInt(n,10).toString(16)}`.slice(-2)).join("")}`):t}},Ll=new R("color","ql-color",{scope:p.INLINE}),_e=new Oe("color","color",{scope:p.INLINE});var ql=new R("background","ql-bg",{scope:p.INLINE}),Ie=new Oe("background","background-color",{scope:p.INLINE});var ut=class extends it{static create(e){let t=super.create(e);return t.setAttribute("spellcheck","false"),t}code(e,t){return this.children.map(s=>s.length()<=1?"":s.domNode.innerText).join(`
`).slice(e,e+t)}html(e,t){return`<pre>
${Gt(this.code(e,t))}
</pre>`}},U=(()=>{class r extends I{static TAB="  ";static register(){f.register(ut)}}return r})(),Sl=(()=>{class r extends j{}return r.blotName="code",r.tagName="CODE",r})();U.blotName="code-block";U.className="ql-code-block";U.tagName="DIV";ut.blotName="code-block-container";ut.className="ql-code-block-container";ut.tagName="DIV";ut.allowedChildren=[U];U.allowedChildren=[_,P,Ot];U.requiredContainer=ut;var Zs={scope:p.BLOCK,whitelist:["rtl"]},br=new $("direction","dir",Zs),Qs=new R("direction","ql-direction",Zs),yr=new Z("direction","direction",Zs);var kl={scope:p.INLINE,whitelist:["serif","monospace"]},Ys=new R("font","ql-font",kl),Xs=class extends Z{value(e){return super.value(e).replace(/["']/g,"")}},vr=new Xs("font","font-family",kl);var Js=new R("size","ql-size",{scope:p.INLINE,whitelist:["small","large","huge"]}),Er=new Z("size","font-size",{scope:p.INLINE,whitelist:["10px","18px","32px"]});var F=J(rt(),1);var ch=st("quill:keyboard"),uh=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",Re=class r extends O{static match(e,t){return["altKey","ctrlKey","metaKey","shiftKey"].some(s=>!!t[s]!==e[s]&&t[s]!==null)?!1:t.key===e.key||t.key===e.which}constructor(e,t){super(e,t),this.bindings={},Object.keys(this.options.bindings).forEach(s=>{this.options.bindings[s]&&this.addBinding(this.options.bindings[s])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=fh(e);if(n==null){ch.warn("Attempted to add invalid keyboard binding",n);return}typeof t=="function"&&(t={handler:t}),typeof s=="function"&&(s={handler:s}),(Array.isArray(n.key)?n.key:[n.key]).forEach(l=>{let o=T(T(At(T({},n),{key:l}),t),s);this.bindings[o.key]=this.bindings[o.key]||[],this.bindings[o.key].push(o)})}listen(){this.quill.root.addEventListener("keydown",e=>{if(e.defaultPrevented||e.isComposing||e.keyCode===229&&(e.key==="Enter"||e.key==="Backspace"))return;let n=(this.bindings[e.key]||[]).concat(this.bindings[e.which]||[]).filter(y=>r.match(e,y));if(n.length===0)return;let i=f.find(e.target,!0);if(i&&i.scroll!==this.quill.scroll)return;let l=this.quill.getSelection();if(l==null||!this.quill.hasFocus())return;let[o,a]=this.quill.getLine(l.index),[c,u]=this.quill.getLeaf(l.index),[h,d]=l.length===0?[c,u]:this.quill.getLeaf(l.index+l.length),m=c instanceof re?c.value().slice(0,u):"",g=h instanceof re?h.value().slice(d):"",v={collapsed:l.length===0,empty:l.length===0&&o.length()<=1,format:this.quill.getFormat(l),line:o,offset:a,prefix:m,suffix:g,event:e};n.some(y=>{if(y.collapsed!=null&&y.collapsed!==v.collapsed||y.empty!=null&&y.empty!==v.empty||y.offset!=null&&y.offset!==v.offset)return!1;if(Array.isArray(y.format)){if(y.format.every(N=>v.format[N]==null))return!1}else if(typeof y.format=="object"&&!Object.keys(y.format).every(N=>y.format[N]===!0?v.format[N]!=null:y.format[N]===!1?v.format[N]==null:Qt(y.format[N],v.format[N])))return!1;return y.prefix!=null&&!y.prefix.test(v.prefix)||y.suffix!=null&&!y.suffix.test(v.suffix)?!1:y.handler.call(this,l,v,y)!==!0})&&e.preventDefault()})}handleBackspace(e,t){let s=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(t.prefix)?2:1;if(e.index===0||this.quill.getLength()<=1)return;let n={},[i]=this.quill.getLine(e.index),l=new F.default().retain(e.index-s).delete(s);if(t.offset===0){let[o]=this.quill.getLine(e.index-1);if(o&&!(o.statics.blotName==="block"&&o.length()<=1)){let c=i.formats(),u=this.quill.getFormat(e.index-1,1);if(n=F.AttributeMap.diff(c,u)||{},Object.keys(n).length>0){let h=new F.default().retain(e.index+i.length()-2).retain(1,n);l=l.compose(h)}}}this.quill.updateContents(l,f.sources.USER),this.quill.focus()}handleDelete(e,t){let s=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(t.suffix)?2:1;if(e.index>=this.quill.getLength()-s)return;let n={},[i]=this.quill.getLine(e.index),l=new F.default().retain(e.index).delete(s);if(t.offset>=i.length()-1){let[o]=this.quill.getLine(e.index+1);if(o){let a=i.formats(),c=this.quill.getFormat(e.index,1);n=F.AttributeMap.diff(a,c)||{},Object.keys(n).length>0&&(l=l.retain(o.length()-1).retain(1,n))}}this.quill.updateContents(l,f.sources.USER),this.quill.focus()}handleDeleteRange(e){Be({range:e,quill:this.quill}),this.quill.focus()}handleEnter(e,t){let s=Object.keys(t.format).reduce((i,l)=>(this.quill.scroll.query(l,p.BLOCK)&&!Array.isArray(t.format[l])&&(i[l]=t.format[l]),i),{}),n=new F.default().retain(e.index).delete(e.length).insert(`
`,s);this.quill.updateContents(n,f.sources.USER),this.quill.setSelection(e.index+1,f.sources.SILENT),this.quill.focus()}},hh={bindings:{bold:tn("bold"),italic:tn("italic"),underline:tn("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(r,e){return e.collapsed&&e.offset!==0?!0:(this.quill.format("indent","+1",f.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(r,e){return e.collapsed&&e.offset!==0?!0:(this.quill.format("indent","-1",f.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(r,e){e.format.indent!=null?this.quill.format("indent","-1",f.sources.USER):e.format.list!=null&&this.quill.format("list",!1,f.sources.USER)}},"indent code-block":Cl(!0),"outdent code-block":Cl(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(r){this.quill.deleteText(r.index-1,1,f.sources.USER)}},tab:{key:"Tab",handler(r,e){if(e.format.table)return!0;this.quill.history.cutoff();let t=new F.default().retain(r.index).delete(r.length).insert("	");return this.quill.updateContents(t,f.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(r.index+1,f.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,f.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(r,e){let t={list:!1};e.format.indent&&(t.indent=!1),this.quill.formatLine(r.index,r.length,t,f.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(r){let[e,t]=this.quill.getLine(r.index),s=At(T({},e.formats()),{list:"checked"}),n=new F.default().retain(r.index).insert(`
`,s).retain(e.length()-t-1).retain(1,{list:"unchecked"});this.quill.updateContents(n,f.sources.USER),this.quill.setSelection(r.index+1,f.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(r,e){let[t,s]=this.quill.getLine(r.index),n=new F.default().retain(r.index).insert(`
`,e.format).retain(t.length()-s-1).retain(1,{header:null});this.quill.updateContents(n,f.sources.USER),this.quill.setSelection(r.index+1,f.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(r){let e=this.quill.getModule("table");if(e){let[t,s,n,i]=e.getTable(r),l=dh(t,s,n,i);if(l==null)return;let o=t.offset();if(l<0){let a=new F.default().retain(o).insert(`
`);this.quill.updateContents(a,f.sources.USER),this.quill.setSelection(r.index+1,r.length,f.sources.SILENT)}else if(l>0){o+=t.length();let a=new F.default().retain(o).insert(`
`);this.quill.updateContents(a,f.sources.USER),this.quill.setSelection(o,f.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(r,e){let{event:t,line:s}=e,n=s.offset(this.quill.scroll);t.shiftKey?this.quill.setSelection(n-1,f.sources.USER):this.quill.setSelection(n+s.length(),f.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(r,e){if(this.quill.scroll.query("list")==null)return!0;let{length:t}=e.prefix,[s,n]=this.quill.getLine(r.index);if(n>t)return!0;let i;switch(e.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(r.index," ",f.sources.USER),this.quill.history.cutoff();let l=new F.default().retain(r.index-n).delete(t+1).retain(s.length()-2-n).retain(1,{list:i});return this.quill.updateContents(l,f.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(r.index-t,f.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(r){let[e,t]=this.quill.getLine(r.index),s=2,n=e;for(;n!=null&&n.length()<=1&&n.formats()["code-block"];)if(n=n.prev,s-=1,s<=0){let i=new F.default().retain(r.index+e.length()-t-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,f.sources.USER),this.quill.setSelection(r.index-1,f.sources.SILENT),!1}return!0}},"embed left":Nr("ArrowLeft",!1),"embed left shift":Nr("ArrowLeft",!0),"embed right":Nr("ArrowRight",!1),"embed right shift":Nr("ArrowRight",!0),"table down":Ol(!1),"table up":Ol(!0)}};Re.DEFAULTS=hh;function Cl(r){return{key:"Tab",shiftKey:!r,format:{"code-block":!0},handler(e,t){let{event:s}=t,n=this.quill.scroll.query("code-block"),{TAB:i}=n;if(e.length===0&&!s.shiftKey){this.quill.insertText(e.index,i,f.sources.USER),this.quill.setSelection(e.index+i.length,f.sources.SILENT);return}let l=e.length===0?this.quill.getLines(e.index,1):this.quill.getLines(e),{index:o,length:a}=e;l.forEach((c,u)=>{r?(c.insertAt(0,i),u===0?o+=i.length:a+=i.length):c.domNode.textContent.startsWith(i)&&(c.deleteAt(0,i.length),u===0?o-=i.length:a-=i.length)}),this.quill.update(f.sources.USER),this.quill.setSelection(o,a,f.sources.SILENT)}}}function Nr(r,e){return{key:r,shiftKey:e,altKey:null,[r==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(s){let{index:n}=s;r==="ArrowRight"&&(n+=s.length+1);let[i]=this.quill.getLeaf(n);return i instanceof k?(r==="ArrowLeft"?e?this.quill.setSelection(s.index-1,s.length+1,f.sources.USER):this.quill.setSelection(s.index-1,f.sources.USER):e?this.quill.setSelection(s.index,s.length+1,f.sources.USER):this.quill.setSelection(s.index+s.length+1,f.sources.USER),!1):!0}}}function tn(r){return{key:r[0],shortKey:!0,handler(e,t){this.quill.format(r,!t.format[r],f.sources.USER)}}}function Ol(r){return{key:r?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(e,t){let s=r?"prev":"next",n=t.line,i=n.parent[s];if(i!=null){if(i.statics.blotName==="table-row"){let l=i.children.head,o=n;for(;o.prev!=null;)o=o.prev,l=l.next;let a=l.offset(this.quill.scroll)+Math.min(t.offset,l.length()-1);this.quill.setSelection(a,0,f.sources.USER)}}else{let l=n.table()[s];l!=null&&(r?this.quill.setSelection(l.offset(this.quill.scroll)+l.length()-1,0,f.sources.USER):this.quill.setSelection(l.offset(this.quill.scroll),0,f.sources.USER))}return!1}}}function fh(r){if(typeof r=="string"||typeof r=="number")r={key:r};else if(typeof r=="object")r=ft(r);else return null;return r.shortKey&&(r[uh]=r.shortKey,delete r.shortKey),r}function Be(r){let{quill:e,range:t}=r,s=e.getLines(t),n={};if(s.length>1){let i=s[0].formats(),l=s[s.length-1].formats();n=F.AttributeMap.diff(l,i)||{}}e.deleteText(t,f.sources.USER),Object.keys(n).length>0&&e.formatLine(t.index,1,n,f.sources.USER),e.setSelection(t.index,f.sources.SILENT)}function dh(r,e,t,s){return e.prev==null&&e.next==null?t.prev==null&&t.next==null?s===0?-1:1:t.prev==null?-1:1:e.prev==null?-1:e.next==null?1:null}var mh=/font-weight:\s*normal/,ph=["P","OL","UL"],_l=r=>r&&ph.includes(r.tagName),gh=r=>{Array.from(r.querySelectorAll("br")).filter(e=>_l(e.previousElementSibling)&&_l(e.nextElementSibling)).forEach(e=>{e.parentNode?.removeChild(e)})},bh=r=>{Array.from(r.querySelectorAll('b[style*="font-weight"]')).filter(e=>e.getAttribute("style")?.match(mh)).forEach(e=>{let t=r.createDocumentFragment();t.append(...e.childNodes),e.parentNode?.replaceChild(t,e)})};function en(r){r.querySelector('[id^="docs-internal-guid-"]')&&(bh(r),gh(r))}var yh=/\bmso-list:[^;]*ignore/i,vh=/\bmso-list:[^;]*\bl(\d+)/i,Eh=/\bmso-list:[^;]*\blevel(\d+)/i,Nh=(r,e)=>{let t=r.getAttribute("style"),s=t?.match(vh);if(!s)return null;let n=Number(s[1]),i=t?.match(Eh),l=i?Number(i[1]):1,o=new RegExp(`@list l${n}:level${l}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),a=e.match(o),c=a&&a[1]==="bullet"?"bullet":"ordered";return{id:n,indent:l,type:c,element:r}},xh=r=>{let e=Array.from(r.querySelectorAll("[style*=mso-list]")),t=[],s=[];e.forEach(l=>{(l.getAttribute("style")||"").match(yh)?t.push(l):s.push(l)}),t.forEach(l=>l.parentNode?.removeChild(l));let n=r.documentElement.innerHTML,i=s.map(l=>Nh(l,n)).filter(l=>l);for(;i.length;){let l=[],o=i.shift();for(;o;)l.push(o),o=i.length&&i[0]?.element===o.element.nextElementSibling&&i[0].id===o.id?i.shift():null;let a=document.createElement("ul");l.forEach(h=>{let d=document.createElement("li");d.setAttribute("data-list",h.type),h.indent>1&&d.setAttribute("class",`ql-indent-${h.indent-1}`),d.innerHTML=h.element.innerHTML,a.appendChild(d)});let c=l[0]?.element,{parentNode:u}=c??{};c&&u?.replaceChild(a,c),l.slice(1).forEach(h=>{let{element:d}=h;u?.removeChild(d)})}};function rn(r){r.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&xh(r)}var Ah=[rn,en],wh=r=>{r.documentElement&&Ah.forEach(e=>{e(r)})},Il=wh;var Th=st("quill:clipboard"),Lh=[[Node.TEXT_NODE,Uh],[Node.TEXT_NODE,Bl],["br",Oh],[Node.ELEMENT_NODE,Bl],[Node.ELEMENT_NODE,Ch],[Node.ELEMENT_NODE,kh],[Node.ELEMENT_NODE,Mh],["li",Rh],["ol, ul",Bh],["pre",_h],["tr",Dh],["b",sn("bold")],["i",sn("italic")],["strike",sn("strike")],["style",Ih]],qh=[Tl,br].reduce((r,e)=>(r[e.keyName]=e,r),{}),Rl=[gr,Ie,_e,yr,vr,Er].reduce((r,e)=>(r[e.keyName]=e,r),{}),Ml=(()=>{class r extends O{static DEFAULTS={matchers:[]};constructor(t,s){super(t,s),this.quill.root.addEventListener("copy",n=>this.onCaptureCopy(n,!1)),this.quill.root.addEventListener("cut",n=>this.onCaptureCopy(n,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Lh.concat(this.options.matchers??[]).forEach(n=>{let[i,l]=n;this.addMatcher(i,l)})}addMatcher(t,s){this.matchers.push([t,s])}convert(t){let{html:s,text:n}=t,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(i[U.blotName])return new X.default().insert(n||"",{[U.blotName]:i[U.blotName]});if(!s)return new X.default().insert(n||"",i);let l=this.convertHTML(s);return Me(l,`
`)&&(l.ops[l.ops.length-1].attributes==null||i.table)?l.compose(new X.default().retain(l.length()-1).delete(1)):l}normalizeHTML(t){Il(t)}convertHTML(t){let s=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(s);let n=s.body,i=new WeakMap,[l,o]=this.prepareMatching(n,i);return Ar(this.quill.scroll,n,l,o,i)}dangerouslyPasteHTML(t,s){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:f.sources.API;if(typeof t=="string"){let i=this.convert({html:t,text:""});this.quill.setContents(i,s),this.quill.setSelection(0,f.sources.SILENT)}else{let i=this.convert({html:s,text:""});this.quill.updateContents(new X.default().retain(t).concat(i),n),this.quill.setSelection(t+i.length(),f.sources.SILENT)}}onCaptureCopy(t){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();let[n]=this.quill.selection.getRange();if(n==null)return;let{html:i,text:l}=this.onCopy(n,s);t.clipboardData?.setData("text/plain",l),t.clipboardData?.setData("text/html",i),s&&Be({range:n,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(s=>s[0]!=="#").join(`
`)}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();let s=this.quill.getSelection(!0);if(s==null)return;let n=t.clipboardData?.getData("text/html"),i=t.clipboardData?.getData("text/plain");if(!n&&!i){let o=t.clipboardData?.getData("text/uri-list");o&&(i=this.normalizeURIList(o))}let l=Array.from(t.clipboardData?.files||[]);if(!n&&l.length>0){this.quill.uploader.upload(s,l);return}if(n&&l.length>0){let o=new DOMParser().parseFromString(n,"text/html");if(o.body.childElementCount===1&&o.body.firstElementChild?.tagName==="IMG"){this.quill.uploader.upload(s,l);return}}this.onPaste(s,{html:n,text:i})}onCopy(t){let s=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:s}}onPaste(t,s){let{text:n,html:i}=s,l=this.quill.getFormat(t.index),o=this.convert({text:n,html:i},l);Th.log("onPaste",o,{text:n,html:i});let a=new X.default().retain(t.index).delete(t.length).concat(o);this.quill.updateContents(a,f.sources.USER),this.quill.setSelection(a.length()-t.length,f.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,s){let n=[],i=[];return this.matchers.forEach(l=>{let[o,a]=l;switch(o){case Node.TEXT_NODE:i.push(a);break;case Node.ELEMENT_NODE:n.push(a);break;default:Array.from(t.querySelectorAll(o)).forEach(c=>{s.has(c)?s.get(c)?.push(a):s.set(c,[a])});break}}),[n,i]}}return r})();function Wt(r,e,t,s){return s.query(e)?r.reduce((n,i)=>{if(!i.insert)return n;if(i.attributes&&i.attributes[e])return n.push(i);let l=t?{[e]:t}:{};return n.insert(i.insert,T(T({},l),i.attributes))},new X.default):r}function Me(r,e){let t="";for(let s=r.ops.length-1;s>=0&&t.length<e.length;--s){let n=r.ops[s];if(typeof n.insert!="string")break;t=n.insert+t}return t.slice(-1*e.length)===e}function It(r,e){if(!(r instanceof Element))return!1;let t=e.query(r);return t&&t.prototype instanceof k?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(r.tagName.toLowerCase())}function Sh(r,e){return r.previousElementSibling&&r.nextElementSibling&&!It(r.previousElementSibling,e)&&!It(r.nextElementSibling,e)}var xr=new WeakMap;function Dl(r){return r==null?!1:(xr.has(r)||(r.tagName==="PRE"?xr.set(r,!0):xr.set(r,Dl(r.parentNode))),xr.get(r))}function Ar(r,e,t,s,n){return e.nodeType===e.TEXT_NODE?s.reduce((i,l)=>l(e,i,r),new X.default):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce((i,l)=>{let o=Ar(r,l,t,s,n);return l.nodeType===e.ELEMENT_NODE&&(o=t.reduce((a,c)=>c(l,a,r),o),o=(n.get(l)||[]).reduce((a,c)=>c(l,a,r),o)),i.concat(o)},new X.default):new X.default}function sn(r){return(e,t,s)=>Wt(t,r,!0,s)}function kh(r,e,t){let s=$.keys(r),n=R.keys(r),i=Z.keys(r),l={};return s.concat(n).concat(i).forEach(o=>{let a=t.query(o,p.ATTRIBUTE);a!=null&&(l[a.attrName]=a.value(r),l[a.attrName])||(a=qh[o],a!=null&&(a.attrName===o||a.keyName===o)&&(l[a.attrName]=a.value(r)||void 0),a=Rl[o],a!=null&&(a.attrName===o||a.keyName===o)&&(a=Rl[o],l[a.attrName]=a.value(r)||void 0))}),Object.entries(l).reduce((o,a)=>{let[c,u]=a;return Wt(o,c,u,t)},e)}function Ch(r,e,t){let s=t.query(r);if(s==null)return e;if(s.prototype instanceof k){let n={},i=s.value(r);if(i!=null)return n[s.blotName]=i,new X.default().insert(n,s.formats(r,t))}else if(s.prototype instanceof Ut&&!Me(e,`
`)&&e.insert(`
`),"blotName"in s&&"formats"in s&&typeof s.formats=="function")return Wt(e,s.blotName,s.formats(r,t),t);return e}function Oh(r,e){return Me(e,`
`)||e.insert(`
`),e}function _h(r,e,t){let s=t.query("code-block"),n=s&&"formats"in s&&typeof s.formats=="function"?s.formats(r,t):!0;return Wt(e,"code-block",n,t)}function Ih(){return new X.default}function Rh(r,e,t){let s=t.query(r);if(s==null||s.blotName!=="list"||!Me(e,`
`))return e;let n=-1,i=r.parentNode;for(;i!=null;)["OL","UL"].includes(i.tagName)&&(n+=1),i=i.parentNode;return n<=0?e:e.reduce((l,o)=>o.insert?o.attributes&&typeof o.attributes.indent=="number"?l.push(o):l.insert(o.insert,T({indent:n},o.attributes||{})):l,new X.default)}function Bh(r,e,t){let s=r,n=s.tagName==="OL"?"ordered":"bullet",i=s.getAttribute("data-checked");return i&&(n=i==="true"?"checked":"unchecked"),Wt(e,"list",n,t)}function Bl(r,e,t){if(!Me(e,`
`)){if(It(r,t)&&(r.childNodes.length>0||r instanceof HTMLParagraphElement))return e.insert(`
`);if(e.length()>0&&r.nextSibling){let s=r.nextSibling;for(;s!=null;){if(It(s,t))return e.insert(`
`);let n=t.query(s);if(n&&n.prototype instanceof D)return e.insert(`
`);s=s.firstChild}}}return e}function Mh(r,e,t){let s={},n=r.style||{};return n.fontStyle==="italic"&&(s.italic=!0),n.textDecoration==="underline"&&(s.underline=!0),n.textDecoration==="line-through"&&(s.strike=!0),(n.fontWeight?.startsWith("bold")||parseInt(n.fontWeight,10)>=700)&&(s.bold=!0),e=Object.entries(s).reduce((i,l)=>{let[o,a]=l;return Wt(i,o,a,t)},e),parseFloat(n.textIndent||0)>0?new X.default().insert("	").concat(e):e}function Dh(r,e,t){let s=r.parentElement?.tagName==="TABLE"?r.parentElement:r.parentElement?.parentElement;if(s!=null){let i=Array.from(s.querySelectorAll("tr")).indexOf(r)+1;return Wt(e,"table",i,t)}return e}function Uh(r,e,t){let s=r.data;if(r.parentElement?.tagName==="O:P")return e.insert(s.trim());if(!Dl(r)){if(s.trim().length===0&&s.includes(`
`)&&!Sh(r,t))return e;s=s.replace(/[^\S\u00a0]/g," "),s=s.replace(/ {2,}/g," "),(r.previousSibling==null&&r.parentElement!=null&&It(r.parentElement,t)||r.previousSibling instanceof Element&&It(r.previousSibling,t))&&(s=s.replace(/^ /,"")),(r.nextSibling==null&&r.parentElement!=null&&It(r.parentElement,t)||r.nextSibling instanceof Element&&It(r.nextSibling,t))&&(s=s.replace(/ $/,"")),s=s.replaceAll("\xA0"," ")}return e.insert(s)}var Pl=(()=>{class r extends O{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,s){super(t,s),this.quill.on(f.events.EDITOR_CHANGE,(n,i,l,o)=>{n===f.events.SELECTION_CHANGE?i&&o!==f.sources.SILENT&&(this.currentRange=i):n===f.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||o===f.sources.USER?this.record(i,l):this.transform(i)),this.currentRange=nn(this.currentRange,i))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",n=>{n.inputType==="historyUndo"?(this.undo(),n.preventDefault()):n.inputType==="historyRedo"&&(this.redo(),n.preventDefault())})}change(t,s){if(this.stack[t].length===0)return;let n=this.stack[t].pop();if(!n)return;let i=this.quill.getContents(),l=n.delta.invert(i);this.stack[s].push({delta:l,range:nn(n.range,l)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n.delta,f.sources.USER),this.ignoreChange=!1,this.restoreSelection(n)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,s){if(t.ops.length===0)return;this.stack.redo=[];let n=t.invert(s),i=this.currentRange,l=Date.now();if(this.lastRecorded+this.options.delay>l&&this.stack.undo.length>0){let o=this.stack.undo.pop();o&&(n=n.compose(o.delta),i=o.range)}else this.lastRecorded=l;n.length()!==0&&(this.stack.undo.push({delta:n,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(t){Ul(this.stack.undo,t),Ul(this.stack.redo,t)}undo(){this.change("undo","redo")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,f.sources.USER);else{let s=jh(this.quill.scroll,t.delta);this.quill.setSelection(s,f.sources.USER)}}}return r})();function Ul(r,e){let t=e;for(let s=r.length-1;s>=0;s-=1){let n=r[s];r[s]={delta:t.transform(n.delta,!0),range:n.range&&nn(n.range,t)},t=n.delta.transform(t),r[s].delta.length()===0&&r.splice(s,1)}}function Ph(r,e){let t=e.ops[e.ops.length-1];return t==null?!1:t.insert!=null?typeof t.insert=="string"&&t.insert.endsWith(`
`):t.attributes!=null?Object.keys(t.attributes).some(s=>r.query(s,p.BLOCK)!=null):!1}function jh(r,e){let t=e.reduce((n,i)=>n+(i.delete||0),0),s=e.length()-t;return Ph(r,e)&&(s-=1),s}function nn(r,e){if(!r)return r;let t=e.transformPosition(r.index),s=e.transformPosition(r.index+r.length);return{index:t,length:s-t}}var jl=J(rt(),1);var Hh=(()=>{class r extends O{constructor(t,s){super(t,s),t.root.addEventListener("drop",n=>{n.preventDefault();let i=null;if(document.caretRangeFromPoint)i=document.caretRangeFromPoint(n.clientX,n.clientY);else if(document.caretPositionFromPoint){let o=document.caretPositionFromPoint(n.clientX,n.clientY);i=document.createRange(),i.setStart(o.offsetNode,o.offset),i.setEnd(o.offsetNode,o.offset)}let l=i&&t.selection.normalizeNative(i);if(l){let o=t.selection.normalizedToRange(l);n.dataTransfer?.files&&this.upload(o,n.dataTransfer.files)}})}upload(t,s){let n=[];Array.from(s).forEach(i=>{i&&this.options.mimetypes?.includes(i.type)&&n.push(i)}),n.length>0&&this.options.handler.call(this,t,n)}}return r.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(e,t){if(!this.quill.scroll.query("image"))return;let s=t.map(n=>new Promise(i=>{let l=new FileReader;l.onload=()=>{i(l.result)},l.readAsDataURL(n)}));Promise.all(s).then(n=>{let i=n.reduce((l,o)=>l.insert({image:o}),new jl.default().retain(e.index).delete(e.length));this.quill.updateContents(i,b.sources.USER),this.quill.setSelection(e.index+n.length,b.sources.SILENT)})}},r})(),Hl=Hh;var Rt=J(rt(),1);var Fl=J(rt(),1);var Fh=["insertText","insertReplacementText"],ln=class extends O{constructor(e,t){super(e,t),e.root.addEventListener("beforeinput",s=>{this.handleBeforeInput(s)}),/Android/i.test(navigator.userAgent)||e.on(f.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(e){Be({range:e,quill:this.quill})}replaceText(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(e.length===0)return!1;if(t){let s=this.quill.getFormat(e.index,1);this.deleteRange(e),this.quill.updateContents(new Fl.default().retain(e.index).insert(t,s),f.sources.USER)}else this.deleteRange(e);return this.quill.setSelection(e.index+t.length,0,f.sources.SILENT),!0}handleBeforeInput(e){if(this.quill.composition.isComposing||e.defaultPrevented||!Fh.includes(e.inputType))return;let t=e.getTargetRanges?e.getTargetRanges()[0]:null;if(!t||t.collapsed===!0)return;let s=$h(e);if(s==null)return;let n=this.quill.selection.normalizeNative(t),i=n?this.quill.selection.normalizedToRange(n):null;i&&this.replaceText(i,s)&&e.preventDefault()}handleCompositionStart(){let e=this.quill.getSelection();e&&this.replaceText(e)}};function $h(r){return typeof r.data=="string"?r.data:r.dataTransfer?.types.includes("text/plain")?r.dataTransfer.getData("text/plain"):null}var $l=ln;var zh=/Mac/i.test(navigator.platform),Vh=100,Kh=r=>!!(r.key==="ArrowLeft"||r.key==="ArrowRight"||r.key==="ArrowUp"||r.key==="ArrowDown"||r.key==="Home"||zh&&r.key==="a"&&r.ctrlKey===!0),on=class extends O{isListening=!1;selectionChangeDeadline=0;constructor(e,t){super(e,t),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,t){let{line:s,event:n}=t;if(!(s instanceof W)||!s.uiNode)return!0;let i=getComputedStyle(s.domNode).direction==="rtl";return i&&n.key!=="ArrowRight"||!i&&n.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(n.shiftKey?1:0),f.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&Kh(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+Vh,this.isListening)return;this.isListening=!0;let e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){let e=document.getSelection();if(!e)return;let t=e.getRangeAt(0);if(t.collapsed!==!0||t.startOffset!==0)return;let s=this.quill.scroll.find(t.startContainer);if(!(s instanceof W)||!s.uiNode)return;let n=document.createRange();n.setStartAfter(s.uiNode),n.setEndAfter(s.uiNode),e.removeAllRanges(),e.addRange(n)}},zl=on;f.register({"blots/block":I,"blots/block/embed":D,"blots/break":P,"blots/container":it,"blots/cursor":Ot,"blots/embed":he,"blots/inline":j,"blots/scroll":wl,"blots/text":_,"modules/clipboard":Ml,"modules/history":Pl,"modules/keyboard":Re,"modules/uploader":Hl,"modules/input":$l,"modules/uiNode":zl});var wr=f;var an=class extends R{add(e,t){let s=0;if(t==="+1"||t==="-1"){let n=this.value(e)||0;s=t==="+1"?n+1:n-1}else typeof t=="number"&&(s=t);return s===0?(this.remove(e),!0):super.add(e,s.toString())}canAdd(e,t){return super.canAdd(e,t)||super.canAdd(e,parseInt(t,10))}value(e){return parseInt(super.value(e),10)||void 0}},Gh=new an("indent","ql-indent",{scope:p.BLOCK,whitelist:[1,2,3,4,5,6,7,8]}),Vl=Gh;var Wh=(()=>{class r extends I{static blotName="blockquote";static tagName="blockquote"}return r})(),Kl=Wh;var Zh=(()=>{class r extends I{static blotName="header";static tagName=["H1","H2","H3","H4","H5","H6"];static formats(t){return this.tagName.indexOf(t.tagName)+1}}return r})(),Gl=Zh;var cn=(()=>{class r extends it{}return r.blotName="list-container",r.tagName="OL",r})(),Tr=(()=>{class r extends I{static create(t){let s=super.create();return s.setAttribute("data-list",t),s}static formats(t){return t.getAttribute("data-list")||void 0}static register(){f.register(cn)}constructor(t,s){super(t,s);let n=s.ownerDocument.createElement("span"),i=l=>{if(!t.isEnabled())return;let o=this.statics.formats(s,t);o==="checked"?(this.format("list","unchecked"),l.preventDefault()):o==="unchecked"&&(this.format("list","checked"),l.preventDefault())};n.addEventListener("mousedown",i),n.addEventListener("touchstart",i),this.attachUI(n)}format(t,s){t===this.statics.blotName&&s?this.domNode.setAttribute("data-list",s):super.format(t,s)}}return r.blotName="list",r.tagName="LI",r})();cn.allowedChildren=[Tr];Tr.requiredContainer=cn;var Qh=(()=>{class r extends j{static blotName="bold";static tagName=["STRONG","B"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}return r})(),me=Qh;var Xh=(()=>{class r extends me{static blotName="italic";static tagName=["EM","I"]}return r})(),Wl=Xh;var Zt=(()=>{class r extends j{static blotName="link";static tagName="A";static SANITIZED_URL="about:blank";static PROTOCOL_WHITELIST=["http","https","mailto","tel","sms"];static create(t){let s=super.create(t);return s.setAttribute("href",this.sanitize(t)),s.setAttribute("rel","noopener noreferrer"),s.setAttribute("target","_blank"),s}static formats(t){return t.getAttribute("href")}static sanitize(t){return un(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,s){t!==this.statics.blotName||!s?super.format(t,s):this.domNode.setAttribute("href",this.constructor.sanitize(s))}}return r})();function un(r,e){let t=document.createElement("a");t.href=r;let s=t.href.slice(0,t.href.indexOf(":"));return e.indexOf(s)>-1}var Yh=(()=>{class r extends j{static blotName="script";static tagName=["SUB","SUP"];static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}return r})(),Zl=Yh;var Jh=(()=>{class r extends me{static blotName="strike";static tagName=["S","STRIKE"]}return r})(),Ql=Jh;var tf=(()=>{class r extends j{static blotName="underline";static tagName="U"}return r})(),Xl=tf;var ef=(()=>{class r extends he{static blotName="formula";static className="ql-formula";static tagName="SPAN";static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");let s=super.create(t);return typeof t=="string"&&(window.katex.render(t,s,{throwOnError:!1,errorColor:"#f00"}),s.setAttribute("data-value",t)),s}static value(t){return t.getAttribute("data-value")}html(){let{formula:t}=this.value();return`<span>${t}</span>`}}return r})(),Yl=ef;var Jl=["alt","height","width"],rf=(()=>{class r extends k{static blotName="image";static tagName="IMG";static create(t){let s=super.create(t);return typeof t=="string"&&s.setAttribute("src",this.sanitize(t)),s}static formats(t){return Jl.reduce((s,n)=>(t.hasAttribute(n)&&(s[n]=t.getAttribute(n)),s),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return un(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,s){Jl.indexOf(t)>-1?s?this.domNode.setAttribute(t,s):this.domNode.removeAttribute(t):super.format(t,s)}}return r})(),to=rf;var eo=["height","width"],sf=(()=>{class r extends D{static blotName="video";static className="ql-video";static tagName="IFRAME";static create(t){let s=super.create(t);return s.setAttribute("frameborder","0"),s.setAttribute("allowfullscreen","true"),s.setAttribute("src",this.sanitize(t)),s}static formats(t){return eo.reduce((s,n)=>(t.hasAttribute(n)&&(s[n]=t.getAttribute(n)),s),{})}static sanitize(t){return Zt.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,s){eo.indexOf(t)>-1?s?this.domNode.setAttribute(t,s):this.domNode.removeAttribute(t):super.format(t,s)}html(){let{video:t}=this.value();return`<a href="${t}">${t}</a>`}}return r})(),ro=sf;var qr=J(rt(),1);var De=new R("code-token","hljs",{scope:p.INLINE}),Ue=(()=>{class r extends j{static formats(t,s){for(;t!=null&&t!==s.domNode;){if(t.classList&&t.classList.contains(U.className))return super.formats(t,s);t=t.parentNode}}constructor(t,s,n){super(t,s,n),De.add(this.domNode,n)}format(t,s){t!==r.blotName?super.format(t,s):s?De.add(this.domNode,s):(De.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),De.value(this.domNode)||this.unwrap()}}return r.blotName="code-token",r.className="ql-token",r})(),Y=class extends U{static create(e){let t=super.create(e);return typeof e=="string"&&t.setAttribute("data-language",e),t}static formats(e){return e.getAttribute("data-language")||"plain"}static register(){}format(e,t){e===this.statics.blotName&&t?this.domNode.setAttribute("data-language",t):super.format(e,t)}replaceWith(e,t){return this.formatAt(0,this.length(),Ue.blotName,!1),super.replaceWith(e,t)}},Lr=(()=>{class r extends ut{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,s){t===Y.blotName&&(this.forceNext=!0,this.children.forEach(n=>{n.format(t,s)}))}formatAt(t,s,n,i){n===Y.blotName&&(this.forceNext=!0),super.formatAt(t,s,n,i)}highlight(t){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;let i=`${Array.from(this.domNode.childNodes).filter(o=>o!==this.uiNode).map(o=>o.textContent).join(`
`)}
`,l=Y.formats(this.children.head.domNode);if(s||this.forceNext||this.cachedText!==i){if(i.trim().length>0||this.cachedText==null){let o=this.children.reduce((c,u)=>c.concat(Cs(u,!1)),new qr.default),a=t(i,l);o.diff(a).reduce((c,u)=>{let{retain:h,attributes:d}=u;return h?(d&&Object.keys(d).forEach(m=>{[Y.blotName,Ue.blotName].includes(m)&&this.formatAt(c,h,m,d[m])}),c+h):c},0)}this.cachedText=i,this.forceNext=!1}}html(t,s){let[n]=this.children.find(t);return`<pre data-language="${n?Y.formats(n.domNode):"plain"}">
${Gt(this.code(t,s))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){let s=Y.formats(this.children.head.domNode);s!==this.uiNode.value&&(this.uiNode.value=s)}}}return r.allowedChildren=[Y],r})();Y.requiredContainer=Lr;Y.allowedChildren=[Ue,Ot,_,P];var nf=(r,e,t)=>{if(typeof r.versionString=="string"){let s=r.versionString.split(".")[0];if(parseInt(s,10)>=11)return r.highlight(t,{language:e}).value}return r.highlight(e,t).value},Pe=class extends O{static register(){f.register(Ue,!0),f.register(Y,!0),f.register(Lr,!0)}constructor(e,t){if(super(e,t),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((s,n)=>{let{key:i}=n;return s[i]=!0,s},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(f.events.SCROLL_BLOT_MOUNT,e=>{if(!(e instanceof Lr))return;let t=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(s=>{let{key:n,label:i}=s,l=t.ownerDocument.createElement("option");l.textContent=i,l.setAttribute("value",n),t.appendChild(l)}),t.addEventListener("change",()=>{e.format(Y.blotName,t.value),this.quill.root.focus(),this.highlight(e,!0)}),e.uiNode==null&&(e.attachUI(t),e.children.head&&(t.value=Y.formats(e.children.head.domNode)))})}initTimer(){let e=null;this.quill.on(f.events.SCROLL_OPTIMIZE,()=>{e&&clearTimeout(e),e=setTimeout(()=>{this.highlight(),e=null},this.options.interval)})}highlight(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(f.sources.USER);let s=this.quill.getSelection();(e==null?this.quill.scroll.descendants(Lr):[e]).forEach(i=>{i.highlight(this.highlightBlot,t)}),this.quill.update(f.sources.SILENT),s!=null&&this.quill.setSelection(s,f.sources.SILENT)}highlightBlot(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(t=this.languages[t]?t:"plain",t==="plain")return Gt(e).split(`
`).reduce((n,i,l)=>(l!==0&&n.insert(`
`,{[U.blotName]:t}),n.insert(i)),new qr.default);let s=this.quill.root.ownerDocument.createElement("div");return s.classList.add(U.className),s.innerHTML=nf(this.options.hljs,t,e),Ar(this.quill.scroll,s,[(n,i)=>{let l=De.value(n);return l?i.compose(new qr.default().retain(i.length(),{[Ue.blotName]:l})):i}],[(n,i)=>n.data.split(`
`).reduce((l,o,a)=>(a!==0&&l.insert(`
`,{[U.blotName]:t}),l.insert(o)),i)],new WeakMap)}};Pe.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};var so=J(rt(),1);var ht=(()=>{class r extends I{static blotName="table";static tagName="TD";static create(t){let s=super.create();return t?s.setAttribute("data-row",t):s.setAttribute("data-row",kr()),s}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,s){t===r.blotName&&s?this.domNode.setAttribute("data-row",s):super.format(t,s)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}return r})(),Bt=(()=>{class r extends it{static blotName="table-row";static tagName="TR";checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){let t=this.children.head.formats(),s=this.children.tail.formats(),n=this.next.children.head.formats(),i=this.next.children.tail.formats();return t.table===s.table&&t.table===n.table&&t.table===i.table}return!1}optimize(t){super.optimize(t),this.children.forEach(s=>{if(s.next==null)return;let n=s.formats(),i=s.next.formats();if(n.table!==i.table){let l=this.splitAfter(s);l&&l.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}return r})(),Nt=(()=>{class r extends it{static blotName="table-body";static tagName="TBODY"}return r})(),Sr=(()=>{class r extends it{static blotName="table-container";static tagName="TABLE";balanceCells(){let t=this.descendants(Bt),s=t.reduce((n,i)=>Math.max(i.children.length,n),0);t.forEach(n=>{new Array(s-n.children.length).fill(0).forEach(()=>{let i;n.children.head!=null&&(i=ht.formats(n.children.head.domNode));let l=this.scroll.create(ht.blotName,i);n.appendChild(l),l.optimize()})})}cells(t){return this.rows().map(s=>s.children.at(t))}deleteColumn(t){let[s]=this.descendant(Nt);s==null||s.children.head==null||s.children.forEach(n=>{let i=n.children.at(t);i?.remove()})}insertColumn(t){let[s]=this.descendant(Nt);s==null||s.children.head==null||s.children.forEach(n=>{let i=n.children.at(t),l=ht.formats(n.children.head.domNode),o=this.scroll.create(ht.blotName,l);n.insertBefore(o,i)})}insertRow(t){let[s]=this.descendant(Nt);if(s==null||s.children.head==null)return;let n=kr(),i=this.scroll.create(Bt.blotName);s.children.head.children.forEach(()=>{let o=this.scroll.create(ht.blotName,n);i.appendChild(o)});let l=s.children.at(t);s.insertBefore(i,l)}rows(){let t=this.children.head;return t==null?[]:t.children.map(s=>s)}}return r.allowedChildren=[Nt],r})();Nt.requiredContainer=Sr;Nt.allowedChildren=[Bt];Bt.requiredContainer=Nt;Bt.allowedChildren=[ht];ht.requiredContainer=Bt;function kr(){return`row-${Math.random().toString(36).slice(2,6)}`}var hn=class extends O{static register(){f.register(ht),f.register(Bt),f.register(Nt),f.register(Sr)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(Sr).forEach(e=>{e.balanceCells()})}deleteColumn(){let[e,,t]=this.getTable();t!=null&&(e.deleteColumn(t.cellOffset()),this.quill.update(f.sources.USER))}deleteRow(){let[,e]=this.getTable();e!=null&&(e.remove(),this.quill.update(f.sources.USER))}deleteTable(){let[e]=this.getTable();if(e==null)return;let t=e.offset();e.remove(),this.quill.update(f.sources.USER),this.quill.setSelection(t,f.sources.SILENT)}getTable(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(e==null)return[null,null,null,-1];let[t,s]=this.quill.getLine(e.index);if(t==null||t.statics.blotName!==ht.blotName)return[null,null,null,-1];let n=t.parent;return[n.parent.parent,n,t,s]}insertColumn(e){let t=this.quill.getSelection();if(!t)return;let[s,n,i]=this.getTable(t);if(i==null)return;let l=i.cellOffset();s.insertColumn(l+e),this.quill.update(f.sources.USER);let o=n.rowOffset();e===0&&(o+=1),this.quill.setSelection(t.index+o,t.length,f.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(e){let t=this.quill.getSelection();if(!t)return;let[s,n,i]=this.getTable(t);if(i==null)return;let l=n.rowOffset();s.insertRow(l+e),this.quill.update(f.sources.USER),e>0?this.quill.setSelection(t,f.sources.SILENT):this.quill.setSelection(t.index+n.children.length,t.length,f.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(e,t){let s=this.quill.getSelection();if(s==null)return;let n=new Array(e).fill(0).reduce(i=>{let l=new Array(t).fill(`
`).join("");return i.insert(l,{table:kr()})},new so.default().retain(s.index));this.quill.updateContents(n,f.sources.USER),this.quill.setSelection(s.index,f.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(f.events.SCROLL_OPTIMIZE,e=>{e.some(t=>["TD","TR","TBODY","TABLE"].includes(t.target.tagName)?(this.quill.once(f.events.TEXT_CHANGE,(s,n,i)=>{i===f.sources.USER&&this.balanceTables()}),!0):!1)})}},no=hn;var oo=J(rt(),1);var io=st("quill:toolbar"),fn=(()=>{class r extends O{constructor(t,s){if(super(t,s),Array.isArray(this.options.container)){let n=document.createElement("div");n.setAttribute("role","toolbar"),lf(n,this.options.container),t.container?.parentNode?.insertBefore(n,t.container),this.container=n}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){io.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(n=>{let i=this.options.handlers?.[n];i&&this.addHandler(n,i)}),Array.from(this.container.querySelectorAll("button, select")).forEach(n=>{this.attach(n)}),this.quill.on(f.events.EDITOR_CHANGE,()=>{let[n]=this.quill.selection.getRange();this.update(n)})}addHandler(t,s){this.handlers[t]=s}attach(t){let s=Array.from(t.classList).find(i=>i.indexOf("ql-")===0);if(!s)return;if(s=s.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[s]==null&&this.quill.scroll.query(s)==null){io.warn("ignoring attaching to nonexistent format",s,t);return}let n=t.tagName==="SELECT"?"change":"click";t.addEventListener(n,i=>{let l;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;let a=t.options[t.selectedIndex];a.hasAttribute("selected")?l=!1:l=a.value||!1}else t.classList.contains("ql-active")?l=!1:l=t.value||!t.hasAttribute("value"),i.preventDefault();this.quill.focus();let[o]=this.quill.selection.getRange();if(this.handlers[s]!=null)this.handlers[s].call(this,l);else if(this.quill.scroll.query(s).prototype instanceof k){if(l=prompt(`Enter ${s}`),!l)return;this.quill.updateContents(new oo.default().retain(o.index).delete(o.length).insert({[s]:l}),f.sources.USER)}else this.quill.format(s,l,f.sources.USER);this.update(o)}),this.controls.push([s,t])}update(t){let s=t==null?{}:this.quill.getFormat(t);this.controls.forEach(n=>{let[i,l]=n;if(l.tagName==="SELECT"){let o=null;if(t==null)o=null;else if(s[i]==null)o=l.querySelector("option[selected]");else if(!Array.isArray(s[i])){let a=s[i];typeof a=="string"&&(a=a.replace(/"/g,'\\"')),o=l.querySelector(`option[value="${a}"]`)}o==null?(l.value="",l.selectedIndex=-1):o.selected=!0}else if(t==null)l.classList.remove("ql-active"),l.setAttribute("aria-pressed","false");else if(l.hasAttribute("value")){let o=s[i],a=o===l.getAttribute("value")||o!=null&&o.toString()===l.getAttribute("value")||o==null&&!l.getAttribute("value");l.classList.toggle("ql-active",a),l.setAttribute("aria-pressed",a.toString())}else{let o=s[i]!=null;l.classList.toggle("ql-active",o),l.setAttribute("aria-pressed",o.toString())}})}}return r.DEFAULTS={},r})();function lo(r,e,t){let s=document.createElement("button");s.setAttribute("type","button"),s.classList.add(`ql-${e}`),s.setAttribute("aria-pressed","false"),t!=null?(s.value=t,s.setAttribute("aria-label",`${e}: ${t}`)):s.setAttribute("aria-label",e),r.appendChild(s)}function lf(r,e){Array.isArray(e[0])||(e=[e]),e.forEach(t=>{let s=document.createElement("span");s.classList.add("ql-formats"),t.forEach(n=>{if(typeof n=="string")lo(s,n);else{let i=Object.keys(n)[0],l=n[i];Array.isArray(l)?of(s,i,l):lo(s,i,l)}}),r.appendChild(s)})}function of(r,e,t){let s=document.createElement("select");s.classList.add(`ql-${e}`),t.forEach(n=>{let i=document.createElement("option");n!==!1?i.setAttribute("value",String(n)):i.setAttribute("selected","selected"),s.appendChild(i)}),r.appendChild(s)}fn.DEFAULTS={container:null,handlers:{clean(){let r=this.quill.getSelection();if(r!=null)if(r.length===0){let e=this.quill.getFormat();Object.keys(e).forEach(t=>{this.quill.scroll.query(t,p.INLINE)!=null&&this.quill.format(t,!1,f.sources.USER)})}else this.quill.removeFormat(r.index,r.length,f.sources.USER)},direction(r){let{align:e}=this.quill.getFormat();r==="rtl"&&e==null?this.quill.format("align","right",f.sources.USER):!r&&e==="right"&&this.quill.format("align",!1,f.sources.USER),this.quill.format("direction",r,f.sources.USER)},indent(r){let e=this.quill.getSelection(),t=this.quill.getFormat(e),s=parseInt(t.indent||0,10);if(r==="+1"||r==="-1"){let n=r==="+1"?1:-1;t.direction==="rtl"&&(n*=-1),this.quill.format("indent",s+n,f.sources.USER)}},link(r){r===!0&&(r=prompt("Enter link URL:")),this.quill.format("link",r,f.sources.USER)},list(r){let e=this.quill.getSelection(),t=this.quill.getFormat(e);r==="check"?t.list==="checked"||t.list==="unchecked"?this.quill.format("list",!1,f.sources.USER):this.quill.format("list","unchecked",f.sources.USER):this.quill.format("list",r,f.sources.USER)}}};var af='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',cf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',uf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',hf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',ff='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',df='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',mf='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',pf='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',ao='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',gf='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',bf='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',yf='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',vf='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',Ef='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',Nf='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',xf='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Af='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',wf='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Tf='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',Lf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',qf='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',Sf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',kf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',Cf='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',Of='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',_f='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',If='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',Rf='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',Bf='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',Mf='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',Df='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',Uf='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',Pf='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',Mt={align:{"":af,center:cf,right:uf,justify:hf},background:ff,blockquote:df,bold:mf,clean:pf,code:ao,"code-block":ao,color:gf,direction:{"":bf,rtl:yf},formula:vf,header:{1:Ef,2:Nf,3:xf,4:Af,5:wf,6:Tf},italic:Lf,image:qf,indent:{"+1":Sf,"-1":kf},link:Cf,list:{bullet:Of,check:_f,ordered:If},script:{sub:Rf,super:Bf},strike:Mf,table:Df,underline:Uf,video:Pf};var jf='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>',co=0;function uo(r,e){r.setAttribute(e,`${r.getAttribute(e)!=="true"}`)}var dn=class{constructor(e){this.select=e,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",t=>{switch(t.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),t.preventDefault();break;default:}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),uo(this.label,"aria-expanded"),uo(this.options,"aria-hidden")}buildItem(e){let t=document.createElement("span");t.tabIndex="0",t.setAttribute("role","button"),t.classList.add("ql-picker-item");let s=e.getAttribute("value");return s&&t.setAttribute("data-value",s),e.textContent&&t.setAttribute("data-label",e.textContent),t.addEventListener("click",()=>{this.selectItem(t,!0)}),t.addEventListener("keydown",n=>{switch(n.key){case"Enter":this.selectItem(t,!0),n.preventDefault();break;case"Escape":this.escape(),n.preventDefault();break;default:}}),t}buildLabel(){let e=document.createElement("span");return e.classList.add("ql-picker-label"),e.innerHTML=jf,e.tabIndex="0",e.setAttribute("role","button"),e.setAttribute("aria-expanded","false"),this.container.appendChild(e),e}buildOptions(){let e=document.createElement("span");e.classList.add("ql-picker-options"),e.setAttribute("aria-hidden","true"),e.tabIndex="-1",e.id=`ql-picker-options-${co}`,co+=1,this.label.setAttribute("aria-controls",e.id),this.options=e,Array.from(this.select.options).forEach(t=>{let s=this.buildItem(t);e.appendChild(s),t.selected===!0&&this.selectItem(s)}),this.container.appendChild(e)}buildPicker(){Array.from(this.select.attributes).forEach(e=>{this.container.setAttribute(e.name,e.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=this.container.querySelector(".ql-selected");e!==s&&(s?.classList.remove("ql-selected"),e!=null&&(e.classList.add("ql-selected"),this.select.selectedIndex=Array.from(e.parentNode.children).indexOf(e),e.hasAttribute("data-value")?this.label.setAttribute("data-value",e.getAttribute("data-value")):this.label.removeAttribute("data-value"),e.hasAttribute("data-label")?this.label.setAttribute("data-label",e.getAttribute("data-label")):this.label.removeAttribute("data-label"),t&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let e;if(this.select.selectedIndex>-1){let s=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];e=this.select.options[this.select.selectedIndex],this.selectItem(s)}else this.selectItem(null);let t=e!=null&&e!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",t)}},Dt=dn;var mn=class extends Dt{constructor(e,t){super(e),this.label.innerHTML=t,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(s=>{s.classList.add("ql-primary")})}buildItem(e){let t=super.buildItem(e);return t.style.backgroundColor=e.getAttribute("value")||"",t}selectItem(e,t){super.selectItem(e,t);let s=this.label.querySelector(".ql-color-label"),n=e&&e.getAttribute("data-value")||"";s&&(s.tagName==="line"?s.style.stroke=n:s.style.fill=n)}},Cr=mn;var pn=class extends Dt{constructor(e,t){super(e),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(s=>{s.innerHTML=t[s.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(e,t){super.selectItem(e,t);let s=e||this.defaultItem;if(s!=null){if(this.label.innerHTML===s.innerHTML)return;this.label.innerHTML=s.innerHTML}}},Or=pn;var Hf=r=>{let{overflowY:e}=getComputedStyle(r,null);return e!=="visible"&&e!=="clip"},gn=class{constructor(e,t){this.quill=e,this.boundsContainer=t||document.body,this.root=e.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,Hf(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(e){let t=e.left+e.width/2-this.root.offsetWidth/2,s=e.bottom+this.quill.root.scrollTop;this.root.style.left=`${t}px`,this.root.style.top=`${s}px`,this.root.classList.remove("ql-flip");let n=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect(),l=0;if(i.right>n.right&&(l=n.right-i.right,this.root.style.left=`${t+l}px`),i.left<n.left&&(l=n.left-i.left,this.root.style.left=`${t+l}px`),i.bottom>n.bottom){let o=i.bottom-i.top,a=e.bottom-e.top+o;this.root.style.top=`${s-a}px`,this.root.classList.add("ql-flip")}return l}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}},_r=gn;var Ff=[!1,"center","right","justify"],$f=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],zf=[!1,"serif","monospace"],Vf=["1","2","3",!1],Kf=["small",!1,"large","huge"],xt=class extends fe{constructor(e,t){super(e,t);let s=n=>{if(!document.body.contains(e.root)){document.body.removeEventListener("click",s);return}this.tooltip!=null&&!this.tooltip.root.contains(n.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(i=>{i.container.contains(n.target)||i.close()})};e.emitter.listenDOM("click",document.body,s)}addModule(e){let t=super.addModule(e);return e==="toolbar"&&this.extendToolbar(t),t}buildButtons(e,t){Array.from(e).forEach(s=>{(s.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),t[i]!=null))if(i==="direction")s.innerHTML=t[i][""]+t[i].rtl;else if(typeof t[i]=="string")s.innerHTML=t[i];else{let l=s.value||"";l!=null&&t[i][l]&&(s.innerHTML=t[i][l])}})})}buildPickers(e,t){this.pickers=Array.from(e).map(n=>{if(n.classList.contains("ql-align")&&(n.querySelector("option")==null&&je(n,Ff),typeof t.align=="object"))return new Or(n,t.align);if(n.classList.contains("ql-background")||n.classList.contains("ql-color")){let i=n.classList.contains("ql-background")?"background":"color";return n.querySelector("option")==null&&je(n,$f,i==="background"?"#ffffff":"#000000"),new Cr(n,t[i])}return n.querySelector("option")==null&&(n.classList.contains("ql-font")?je(n,zf):n.classList.contains("ql-header")?je(n,Vf):n.classList.contains("ql-size")&&je(n,Kf)),new Dt(n)});let s=()=>{this.pickers.forEach(n=>{n.update()})};this.quill.on(b.events.EDITOR_CHANGE,s)}};xt.DEFAULTS=tt({},fe.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let r=this.container.querySelector("input.ql-image[type=file]");r==null&&(r=document.createElement("input"),r.setAttribute("type","file"),r.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),r.classList.add("ql-image"),r.addEventListener("change",()=>{let e=this.quill.getSelection(!0);this.quill.uploader.upload(e,r.files),r.value=""}),this.container.appendChild(r)),r.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});var pe=class extends _r{constructor(e,t){super(e,t),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",e=>{e.key==="Enter"?(this.save(),e.preventDefault()):e.key==="Escape"&&(this.cancel(),e.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;t!=null?this.textbox.value=t:e!==this.root.getAttribute("data-mode")&&(this.textbox.value="");let s=this.quill.getBounds(this.quill.selection.savedRange);s!=null&&this.position(s),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${e}`)||""),this.root.setAttribute("data-mode",e)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:e}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{let{scrollTop:t}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",e,b.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",e,b.sources.USER)),this.quill.root.scrollTop=t;break}case"video":e=Gf(e);case"formula":{if(!e)break;let t=this.quill.getSelection(!0);if(t!=null){let s=t.index+t.length;this.quill.insertEmbed(s,this.root.getAttribute("data-mode"),e,b.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(s+1," ",b.sources.USER),this.quill.setSelection(s+2,b.sources.USER)}break}default:}this.textbox.value="",this.hide()}};function Gf(r){let e=r.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||r.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return e?`${e[1]||"https"}://www.youtube.com/embed/${e[2]}?showinfo=0`:(e=r.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${e[1]||"https"}://player.vimeo.com/video/${e[2]}/`:r}function je(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;e.forEach(s=>{let n=document.createElement("option");s===t?n.setAttribute("selected","selected"):n.setAttribute("value",String(s)),r.appendChild(n)})}var Wf=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],bn=class extends pe{static TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join("");constructor(e,t){super(e,t),this.quill.on(b.events.EDITOR_CHANGE,(s,n,i,l)=>{if(s===b.events.SELECTION_CHANGE)if(n!=null&&n.length>0&&l===b.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;let o=this.quill.getLines(n.index,n.length);if(o.length===1){let a=this.quill.getBounds(n);a!=null&&this.position(a)}else{let a=o[o.length-1],c=this.quill.getIndex(a),u=Math.min(a.length()-1,n.index+n.length-c),h=this.quill.getBounds(new K(c,u));h!=null&&this.position(h)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(b.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;let e=this.quill.getSelection();if(e!=null){let t=this.quill.getBounds(e);t!=null&&this.position(t)}},1)})}cancel(){this.show()}position(e){let t=super.position(e),s=this.root.querySelector(".ql-tooltip-arrow");return s.style.marginLeft="",t!==0&&(s.style.marginLeft=`${-1*t-s.offsetWidth/2}px`),t}},He=class extends xt{constructor(e,t){t.modules.toolbar!=null&&t.modules.toolbar.container==null&&(t.modules.toolbar.container=Wf),super(e,t),this.quill.container.classList.add("ql-bubble")}extendToolbar(e){this.tooltip=new bn(this.quill,this.options.bounds),e.container!=null&&(this.tooltip.root.appendChild(e.container),this.buildButtons(e.container.querySelectorAll("button"),Mt),this.buildPickers(e.container.querySelectorAll("select"),Mt))}};He.DEFAULTS=tt({},xt.DEFAULTS,{modules:{toolbar:{handlers:{link(r){r?this.quill.theme.tooltip.edit():this.quill.format("link",!1,f.sources.USER)}}}}});var Zf=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],yn=class extends pe{static TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join("");preview=this.root.querySelector("a.ql-preview");listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){let t=this.linkRange;this.restoreFocus(),this.quill.formatText(t,"link",!1,b.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(b.events.SELECTION_CHANGE,(e,t,s)=>{if(e!=null){if(e.length===0&&s===b.sources.USER){let[n,i]=this.quill.scroll.descendant(Zt,e.index);if(n!=null){this.linkRange=new K(e.index-i,n.length());let l=Zt.formats(n.domNode);this.preview.textContent=l,this.preview.setAttribute("href",l),this.show();let o=this.quill.getBounds(this.linkRange);o!=null&&this.position(o);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}},Ir=class extends xt{constructor(e,t){t.modules.toolbar!=null&&t.modules.toolbar.container==null&&(t.modules.toolbar.container=Zf),super(e,t),this.quill.container.classList.add("ql-snow")}extendToolbar(e){e.container!=null&&(e.container.classList.add("ql-snow"),this.buildButtons(e.container.querySelectorAll("button"),Mt),this.buildPickers(e.container.querySelectorAll("select"),Mt),this.tooltip=new yn(this.quill,this.options.bounds),e.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(t,s)=>{e.handlers.link.call(e,!s.format.link)}))}};Ir.DEFAULTS=tt({},xt.DEFAULTS,{modules:{toolbar:{handlers:{link(r){if(r){let e=this.quill.getSelection();if(e==null||e.length===0)return;let t=this.quill.getText(e);/^\S+@\S+\.\S+$/.test(t)&&t.indexOf("mailto:")!==0&&(t=`mailto:${t}`);let{tooltip:s}=this.quill.theme;s.edit("link",t)}else this.quill.format("link",!1,f.sources.USER)}}}}});var ho=Ir;wr.register({"attributors/attribute/direction":br,"attributors/class/align":Ws,"attributors/class/background":ql,"attributors/class/color":Ll,"attributors/class/direction":Qs,"attributors/class/font":Ys,"attributors/class/size":Js,"attributors/style/align":gr,"attributors/style/background":Ie,"attributors/style/color":_e,"attributors/style/direction":yr,"attributors/style/font":vr,"attributors/style/size":Er},!0);wr.register({"formats/align":Ws,"formats/direction":Qs,"formats/indent":Vl,"formats/background":Ie,"formats/color":_e,"formats/font":Ys,"formats/size":Js,"formats/blockquote":Kl,"formats/code-block":U,"formats/header":Gl,"formats/list":Tr,"formats/bold":me,"formats/code":Sl,"formats/italic":Wl,"formats/link":Zt,"formats/script":Zl,"formats/strike":Ql,"formats/underline":Xl,"formats/formula":Yl,"formats/image":to,"formats/video":ro,"modules/syntax":Pe,"modules/table":no,"modules/toolbar":fn,"themes/bubble":He,"themes/snow":ho,"ui/icons":Mt,"ui/picker":Dt,"ui/icon-picker":Or,"ui/color-picker":Cr,"ui/tooltip":_r},!0);var t0=wr;var export_AttributeMap=Rt.AttributeMap;var export_Delta=Rt.default;var export_Op=Rt.Op;var export_OpIterator=Rt.OpIterator;export{export_AttributeMap as AttributeMap,export_Delta as Delta,O as Module,export_Op as Op,export_OpIterator as OpIterator,ye as Parchment,K as Range,t0 as default};
