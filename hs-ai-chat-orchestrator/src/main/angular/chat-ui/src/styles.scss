/* src/styles.scss */
/* Global styles */

// Variables
:root {
  --sidebar-width: 250px;
  --sidebar-collapsed-width: 70px;
  --topbar-height: 56px;
  --primary-color: #3f51b5;
  --secondary-color: #6c757d;
  --dark-bg: #343a40;
}

html, body {
  height: 100%;
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Card styles */
.card {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;

  .card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 500;
    padding: 0.75rem 1.25rem;
  }

  .card-title {
    margin-bottom: 0.5rem;
    font-weight: 600;
  }
}

/* Table styles */
.table {
  margin-bottom: 0;

  th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-top: none;
  }
}

/* Layout styles */
.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.layout-main {
  display: flex;
  flex: 1;
}

.content-container {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease;

  &.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
  }
}

/* Sidebar styles */
.sidebar {
  width: var(--sidebar-width);
  height: calc(100vh - var(--topbar-height));
  background-color: var(--dark-bg);
  color: rgba(255, 255, 255, 0.8);
  transition: width 0.3s ease;
  position: fixed;
  top: var(--topbar-height);
  left: 0;
  overflow-y: auto;
  z-index: 1020;

  &.collapsed {
    width: var(--sidebar-collapsed-width);
  }

  .nav-link {
    color: rgba(255, 255, 255, 0.7);

    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.active {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

/* Content area styles */
.content-wrapper {
  padding: 1.5rem;
  height: calc(100vh - var(--topbar-height));
  overflow-y: auto;
  background-color: #f5f7fa;
}
