import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { PromptsService } from '../services/prompts.service';
import * as PromptsActions from './prompts.actions';
import { catchError, map, mergeMap, of, switchMap } from 'rxjs';

@Injectable()
export class PromptsEffects {

  loadPrompts$;
  createPrompt$;
  updatePrompt$;
  deletePrompt$;

  constructor(
    private actions$: Actions,
    private promptsService: PromptsService
  ) {

    this.loadPrompts$ = createEffect(() =>
      this.actions$.pipe(
        ofType(PromptsActions.loadPrompts),
        switchMap(() =>
          this.promptsService.getPrompts().pipe(
            map(prompts => PromptsActions.loadPromptsSuccess({prompts})),
            catchError(error => of(PromptsActions.loadPromptsFailure({error: error.message})))
          )
        )
      )
    );

    this.createPrompt$ = createEffect(() =>
      this.actions$.pipe(
        ofType(PromptsActions.createPrompt),
        switchMap(({prompt}) =>
          this.promptsService.createPrompt(prompt).pipe(
            map(createdPrompt => PromptsActions.createPromptSuccess({prompt: createdPrompt})),
            catchError(error => of(PromptsActions.createPromptFailure({error: error.message})))
          )
        )
      )
    );

    this.updatePrompt$ = createEffect(() =>
      this.actions$.pipe(
        ofType(PromptsActions.updatePrompt),
        switchMap(({prompt}) =>
          this.promptsService.updatePrompt(prompt).pipe(
            map(updatedPrompt => PromptsActions.updatePromptSuccess({prompt: updatedPrompt})),
            catchError(error => of(PromptsActions.updatePromptFailure({error: error.message})))
          )
        )
      )
    );

    this.deletePrompt$ = createEffect(() =>
      this.actions$.pipe(
        ofType(PromptsActions.deletePrompt),
        switchMap(({promptId}) =>
          this.promptsService.deletePrompt(promptId).pipe(
            map(() => PromptsActions.deletePromptSuccess({promptId})),
            catchError(error => of(PromptsActions.deletePromptFailure({error: error.message})))
          )
        )
      )
    );
  }
}
