import { createReducer, on } from '@ngrx/store';
import * as DashboardActions from './dashboard.actions';

export interface DashboardState {
  metrics: any;
  loading: boolean;
  error: any;
}

export const initialState: DashboardState = {
  metrics: null,
  loading: false,
  error: null,
};

export const dashboardReducer = createReducer(
  initialState,
  on(DashboardActions.loadDashboardData, state => ({
    ...state,
    loading: true,
    error: null
  })),
  on(DashboardActions.loadDashboardDataSuccess, (state, { metrics }) => ({
    ...state,
    metrics,
    loading: false
  })),
  on(DashboardActions.loadDashboardDataFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false
  }))
);
