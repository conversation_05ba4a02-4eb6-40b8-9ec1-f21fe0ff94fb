<div class="container py-4">

  <!-- Page Title -->
  <h1 class="mb-4 fw-bold text-primary">Manage Prompts</h1>

  <!-- Search Bar + Create Button -->
  <div class="d-flex mb-3">
    <input
      type="text"
      class="form-control me-2"
      placeholder="Search Prompts..."
      [(ngModel)]="searchTerm"
      (input)="applyFilter()"
    />
    <button class="btn btn-success" (click)="openCreateDialog()">Create New Prompt</button>
  </div>

  <!-- Prompt Table -->
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead class="table-light">
      <tr>
        <!-- Title Column with Sorting -->
        <th (click)="toggleSort('title')" class="sortable">
          Title
          <span *ngIf="sortColumn === 'title'">
              <i class="fas" [ngClass]="sortDirection === 'asc' ? 'fa-arrow-up' : 'fa-arrow-down'"></i>
            </span>
        </th>

        <!-- Description Column with Sorting -->
        <th (click)="toggleSort('description')" class="sortable">
          Description
          <span *ngIf="sortColumn === 'description'">
              <i class="fas" [ngClass]="sortDirection === 'asc' ? 'fa-arrow-up' : 'fa-arrow-down'"></i>
            </span>
        </th>

        <!-- Actions Column -->
        <th class="text-center" style="width: 150px;">Actions</th>
      </tr>
      </thead>

      <tbody>
      <tr *ngFor="let prompt of pagedPrompts()">
        <td>{{ prompt.title }}</td>
        <td>{{ prompt.description }}</td>
        <td class="text-center">
          <button
            class="btn btn-sm btn-outline-primary me-1"
            (click)="openEditDialog(prompt)">
            Edit
          </button>
          <button
            class="btn btn-sm btn-outline-danger"
            (click)="deletePrompt(prompt)">
            Delete
          </button>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination Controls -->
  <nav *ngIf="totalPages() > 1" class="d-flex justify-content-center mt-3">
    <ul class="pagination">
      <!-- Previous Button -->
      <li class="page-item" [class.disabled]="currentPage === 1">
        <button
          class="page-link"
          (click)="goToPage(currentPage - 1)"
          [disabled]="currentPage === 1">
          Previous
        </button>
      </li>

      <!-- Page Numbers -->
      <li
        class="page-item"
        *ngFor="let page of [].constructor(totalPages()); let idx = index"
        [class.active]="currentPage === idx + 1">
        <button class="page-link" (click)="goToPage(idx + 1)">
          {{ idx + 1 }}
        </button>
      </li>

      <!-- Next Button -->
      <li class="page-item" [class.disabled]="currentPage === totalPages()">
        <button
          class="page-link"
          (click)="goToPage(currentPage + 1)"
          [disabled]="currentPage === totalPages()">
          Next
        </button>
      </li>
    </ul>
  </nav>

</div>

<!-- Dialog Modal -->
<div class="modal fade"
     id="promptEditorModal"
     tabindex="-1"
     aria-labelledby="promptEditorModalLabel"
     aria-hidden="true"
     [class.show]="showDialog"
     [style.display]="showDialog ? 'block' : 'none'">
  <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="promptEditorModalLabel">
          {{ isEditMode ? 'Edit Prompt' : 'Create New Prompt' }}
        </h5>
        <button type="button"
                class="btn-close"
                aria-label="Close"
                (click)="closeDialog()"></button>
      </div>
      <div class="modal-body">
        <app-prompt-editor
          *ngIf="showDialog"
          (saveComplete)="onSaveComplete()"
          (cancelled)="onCancelled()">
        </app-prompt-editor>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeDialog()">
          Cancel
        </button>
        <button type="button" class="btn btn-primary" (click)="saveFromDialog()">
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Backdrop -->
<div class="modal-backdrop fade"
     *ngIf="showDialog"
     [class.show]="showDialog"
     (click)="closeDialog()"></div>
