import { createAction, props } from '@ngrx/store';
import { Prompt } from '../models/prompt.model';

// Load Actions
export const loadPrompts = createAction('[Prompts] Load Prompts');
export const loadPromptsSuccess = createAction(
  '[Prompts] Load Prompts Success',
  props<{ prompts: Prompt[] }>()
);
export const loadPromptsFailure = createAction(
  '[Prompts] Load Prompts Failure',
  props<{ error: string }>()
);

// Create Actions
export const createPrompt = createAction(
  '[Prompts] Create Prompt',
  props<{ prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt'> }>()
);
export const createPromptSuccess = createAction(
  '[Prompts] Create Prompt Success',
  props<{ prompt: Prompt }>()
);
export const createPromptFailure = createAction(
  '[Prompts] Create Prompt Failure',
  props<{ error: string }>()
);

// Update Actions
export const updatePrompt = createAction(
  '[Prompts] Update Prompt',
  props<{ prompt: Prompt }>()
);
export const updatePromptSuccess = createAction(
  '[Prompts] Update Prompt Success',
  props<{ prompt: Prompt }>()
);
export const updatePromptFailure = createAction(
  '[Prompts] Update Prompt Failure',
  props<{ error: string }>()
);

// Delete Actions
export const deletePrompt = createAction(
  '[Prompts] Delete Prompt',
  props<{ promptId: string }>()
);
export const deletePromptSuccess = createAction(
  '[Prompts] Delete Prompt Success',
  props<{ promptId: string }>()
);
export const deletePromptFailure = createAction(
  '[Prompts] Delete Prompt Failure',
  props<{ error: string }>()
);

// Select Actions
export const selectPrompt = createAction(
  '[Prompts] Select Prompt',
  props<{ prompt: Prompt | null }>()
);
