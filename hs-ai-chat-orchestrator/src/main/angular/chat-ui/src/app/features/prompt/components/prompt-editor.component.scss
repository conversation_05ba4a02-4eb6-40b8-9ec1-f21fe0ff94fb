.card {
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

label {
  font-weight: bold;
}

input.form-control,
textarea.form-control,
.form-select {
  border-radius: 0.5rem;
  white-space: pre-line; // Added for preserving line breaks
}

.section-header {
  color: #0d6efd;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.section-description {
  font-size: 0.875rem;
  color: #6c757d;
  font-style: italic;
  margin-bottom: 0.5rem;
}

quill-editor {
  display: block;
  width: 100%;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 1rem;
  white-space: pre-line; // Added for preserving line breaks

  .ql-toolbar {
    background: #f8f9fa;
    border-bottom: 1px solid #ced4da;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    padding: 0.5rem;

    button {
      height: 30px;
      width: 30px;
      font-size: 16px;
      i {
        font-size: 16px;
      }
    }
  }

  .ql-container {
    height: 200px; /* Reduced height for multiple sections */
    overflow-y: auto;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    background: white;
    padding: 0.5rem;
    white-space: pre-line; // Added for preserving line breaks
  }

  .ql-editor {
    height: 100%;
    overflow-y: auto;
    font-size: 1rem;
    color: #212529;
    line-height: 1.5;
    padding: 0.5rem 0;
    white-space: pre-line; // Added for preserving line breaks

    &.ql-blank::before {
      font-style: italic;
      color: #6c757d;
    }
  }
}

.text-end button {
  min-width: 100px;
}

// Additional classes for content display
.prompt-content {
  white-space: pre-line;
  font-family: inherit;
  line-height: 1.5;
}

// For displaying formatted content in cards or lists
.formatted-content {
  white-space: pre-line;
  font-family: inherit;
  margin: 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.5;
}
