h1 {
  font-size: 2rem;
}

.table {
  border-radius: 0.5rem;
  overflow: hidden;
}

.page-link {
  cursor: pointer;
}

th.sortable {
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
}

th.sortable i {
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

th.sortable:hover {
  text-decoration: underline;
}

.pagination {
  margin-top: 1rem;
}

.page-link {
  cursor: pointer;
}

.page-item.disabled .page-link {
  cursor: not-allowed;
  opacity: 0.6;
}

// Dialog Modal Styles
.modal {
  z-index: 1055;

  &.show {
    animation: fadeIn 0.15s ease-in-out;
  }
}

.modal-backdrop {
  z-index: 1050;
  background-color: rgba(0, 0, 0, 0.5);

  &.show {
    opacity: 1;
  }
}

.modal-dialog {
  max-width: 90vw;
  width: 100%;
  margin: 1.75rem auto;
}

.modal-xl {
  max-width: 1200px;
}

.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.modal-header {
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
  color: white;
  border-bottom: none;
  padding: 1.5rem;

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
  }

  .btn-close {
    background: transparent;
    border: none;
    color: white;
    opacity: 0.8;
    font-size: 1.5rem;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      opacity: 1;
      transform: scale(1.1);
    }
  }
}

.modal-body {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;

  // Ensure the editor takes full width
  app-prompt-editor {
    display: block;
    width: 100%;
  }
}

.modal-footer {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 1rem 2rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;

  .btn {
    min-width: 100px;
    font-weight: 500;
  }

  .btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
      transform: translateY(-1px);
    }
  }
}

// Animation
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
  }

  .modal-body {
    padding: 1rem;
    max-height: 60vh;
  }

  .modal-header,
  .modal-footer {
    padding: 1rem;
  }
}
