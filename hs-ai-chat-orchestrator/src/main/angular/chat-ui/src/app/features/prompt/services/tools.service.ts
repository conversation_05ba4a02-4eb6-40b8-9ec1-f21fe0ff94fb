// Create this file: src/app/services/tools.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {ToolDTO, ToolOption} from '../models/prompt.model';
import {environment} from '../../../../environment/environment';

@Injectable({
  providedIn: 'root'
})
export class ToolsService {
  private readonly apiUrl = `${environment.apiUrl}/v3/assistant/api/prompts/tools`;
  private toolsSubject = new BehaviorSubject<ToolOption[]>([]);
  public tools$ = this.toolsSubject.asObservable();

  constructor(private http: HttpClient) {
    // Don't auto-load - let components decide when to load
  }

  loadTools(): void {
    console.log('ToolsService: Loading tools from API...');
    this.http.get<ToolDTO[]>(this.apiUrl)
      .pipe(
        map(tools => {
          console.log('ToolsService: Received tools from API:', tools);
          return this.transformToOptions(tools);
        }),
        catchError(error => {
          console.error('ToolsService: Failed to load tools:', error);
          // Return default options if API fails
          return this.getDefaultTools();
        })
      )
      .subscribe(options => {
        console.log('ToolsService: Setting tools:', options);
        this.toolsSubject.next(options);
      });
  }

  private transformToOptions(tools: ToolDTO[]): ToolOption[] {
    return tools.map(tool => ({
      value: tool.category,
      label: `${tool.category} - ${tool.documentation}`,
      category: tool.category,
      documentation: tool.documentation
    }));
  }

  private getDefaultTools(): Observable<ToolOption[]> {
    // Fallback options if API is unavailable
    const defaultTools: ToolOption[] = [
      {
        value: 'POINTS',
        label: 'POINTS - Member points and rewards management',
        category: 'POINTS',
        documentation: 'Member points and rewards management'
      },
      {
        value: 'ACCOUNT',
        label: 'ACCOUNT - Account balance and transaction history',
        category: 'ACCOUNT',
        documentation: 'Account balance and transaction history'
      },
      {
        value: 'GENERAL',
        label: 'GENERAL - General assistance and information',
        category: 'GENERAL',
        documentation: 'General assistance and information'
      }
    ];

    console.log('ToolsService: Using default tools:', defaultTools);
    return of(defaultTools);
  }

  getTools(): Observable<ToolOption[]> {
    return this.tools$;
  }

  refreshTools(): void {
    console.log('ToolsService: Refreshing tools...');
    this.loadTools();
  }
}
