import { Injectable } from '@angular/core';
import { of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class DashboardService {
  getDashboardMetrics() {
    return of({
      cards: [
        { label: 'Prompts Created', value: 120 },
        { label: 'Projects Used', value: 45 },
        { label: 'Questions Answered', value: 780 },
        { label: 'Documents Indexed', value: 320 }
      ],
      questionsOverTime: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
        data: [150, 200, 250, 400, 450]
      },
      documentsByType: {
        labels: ['PDF', 'Word', 'Text'],
        data: [200, 100, 20]
      },
      recentActivities: [
        { type: 'Prompt Created', details: 'Created a welcome prompt', timestamp: new Date() },
        { type: 'Document Indexed', details: 'Indexed GDPR policy', timestamp: new Date() }
      ]
    });
  }
}
