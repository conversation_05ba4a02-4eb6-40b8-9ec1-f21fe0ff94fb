import { createFeatureSelector, createSelector } from '@ngrx/store';
import { PromptsState } from './prompts.reducer';

export const selectPromptsState = createFeatureSelector<PromptsState>('prompts');

export const selectAllPrompts = createSelector(
  selectPromptsState,
  state => state.prompts
);

export const selectSelectedPrompt = createSelector(
  selectPromptsState,
  state => state.selectedPrompt
);
