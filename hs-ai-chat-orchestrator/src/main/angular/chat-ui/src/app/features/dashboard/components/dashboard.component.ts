import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { DashboardState } from '../store/dashboard.reducer';
import { loadDashboardData } from '../store/dashboard.actions';
import { selectDashboardMetrics, selectDashboardLoading, selectDashboardError } from '../store/dashboard.selectors';
import {Observable} from 'rxjs';
import {NgChartsModule} from 'ng2-charts';
import {AsyncPipe, DatePipe, NgForOf, NgIf} from '@angular/common';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  imports: [
    NgChartsModule,
    DatePipe,
    NgForOf,
    NgIf,
    AsyncPipe
  ],
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  metrics$: Observable<any> | undefined;
  loading$: Observable<boolean>| undefined;
  error$: Observable<any>| undefined;

  constructor(private readonly store: Store<DashboardState>) {}

  ngOnInit(): void {
    this.store.dispatch(loadDashboardData());

    this.metrics$ = this.store.select(selectDashboardMetrics);
    this.loading$ = this.store.select(selectDashboardLoading);
    this.error$ = this.store.select(selectDashboardError);
  }
}
