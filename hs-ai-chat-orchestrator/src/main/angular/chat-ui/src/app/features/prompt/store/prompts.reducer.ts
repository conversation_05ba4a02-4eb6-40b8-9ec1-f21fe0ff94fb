import { createReducer, on } from '@ngrx/store';
import * as PromptsActions from './prompts.actions';
import { Prompt } from '../models/prompt.model';

export interface PromptsState {
  prompts: Prompt[];
  selectedPrompt: Prompt | null;
  loading: boolean;
  error: string | null;
}

export const initialState: PromptsState = {
  prompts: [],
  selectedPrompt: null,
  loading: false,
  error: null
};

export const promptsReducer = createReducer(
  initialState,

  // Load Prompts
  on(PromptsActions.loadPrompts, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(PromptsActions.loadPromptsSuccess, (state, { prompts }) => ({
    ...state,
    prompts,
    loading: false,
    error: null
  })),

  on(PromptsActions.loadPromptsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Create Prompt
  on(PromptsActions.createPrompt, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(PromptsActions.createPromptSuccess, (state, { prompt }) => ({
    ...state,
    prompts: [...state.prompts, prompt],
    selectedPrompt: null,
    loading: false,
    error: null
  })),

  on(PromptsActions.createPromptFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Update Prompt
  on(PromptsActions.updatePrompt, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(PromptsActions.updatePromptSuccess, (state, { prompt }) => ({
    ...state,
    prompts: state.prompts.map(p => p.id === prompt.id ? prompt : p),
    selectedPrompt: null,
    loading: false,
    error: null
  })),

  on(PromptsActions.updatePromptFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Delete Prompt
  on(PromptsActions.deletePrompt, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(PromptsActions.deletePromptSuccess, (state, { promptId }) => ({
    ...state,
    prompts: state.prompts.filter(p => p.id !== promptId),
    loading: false,
    error: null
  })),

  on(PromptsActions.deletePromptFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Select Prompt
  on(PromptsActions.selectPrompt, (state, { prompt }) => ({
    ...state,
    selectedPrompt: prompt
  }))
);
