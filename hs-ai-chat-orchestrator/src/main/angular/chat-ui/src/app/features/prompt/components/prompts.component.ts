import { Component, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { Prompt } from '../models/prompt.model';
import { loadPrompts, selectPrompt, deletePrompt } from '../store/prompts.actions';
import { selectAllPrompts, selectSelectedPrompt } from '../store/prompts.selectors';
import { PromptEditorComponent } from './prompt-editor.component';
import { AsyncPipe, NgClass, NgForOf, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-prompts',
  standalone: true,
  imports: [
    PromptEditorComponent,
    NgIf,
    NgForOf,
    FormsModule,
    NgClass
  ],
  templateUrl: './prompts.component.html',
  styleUrls: ['./prompts.component.scss']
})
export class PromptsComponent implements OnInit {
  @ViewChild(PromptEditorComponent) promptEditor!: PromptEditorComponent;

  prompts$: Observable<Prompt[]> | undefined;
  selectedPrompt$: Observable<Prompt | null>;
  allPrompts: Prompt[] = [];
  filteredPrompts: Prompt[] = [];
  searchTerm: string = '';
  pageSize: number = 5;
  currentPage: number = 1;
  sortColumn: keyof Prompt = 'title';
  sortDirection: 'asc' | 'desc' = 'asc';

  // Dialog state
  showDialog: boolean = false;
  isEditMode: boolean = false;

  constructor(private store: Store) {
    this.selectedPrompt$ = this.store.select(selectSelectedPrompt);
  }

  ngOnInit(): void {
    this.prompts$ = this.store.select(selectAllPrompts);
    this.prompts$.subscribe(prompts => {
      this.allPrompts = prompts;
      this.applyFilter();
    });

    // Listen to selected prompt changes to open/close dialog
    this.selectedPrompt$.subscribe(prompt => {
      if (prompt !== null) {
        this.isEditMode = true;
        this.showDialog = true;
        document.body.classList.add('modal-open');
      }
    });

    this.store.dispatch(loadPrompts());
  }

  applyFilter() {
    const search = this.searchTerm.toLowerCase();
    this.filteredPrompts = this.allPrompts.filter(p =>
      p.title.toLowerCase().includes(search) ||
      p.description.toLowerCase().includes(search)
    );
    this.sortPrompts();
    this.currentPage = 1;
  }

  sortPrompts() {
    this.filteredPrompts.sort((a, b) => {
      const valA = (a[this.sortColumn] || '').toString().toLowerCase();
      const valB = (b[this.sortColumn] || '').toString().toLowerCase();
      if (valA < valB) return this.sortDirection === 'asc' ? -1 : 1;
      if (valA > valB) return this.sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }

  toggleSort(column: keyof Prompt) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }
    this.sortPrompts();
  }

  pagedPrompts(): Prompt[] {
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    return this.filteredPrompts.slice(start, end);
  }

  totalPages(): number {
    return Math.ceil(this.filteredPrompts.length / this.pageSize);
  }

  // Dialog methods
  openEditDialog(prompt: Prompt) {
    this.isEditMode = true;
    this.showDialog = true;
    this.store.dispatch(selectPrompt({ prompt }));
    document.body.classList.add('modal-open');
  }

  openCreateDialog() {
    this.isEditMode = false;
    this.showDialog = true;
    this.store.dispatch(selectPrompt({ prompt: null }));
    document.body.classList.add('modal-open');
  }

  closeDialog() {
    this.showDialog = false;
    this.isEditMode = false;
    this.store.dispatch(selectPrompt({ prompt: null }));
    document.body.classList.remove('modal-open');
  }

  saveFromDialog() {
    // Trigger save from the prompt editor component
    if (this.promptEditor) {
      this.promptEditor.save();
      // Dialog will close via onSaveComplete() event
    }
  }

  // Event handlers from prompt editor
  onSaveComplete() {
    this.closeDialog();
  }

  onCancelled() {
    this.closeDialog();
  }

  deletePrompt(prompt: Prompt) {
    if (confirm(`Are you sure you want to delete prompt: "${prompt.title}"?`)) {
      this.store.dispatch(deletePrompt({ promptId: prompt.id! }));
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage = page;
    }
  }
}
