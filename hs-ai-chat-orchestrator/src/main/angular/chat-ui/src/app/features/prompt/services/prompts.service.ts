import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Prompt } from '../models/prompt.model';
import {environment} from '../../../../environment/environment';

@Injectable({ providedIn: 'root' })
export class PromptsService {
  private readonly apiUrl = `${environment.apiUrl}/v3/assistant/api/prompts`;

  constructor(private http: HttpClient) {}

  getPrompts(): Observable<Prompt[]> {
    return this.http.get<Prompt[]>(this.apiUrl)
      .pipe(
        catchError(this.handleError)
      );
  }

  getPrompt(id: string): Observable<Prompt> {
    return this.http.get<Prompt>(`${this.apiUrl}/${id}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  createPrompt(prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt'>): Observable<Prompt> {
    return this.http.post<Prompt>(this.apiUrl, prompt)
      .pipe(
        catchError(this.handleError)
      );
  }

  updatePrompt(prompt: Prompt): Observable<Prompt> {
    return this.http.put<Prompt>(`${this.apiUrl}/${prompt.id}`, prompt)
      .pipe(
        catchError(this.handleError)
      );
  }

  deletePrompt(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server Error: ${error.status} - ${error.message}`;
    }

    console.error('PromptsService Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
