<div class="container-fluid py-4">

  <h1 class="mb-4 fw-bold text-primary">ChatAI Dashboard</h1>

  <!-- Loading -->
  <div *ngIf="loading$ | async" class="text-center my-5">
    <div class="spinner-border text-primary"></div>
    <p class="text-muted">Loading dashboard data...</p>
  </div>

  <!-- Error -->
  <div *ngIf="error$ | async as error" class="alert alert-danger">
    Error loading dashboard: {{ error }}
  </div>

  <!-- Metrics -->
  <div *ngIf="metrics$ | async as metrics">

    <div class="row g-3">
      <div class="col-md-3" *ngFor="let card of metrics.cards">
        <div class="card h-100 shadow-sm border-0 rounded-4 text-center">
          <div class="card-body d-flex flex-column justify-content-center align-items-center">
            <h2 class="fw-bold text-primary">{{ card.value }}</h2>
            <p class="text-muted mb-0">{{ card.label }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts -->
    <div class="row g-4 mt-4">
      <div class="col-md-6">
        <div class="card shadow-sm border-0 rounded-4">
          <div class="card-header bg-white border-0 fw-bold text-primary">Questions Answered Over Time</div>
          <div class="card-body">
            <canvas baseChart
                    [datasets]="[{ data: metrics.questionsOverTime.data, label: 'Questions Answered' }]"
                    [labels]="metrics.questionsOverTime.labels"
                    [options]="{ responsive: true, maintainAspectRatio: false }"
                    [legend]="true"
                    [height]="300"
                    chartType="line">
            </canvas>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="card shadow-sm border-0 rounded-4">
          <div class="card-header bg-white border-0 fw-bold text-primary">Documents Indexed by Type</div>
          <div class="card-body">
            <canvas baseChart
                    [datasets]="[{ data: metrics.documentsByType.data, label: 'Documents' }]"
                    [labels]="metrics.documentsByType.labels"
                    [options]="{ responsive: true, maintainAspectRatio: false }"
                    [legend]="true"
                    [height]="300"
                    chartType="doughnut">
            </canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activities -->
    <div class="card mt-5 shadow-sm border-0 rounded-4">
      <div class="card-header bg-white border-0 fw-bold text-primary">
        Recent Activities
      </div>
      <div class="card-body p-0">
        <ul class="list-group list-group-flush">
          <li *ngFor="let activity of metrics.recentActivities" class="list-group-item d-flex justify-content-between align-items-center">
            <div>
              <span class="fw-bold">{{ activity.type }}</span> -
              <span class="text-muted">{{ activity.details }}</span>
            </div>
            <small class="text-muted">{{ activity.timestamp | date:'short' }}</small>
          </li>
        </ul>
      </div>
    </div>

  </div>

</div>
