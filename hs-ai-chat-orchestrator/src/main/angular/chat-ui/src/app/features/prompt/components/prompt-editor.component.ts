import { Compo<PERSON>, On<PERSON><PERSON>roy, Output, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { selectSelectedPrompt } from '../store/prompts.selectors';
import { Prompt, ToolDTO, ToolOption } from '../models/prompt.model';
import { updatePrompt, createPrompt, selectPrompt } from '../store/prompts.actions';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormsModule } from '@angular/forms';
import { QuillEditorComponent } from 'ngx-quill';
import {NgIf, NgForOf, AsyncPipe, NgClass} from '@angular/common';
import { ToolsService } from '../services/tools.service';

@Component({
  selector: 'app-prompt-editor',
  templateUrl: './prompt-editor.component.html',
  standalone: true,
  imports: [
    FormsModule,
    QuillEditorComponent,
    NgI<PERSON>,
    <PERSON><PERSON>orO<PERSON>,
    AsyncPipe,
    NgClass
  ],
  styleUrls: ['./prompt-editor.component.scss']
})
export class PromptEditorComponent implements OnInit, OnDestroy {
  @Output() saveComplete = new EventEmitter<void>();
  @Output() cancelled = new EventEmitter<void>();

  selectedPrompt$: Observable<Prompt | null>;
  tools$: Observable<ToolOption[]>;
  private destroy$ = new Subject<void>();
  isLoadingTools = false;

  // Quill configuration
  quillModules = {
    toolbar: [
      ['bold', 'italic', 'underline'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['clean']
    ]
  };

  quillFormats = [
    'bold', 'italic', 'underline', 'list'
  ];

  localPrompt: Prompt = {
    title: '',
    description: '',
    keywords: [],
    sourceTool: '',
    context: '',
    data: '',
    rules: '',
    output: '',
    unbreakableCompliance: ''
  };

  keywordsString: string = '';

  constructor(
    private store: Store,
    private toolsService: ToolsService
  ) {
    this.selectedPrompt$ = this.store.select(selectSelectedPrompt);
    this.tools$ = this.toolsService.getTools();
  }

  ngOnInit(): void {
    // Load tools when component initializes (dialog opens)
    console.log('Dialog opened, loading tools...');
    this.loadTools();

    this.selectedPrompt$
      .pipe(takeUntil(this.destroy$))
      .subscribe(prompt => {
        console.log('Selected prompt changed:', prompt);
        if (prompt) {
          console.log('Prompt ID:', prompt.id);
          this.localPrompt = {
            ...prompt,
            keywords: prompt.keywords ? [...prompt.keywords] : []
          };
          this.keywordsString = prompt.keywords ? prompt.keywords.join(', ') : '';
        } else {
          this.localPrompt = {
            title: '',
            description: '',
            keywords: [],
            sourceTool: '',
            context: '',
            data: '',
            rules: '',
            output: '',
            unbreakableCompliance: ''
          };
          this.keywordsString = '';
        }
        console.log('Local prompt after assignment:', this.localPrompt);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadTools(): void {
    console.log('Loading tools from API...');
    this.isLoadingTools = true;
    this.toolsService.loadTools();

    // Reset loading state after a short delay
    setTimeout(() => {
      this.isLoadingTools = false;
    }, 1000);
  }

  refreshTools(): void {
    console.log('Refreshing tools...');
    this.isLoadingTools = true;
    this.toolsService.refreshTools();

    // Reset loading state after a short delay
    setTimeout(() => {
      this.isLoadingTools = false;
    }, 1000);
  }

  updateKeywords() {
    this.localPrompt.keywords = this.keywordsString
      .split(',')
      .map(k => k.trim())
      .filter(k => k.length > 0);
  }

  save() {
    console.log('Saving prompt. ID exists?', !!this.localPrompt.id);
    console.log('Full prompt object:', this.localPrompt);

    this.updateKeywords(); // Ensure keywords are updated

    if (this.localPrompt.id) {
      console.log('Dispatching UPDATE action');
      this.store.dispatch(updatePrompt({ prompt: this.localPrompt }));
    } else {
      console.log('Dispatching CREATE action');
      console.log('Prompt without ID:', this.localPrompt);
      const { id, createdAt, updatedAt, ...promptWithoutId } = this.localPrompt;
      this.store.dispatch(createPrompt({ prompt: promptWithoutId }));
    }

    // Clear selection and emit save complete
    this.store.dispatch(selectPrompt({ prompt: null }));
    this.saveComplete.emit();
  }

  cancel() {
    this.store.dispatch(selectPrompt({ prompt: null }));
    this.cancelled.emit();
  }
}
