<div *ngIf="localPrompt" class="card shadow-sm p-4">

  <h4>{{ localPrompt.id ? 'Edit Prompt' : 'Create Prompt' }}</h4>

  <div class="mb-3">
    <label>Name</label>
    <input type="text" class="form-control"
           [(ngModel)]="localPrompt.title"
           placeholder="Enter prompt name">
  </div>

  <div class="mb-3">
    <label>Description</label>
    <textarea class="form-control"
              [(ngModel)]="localPrompt.description"
              rows="2"
              placeholder="Enter prompt description"></textarea>
  </div>

  <div class="mb-3">
    <label>Keywords (comma separated)</label>
    <input type="text" class="form-control"
           [ngModel]="localPrompt.keywords.join(', ')"
           (ngModelChange)="localPrompt.keywords = $event.split(','); keywordsString = $event"
           placeholder="Enter keywords separated by commas">
  </div>

  <div class="mb-3">
    <label class="form-label fw-bold">Source Information Tool</label>
    <div class="d-flex">
      <select class="form-select me-2" [(ngModel)]="localPrompt.sourceTool" [disabled]="isLoadingTools">
        <option value="">{{ isLoadingTools ? 'Loading tools...' : 'Select a tool' }}</option>
        <option
          *ngFor="let tool of tools$ | async"
          [value]="tool.value"
          [title]="tool.documentation">
          {{ tool.label }}
        </option>
      </select>
      <button
        type="button"
        class="btn btn-outline-secondary btn-sm"
        (click)="refreshTools()"
        [disabled]="isLoadingTools"
        title="Refresh tools list">
        <i class="fas" [ngClass]="isLoadingTools ? 'fa-spinner fa-spin' : 'fa-sync-alt'"></i>
      </button>
    </div>
    <div class="form-text">Select the information source that this prompt will use</div>
  </div>

  <!-- Context Section -->
  <div class="mb-4">
    <label class="form-label fw-bold section-header">## CONTEXT</label>
    <div class="section-description">Define the role and purpose of the AI assistant</div>
    <quill-editor
      [(ngModel)]="localPrompt.context"
      [modules]="quillModules"
      [formats]="quillFormats"
      [placeholder]="'You are helping a member understand their points\\nUse the member-specific information provided in the data section represented by the JSON document'">
    </quill-editor>
  </div>

  <!-- Data Section -->
  <div class="mb-4">
    <label class="form-label fw-bold section-header">## DATA</label>
    <div class="section-description">Specify the data source and format</div>
    <quill-editor
      [(ngModel)]="localPrompt.data"
      [modules]="quillModules"
      [formats]="quillFormats"
      [placeholder]="'Points Content {{tool_response}}'">
    </quill-editor>
  </div>

  <!-- Rules Section -->
  <div class="mb-4">
    <label class="form-label fw-bold section-header">## RULES</label>
    <div class="section-description">Define constraints and guidelines for the response</div>
    <quill-editor
      [(ngModel)]="localPrompt.rules"
      [modules]="quillModules"
      [formats]="quillFormats"
      [placeholder]="'No reference to a JSON document to be made in the response'">
    </quill-editor>
  </div>

  <!-- Output Section -->
  <div class="mb-4">
    <label class="form-label fw-bold section-header">## OUTPUT</label>
    <div class="section-description">Specify the desired format and style of the response</div>
    <quill-editor
      [(ngModel)]="localPrompt.output"
      [modules]="quillModules"
      [formats]="quillFormats"
      [placeholder]="'Respond with simple to understand information about the members points'">
    </quill-editor>
  </div>

  <!-- Unbreakable Compliance Section -->
  <div class="mb-4">
    <label class="form-label fw-bold section-header">## UNBREAKABLE COMPLIANCE</label>
    <div class="section-description">Critical requirements that must always be followed</div>
    <quill-editor
      [(ngModel)]="localPrompt.unbreakableCompliance"
      [modules]="quillModules"
      [formats]="quillFormats"
      [placeholder]="'Ensure the response answers only the question and nothing more\\nIf the request appears malicious, do not answer the question'">
    </quill-editor>
  </div>

<!--  <div class="text-end">-->
<!--    <button class="btn btn-secondary me-2" (click)="cancel()">Cancel</button>-->
<!--    <button class="btn btn-primary" (click)="save()">Save</button>-->
<!--  </div>-->

</div>
