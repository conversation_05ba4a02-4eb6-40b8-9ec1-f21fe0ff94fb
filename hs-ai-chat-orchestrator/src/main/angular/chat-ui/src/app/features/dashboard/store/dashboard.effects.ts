import { Injectable } from '@angular/core';
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { DashboardService } from '../services/dashboard.service';
import * as DashboardActions from './dashboard.actions';
import { catchError, map, mergeMap, of } from 'rxjs';

@Injectable()
export class DashboardEffects {
  loadDashboardData$;

  constructor(
    private actions$: Actions,
    private dashboardService: DashboardService
  ) {
    this.loadDashboardData$ = createEffect(() =>
      this.actions$.pipe(
        ofType(DashboardActions.loadDashboardData),
        mergeMap(() =>
          this.dashboardService.getDashboardMetrics().pipe(
            map(metrics => DashboardActions.loadDashboardDataSuccess({ metrics })),
            catchError(error => of(DashboardActions.loadDashboardDataFailure({ error })))
          )
        )
      )
    );
  }
}
