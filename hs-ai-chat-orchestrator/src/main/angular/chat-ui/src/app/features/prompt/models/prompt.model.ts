
export interface Prompt {
  id?: string;
  title: string;
  description: string;
  keywords: string[];
  sourceTool: string;
  context: string;
  data: string;
  rules: string;
  output: string;
  unbreakableCompliance: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ToolDTO {
  category: string;
  documentation: string;
}

export interface ToolOption {
  value: string;      // category
  label: string;      // category + "-" + documentation
  category: string;
  documentation: string;
}
