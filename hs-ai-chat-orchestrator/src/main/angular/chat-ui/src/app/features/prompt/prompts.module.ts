import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PromptsComponent } from './components/prompts.component';
import { PromptEditorComponent } from './components/prompt-editor.component';
import { PromptsRoutingModule } from './prompts-routing.module';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { promptsReducer } from './store/prompts.reducer';
import { PromptsEffects } from './store/prompts.effects';
import { FormsModule } from '@angular/forms';
import { QuillModule } from 'ngx-quill';

@NgModule({
  declarations: [

  ],
  imports: [
    CommonModule,
    FormsModule,
    QuillModule.forRoot(),
    PromptsRoutingModule,
    StoreModule.forFeature('prompts', promptsReducer),
    EffectsModule.forFeature([PromptsEffects]),
    PromptsComponent,
    PromptEditorComponent
  ]
})
export class PromptsModule {}
