/* src/app/layout/top-bar/top-bar.component.scss */
.navbar {
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .navbar-brand {
    font-weight: 600;

    i {
      color: #ffc107;
    }
  }

  .navbar-nav {
    display: flex;
    flex-direction: row;
    align-items: center;

    .nav-item {
      margin-left: 0.5rem;
    }

    .nav-link {
      padding: 0.5rem;
      position: relative;

      .badge {
        font-size: 0.6rem;
        padding: 0.25em 0.4em;
      }
    }
  }

  .dropdown-menu {
    right: 0;
    left: auto;
    margin-top: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);

    .dropdown-item {
      padding: 0.5rem 1rem;

      i {
        width: 1rem;
        text-align: center;
      }
    }
  }
}

/* Make sure navbar appears correctly at different screen sizes */
@media (max-width: 991.98px) {
  .navbar-nav {
    flex-direction: column !important;

    .nav-item {
      margin-left: 0 !important;
      margin-bottom: 0.5rem;
    }
  }
}
