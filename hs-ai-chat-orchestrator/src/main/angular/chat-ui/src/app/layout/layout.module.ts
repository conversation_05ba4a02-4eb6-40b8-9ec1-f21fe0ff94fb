// src/app/layout/layout.module.ts
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TopBarComponent } from './top-bar/top-bar.component';
import { SideNavComponent } from './side-nav/side-nav.component';
import { ContentComponent } from './content/content.component';
import { MainLayoutComponent } from './main-layout/main-layout.component';

@NgModule({
  declarations: [
    TopBarComponent,
    SideNavComponent,
    ContentComponent,
    MainLayoutComponent
  ],
  imports: [
    CommonModule,
    RouterModule,

  ],
  exports: [
    TopBarComponent,
    SideNavComponent,
    ContentComponent,
    MainLayoutComponent
  ]
})
export class LayoutModule { }
