<div class="sidebar" [ngClass]="{'collapsed': isCollapsed}">
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" routerLink="/dashboard" routerLinkActive="active">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    <span class="nav-label" *ngIf="!isCollapsed">Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" routerLink="/prompt" routerLinkActive="active">
                    <i class="fas fa-user-plus nav-icon"></i>
                    <span class="nav-label" *ngIf="!isCollapsed">Prompts</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" routerLink="/matched-members" routerLinkActive="active">
                    <i class="fas fa-link nav-icon"></i>
                    <span class="nav-label" *ngIf="!isCollapsed">Documents</span>
                </a>
            </li>
        </ul>
    </div>
</div>
