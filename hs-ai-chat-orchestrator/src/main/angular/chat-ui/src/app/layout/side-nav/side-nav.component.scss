/* src/app/layout/side-nav/side-nav.component.scss */
.sidebar {
  width: var(--sidebar-width);
  min-height: calc(100vh - var(--topbar-height));
  background-color: var(--dark-bg);
  color: rgba(255, 255, 255, 0.8);
  transition: width 0.3s ease;
  position: fixed;
  top: var(--topbar-height);
  left: 0;
  overflow-y: auto;
  z-index: 1020;

  &.collapsed {
    width: var(--sidebar-collapsed-width);
  }

  .sidebar-content {
    padding: 1rem 0;
  }

  .nav-item {
    margin-bottom: 0.25rem;
  }

  .nav-link {
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    border-radius: 0;

    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.active {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.2);
      border-left: 3px solid #ffeb3b;
    }

    .nav-icon {
      width: 20px;
      text-align: center;
      margin-right: 1rem;
      font-size: 1rem;
    }
  }
}
