// src/app/app.routes.ts
import { Routes } from '@angular/router';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';

export const routes: Routes = [
    {
        path: '',
        component: MainLayoutComponent,  // This comes from the LayoutModule
        children: [
            {
                path: '',
                redirectTo: 'dashboard',
                pathMatch: 'full'
            },
            {
                path: 'dashboard',
                loadChildren: () => import('./features/dashboard/dashboard.module')
                    .then(m => m.DashboardModule)
            },
          {
            path: 'prompt',
            loadChildren: () => import('./features/prompt/prompts.module')
              .then(m => m.PromptsModule)
          }
        ]
    }
];
