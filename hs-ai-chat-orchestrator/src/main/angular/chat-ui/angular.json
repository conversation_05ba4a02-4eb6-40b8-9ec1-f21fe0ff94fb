{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"member-matcher": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/member-matcher", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/quill/dist/quill.snow.css", "src/styles.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "spring": {"outputPath": "../../resources/static", "baseHref": "/v3/assistant/", "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "9mb"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "member-matcher:build:production"}, "development": {"buildTarget": "member-matcher:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "src/styles.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}}}}}}