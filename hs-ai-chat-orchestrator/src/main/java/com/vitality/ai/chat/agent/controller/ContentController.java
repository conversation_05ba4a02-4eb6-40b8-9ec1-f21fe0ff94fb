package com.vitality.ai.chat.agent.controller;

import com.vitality.ai.chat.agent.delegate.ChatDelegate;
import com.vitality.ai.chat.agent.delegate.ContentDelegate;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ContentController {

    private final ContentDelegate chatDelegate;


    @GetMapping("/load")
    public void loadContent() throws IOException {
        chatDelegate.loadContent();
    }

    @GetMapping("/removeAll")
    public void removeAll() throws IOException {
        chatDelegate.removeAll();
    }

}
