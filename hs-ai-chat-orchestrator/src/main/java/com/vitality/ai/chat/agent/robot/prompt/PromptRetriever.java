package com.vitality.ai.chat.agent.robot.prompt;

import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.model.embedding.EmbeddingModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.vitality.ai.chat.agent.util.Similarity.cosineSimilarity;

@Service
@RequiredArgsConstructor
public class PromptRetriever {

    private final EmbeddingModel embeddingModel;
    private final PromptDefinitionRepository repository;

    public List<PromptDefinition> findRelevantPrompts(String userInput, ChatMemory memorySummary) {
        final List<Float> inputEmbedding = embeddingModel.embed(userInput).content().vectorAsList();

        return repository.allPrompts().stream()
                .map(prompt -> new AbstractMap.SimpleEntry<>(prompt, cosineSimilarity(prompt.getEmbedding(), inputEmbedding)))
                .sorted(Map.Entry.<PromptDefinition, Double>comparingByValue().reversed())
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }



}
