package com.vitality.ai.chat.agent.robot.fullfillment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AgentFactory implements ApplicationContextAware {

    @Autowired
    List<AgentFiller> allAgentFillers = new ArrayList<>();

    @Autowired
    ObjectMapper objectMapper;

    final Map<String, List<AgentModel>> annotatedMethods = new HashMap<>();

    private ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        allAgentFillers.forEach(operator -> {
            List<AgentModel> annotatedMethodsList = AgentScanner.findAnnotatedMethods(operator);
            annotatedMethodsList.forEach(formulaModel -> {
                annotatedMethods.computeIfAbsent(formulaModel.getCategory(), s -> new ArrayList<>()).add(formulaModel);
            });
        });
    }

    public List<AgentModel> getPotentialModels(String category) {
        return annotatedMethods.get(category);
    }

    public List<AgentModel> getAllTools() {
        List<AgentModel> returnAnnotatedMethods = new ArrayList<>();
        annotatedMethods.forEach((key, value) -> {
            returnAnnotatedMethods.addAll(value);
        });
        return returnAnnotatedMethods;
    }

    public String completeRequest(String category, String beanName) {
        List<AgentModel> potentialModels = getPotentialModels(category);
        if (potentialModels != null) {
            for (AgentModel model : potentialModels) {
                if (model.getBeanName().equals(beanName)) { //Can only be 1
                    final Object myBean = applicationContext.getBean(beanName, Object.class);
                    final Class<?> beanClass = myBean.getClass();
                    try {
                        final Method method = beanClass.getMethod(model.getMethodName());
                        method.setAccessible(true);
                        final Object invoke = method.invoke(myBean, new Object[]{});
                        if (invoke != null) {
                            final String value = objectMapper.writeValueAsString(invoke);
                            final StringBuffer stringBuffer = new StringBuffer();
                            stringBuffer.append("-----");
                            stringBuffer.append(category + "\n");
                            stringBuffer.append(value + "\n");
                            stringBuffer.append("-----");
                            return stringBuffer.toString();
                        }
                    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                        throw new RuntimeException(e);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    //FindAgent to Find




}
