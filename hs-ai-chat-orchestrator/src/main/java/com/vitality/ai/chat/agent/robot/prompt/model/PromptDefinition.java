package com.vitality.ai.chat.agent.robot.prompt.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PromptDefinition {

    private  Long id;
    private  String name;
    private  String description;
    private  List<String> keywords;
    private  List<String> boosts;
    private  String content;
    private Map<String, String> sections;
    private  List<Float> embedding;
    private  String agentCategory;

}
