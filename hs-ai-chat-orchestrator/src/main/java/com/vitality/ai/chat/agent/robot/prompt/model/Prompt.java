package com.vitality.ai.chat.agent.robot.prompt.model;

import com.vitality.ai.chat.agent.robot.retriever.IntelligentContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public  class Prompt {
    private Long promptId;
    private String fullPrompt;
    private String promptName;
    private String context;
    private ArrayList<String> personContent;
    private String userInput;
    private List<IntelligentContent> contextDocs;
}