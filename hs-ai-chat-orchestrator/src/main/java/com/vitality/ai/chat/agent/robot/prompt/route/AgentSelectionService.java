package com.vitality.ai.chat.agent.robot.prompt.route;

import com.vitality.ai.chat.agent.robot.content.ContentRetriever;
import com.vitality.ai.chat.agent.robot.fullfillment.AgentFactory;
import com.vitality.ai.chat.agent.robot.prompt.dataType.DataType;
import com.vitality.ai.chat.agent.robot.prompt.model.Prompt;
import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.agent.robot.prompt.route.model.PromptData;
import com.vitality.ai.chat.agent.robot.prompt.speach.ComplianceControl;
import com.vitality.ai.chat.agent.robot.retriever.IntelligentContent;
import dev.langchain4j.memory.ChatMemory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.vitality.ai.chat.agent.robot.prompt.dataType.DataType.CONTEXT;
import static com.vitality.ai.chat.agent.robot.prompt.dataType.DataType.TOOL_RESPONSE;

@Service
@RequiredArgsConstructor
@Slf4j
public class AgentSelectionService {

    private final PromptSelectorService promptSelectorService; // ✅ new injection
    private final ContentRetriever retriever;
    private final AgentFactory agentFactory;
    private final ComplianceControl complianceControl;


    //@Todo - Refactor this method to be more readable and maintainable
    public Prompt determinePromptForIntent(String userInput, ChatMemory memory) {
        final PromptData.PromptDataBuilder builder = PromptData.builder();

        final PromptDefinition promptDefinition = promptSelectorService.selectPrompt(userInput, memory);
        builder.promptDefinition(promptDefinition);

        //Populate the prompt with data that is contextual and relevant

        final String template = promptDefinition.getContent();
        final List<DataType> placeholders = extractPlaceholders(template);
        builder.placeholders(placeholders);
        //Tools
        final ArrayList<String> contentDetails = populateToolRequest(promptDefinition);
        builder.toolResponses(contentDetails);
        //Content
        List<IntelligentContent> contextDocs = new ArrayList<>();
        if (placeholders.contains(CONTEXT)) {
            contextDocs = populateDataLookupRequests(userInput, promptDefinition);
            final String context = contextDocs.stream()
                    .map(x -> x.textSegment().text())
                    .collect(Collectors.joining("\n\n"));
            builder.context(context);
        }



        //End result
        PromptData build = builder.build();
        String fullPrompt = createCompletedPromptRequest(build);

        fullPrompt = complianceControl.apply(fullPrompt);
        log.info("Full Prompt: {}", fullPrompt);
        return Prompt.builder()
                .promptId(build.getPromptDefinition().getId())
                .fullPrompt(fullPrompt)
                .promptName(build.getPromptDefinition().getName())
                .context(build.getContext())
                .personContent(build.getToolResponses())
                .contextDocs(contextDocs)
                .userInput(userInput)
                .build();
    }

    private String createCompletedPromptRequest(PromptData build) {
        String template = build.getPromptDefinition().getContent();
        for (DataType placeholder : build.getPlaceholders()) {
            switch (placeholder) {
                case CONTEXT:
                    template = template.replace("{{" + CONTEXT.getType() +"}}", build.getContext());
                    break;
                case TOOL_RESPONSE:
                    template = template.replace("{{" + TOOL_RESPONSE.getType() +"}}", String.join(", ", build.getToolResponses()));
                    break;
                default:
                    template = template.replace("{{" + placeholder + "}}", "");
                    break;
            }
        }
        return template;
    }

    @NotNull
    private List<IntelligentContent> populateDataLookupRequests(String userInput, PromptDefinition promptDefinition) {
        final List<IntelligentContent> contextDocs = new ArrayList<>();
            final List<String> boosts = promptDefinition.getBoosts();
            final String aidedRequest = userInput + " - " + String.join(", ", boosts);
            contextDocs.addAll(retriever.retrieve(aidedRequest));
        return contextDocs;
    }

    @NotNull
    private ArrayList<String> populateToolRequest(PromptDefinition promptDefinition) {
        final ArrayList<String> contentDetails = new ArrayList<>();
        if (promptDefinition.getAgentCategory() != null && !promptDefinition.getAgentCategory().isBlank()) {
            contentDetails.addAll(agentFactory.getPotentialModels(promptDefinition.getAgentCategory())
                    .stream()
                    .map(agentModel -> {
                        //@Todo needs to be more robust.
                        String completeRequest = agentFactory.completeRequest(agentModel.getCategory(), agentModel.getBeanName());
                        return completeRequest;
                    })
                    .filter(request -> request != null)
                    .collect(Collectors.toCollection(ArrayList::new)));
        }
        return contentDetails;
    }

    public static List<DataType> extractPlaceholders(String input) {
        List<DataType> placeholders = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\{\\{(.*?)}}");
        Matcher matcher = pattern.matcher(input);

        while (matcher.find()) {
            String group = matcher.group(1);
            placeholders.add(DataType.fromString(group)); // Just the `xxx` part
        }

        return placeholders;
    }

}
