package com.vitality.ai.chat.agent.robot.memory;

import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
//@Todo should really be saved message from either a database or a cache
public class MemoryManager {

    private final Map<String, ChatMemory> memoryBySession = new ConcurrentHashMap<>();

    public ChatMemory get(String sessionId) {
        return memoryBySession.computeIfAbsent(sessionId, id ->
                MessageWindowChatMemory.withMaxMessages(20)
        );
    }

    public void clear(String sessionId) {
        memoryBySession.remove(sessionId);
    }
}