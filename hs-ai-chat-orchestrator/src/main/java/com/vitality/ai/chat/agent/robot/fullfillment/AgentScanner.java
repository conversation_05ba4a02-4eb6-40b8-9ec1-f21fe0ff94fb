package com.vitality.ai.chat.agent.robot.fullfillment;


import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class to scan and retrieve methods annotated with @FunctionalMethod.
 */
public class AgentScanner {

    public static List<AgentModel> findAnnotatedMethods(AgentFiller clazz) {
        final List<AgentModel> annotatedMethods = new ArrayList<>();
            for (final Method method : clazz.getClass().getDeclaredMethods()) {
                if (method.isAnnotationPresent(AgentFullfillment.class)) {
                    final AgentFullfillment annotation = method.getAnnotation(AgentFullfillment.class);
                    final Service serviceAnnotation = clazz.getClass().getAnnotation(Service.class);
                    String beanName = serviceAnnotation.value();
                    if (StringUtils.isEmpty(beanName)) {
                        String simpleName = serviceAnnotation.getClass().getSimpleName();
                        beanName = StringUtils.uncapitalize(simpleName);
                    }
                    final AgentModel.AgentModelBuilder builder = AgentModel.builder()
                            .category(annotation.category())
                            .methodName(annotation.methodName())
                            .beanName(beanName)
                            .documentation(annotation.documentation());

                    AgentModel build = builder.build();
                    annotatedMethods.add(build);
                }
            }
        return annotatedMethods;
    }


}

