package com.vitality.ai.chat.agent.database;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RagExecutionLogRepository {
    //@Todo save to real database
    public void save(Object o) {
        log.info("----------------------------------------");
        log.info(o.toString());
        log.info("----------------------------------------");
    }
}
