package com.vitality.ai.chat.ui.service;

import com.vitality.ai.chat.agent.robot.fullfillment.AgentFactory;
import com.vitality.ai.chat.agent.robot.fullfillment.AgentModel;
import com.vitality.ai.chat.agent.robot.prompt.PromptRetrieverService;
import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.database.databaseMapping.Prompt;
import com.vitality.ai.chat.database.databaseMapping.PromptAgentCategory;
import com.vitality.ai.chat.database.databaseMapping.PromptKeywords;
import com.vitality.ai.chat.database.databaseMapping.PromptTemplate;
import com.vitality.ai.chat.ui.exception.DuplicatePromptException;
import com.vitality.ai.chat.ui.exception.PromptNotFoundException;
import com.vitality.ai.chat.ui.model.PromptDTO;
import com.vitality.ai.chat.ui.model.PromptSections;
import com.vitality.ai.chat.ui.model.ToolDTO;
import dev.langchain4j.agent.tool.P;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class DatabasePromptService implements PromptServiceDelegate {

    private final PromptRetrieverService promptRetrieverService;
    private final AgentFactory agentFactory;

    public List<PromptDTO> getAllPrompts() {
        Map<String, PromptDefinition> general = promptRetrieverService.getPromptsInDatabase("general");
        List<PromptDTO> collect = general.values().stream()
                .map(promptDefinition -> {
                    return convertToDto(promptDefinition);

                })
                .sorted(Comparator.comparing(PromptDTO::getId))
                .toList();
        return collect;
    }

    @Override
    public List<ToolDTO> getAllTools() {
        log.info("Fetching all tools");
        try {
            List<AgentModel> allTools = agentFactory.getAllTools();
            return allTools.stream()
                    .map(tool -> new ToolDTO(tool.getCategory(), tool.getDocumentation()))
                    .sorted(Comparator.comparing(ToolDTO::getCategory))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error reading tools", e);
            return Collections.emptyList();
        }
    }

    private static PromptDTO convertToDto(PromptDefinition promptDefinition) {
        return PromptDTO.builder()
                .id(promptDefinition.getId().toString())
                .rules(promptDefinition.getSections().get(PromptSections.RULES.getSectionName()))
                .context(promptDefinition.getSections().get(PromptSections.CONTEXT.getSectionName()))
                .data(promptDefinition.getSections().get(PromptSections.DATA.getSectionName()))
                .output(promptDefinition.getSections().get(PromptSections.OUTPUT.getSectionName()))
                .unbreakableCompliance(promptDefinition.getSections().get(PromptSections.UNBREAKABLE_COMPLIANCE.getSectionName()))
                .title(promptDefinition.getName())
                .description(promptDefinition.getDescription())
                .keywords(promptDefinition.getKeywords())
                .sourceTool(promptDefinition.getAgentCategory())
                .build();
    }

    public PromptDTO getPromptById(String id) {
        log.info("Fetching prompt with id: {}", id);
        PromptDefinition byId = promptRetrieverService.findById(id);
        if (byId == null) {
            log.error("Prompt with id {} not found", id);
            throw new PromptNotFoundException("Prompt not found with id: " + id);
        }
        return convertToDto(byId);
    }

    @Transactional
    public PromptDTO createPrompt(PromptDTO promptDTO) {
        final Prompt prompt = new Prompt();
        prompt.setPromptName(promptDTO.getTitle());
        prompt.setPromptDescription(promptDTO.getDescription());
        prompt.setPromptKeywordses(promptDTO.getKeywords().stream()
                .map(keyword -> {
                    PromptKeywords promptKeywords = new PromptKeywords();
                    promptKeywords.setPrompt(prompt);
                    promptKeywords.setKeyword(keyword);
                    return promptKeywords;
                }).collect(Collectors.toSet()));
        prompt.setPromptAgentCategories(Set.of(new PromptAgentCategory(prompt, promptDTO.getSourceTool())));
        prompt.setPromptTemplates(Set.of(
                new PromptTemplate(prompt, 1l, PromptSections.CONTEXT.getSectionName(), promptDTO.getContext().getBytes()),
                new PromptTemplate(prompt, 2l, PromptSections.DATA.getSectionName(), promptDTO.getData().getBytes()),
                new PromptTemplate(prompt, 3l, PromptSections.RULES.getSectionName(), promptDTO.getRules().getBytes()),
                new PromptTemplate(prompt, 4l, PromptSections.OUTPUT.getSectionName(), promptDTO.getOutput().getBytes()),
                new PromptTemplate(prompt, 5l, PromptSections.UNBREAKABLE_COMPLIANCE.getSectionName(), promptDTO.getUnbreakableCompliance().getBytes())
        ));
        log.info("Creating new prompt: {}", promptDTO.getTitle());
        Prompt promptSaved = promptRetrieverService.createPrompt(prompt);
        promptDTO.setId(promptSaved.getPromptId().toString());
        return promptDTO;
    }

    @Transactional
    public PromptDTO updatePrompt(String id, PromptDTO promptDTO) {
        log.info("Updating prompt with id: {}", id);
        deletePrompt(id);
        return createPrompt(promptDTO);
    }

    @Transactional
    public void deletePrompt(String id) {
        log.info("Deleting prompt with id: {}", id);
        promptRetrieverService.deleteById(id);
    }

    //Complex
    @Transactional(readOnly = true)
    public List<PromptDTO> searchPromptsByTitle(String title) {
        log.info("Searching prompts by title: {}", title);
        List<PromptDefinition> titleMatches = promptRetrieverService.searchPromptsByTitle(title);
        if (titleMatches.isEmpty()) {
            log.warn("No prompts found with title: {}", title);
            return Collections.emptyList();
        }
        return titleMatches.stream()
                .map(DatabasePromptService::convertToDto)
                .sorted(Comparator.comparing(PromptDTO::getId))
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<PromptDTO> getPromptsBySourceTool(String sourceTool) {
        log.info("Fetching prompts by source tool: {}", sourceTool);
        List<PromptDefinition> titleMatches = promptRetrieverService.getPromptsBySourceTool(sourceTool);
        if (titleMatches.isEmpty()) {
            log.warn("No prompts found with title: {}", sourceTool);
            return Collections.emptyList();
        }
        return titleMatches.stream()
                .map(DatabasePromptService::convertToDto)
                .sorted(Comparator.comparing(PromptDTO::getId))
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<PromptDTO> searchPromptsByKeyword(String keyword) {
        log.info("Searching prompts by keyword: {}", keyword);
        List<PromptDefinition> titleMatches = promptRetrieverService.searchPromptsByKeyword(keyword);
        if (titleMatches.isEmpty()) {
            log.warn("No prompts found with title: {}", keyword);
            return Collections.emptyList();
        }
        return titleMatches.stream()
                .map(DatabasePromptService::convertToDto)
                .sorted(Comparator.comparing(PromptDTO::getId))
                .collect(Collectors.toList());
    }

}