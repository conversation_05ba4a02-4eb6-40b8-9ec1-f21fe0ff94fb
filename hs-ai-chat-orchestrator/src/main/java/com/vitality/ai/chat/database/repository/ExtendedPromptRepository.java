package com.vitality.ai.chat.database.repository;

import com.vitality.ai.chat.database.databaseMapping.Prompt;
import com.vitality.ai.chat.database.repository.PromptRepository;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Primary
public interface ExtendedPromptRepository extends PromptRepository {

    @Query("SELECT p FROM Prompt p JOIN p.promptAgentCategories pac WHERE UPPER(pac.agentCategory) LIKE UPPER(:promptAgentCategories)")
    List<Prompt> findPromptByPromptAgentCategoriesIsContainingIgnoreCase(String promptAgentCategories);
    List<Prompt> findPromptByPromptNameContainingIgnoreCase(String promptName);

    @Query("SELECT p FROM Prompt p JOIN p.promptKeywordses pk WHERE UPPER(pk.keyword) LIKE UPPER(:promptKeywords)")
    List<Prompt> findPromptByPromptKeywordsesContainingIgnoreCase(String promptKeywords);
}
