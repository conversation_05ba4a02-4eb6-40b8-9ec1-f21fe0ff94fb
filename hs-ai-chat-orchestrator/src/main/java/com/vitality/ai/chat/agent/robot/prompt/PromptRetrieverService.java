package com.vitality.ai.chat.agent.robot.prompt;

import com.vitality.ai.chat.database.repository.ExtendedPromptRepository;
import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.database.databaseMapping.Prompt;
import com.vitality.ai.chat.database.databaseMapping.PromptAgentCategory;
import com.vitality.ai.chat.database.databaseMapping.PromptKeywords;
import com.vitality.ai.chat.database.databaseMapping.PromptTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class PromptRetrieverService {

    private final ExtendedPromptRepository promptRepository;

    @Transactional(readOnly = true)
    public Map<String, PromptDefinition> getPromptsInDatabase(String promptCategory)  {
        final Map<String, PromptDefinition> prompts = new ConcurrentHashMap<>();

        promptRepository.findAll().forEach(prompt -> {
            final PromptDefinition promptDefinition = convertToPromptDefinition(prompt);

            prompts.put(promptDefinition.getName(), promptDefinition);
        });
        return prompts;
    }

    private static PromptDefinition convertToPromptDefinition(Prompt prompt) {
        final PromptDefinition.PromptDefinitionBuilder builder = PromptDefinition.builder();
        builder.id(prompt.getPromptId());
        if (prompt.getPromptAgentCategories() != null && !prompt.getPromptAgentCategories().isEmpty()) {
            final PromptAgentCategory promptAgentCategory = prompt.getPromptAgentCategories().stream().findFirst().get();
            builder.agentCategory(promptAgentCategory.getAgentCategory());
        }

        builder.name(prompt.getPromptName()).description(prompt.getPromptDescription());

        if (prompt.getPromptKeywordses() != null) {
            List<String> keywords = prompt.getPromptKeywordses().stream()
                    .map(PromptKeywords::getKeyword)
                    .toList();
            builder.keywords(keywords);
        }

        if (prompt.getPromptTemplates() != null) {
            final Set<PromptTemplate> promptTemplates = prompt.getPromptTemplates();
            final StringBuffer contentBuffer = new StringBuffer();
            Map<String, String> contentSections = new HashMap<>();
            builder.sections(contentSections);
            promptTemplates.stream().sorted(Comparator.comparing(PromptTemplate::getPromptSectionId)).forEach(promptTemplate -> {
                contentBuffer.append("## ").append(promptTemplate.getPromptSectionName()).append("\n");
                String sectionString = new String(promptTemplate.getTemplateText());
                contentBuffer.append(sectionString).append("\n\n");
                contentSections.put(promptTemplate.getPromptSectionName(), sectionString);
            });
            builder.content(contentBuffer.toString().trim());
        }

        final PromptDefinition promptDefinition = builder.build();
        return promptDefinition;
    }

    @Transactional(readOnly = true)
    public PromptDefinition findById(String id) {
        Optional<Prompt> byId = promptRepository.findById(Long.valueOf(id));
        if (byId.isPresent()) {
            Prompt prompt = byId.get();
            return convertToPromptDefinition(prompt);
        } else {
            throw new IllegalArgumentException("Prompt with id " + id + " not found.");
        }
    }


    public void deleteById(String id) {
        promptRepository.deleteById(Long.valueOf(id));

    }

    public List<PromptDefinition> getPromptsBySourceTool(String title) {
        List<Prompt> promptByPromptAgentCategoriesIsContainingIgnoreCase = promptRepository.findPromptByPromptAgentCategoriesIsContainingIgnoreCase(title);
        List<PromptDefinition> collect = promptByPromptAgentCategoriesIsContainingIgnoreCase.stream()
                .map(PromptRetrieverService::convertToPromptDefinition)
                .toList();
        return collect;
    }

    public List<PromptDefinition> searchPromptsByTitle(String title) {
        List<Prompt> promptByPromptNameIsContainingIgnoreCase = promptRepository.findPromptByPromptNameContainingIgnoreCase(title);
        List<PromptDefinition> collect = promptByPromptNameIsContainingIgnoreCase.stream()
                .map(PromptRetrieverService::convertToPromptDefinition)
                .toList();
        return collect;
    }

    public List<PromptDefinition> searchPromptsByKeyword(String keyword) {
        List<Prompt> promptByPromptKeywordsesKeywordIsContainingIgnoreCase = promptRepository.findPromptByPromptKeywordsesContainingIgnoreCase(keyword);
        List<PromptDefinition> collect = promptByPromptKeywordsesKeywordIsContainingIgnoreCase.stream()
                .map(PromptRetrieverService::convertToPromptDefinition)
                .toList();
        return collect;
    }


    public Prompt createPrompt(Prompt prompt) {
        return promptRepository.save(prompt);
    }
}
