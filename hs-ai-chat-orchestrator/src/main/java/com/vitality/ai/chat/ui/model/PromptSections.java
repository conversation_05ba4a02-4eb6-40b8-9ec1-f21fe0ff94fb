package com.vitality.ai.chat.ui.model;

public enum PromptSections {

    CONTEXT("CONTEXT"),
    DATA("DATA"),
    RULES("RULES"),
    OUTPUT("OUTPUT"),
    UNBREAKABLE_COMPLIANCE("UNBREAKABLE COMPLIANCE");

    private String sectionName;

    PromptSections(String sectionName) {
        this.sectionName = sectionName;
    }

    public String getSectionName() {
        return sectionName;
    }

}
