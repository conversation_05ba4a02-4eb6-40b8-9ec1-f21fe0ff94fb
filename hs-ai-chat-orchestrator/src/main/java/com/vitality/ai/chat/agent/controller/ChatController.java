package com.vitality.ai.chat.agent.controller;

import com.vitality.ai.chat.agent.delegate.ChatDelegate;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/chat")
@RequiredArgsConstructor
public class ChatController {

    private final ChatDelegate chatDelegate;

    @PostMapping("/chat")
    public String chatter(
            @RequestParam String sessionId,
            @RequestBody String userInput) {
        return chatDelegate.executePrompt(sessionId, userInput);
    }


}