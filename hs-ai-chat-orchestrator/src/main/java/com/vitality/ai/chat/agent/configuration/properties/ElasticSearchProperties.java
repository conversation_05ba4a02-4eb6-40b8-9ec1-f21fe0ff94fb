package com.vitality.ai.chat.agent.configuration.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

@ConfigurationProperties(prefix = "elasticsearch")
@Data
public class ElasticSearchProperties {
    private String user;
    private String password;
    private String table;
    private Integer port;
    private String host;
}
