package com.vitality.ai.chat.agent.robot.prompt.route.model;

import com.vitality.ai.chat.agent.robot.prompt.dataType.DataType;
import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.agent.robot.retriever.IntelligentContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PromptData {

    private PromptDefinition promptDefinition;
    private List<DataType> placeholders;
    private ArrayList<String> toolResponses;
    private String context;

}
