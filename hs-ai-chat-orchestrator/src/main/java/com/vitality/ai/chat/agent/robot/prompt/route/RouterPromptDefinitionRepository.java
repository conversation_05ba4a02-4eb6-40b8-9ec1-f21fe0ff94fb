package com.vitality.ai.chat.agent.robot.prompt.route;

import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.flywaydb.core.internal.util.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class RouterPromptDefinitionRepository {

    @Value("${router.prompt.path:prompts/router}")
    private String routerPath;

    private PromptDefinition defaultPrompt;
    private final Map<String, PromptDefinition> prompts = new ConcurrentHashMap<>();

    public PromptDefinition getPrompt(String name) {
        PromptDefinition def = prompts.get(name);
        if (def == null) {
           return defaultPrompt;
        }
        return def;
    }

    public PromptDefinition getDefaultPromptValue() {
        return defaultPrompt;
    }


//    Caching
    @PostConstruct
    public void init() throws IOException {
        final ClassPathResource resource = new ClassPathResource(routerPath);

        Files.list(Path.of(resource.getFile().getAbsolutePath()))
                .forEach(path -> {
                    final String content = FileUtils.readAsString(path);
                    final String name = path.getFileName().toString().replace(".prompt", "");
                    final PromptDefinition def = parsePrompt(name, content);
                    prompts.put(def.getName(), def);
                });

        defaultPrompt = prompts.get("default");
        if (defaultPrompt == null) {
            throw new IllegalStateException("Missing required default.prompt file in prompts folder.");
        }
    }

    private PromptDefinition parsePrompt(String name, String content) {
        return PromptDefinition.builder()
                .name(name)
                .content(content)
                .build();
    }

}

