package com.vitality.ai.chat.agent.delegate;

import com.vitality.ai.chat.agent.robot.service.ConversationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor

public class ChatDelegate {

    private final ConversationService executor;

    public String executePrompt(String sessionId, String userInput) {
        return executor.executePrompt(sessionId, userInput);
    }

}


