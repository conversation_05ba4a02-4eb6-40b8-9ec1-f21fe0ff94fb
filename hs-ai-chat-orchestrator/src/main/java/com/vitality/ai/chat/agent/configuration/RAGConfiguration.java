package com.vitality.ai.chat.agent.configuration;

import com.vitality.ai.chat.agent.robot.content.ContentPopulator;
import com.vitality.ai.chat.agent.robot.content.ContentRetriever;
import com.vitality.ai.chat.agent.robot.retriever.IntelligenceEmbeddingStoreContentRetriever;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.bgesmallenv15q.BgeSmallEnV15QuantizedEmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RAGConfiguration {

    private static final Integer MAX_RESULTS = 2;
    private static final Double MIN_SCORE = 0.75;


    @Bean
    public EmbeddingModel embeddingModel() {
        return new BgeSmallEnV15QuantizedEmbeddingModel();
    }


    @Bean
    public IntelligenceEmbeddingStoreContentRetriever embeddingStoreContentRetriever(EmbeddingStore<TextSegment> embeddingStore) {
        return new IntelligenceEmbeddingStoreContentRetriever(embeddingStore, embeddingModel(), MAX_RESULTS, MIN_SCORE);
    }

    @Bean
    public ContentRetriever contentRetriever(EmbeddingStore<TextSegment> embeddingStore) {
        return new ContentRetriever(embeddingStoreContentRetriever(embeddingStore));
    }

    @Bean
    public DocumentSplitter documentSplitter() {
        return DocumentSplitters.recursive(2000, 0);
    }

    @Bean
    public EmbeddingStoreIngestor ingestorFactory(EmbeddingStore<TextSegment> embeddingStore) {
        return EmbeddingStoreIngestor.builder()
                .documentSplitter(documentSplitter())
                .embeddingModel(embeddingModel())
                .embeddingStore(embeddingStore)
                .build();
    }

    @Bean
    public ContentPopulator contentPopulator(EmbeddingStore<TextSegment> embeddingStore) {
        return new ContentPopulator(ingestorFactory(embeddingStore), embeddingStore);
    }

}
