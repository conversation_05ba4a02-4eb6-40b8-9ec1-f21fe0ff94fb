package com.vitality.ai.chat.agent.robot.content;

import com.vitality.ai.chat.agent.robot.retriever.IntelligenceEmbeddingStoreContentRetriever;
import com.vitality.ai.chat.agent.robot.retriever.IntelligentContent;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.query.Query;

import java.util.ArrayList;
import java.util.List;

public class ContentRetriever {

    IntelligenceEmbeddingStoreContentRetriever retriever;


    public ContentRetriever(IntelligenceEmbeddingStoreContentRetriever retriever) {
        this.retriever = retriever;
    }

    public List<IntelligentContent> retrieve(String query) {
        return retrieve(new Query(query));
    }

    public List<IntelligentContent> retrieve(Query query) {
        final List<Content> retrieve = retriever.retrieve(query);
        final ArrayList<IntelligentContent> contents = new ArrayList<>();
        for (Content content : retrieve) {
            final IntelligentContent intelligentContent = (IntelligentContent)content;
            contents.add(intelligentContent);
        }
        return contents;
    }

}
