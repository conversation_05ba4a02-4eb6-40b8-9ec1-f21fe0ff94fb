package com.vitality.ai.chat.ui.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * Creates a custom message converter specifically for handling JSON in multipart requests,
     * without affecting other API endpoints.
     */
    @Bean
    public MappingJackson2HttpMessageConverter multipartJsonConverter() {
        // Create a more specific converter for multipart JSON
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();

        // Support specific media types related to your multipart scenario
        // This is more targeted than supporting all octet-stream content
        converter.setSupportedMediaTypes(Arrays.asList(
                MediaType.APPLICATION_JSON,
                MediaType.valueOf("application/json;charset=UTF-8"),
                MediaType.valueOf("application/octet-stream+json"),
                MediaType.valueOf("application/octet-stream")
        ));

        return converter;
    }

    /**
     * Override to ensure our converter is added but doesn't interfere with default converters
     */
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        // Add our custom converter - it will be used when appropriate based on its supported media types
        converters.add(multipartJsonConverter());
    }

    @Bean
    public PageableHandlerMethodArgumentResolverCustomizer pageableResolverCustomizer() {
        return pageableResolver -> {
            pageableResolver.setOneIndexedParameters(false); // Use 0-based indices
            pageableResolver.setMaxPageSize(100);            // Maximum page size
            pageableResolver.setPageParameterName("page");   // Parameter name for page number
            pageableResolver.setSizeParameterName("size");   // Parameter name for page size
        };
    }
}
