package com.vitality.ai.chat.agent.robot.service;

import com.vitality.ai.chat.agent.database.RagExecutionDetailsLog;
import com.vitality.ai.chat.agent.database.RagExecutionLog;
import com.vitality.ai.chat.agent.database.RagExecutionLogRepository;
import com.vitality.ai.chat.agent.robot.prompt.model.Prompt;
import com.vitality.ai.chat.agent.robot.retriever.IntelligentContent;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ConversationLogService {

    private final RagExecutionLogRepository logRepository;


    //@Todo fix this to read things
    public void saveResult(String sessionId, Prompt prompt, List<ChatMessage> chatMessageList, AiMessage response) {
        final List<RagExecutionDetailsLog> matchPercentages = new ArrayList<>();
        for (final IntelligentContent content : prompt.getContextDocs()) {
            final RagExecutionDetailsLog detailsLog = new RagExecutionDetailsLog();
            detailsLog.setFilename(content.textSegment().metadata().getString("file_name"));
            detailsLog.setIndexId(content.textSegment().metadata().getString("index"));
            detailsLog.setFilePath(content.textSegment().metadata().getString("absolute_directory_path"));
            detailsLog.setMatchPercentage(content.getMatchPercentage().toString());
            matchPercentages.add(detailsLog);
        }

        final RagExecutionLog log = new RagExecutionLog();
        log.setSessionId(sessionId);
        log.setPromptName(prompt.getPromptName());
        log.setPromptId(prompt.getPromptId());
        log.setUserInput(prompt.getUserInput());
        log.setContext(prompt.getContext());
        log.setResponse(response.text());
        log.setRagExecutionDetailsLogsList(matchPercentages);

        logRepository.save(log);
    }

}
