package com.vitality.ai.chat;

import com.vitality.ai.chat.agent.configuration.properties.ElasticSearchProperties;
import com.vitality.ai.chat.agent.configuration.properties.PostGresProperties;
import com.vitality.ai.properties.VertexProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = "com.vitality.ai")
@EnableScheduling
@EnableConfigurationProperties({ElasticSearchProperties.class, PostGresProperties.class, VertexProperties.class})
public class ChatOrchestrationApplication extends SpringBootServletInitializer {


    @Override
    protected SpringApplicationBuilder configure(final SpringApplicationBuilder application) {
        return application.sources(ChatOrchestrationApplication.class);
    }

    public static void main(final String[] args) {
        SpringApplication.run(ChatOrchestrationApplication.class, args);
    }

}
