package com.vitality.ai.chat.agent.robot.retriever;

import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.rag.content.DefaultContent;

public class IntelligentContent extends DefaultContent {

    private Double matchPercentage;

    public IntelligentContent(String text) {
        super(text);
    }

    public IntelligentContent(TextSegment textSegment) {
        super(textSegment);
    }

    public Double getMatchPercentage() {
        return matchPercentage;
    }

    public void setMatchPercentage(Double matchPercentage) {
        this.matchPercentage = matchPercentage;
    }
}
