package com.vitality.ai.chat.agent.delegate;

import com.vitality.ai.chat.agent.robot.content.ContentPopulator;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@RequiredArgsConstructor
public class ContentDelegate {

    private final ContentPopulator contentPopulator;

    public void loadContent() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("/documentation");
        contentPopulator.populate(classPathResource.getFile().getAbsolutePath());
    }

    public void removeAll() {
        contentPopulator.clearContent();
    }

}
