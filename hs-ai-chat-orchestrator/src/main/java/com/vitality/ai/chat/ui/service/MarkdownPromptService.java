package com.vitality.ai.chat.ui.service;

import com.vitality.ai.chat.agent.robot.fullfillment.AgentFactory;
import com.vitality.ai.chat.agent.robot.fullfillment.AgentModel;
import com.vitality.ai.chat.agent.robot.prompt.PromptRetrieverService;
import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.ui.exception.DuplicatePromptException;
import com.vitality.ai.chat.ui.exception.PromptNotFoundException;
import com.vitality.ai.chat.ui.model.PromptDTO;
import com.vitality.ai.chat.ui.model.ToolDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
@Profile("file")
public class MarkdownPromptService implements PromptServiceDelegate {

    @Value("${app.prompts.directory:/Users/<USER>/development/research/chatAgent/hs-chat-orchestrator/src/main/resources/prompts}")
    private String promptsDirectory;

    private Path promptsPath;
    private final PromptRetrieverService promptRetrieverService;

    private final AgentFactory agentFactory;
    // Fixed patterns to match the actual file format exactly
    private static final Pattern METADATA_PATTERN = Pattern.compile("---\\s*(.*?)\\s*---",
            Pattern.DOTALL);
    private static final Pattern SECTION_PATTERN = Pattern.compile("##\\s+([A-Z\\s]+?)\\s*\\n(.*?)(?=\\n##|$)",
            Pattern.DOTALL);
    private static final Pattern METADATA_FIELD_PATTERN = Pattern.compile("([a-zA-Z_]+):\\s*([^\\n]*?)(?=\\s+[a-zA-Z_]+:|$)",
            Pattern.DOTALL);

    @PostConstruct
    public void initializeDirectory() {
        try {
            promptsPath = Paths.get(promptsDirectory);

            // Create directory if it doesn't exist
            if (!Files.exists(promptsPath)) {
                Files.createDirectories(promptsPath);
                log.info("Created prompts directory: {}", promptsPath.toAbsolutePath());
            }

            // Create sample prompts if directory is empty
            if (isDirectoryEmpty()) {
                createSamplePrompts();
            }

            log.info("Prompts directory initialized: {}", promptsPath.toAbsolutePath());
        } catch (IOException e) {
            log.error("Failed to initialize prompts directory", e);
            throw new RuntimeException("Failed to initialize prompts directory", e);
        }
    }

    public List<PromptDTO> getAllPrompts() {
        log.info("Fetching all prompts from directory: {}", promptsPath);
        //@Todo change to database
        try (Stream<Path> files = Files.walk(promptsPath, 1)) {
            return files
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".prompt"))
                    .map(this::readPromptFromFile)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(PromptDTO::getUpdatedAt,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("Error reading prompts from directory", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ToolDTO> getAllTools() {
        log.info("Fetching all tools");
        try {
            List<AgentModel> allTools = agentFactory.getAllTools();
            return allTools.stream()
                    .map(tool -> new ToolDTO(tool.getCategory(), tool.getDocumentation()))
                    .sorted(Comparator.comparing(ToolDTO::getCategory))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error reading tools", e);
            return Collections.emptyList();
        }
    }

    @Override
    public PromptDTO getPromptById(String id) {
        log.info("Fetching prompt with id: {}", id);

        Path filePath = getPromptFilePath(id);
        if (!Files.exists(filePath)) {
            throw new PromptNotFoundException("Prompt not found with id: " + id);
        }

        PromptDTO prompt = readPromptFromFile(filePath);
        if (prompt == null) {
            throw new PromptNotFoundException("Failed to read prompt with id: " + id);
        }

        return prompt;
    }

    @Override
    public PromptDTO createPrompt(PromptDTO promptDTO) {
        log.info("Creating new prompt with title: {}", promptDTO.getTitle());

        // Generate UUID for new prompt
        String id = UUID.randomUUID().toString();
        promptDTO.setId(id);
        promptDTO.setCreatedAt(LocalDateTime.now());
        promptDTO.setUpdatedAt(LocalDateTime.now());

        // Check if prompt with same title already exists
        if (promptWithTitleExists(promptDTO.getTitle())) {
            throw new DuplicatePromptException("Prompt with title '" + promptDTO.getTitle() + "' already exists");
        }

        // Save to file
        Path filePath = getPromptFilePath(id);
        try {
            //@Todo change to Repository
            writePromptToFile(promptDTO, filePath);
            log.info("Successfully created prompt with id: {}", id);
            return promptDTO;
        } catch (IOException e) {
            log.error("Failed to create prompt with id: {}", id, e);
            throw new RuntimeException("Failed to create prompt", e);
        }
    }

    @Override
    public PromptDTO updatePrompt(String id, PromptDTO promptDTO) {
        log.info("Updating prompt with id: {}", id);

        Path filePath = getPromptFilePath(id);
        if (!Files.exists(filePath)) {
            throw new PromptNotFoundException("Prompt not found with id: " + id);
        }

        // Read existing prompt to preserve created date
        PromptDTO existingPrompt = readPromptFromFile(filePath);
        if (existingPrompt == null) {
            throw new PromptNotFoundException("Failed to read existing prompt with id: " + id);
        }

        // Check if title is being changed and if new title already exists (excluding current prompt)
        if (!existingPrompt.getTitle().equals(promptDTO.getTitle()) &&
                promptWithTitleExistsExcluding(promptDTO.getTitle(), id)) {
            throw new DuplicatePromptException("Prompt with title '" + promptDTO.getTitle() + "' already exists");
        }

        // Update prompt data
        promptDTO.setId(id);
        promptDTO.setCreatedAt(existingPrompt.getCreatedAt());
        promptDTO.setUpdatedAt(LocalDateTime.now());

        try {
            //@Todo replace
            writePromptToFile(promptDTO, filePath);
            log.info("Successfully updated prompt with id: {}", id);
            return promptDTO;
        } catch (IOException e) {
            log.error("Failed to update prompt with id: {}", id, e);
            throw new RuntimeException("Failed to update prompt", e);
        }
    }

    @Override
    public void deletePrompt(String id) {
        log.info("Deleting prompt with id: {}", id);

        Path filePath = getPromptFilePath(id);
        if (!Files.exists(filePath)) {
            throw new PromptNotFoundException("Prompt not found with id: " + id);
        }

        try {
            Files.delete(filePath);
            log.info("Successfully deleted prompt with id: {}", id);
        } catch (IOException e) {
            log.error("Failed to delete prompt with id: {}", id, e);
            throw new RuntimeException("Failed to delete prompt", e);
        }
    }

    @Override
    public List<PromptDTO> searchPromptsByTitle(String title) {
        log.info("Searching prompts by title: {}", title);
        return getAllPrompts().stream()
                .filter(prompt -> prompt.getTitle().toLowerCase().contains(title.toLowerCase()))
                .collect(Collectors.toList());
    }

    public List<PromptDTO> getPromptsBySourceTool(String sourceTool) {
        log.info("Fetching prompts by source tool: {}", sourceTool);
        return getAllPrompts().stream()
                .filter(prompt -> sourceTool.equals(prompt.getSourceTool()))
                .collect(Collectors.toList());
    }

    public List<PromptDTO> searchPromptsByKeyword(String keyword) {
        log.info("Searching prompts by keyword: {}", keyword);
        return getAllPrompts().stream()
                .filter(prompt -> prompt.getKeywords() != null &&
                        prompt.getKeywords().stream()
                                .anyMatch(k -> k.toLowerCase().contains(keyword.toLowerCase())))
                .collect(Collectors.toList());
    }

    // Private helper methods

    private PromptDTO readPromptFromFile(Path filePath) {
        try {
            String content = Files.readString(filePath);
            log.debug("Reading file: {} with content: {}", filePath.getFileName(), content.substring(0, Math.min(200, content.length())));
            return parseMarkdownPrompt(content, getIdFromFileName(filePath));
        } catch (IOException e) {
            log.error("Error reading prompt file: {}", filePath, e);
            return null;
        }
    }

    private PromptDTO parseMarkdownPrompt(String content, String id) {
        PromptDTO prompt = new PromptDTO();
        prompt.setId(id);

        log.debug("Parsing content for ID: {}", id);
        log.debug("Raw content: [{}]", content);

        // Parse metadata section
        Matcher metadataMatcher = METADATA_PATTERN.matcher(content);
        if (metadataMatcher.find()) {
            String metadataSection = metadataMatcher.group(1).trim();
            log.debug("Found metadata section: [{}]", metadataSection);
            parseMetadata(metadataSection, prompt);
        } else {
            log.warn("No metadata section found in file: {}", id);
        }

        // Extract content after metadata section
        String contentWithoutMetadata = content;
        if (metadataMatcher.find()) {
            metadataMatcher.reset();
            if (metadataMatcher.find()) {
                contentWithoutMetadata = content.substring(metadataMatcher.end()).trim();
            }
        }

        log.debug("Content without metadata: [{}]", contentWithoutMetadata);

        // Parse sections
        if (contentWithoutMetadata.contains("##")) {
            log.debug("Parsing structured format with sections");
            parseStructuredSections(contentWithoutMetadata, prompt);
        } else {
            // Handle simple format
            log.debug("Parsing simple format - treating as context");
            if (!contentWithoutMetadata.isEmpty()) {
                prompt.setContext(contentWithoutMetadata.trim());
            }
        }

        // Set file modification time if available
        try {
            Path filePath = promptsPath.resolve(id + ".prompt");
            if (Files.exists(filePath)) {
                prompt.setUpdatedAt(LocalDateTime.ofInstant(
                        Files.getLastModifiedTime(filePath).toInstant(),
                        java.time.ZoneId.systemDefault()
                ));
                prompt.setCreatedAt(prompt.getUpdatedAt());
            }
        } catch (IOException e) {
            log.warn("Could not read file timestamps for: {}", id);
        }

        log.debug("Final parsed prompt - Title: [{}], Description: [{}], SourceTool: [{}]",
                prompt.getTitle(), prompt.getDescription(), prompt.getSourceTool());
        log.debug("Context: [{}]", prompt.getContext());
        log.debug("Data: [{}]", prompt.getData());
        log.debug("Rules: [{}]", prompt.getRules());
        log.debug("Output: [{}]", prompt.getOutput());
        log.debug("Compliance: [{}]", prompt.getUnbreakableCompliance());

        return prompt;
    }

    private void parseMetadata(String metadataSection, PromptDTO prompt) {
        log.debug("Parsing metadata from: [{}]", metadataSection);

        Matcher fieldMatcher = METADATA_FIELD_PATTERN.matcher(metadataSection);

        while (fieldMatcher.find()) {
            String fieldName = fieldMatcher.group(1).trim();
            String fieldValue = fieldMatcher.group(2).trim();

            log.debug("Found metadata field: [{}] = [{}]", fieldName, fieldValue);

            switch (fieldName.toLowerCase()) {
                case "description":
                    prompt.setDescription(fieldValue);
                    break;
                case "keywords":
                    List<String> keywords = Arrays.stream(fieldValue.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .collect(Collectors.toList());
                    prompt.setKeywords(keywords);
                    break;
                case "agent_category":
                    prompt.setSourceTool(fieldValue);
                    break;
                case "title":
                    prompt.setTitle(fieldValue);
                    break;
                default:
                    log.debug("Unknown metadata field: {}", fieldName);
            }
        }

        // Generate title from filename if not set in metadata
        if (prompt.getTitle() == null || prompt.getTitle().isEmpty()) {
            String generatedTitle = generateTitleFromDescription(prompt.getDescription());
            if (generatedTitle.equals("Untitled Prompt")) {
                // Use filename as fallback
                generatedTitle = prompt.getId().replace("-", " ");
                generatedTitle = generatedTitle.substring(0, 1).toUpperCase() +
                        generatedTitle.substring(1).toLowerCase();
            }
            prompt.setTitle(generatedTitle);
        }

        log.debug("After metadata parsing - Title: [{}], Description: [{}], Keywords: {}, SourceTool: [{}]",
                prompt.getTitle(), prompt.getDescription(), prompt.getKeywords(), prompt.getSourceTool());
    }

    private void parseStructuredSections(String content, PromptDTO prompt) {
        log.debug("Parsing structured sections from: [{}]", content);

        Matcher sectionMatcher = SECTION_PATTERN.matcher(content);

        while (sectionMatcher.find()) {
            String sectionName = sectionMatcher.group(1).trim();
            String sectionContent = sectionMatcher.group(2).trim();

            log.debug("Found section: [{}] with raw content: [{}]", sectionName, sectionContent);

            // Clean up content
            sectionContent = cleanSectionContent(sectionContent);

            log.debug("Cleaned section [{}]: [{}]", sectionName, sectionContent);

            switch (sectionName.toUpperCase()) {
                case "CONTEXT":
                    prompt.setContext(sectionContent);
                    break;
                case "DATA":
                    prompt.setData(sectionContent);
                    break;
                case "RULES":
                    prompt.setRules(sectionContent);
                    break;
                case "OUTPUT":
                    prompt.setOutput(sectionContent);
                    break;
                case "UNBREAKABLE COMPLIANCE":
                    prompt.setUnbreakableCompliance(sectionContent);
                    break;
                default:
                    log.debug("Unknown section: {}", sectionName);
            }
        }
    }

    private String cleanSectionContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Split into lines and clean each line
        String[] lines = content.split("\n");
        List<String> cleanedLines = new ArrayList<>();

        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty()) {
                // Keep the bullet points but ensure consistent formatting
                if (line.startsWith("- ")) {
                    cleanedLines.add(line); // Keep as is
                } else if (line.startsWith("-")) {
                    cleanedLines.add("- " + line.substring(1).trim()); // Add space after dash
                } else {
                    // For lines without bullets, add them (to maintain consistency)
                    cleanedLines.add("- " + line);
                }
            }
        }

        // Convert to HTML for frontend display
        return convertTextToHtml(String.join("\n", cleanedLines));
    }

    /**
     * Converts plain text with line breaks to HTML format for frontend display
     */
    private String convertTextToHtml(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return "";
        }

        // Split by lines and convert to HTML
        String[] lines = plainText.split("\n");
        List<String> htmlLines = new ArrayList<>();

        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty()) {
                if (line.startsWith("- ")) {
                    // Convert bullet points to list items
                    htmlLines.add("<li>" + line.substring(2) + "</li>");
                } else {
                    // Regular text becomes paragraphs
                    htmlLines.add("<p>" + line + "</p>");
                }
            }
        }

        // If we have list items, wrap them in <ul>
        if (htmlLines.stream().anyMatch(line -> line.startsWith("<li>"))) {
            StringBuilder result = new StringBuilder();
            boolean inList = false;

            for (String htmlLine : htmlLines) {
                if (htmlLine.startsWith("<li>")) {
                    if (!inList) {
                        result.append("<ul>");
                        inList = true;
                    }
                    result.append(htmlLine);
                } else {
                    if (inList) {
                        result.append("</ul>");
                        inList = false;
                    }
                    result.append(htmlLine);
                }
            }

            if (inList) {
                result.append("</ul>");
            }

            return result.toString();
        } else {
            // Just paragraphs
            return String.join("", htmlLines);
        }
    }

    private void writePromptToFile(PromptDTO prompt, Path filePath) throws IOException {

        StringBuilder content = new StringBuilder();

        // Write metadata section
        content.append("---\n");
        if (prompt.getDescription() != null && !prompt.getDescription().isEmpty()) {
            content.append("description: ").append(cleanHtmlTags(prompt.getDescription())).append("\n");
        }
        if (prompt.getKeywords() != null && !prompt.getKeywords().isEmpty()) {
            content.append("keywords: ").append(String.join(",", prompt.getKeywords())).append("\n");
        }
        if (prompt.getSourceTool() != null && !prompt.getSourceTool().isEmpty()) {
            content.append("agent_category: ").append(prompt.getSourceTool()).append("\n");
        }
        content.append("---\n\n");

        // Determine if this is a structured prompt or simple prompt
        boolean hasStructuredSections =
                (prompt.getData() != null && !prompt.getData().isEmpty()) ||
                        (prompt.getRules() != null && !prompt.getRules().isEmpty()) ||
                        (prompt.getOutput() != null && !prompt.getOutput().isEmpty()) ||
                        (prompt.getUnbreakableCompliance() != null && !prompt.getUnbreakableCompliance().isEmpty());

        if (hasStructuredSections) {
            // Write structured sections
            if (prompt.getContext() != null && !prompt.getContext().isEmpty()) {
                content.append("## CONTEXT\n");
                content.append(formatSectionContent(cleanHtmlTags(prompt.getContext()))).append("\n\n");
            }

            if (prompt.getData() != null && !prompt.getData().isEmpty()) {
                content.append("## DATA\n");
                content.append(formatSectionContent(cleanHtmlTags(prompt.getData()))).append("\n\n");
            }

            if (prompt.getRules() != null && !prompt.getRules().isEmpty()) {
                content.append("## RULES\n");
                content.append(formatSectionContent(cleanHtmlTags(prompt.getRules()))).append("\n\n");
            }

            if (prompt.getOutput() != null && !prompt.getOutput().isEmpty()) {
                content.append("## OUTPUT\n");
                content.append(formatSectionContent(cleanHtmlTags(prompt.getOutput()))).append("\n\n");
            }

            if (prompt.getUnbreakableCompliance() != null && !prompt.getUnbreakableCompliance().isEmpty()) {
                content.append("## UNBREAKABLE COMPLIANCE\n");
                content.append(formatSectionContent(cleanHtmlTags(prompt.getUnbreakableCompliance()))).append("\n");
            }
        } else {
            // Write simple format (just the context content)
            if (prompt.getContext() != null && !prompt.getContext().isEmpty()) {
                content.append(cleanHtmlTags(prompt.getContext())).append("\n");
            }
        }

        Files.writeString(filePath, content.toString(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        log.debug("Wrote prompt to file: {} with {} characters", filePath.getFileName(), content.length());
    }

    /**
     * Removes HTML tags and cleans up the content for plain text storage
     */
    private String cleanHtmlTags(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Remove HTML tags using regex
        String cleaned = content
                // Remove <p> tags and replace with newlines
                .replaceAll("<p[^>]*>", "")
                .replaceAll("</p>", "\n")
                // Remove <br> tags and replace with newlines
                .replaceAll("<br[^>]*>", "\n")
                // Remove <div> tags and replace with newlines
                .replaceAll("<div[^>]*>", "")
                .replaceAll("</div>", "\n")
                // Remove list items and replace with bullet points
                .replaceAll("<li[^>]*>", "- ")
                .replaceAll("</li>", "\n")
                // Remove list containers
                .replaceAll("<[uo]l[^>]*>", "")
                .replaceAll("</[uo]l>", "")
                // Remove formatting tags but keep content
                .replaceAll("<(strong|b)[^>]*>", "**")
                .replaceAll("</(strong|b)>", "**")
                .replaceAll("<(em|i)[^>]*>", "*")
                .replaceAll("</(em|i)>", "*")
                .replaceAll("<u[^>]*>", "_")
                .replaceAll("</u>", "_")
                // Remove any remaining HTML tags
                .replaceAll("<[^>]+>", "")
                // Decode HTML entities
                .replace("&nbsp;", " ")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&amp;", "&")
                .replace("&quot;", "\"")
                .replace("&#39;", "'")
                // Clean up multiple newlines and spaces
                .replaceAll("\\n\\s*\\n\\s*\\n", "\n\n")  // Multiple newlines to double newline
                .replaceAll("\\n\\s*\\n", "\n")           // Double newlines to single
                .replaceAll("[ \\t]+", " ")               // Multiple spaces to single space
                .trim();

        log.debug("Cleaned HTML content: [{}] -> [{}]", content.substring(0, Math.min(50, content.length())),
                cleaned.substring(0, Math.min(50, cleaned.length())));

        return cleaned;
    }

    private String formatSectionContent(String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        // Split into lines and format as bullet points if multiple lines
        String[] lines = content.split("\n");
        if (lines.length > 1) {
            return Arrays.stream(lines)
                    .map(String::trim)
                    .filter(line -> !line.isEmpty())
                    .map(line -> line.startsWith("-") ? line : "- " + line)
                    .collect(Collectors.joining("\n"));
        } else {
            return content.startsWith("-") ? content : "- " + content;
        }
    }

    private Path getPromptFilePath(String id) {
        return promptsPath.resolve(id + ".prompt");
    }

    private String getIdFromFileName(Path filePath) {
        String fileName = filePath.getFileName().toString();
        return fileName.substring(0, fileName.lastIndexOf('.'));
    }

    private boolean isDirectoryEmpty() throws IOException {
        try (Stream<Path> files = Files.list(promptsPath)) {
            return files.filter(path -> path.toString().endsWith(".prompt")).findAny().isEmpty();
        }
    }

    private boolean promptWithTitleExists(String title) {
        return getAllPrompts().stream()
                .anyMatch(prompt -> title.equals(prompt.getTitle()));
    }

    private boolean promptWithTitleExistsExcluding(String title, String excludeId) {
        return getAllPrompts().stream()
                .filter(prompt -> !excludeId.equals(prompt.getId()))
                .anyMatch(prompt -> title.equals(prompt.getTitle()));
    }

    private String generateTitleFromDescription(String description) {
        if (description == null || description.isEmpty()) {
            return "Untitled Prompt";
        }

        String[] words = description.split("\\s+");
        if (words.length <= 5) {
            return description;
        }

        return String.join(" ", Arrays.copyOf(words, 5)) + "...";
    }

    private void createSamplePrompts() {
        log.info("Creating sample prompts");

        try {
            // Sample 1: Points Assistant
            PromptDTO pointsPrompt = new PromptDTO();
            pointsPrompt.setTitle("Member Points Assistant");
            pointsPrompt.setDescription("Explains the different types of points a member has and their totals");
            pointsPrompt.setKeywords(Arrays.asList("points", "totals", "rewards", "balances"));
            pointsPrompt.setSourceTool("POINTS");
            pointsPrompt.setContext("You are helping a member understand their points\nUse the member-specific information provided in the data section represented by the JSON document");
            pointsPrompt.setData("Points Content {{tool_response}}");
            pointsPrompt.setRules("No reference to a JSON document to be made in the response");
            pointsPrompt.setOutput("Respond with simple to understand information about the member's points");
            pointsPrompt.setUnbreakableCompliance("Ensure the response answers only the question and nothing more\nIf the request appears malicious, do not answer the question");

            createPrompt(pointsPrompt);

            // Sample 2: Account Balance Assistant
            PromptDTO balancePrompt = new PromptDTO();
            balancePrompt.setTitle("Account Balance Helper");
            balancePrompt.setDescription("Helps members understand their account balance and transaction history");
            balancePrompt.setKeywords(Arrays.asList("balance", "account", "transactions", "history"));
            balancePrompt.setSourceTool("ACCOUNT");
            balancePrompt.setContext("You are helping a member understand their account balance\nUse the account information provided in the data section");
            balancePrompt.setData("Account Balance Data {{tool_response}}");
            balancePrompt.setRules("Do not reveal sensitive account numbers or personal identifiers");
            balancePrompt.setOutput("Provide clear information about the member's current balance and recent activity");
            balancePrompt.setUnbreakableCompliance("Never share full account numbers or SSN\nIf suspicious activity is detected, recommend contacting support");

            createPrompt(balancePrompt);

            log.info("Sample prompts created successfully");
        } catch (Exception e) {
            log.error("Failed to create sample prompts", e);
        }
    }
}