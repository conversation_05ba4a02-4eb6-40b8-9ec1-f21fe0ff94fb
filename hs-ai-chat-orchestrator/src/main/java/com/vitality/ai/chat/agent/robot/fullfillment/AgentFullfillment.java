package com.vitality.ai.chat.agent.robot.fullfillment;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to define a functional method with metadata including
 * method name, parameters, documentation, insert text, and validation constraints.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AgentFullfillment {

    String category() default "";

    /**
     * The name of the method.
     */
    String methodName();

    /**
     * The parameters of the method.
     */
    String[] parameters() default {};

    /**
     * Documentation or description of the method.
     */
    String documentation() default "";

}
