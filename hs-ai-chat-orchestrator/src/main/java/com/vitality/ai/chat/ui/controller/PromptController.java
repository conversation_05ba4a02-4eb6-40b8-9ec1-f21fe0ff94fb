package com.vitality.ai.chat.ui.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.chat.agent.robot.fullfillment.AgentFactory;
import com.vitality.ai.chat.ui.model.PromptDTO;
import com.vitality.ai.chat.ui.model.ToolDTO;
import com.vitality.ai.chat.ui.service.MarkdownPromptService;
import com.vitality.ai.chat.ui.service.PromptServiceDelegate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@RequestMapping("/api/prompts")
@RequiredArgsConstructor
@Slf4j
public class PromptController {

    private final PromptServiceDelegate promptService;

    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> download(@RequestParam(name = "promptId", required = true) Long id) throws JsonProcessingException {
        PromptDTO promptById = promptService.getPromptById(id.toString());
        ObjectMapper objectMapper = new ObjectMapper();
        String promptString = objectMapper.writeValueAsString(promptById);

        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(promptString.getBytes(StandardCharsets.UTF_8));
        final HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", String.format("attachment; filename=%s", promptById.getId() + ".prompt"));
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(byteArrayInputStream));

        //
    }

    @PostMapping(path = "/upload", consumes = "multipart/form-data")
    public ResponseEntity<Boolean> process(@RequestParam("file") MultipartFile file) throws IOException {
        byte[] fileContent = file.getInputStream().readAllBytes();
        String content = new String(fileContent, StandardCharsets.UTF_8);
        log.info("Processing uploaded file with content: {}", content);
        return ResponseEntity.ok(Boolean.TRUE);
    }

    @GetMapping
    public ResponseEntity<List<PromptDTO>> getAllPrompts() {
        log.info("GET /api/prompts - Fetching all prompts");
        List<PromptDTO> prompts = promptService.getAllPrompts();
        return ResponseEntity.ok(prompts);
    }

    @GetMapping("/tools")
    public ResponseEntity<List<ToolDTO>> getAllTools() {
        log.info("GET /api/prompts/tools - Fetching all tools");
        List<ToolDTO> prompts = promptService.getAllTools();
        return ResponseEntity.ok(prompts);
    }

    @GetMapping("/{id}")
    public ResponseEntity<PromptDTO> getPromptById(@PathVariable String id) {
        log.info("GET /api/prompts/{} - Fetching prompt by id", id);
        PromptDTO prompt = promptService.getPromptById(id);
        return ResponseEntity.ok(prompt);
    }

    @PostMapping
    public ResponseEntity<PromptDTO> createPrompt(@Valid @RequestBody PromptDTO promptDTO) {
        log.info("POST /api/prompts - Creating new prompt: {}", promptDTO.getTitle());
        PromptDTO createdPrompt = promptService.createPrompt(promptDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdPrompt);
    }

    @PutMapping("/{id}")
    public ResponseEntity<PromptDTO> updatePrompt(
            @PathVariable String id,
            @Valid @RequestBody PromptDTO promptDTO) {
        log.info("PUT /api/prompts/{} - Updating prompt", id);
        PromptDTO updatedPrompt = promptService.updatePrompt(id, promptDTO);
        return ResponseEntity.ok(updatedPrompt);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePrompt(@PathVariable String id) {
        log.info("DELETE /api/prompts/{} - Deleting prompt", id);
        promptService.deletePrompt(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/search")
    public ResponseEntity<List<PromptDTO>> searchPrompts(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String sourceTool,
            @RequestParam(required = false) String keyword) {

        log.info("GET /api/prompts/search - Searching prompts with filters: title={}, sourceTool={}, keyword={}",
                title, sourceTool, keyword);

        List<PromptDTO> prompts;

        if (title != null && !title.trim().isEmpty()) {
            prompts = promptService.searchPromptsByTitle(title);
        } else if (sourceTool != null && !sourceTool.trim().isEmpty()) {
            prompts = promptService.getPromptsBySourceTool(sourceTool);
        } else if (keyword != null && !keyword.trim().isEmpty()) {
            prompts = promptService.searchPromptsByKeyword(keyword);
        } else {
            prompts = promptService.getAllPrompts();
        }

        return ResponseEntity.ok(prompts);
    }
}
