package com.vitality.ai.chat.agent.configuration.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "postgres")
@Data
public class PostGresProperties {
    private String user;
    private String password;
    private String table;
    private Integer dimension;
    private Integer port;
    private String host;
    private String database;
}
