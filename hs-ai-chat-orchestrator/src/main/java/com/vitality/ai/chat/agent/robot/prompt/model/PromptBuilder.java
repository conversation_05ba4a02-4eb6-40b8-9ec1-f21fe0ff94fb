package com.vitality.ai.chat.agent.robot.prompt.model;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * Builder class for creating formatted prompt strings with specific sections.
 * Each section contains itemized content that gets formatted into a structured prompt.
 */
@Service
public class PromptBuilder {

    private final List<String> contextItems = new ArrayList<>();
    private final List<String> keywords = new ArrayList<>();
    private final List<String> description = new ArrayList<>();
    private String agentCategory;
    private final List<String> dataItems = new ArrayList<>();
    private final List<String> rulesItems = new ArrayList<>();
    private final List<String> instructionsItems = new ArrayList<>();
    private final List<String> outputItems = new ArrayList<>();
    private final List<String> complianceItems = new ArrayList<>();

    private static final String SECTION_SEPARATOR = "\n\n";
    private static final String ITEM_PREFIX = "- ";

    /**
     * Add a context item to the Context section
     */
    public PromptBuilder addContext(String contextItem) {
        if (contextItem != null && !contextItem.trim().isEmpty()) {
            this.contextItems.add(contextItem.trim());
        }
        return this;
    }

    /**
     * Add a context item to the Context section
     */
    public PromptBuilder addKeywords(String contextItem) {
        if (contextItem != null && !contextItem.trim().isEmpty()) {
            this.keywords.add(contextItem.trim());
        }
        return this;
    }

    public PromptBuilder addDescription(String contextItem) {
        if (contextItem != null && !contextItem.trim().isEmpty()) {
            this.description.add(contextItem.trim());
        }
        return this;
    }

    /**
     * Add a context item to the Context section
     */
    public PromptBuilder addData(String contextItem) {
        if (dataItems != null && !contextItem.trim().isEmpty()) {
            this.dataItems.add(contextItem.trim());
        }
        return this;
    }

    /**
     * Add multiple context items at once
     */
    public PromptBuilder addContextItems(List<String> items) {
        if (items != null) {
            items.stream()
                    .filter(item -> item != null && !item.trim().isEmpty())
                    .forEach(item -> this.contextItems.add(item.trim()));
        }
        return this;
    }

    /**
     * Add a rule item to the Rules section
     */
    public PromptBuilder addRule(String ruleItem) {
        if (ruleItem != null && !ruleItem.trim().isEmpty()) {
            this.rulesItems.add(ruleItem.trim());
        }
        return this;
    }

    /**
     * Add multiple rule items at once
     */
    public PromptBuilder addRules(List<String> items) {
        if (items != null) {
            items.stream()
                    .filter(item -> item != null && !item.trim().isEmpty())
                    .forEach(item -> this.rulesItems.add(item.trim()));
        }
        return this;
    }

    /**
     * Add an instruction item to the Instructions section
     */
    public PromptBuilder addInstruction(String instructionItem) {
        if (instructionItem != null && !instructionItem.trim().isEmpty()) {
            this.instructionsItems.add(instructionItem.trim());
        }
        return this;
    }

    /**
     * Add multiple instruction items at once
     */
    public PromptBuilder addInstructions(List<String> items) {
        if (items != null) {
            items.stream()
                    .filter(item -> item != null && !item.trim().isEmpty())
                    .forEach(item -> this.instructionsItems.add(item.trim()));
        }
        return this;
    }

    /**
     * Add an output specification item to the Output section
     */
    public PromptBuilder addOutput(String outputItem) {
        if (outputItem != null && !outputItem.trim().isEmpty()) {
            this.outputItems.add(outputItem.trim());
        }
        return this;
    }

    /**
     * Add multiple output items at once
     */
    public PromptBuilder addOutputItems(List<String> items) {
        if (items != null) {
            items.stream()
                    .filter(item -> item != null && !item.trim().isEmpty())
                    .forEach(item -> this.outputItems.add(item.trim()));
        }
        return this;
    }

    /**
     * Add a compliance requirement to the Unbreakable Compliance section
     */
    public PromptBuilder addCompliance(String complianceItem) {
        if (complianceItem != null && !complianceItem.trim().isEmpty()) {
            this.complianceItems.add(complianceItem.trim());
        }
        return this;
    }

    /**
     * Add multiple compliance items at once
     */
    public PromptBuilder addComplianceItems(List<String> items) {
        if (items != null) {
            items.stream()
                    .filter(item -> item != null && !item.trim().isEmpty())
                    .forEach(item -> this.complianceItems.add(item.trim()));
        }
        return this;
    }

    /**
     * Build and return the formatted prompt string
     */
    public String build() {
        StringJoiner promptJoiner = new StringJoiner(SECTION_SEPARATOR);

        //---
        promptJoiner.add("---");
        promptJoiner.add(buildMetaSection("description", description));
        promptJoiner.add(buildMetaSection("keywords", keywords));
        if (agentCategory != null) {
            promptJoiner.add(buildMetaSection("agent_category", agentCategory));
        }


        promptJoiner.add("---");
        //---


        // Add Context section
        if (!contextItems.isEmpty()) {
            promptJoiner.add(buildSection("CONTEXT", contextItems));
        }

        // Add Context section
        if (!dataItems.isEmpty()) {
            promptJoiner.add(buildSection("DATA", dataItems));
        }

        // Add Rules section
        if (!rulesItems.isEmpty()) {
            promptJoiner.add(buildSection("RULES", rulesItems));
        }

        // Add Instructions section
        if (!instructionsItems.isEmpty()) {
            promptJoiner.add(buildSection("INSTRUCTIONS", instructionsItems));
        }

        // Add Output section
        if (!outputItems.isEmpty()) {
            promptJoiner.add(buildSection("OUTPUT", outputItems));
        }

        // Add Unbreakable Compliance section
        if (!complianceItems.isEmpty()) {
            promptJoiner.add(buildSection("UNBREAKABLE COMPLIANCE", complianceItems));
        }

        return promptJoiner.toString();
    }

    /**
     * Helper method to build individual sections with itemized content
     */
    private String buildSection(String sectionTitle, List<String> items) {
        StringBuilder section = new StringBuilder();
        section.append("## ").append(sectionTitle).append("\n");

        for (String item : items) {
            section.append(ITEM_PREFIX).append(item).append("\n");
        }

        return section.toString().trim();
    }

    /**
     * Helper method to build individual sections with itemized content
     */
    private String buildMetaSection(String sectionTitle, List<String> items) {
        StringBuilder section = new StringBuilder();
        section.append(sectionTitle).append(":");

        StringJoiner joiner = new StringJoiner(",");
        for (String item : items) {
            joiner.add(item);
        }

        section.append(joiner);

        return section.toString().trim();
    }

    /**
     * Helper method to build individual sections with itemized content
     */
    private String buildMetaSection(String sectionTitle, String items) {
        StringBuilder section = new StringBuilder();
        section.append(sectionTitle).append(":");
        section.append(items);
        return section.toString().trim();
    }

    /**
     * Clear all sections and reset the builder
     */
    public PromptBuilder clear() {
        contextItems.clear();
        keywords.clear();
        description.clear();
        dataItems.clear();
        rulesItems.clear();
        instructionsItems.clear();
        outputItems.clear();
        complianceItems.clear();
        agentCategory = null;
        return this;
    }

    /**
     * Check if the builder has any content
     */
    public boolean isEmpty() {
        return contextItems.isEmpty() &&
                rulesItems.isEmpty() &&
                instructionsItems.isEmpty() &&
                outputItems.isEmpty() &&
                complianceItems.isEmpty();
    }

    /**
     * Get the count of items in all sections combined
     */
    public int getTotalItemCount() {
        return contextItems.size() +
                rulesItems.size() +
                instructionsItems.size() +
                outputItems.size() +
                complianceItems.size();
    }

    public void addAgentCategory(String points) {
        this.agentCategory = points;
    }
}

