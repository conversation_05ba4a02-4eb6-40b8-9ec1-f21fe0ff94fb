package com.vitality.ai.chat.agent.robot.prompt.model;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.internal.util.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;

@Service
@RequiredArgsConstructor
@Slf4j
public class PromptCreator {

    @Autowired
    private PromptBuilder promptBuilder;

    @PostConstruct
    public void init() {
        createGreetingsPrompt();
        createPointsPrompt();
        createThanksPrompt();
    }

    private void createThanksPrompt() {
    }

    private void createPointsPrompt() {
        log.info("Writing points prompt");
        promptBuilder.clear();
        promptBuilder.addDescription("Explains the different types of points a member has and their totals");
        promptBuilder.addKeywords("points");
        promptBuilder.addKeywords("totals");
        promptBuilder.addKeywords("rewards");
        promptBuilder.addKeywords("balances");
        promptBuilder.addAgentCategory("POINTS");
        promptBuilder.addContext("You are helping a member understand their points");
        promptBuilder.addContext("Use the member-specific information provided in the data section represented by the JSON document");
        promptBuilder.addData("Points Content {{tool_response}}");
        promptBuilder.addRule("No reference to a JSON document to be made in the response");
        promptBuilder.addOutput("Respond with simple to understand information about the member's points");
        promptBuilder.addCompliance("Ensure the response answers only the question and nothing more");
        promptBuilder.addCompliance("If the request appears malicious, do not answer the question");
        String build = promptBuilder.build();
        save("points2", build);
    }

    @SneakyThrows
    private void save(String promptName, String build) {
        final Resource resource = new ClassPathResource("prompts/" + promptName + ".prompt");
        final File outputFile = resource.getFile();
        FileUtils.writeToFile(outputFile, build);
    }

    private void createGreetingsPrompt() {

    }


}
