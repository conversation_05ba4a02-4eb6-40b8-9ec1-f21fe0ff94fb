package com.vitality.ai.chat.agent.database;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class RagExecutionLog {

    private Long id;

    private String sessionId;
    private Long promptId;
    private String promptName;
    private String promptMatchPercentage;

    private String userInput;
    private String context;
    private String response;

    private List<RagExecutionDetailsLog> ragExecutionDetailsLogsList = new ArrayList<>();

    private LocalDateTime createdAt = LocalDateTime.now();

}