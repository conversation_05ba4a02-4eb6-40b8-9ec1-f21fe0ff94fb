package com.vitality.ai.chat.agent.robot.service;

import com.vitality.ai.chat.agent.robot.memory.MemoryManager;
import com.vitality.ai.chat.agent.robot.prompt.model.Prompt;
import com.vitality.ai.chat.agent.robot.prompt.route.AgentSelectionService;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.TextContent;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ConversationService {

    private final ChatModel model;
    private final MemoryManager memoryManager;
    private final ConversationLogService conversationLogService;
    private final AgentSelectionService agentSelectionService;

    public String executePrompt(String sessionId, String userInput) {
        //Restore Chat History
        final ChatMemory memory = memoryManager.get(sessionId);

        // Determine Routing...
        final Prompt fullPrompt = agentSelectionService.determinePromptForIntent(userInput, memory);

        //Get the system prompt to answer the users question and drive the outcome through a system message
        final String prompt = fullPrompt.getFullPrompt(); //Check this...
        final SystemMessage systemMessage = SystemMessage.from(prompt);
        //The actual message
        final TextContent userTextMessage = new TextContent(userInput);
        final UserMessage userMessage = UserMessage.from(userTextMessage);

        final List<ChatMessage> chatMessageList = List.of(systemMessage, userMessage);

        chatMessageList.forEach(chatMessage -> {
            memory.add(chatMessage);
        });

        //Build the message to the AI
        final ChatResponse chat = model.chat(chatMessageList);
        memory.add(chat.aiMessage());
        conversationLogService.saveResult(sessionId, fullPrompt, chatMessageList, chat.aiMessage());

        return chat.aiMessage().text();
    }


}
