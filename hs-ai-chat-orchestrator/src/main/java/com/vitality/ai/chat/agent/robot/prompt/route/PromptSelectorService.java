package com.vitality.ai.chat.agent.robot.prompt.route;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.chat.agent.robot.prompt.PromptDefinitionRepository;
import com.vitality.ai.chat.agent.robot.prompt.PromptRetriever;
import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.agent.robot.prompt.route.model.PromptMatch;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.model.chat.ChatModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@RequiredArgsConstructor
@Slf4j
public class PromptSelectorService {

    private final PromptRetriever retriever;
    private final RouterPromptBuilder builder;
    private final PromptDefinitionRepository repository;
    private final ChatModel model;
    private final ObjectMapper objectMapper;

    public PromptDefinition selectPrompt(String userInput, ChatMemory memorySummary) {

        final List<PromptDefinition> candidates = retriever.findRelevantPrompts(userInput, memorySummary);

        if (candidates.isEmpty()) {
            return repository.getDefaultPromptValue();
        }

        String routerTemplate = builder.buildRoutingPrompt(candidates, memorySummary, userInput);
        String chosenName = "default";

        //@TODO TRACKING
        final String routerPromptResponse = model.chat(routerTemplate);
        System.out.println("Router Prompt Response: " + routerPromptResponse);
        try {
            final List<PromptMatch> matches = parseMatches(routerPromptResponse);
            //find highest score
            matches.sort((a, b) -> Double.compare(b.getConfidence(), a.getConfidence()));
            final PromptMatch first = matches.getFirst();
            chosenName = first.getName();

        } catch (Exception e) {
            log.error("Problem calling router prompt: {}", e.getMessage(), e);
        }

        return repository.getPrompt(chosenName);
    }

    public PromptDefinition getPrompt(String promptName) {
        return repository.getPrompt(promptName);
    }

    public List<PromptMatch> parseMatches(String rawResponse) throws Exception {
        // Extract the JSON array between the first [ and last ]
        Pattern pattern = Pattern.compile("\\[.*?\\]", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(rawResponse);

        if (matcher.find()) {
            String jsonArray = matcher.group();
            return objectMapper.readValue(jsonArray, new TypeReference<List<PromptMatch>>() {});
        } else {
            throw new IllegalArgumentException("No valid JSON array found in response");
        }
    }

}
