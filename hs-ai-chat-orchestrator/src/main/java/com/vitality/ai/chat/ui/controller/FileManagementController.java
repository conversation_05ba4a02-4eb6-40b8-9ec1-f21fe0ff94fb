package com.vitality.ai.chat.ui.controller;

import com.vitality.ai.chat.ui.service.FileUtilitiesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:4200", "http://localhost:3000"})
public class FileManagementController {

    private final FileUtilitiesService fileUtilitiesService;

    @GetMapping("/list")
    public ResponseEntity<List<FileUtilitiesService.FileInfo>> listPromptFiles() {
        log.info("GET /api/files/list - Listing all prompt files");
        List<FileUtilitiesService.FileInfo> files = fileUtilitiesService.listAllPromptFiles();
        return ResponseEntity.ok(files);
    }

    @GetMapping("/content/{fileName}")
    public ResponseEntity<Map<String, Object>> getFileContent(@PathVariable String fileName) {
        log.info("GET /api/files/content/{} - Reading file content", fileName);

        try {
            String content = fileUtilitiesService.readFileContent(fileName);
            Map<String, Object> response = new HashMap<>();
            response.put("fileName", fileName);
            response.put("content", content);
            response.put("isValid", fileUtilitiesService.validateMarkdownFormat(content));

            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("Error reading file: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadPromptFile(@RequestParam("file") MultipartFile file) {
        log.info("POST /api/files/upload - Uploading file: {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "File is empty"));
        }

        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null || !originalFileName.endsWith(".md")) {
            return ResponseEntity.badRequest().body(Map.of("error", "File must be a .md file"));
        }

        try {
            String content = new String(file.getBytes());

            // Validate markdown format
            if (!fileUtilitiesService.validateMarkdownFormat(content)) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid markdown prompt format"));
            }

            // Generate unique filename if needed
            String fileName = generateUniqueFileName(originalFileName);

            fileUtilitiesService.writeFileContent(fileName, content);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "File uploaded successfully");
            response.put("fileName", fileName);
            response.put("originalFileName", originalFileName);

            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (IOException e) {
            log.error("Error uploading file: {}", originalFileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to upload file"));
        }
    }

    @PutMapping("/content/{fileName}")
    public ResponseEntity<Map<String, Object>> updateFileContent(
            @PathVariable String fileName,
            @RequestBody Map<String, String> request) {

        log.info("PUT /api/files/content/{} - Updating file content", fileName);

        String content = request.get("content");
        if (content == null || content.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", "Content cannot be empty"));
        }

        // Validate markdown format
        if (!fileUtilitiesService.validateMarkdownFormat(content)) {
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid markdown prompt format"));
        }

        try {
            // Backup existing file
            fileUtilitiesService.backupFile(fileName);

            // Write new content
            fileUtilitiesService.writeFileContent(fileName, content);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "File updated successfully");
            response.put("fileName", fileName);

            return ResponseEntity.ok(response);

        } catch (IOException e) {
            log.error("Error updating file: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to update file"));
        }
    }

    @DeleteMapping("/{fileName}")
    public ResponseEntity<Map<String, Object>> deleteFile(@PathVariable String fileName) {
        log.info("DELETE /api/files/{} - Deleting file", fileName);

        try {
            // Backup before deletion
            fileUtilitiesService.backupFile(fileName);

            boolean deleted = fileUtilitiesService.deleteFile(fileName);

            if (deleted) {
                return ResponseEntity.ok(Map.of("message", "File deleted successfully"));
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Error deleting file: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete file"));
        }
    }

    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateContent(@RequestBody Map<String, String> request) {
        log.info("POST /api/files/validate - Validating markdown content");

        String content = request.get("content");
        if (content == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "Content is required"));
        }

        boolean isValid = fileUtilitiesService.validateMarkdownFormat(content);

        Map<String, Object> response = new HashMap<>();
        response.put("isValid", isValid);

        if (!isValid) {
            response.put("errors", getValidationErrors(content));
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getDirectoryStats() {
        log.info("GET /api/files/stats - Getting directory statistics");

        List<FileUtilitiesService.FileInfo> files = fileUtilitiesService.listAllPromptFiles();
        long totalSize = fileUtilitiesService.getDirectorySize();

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalFiles", files.size());
        stats.put("totalSize", totalSize);
        stats.put("averageFileSize", files.isEmpty() ? 0 : totalSize / files.size());

        return ResponseEntity.ok(stats);
    }

    // Helper methods

    private String generateUniqueFileName(String originalFileName) {
        String baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        String extension = originalFileName.substring(originalFileName.lastIndexOf('.'));

        String fileName = originalFileName;
        int counter = 1;

        try {
            while (Files.exists(Paths.get(System.getProperty("app.prompts.directory", "./prompts"), fileName))) {
                fileName = baseName + "_" + counter + extension;
                counter++;
            }
        } catch (Exception e) {
            log.warn("Error checking file existence, using timestamp", e);
            fileName = baseName + "_" + System.currentTimeMillis() + extension;
        }

        return fileName;
    }

    private List<String> getValidationErrors(String content) {
        List<String> errors = new ArrayList<>();

        if (!content.contains("---")) {
            errors.add("Missing metadata section (should start and end with ---)");
        }

        if (!content.contains("## CONTEXT")) {
            errors.add("Missing CONTEXT section");
        }

        if (!content.contains("## DATA")) {
            errors.add("Missing DATA section");
        }

        if (!content.contains("## RULES")) {
            errors.add("Missing RULES section");
        }

        if (!content.contains("## OUTPUT")) {
            errors.add("Missing OUTPUT section");
        }

        if (!content.contains("## UNBREAKABLE COMPLIANCE")) {
            errors.add("Missing UNBREAKABLE COMPLIANCE section");
        }

        return errors;
    }
}
