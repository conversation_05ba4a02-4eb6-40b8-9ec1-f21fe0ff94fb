package com.vitality.ai.chat.agent.configuration;

import com.vitality.ai.chat.agent.configuration.properties.ElasticSearchProperties;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.elasticsearch.ElasticsearchEmbeddingStore;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("elasticsearch")
public class ElasticSearchEmbeddedStoreConfiguration {

    @Autowired
    private ElasticSearchProperties elasticSearchProperties;

    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        final HttpHost httpHost = new HttpHost(elasticSearchProperties.getHost(), elasticSearchProperties.getPort());
        final RestClient restClient = RestClient.builder(httpHost)
            .build();

        return ElasticsearchEmbeddingStore.builder()
            .restClient(restClient)
            .indexName(elasticSearchProperties.getTable())
            .build();
    }
}
