package com.vitality.ai.chat.ui.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@Service
@Slf4j
public class FileUtilitiesService {

    @Value("${app.prompts.directory:./prompts}")
    private String promptsDirectory;

    public List<FileInfo> listAllPromptFiles() {
        List<FileInfo> files = new ArrayList<>();
        Path promptsPath = Paths.get(promptsDirectory);

        try (Stream<Path> fileStream = Files.walk(promptsPath, 1)) {
            fileStream
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".md"))
                    .forEach(path -> {
                        try {
                            BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
                            FileInfo fileInfo = new FileInfo();
                            fileInfo.setFileName(path.getFileName().toString());
                            fileInfo.setFilePath(path.toString());
                            fileInfo.setSize(attrs.size());
                            fileInfo.setCreated(LocalDateTime.ofInstant(attrs.creationTime().toInstant(), ZoneId.systemDefault()));
                            fileInfo.setModified(LocalDateTime.ofInstant(attrs.lastModifiedTime().toInstant(), ZoneId.systemDefault()));
                            files.add(fileInfo);
                        } catch (IOException e) {
                            log.warn("Failed to read attributes for file: {}", path, e);
                        }
                    });
        } catch (IOException e) {
            log.error("Error listing prompt files", e);
        }

        return files;
    }

    public String readFileContent(String fileName) throws IOException {
        Path filePath = Paths.get(promptsDirectory, fileName);
        if (!Files.exists(filePath)) {
            throw new IOException("File not found: " + fileName);
        }
        return Files.readString(filePath);
    }

    public void writeFileContent(String fileName, String content) throws IOException {
        Path filePath = Paths.get(promptsDirectory, fileName);
        Files.writeString(filePath, content, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
    }

    public boolean deleteFile(String fileName) {
        try {
            Path filePath = Paths.get(promptsDirectory, fileName);
            return Files.deleteIfExists(filePath);
        } catch (IOException e) {
            log.error("Failed to delete file: {}", fileName, e);
            return false;
        }
    }

    public void backupFile(String fileName) throws IOException {
        Path originalFile = Paths.get(promptsDirectory, fileName);
        Path backupFile = Paths.get(promptsDirectory, "backup_" + fileName);

        if (Files.exists(originalFile)) {
            Files.copy(originalFile, backupFile, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    public void createDirectoryIfNotExists(String directoryPath) throws IOException {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
        }
    }

    public long getDirectorySize() {
        Path promptsPath = Paths.get(promptsDirectory);
        try (Stream<Path> fileStream = Files.walk(promptsPath)) {
            return fileStream
                    .filter(Files::isRegularFile)
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            return 0L;
                        }
                    })
                    .sum();
        } catch (IOException e) {
            log.error("Error calculating directory size", e);
            return 0L;
        }
    }

    public boolean validateMarkdownFormat(String content) {
        // Basic validation for markdown prompt format
        return content.contains("---") &&
                content.contains("## CONTEXT") &&
                content.contains("## DATA") &&
                content.contains("## RULES") &&
                content.contains("## OUTPUT") &&
                content.contains("## UNBREAKABLE COMPLIANCE");
    }

    // Inner class for file information
    public static class FileInfo {
        private String fileName;
        private String filePath;
        private long size;
        private LocalDateTime created;
        private LocalDateTime modified;

        // Getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public long getSize() { return size; }
        public void setSize(long size) { this.size = size; }

        public LocalDateTime getCreated() { return created; }
        public void setCreated(LocalDateTime created) { this.created = created; }

        public LocalDateTime getModified() { return modified; }
        public void setModified(LocalDateTime modified) { this.modified = modified; }
    }
}