package com.vitality.ai.chat.agent.robot.content;

import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.parser.apache.tika.ApacheTikaDocumentParser;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.*;
import java.util.stream.Stream;

import static dev.langchain4j.data.document.loader.FileSystemDocumentLoader.loadDocument;

@Slf4j
public class ContentPopulator {


    private final EmbeddingStoreIngestor embeddedIngestor;
    private final EmbeddingStore embeddingStore;

    public ContentPopulator(EmbeddingStoreIngestor embeddedIngestor, EmbeddingStore embeddingStore) {
        this.embeddedIngestor = embeddedIngestor;
        this.embeddingStore = embeddingStore;
    }

    public void clearContent() {
        embeddingStore.removeAll();
    }

    /**
     * Populates the embedding store by recursively scanning the provided directory
     * and ingesting each document found.
     *
     * @param directoryPath The path to the directory to scan.
     * @throws IOException If an I/O error occurs.
     */
    public void populate(String directoryPath) throws IOException {
        Path startPath = Paths.get(directoryPath);

        if (!Files.exists(startPath) || !Files.isDirectory(startPath)) {
            log.error("The provided path '{}' is not a valid directory.", directoryPath);
            throw new IllegalArgumentException("Invalid directory path: " + directoryPath);
        }

        log.info("Starting to populate documents from directory: {}", directoryPath);
        embeddingStore.removeAll();
        try (Stream<Path> paths = Files.walk(startPath)) {
            paths
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        String filename = path.toString();
                        if (filename.endsWith(".txt")
                                || filename.endsWith(".docx")
                            || filename.endsWith(".pdf")) {
                            return true;
                        }
                        return false;
                    })
                    .forEach(path -> {
                        try {
                            log.debug("Processing file: {}", path.toString());
                            Document document = loadDocument(path, new ApacheTikaDocumentParser());
                            embeddedIngestor.ingest(document);
                            log.info("Successfully ingested document: {}", path.toString());
                        } catch (Exception e) {
                            log.error("Failed to process file: {}. Error: {}", path.toString(), e.getMessage());
                            // Optionally, handle specific exceptions or continue processing
                        }
                    });
        }

        log.info("Completed populating documents from directory: {}", directoryPath);
    }
}
