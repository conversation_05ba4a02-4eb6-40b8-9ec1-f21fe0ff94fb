package com.vitality.ai.chat.agent.configuration;

import com.vitality.ai.chat.agent.configuration.properties.PostGresProperties;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.pgvector.PgVectorEmbeddingStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("postgres")
public class PostgresEmbeddedStoreConfiguration {
    @Autowired
    PostGresProperties config;

    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        PgVectorEmbeddingStore build = PgVectorEmbeddingStore.builder()
                .host(config.getHost())
                .port(config.getPort())
                .user(config.getUser())
                .password(config.getPassword())
                .table(config.getTable())
                .dimension(config.getDimension())
                .database(config.getDatabase())
                .build();
        return build;
    }

}
