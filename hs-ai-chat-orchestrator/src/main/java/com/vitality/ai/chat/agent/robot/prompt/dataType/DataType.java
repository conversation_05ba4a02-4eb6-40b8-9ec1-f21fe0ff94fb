package com.vitality.ai.chat.agent.robot.prompt.dataType;

public enum DataType {

    CONTEXT("context"),
    TOOL_RESPONSE("tool_response");

    private final String type;

    DataType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static DataType fromString(String type) {
        for (DataType dataType : DataType.values()) {
            if (dataType.type.equalsIgnoreCase(type)) {
                return dataType;
            }
        }
        throw new IllegalArgumentException("Unknown data type: " + type);
    }

}
