package com.vitality.ai.chat.agent.robot.prompt.route;

import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.ChatMemory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RouterPromptBuilder {
    
    @Autowired
    RouterPromptDefinitionRepository routerPromptDefinitionRepository;

    public String buildRoutingPrompt(List<PromptDefinition> candidates, ChatMemory memorySummaryChat, String userInput) {
        final String memorySummary = summarizeMemory(memorySummaryChat);

        final StringBuilder candidateTable = new StringBuilder();
        for (final PromptDefinition def : candidates) {
            candidateTable
                    .append("  - ")
                    .append(def.getName())
                    .append(" : ")
                    .append(def.getDescription())
                    .append(" (keywords: ")
                    .append(String.join(", ", def.getKeywords()))
                    .append(")\n");
        }

        final PromptDefinition defaultPromptValue = routerPromptDefinitionRepository.getDefaultPromptValue(); //Default for now
        final Map<String, String> contentDetails = Map.of(
                "userInput", userInput,
                "conversation", memorySummary,
                "candidates", candidateTable.toString()
        );

        return replacePlaceholders(defaultPromptValue, contentDetails);
    }

    private String replacePlaceholders(PromptDefinition template, Map<String, String> variables) {
        String templateStr = template.getContent();
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            templateStr = templateStr.replace("{{" + entry.getKey() + "}}", entry.getValue());
        }
        return templateStr;
    }
    
    

    private String summarizeMemory(ChatMemory memory) {
        final List<String> messages = memory.messages().stream()
                .map(message -> {

                    if (message instanceof UserMessage) {
                        return "User: " + ((UserMessage)message).singleText();
                    } else if (message instanceof AiMessage) {
                        return "AI: " + ((AiMessage)message).text();
                    } else {
                        return "";
                    }
                })
                .collect(Collectors.toList());

        final int totalMessages = messages.size();
        final int fromIndex = Math.max(totalMessages - 3, 0); // take last 4 or fewer

        final List<String> lastMessages = messages.subList(fromIndex, totalMessages);

        return String.join("\n", lastMessages);
    }
}
