package com.vitality.ai.chat.agent.robot.content;

import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;

public class IngestorFactory {

    // This static factory method will be used by <PERSON> to create the EmbeddingStoreIngestor
    public static EmbeddingStoreIngestor createIngestor(DocumentSplitter ds,
                                                        EmbeddingModel em,
                                                        EmbeddingStore<TextSegment> es) {
        return EmbeddingStoreIngestor.builder()
                .documentSplitter(ds)
                .embeddingModel(em)
                .embeddingStore(es)
                .build();
    }
}