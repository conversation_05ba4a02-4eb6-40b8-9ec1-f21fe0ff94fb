package com.vitality.ai.chat.ui.model;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromptDTO {

    private String id;

    @NotBlank(message = "Title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    private String title;

    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;

    private List<String> keywords;

    @Size(max = 100, message = "Source tool must not exceed 100 characters")
    private String sourceTool;

    private String context;

    private String data;

    private String rules;

    private String output;

    private String unbreakableCompliance;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}