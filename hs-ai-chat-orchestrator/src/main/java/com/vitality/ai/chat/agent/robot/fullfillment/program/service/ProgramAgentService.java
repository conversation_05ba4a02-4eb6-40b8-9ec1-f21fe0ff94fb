package com.vitality.ai.chat.agent.robot.fullfillment.program.service;

import com.vitality.ai.chat.activity.model.StatusMemberDtoResponse;
import com.vitality.ai.chat.agent.robot.fullfillment.AgentFiller;
import com.vitality.ai.chat.agent.robot.fullfillment.AgentFullfillment;
import org.springframework.stereotype.Service;

@Service("programAgentService")
public class ProgramAgentService implements AgentFiller {

    @AgentFullfillment(category = "POINTS", methodName = "getMemberPoints", documentation = "Get member points")
    public StatusMemberDtoResponse getMemberPoints() {
        final StatusMemberDtoResponse statusMemberDtoResponse = new StatusMemberDtoResponse();
        statusMemberDtoResponse.setMemberType(StatusMemberDtoResponse.MemberTypeEnum.PRIMARY);
        statusMemberDtoResponse.setAchievedLevel("Gold");
        statusMemberDtoResponse.setRewardStatus("Gold");
        statusMemberDtoResponse.setTotalPoints(13455L);
        statusMemberDtoResponse.setPointsToAchieveNextLevel(3000L);
        statusMemberDtoResponse.setEntityCarryoverPoints(350L);
        return statusMemberDtoResponse;
    }



}
