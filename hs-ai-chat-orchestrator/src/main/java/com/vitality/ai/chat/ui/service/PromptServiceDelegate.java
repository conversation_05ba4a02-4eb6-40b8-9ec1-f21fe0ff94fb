package com.vitality.ai.chat.ui.service;

import com.vitality.ai.chat.ui.model.PromptDTO;
import com.vitality.ai.chat.ui.model.ToolDTO;

import java.util.List;

public interface PromptServiceDelegate {
    List<PromptDTO> getAllPrompts();

    List<ToolDTO> getAllTools();

    PromptDTO getPromptById(String id);

    PromptDTO createPrompt(PromptDTO promptDTO);

    PromptDTO updatePrompt(String id, PromptDTO promptDTO);

    void deletePrompt(String id);

    List<PromptDTO> searchPromptsByTitle(String title);

    public List<PromptDTO> getPromptsBySourceTool(String sourceTool);

    public List<PromptDTO> searchPromptsByKeyword(String keyword);
}
