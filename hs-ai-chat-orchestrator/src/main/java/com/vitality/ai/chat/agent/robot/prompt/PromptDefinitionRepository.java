package com.vitality.ai.chat.agent.robot.prompt;

import com.vitality.ai.chat.agent.robot.prompt.model.PromptDefinition;
import com.vitality.ai.chat.database.databaseMapping.PromptAgentCategory;
import com.vitality.ai.chat.database.databaseMapping.PromptKeywords;
import com.vitality.ai.chat.database.databaseMapping.PromptTemplate;
import com.vitality.ai.chat.database.repository.PromptRepository;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.output.Response;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.flywaydb.core.internal.util.FileUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.vitality.ai.chat.agent.util.Similarity.cosineSimilarity;

@Service
@RequiredArgsConstructor
@Transactional
public class PromptDefinitionRepository implements InitializingBean {

    private static final double MINIMUM_SIMILARITY_THRESHOLD = 0.75;

    private final EmbeddingModel embeddingModel; // 🧠 LangChain4j Embedding Model
    private final PromptRetrieverService promptRetrieverService;
    private PromptDefinition defaultPrompt;
    private final Map<String, PromptDefinition> prompts = new ConcurrentHashMap<>();

    public PromptDefinition getPrompt(String name) {
        PromptDefinition def = prompts.get(name);
        if (def == null) {
           return defaultPrompt;
        }
        return def;
    }

    public Collection<PromptDefinition> allPrompts() {
        return prompts.values();
    }

    public PromptDefinition getDefaultPromptValue() {
        return defaultPrompt;
    }


    public String findBestPrompt(String userInput) {
        List<Float> inputEmbedding = createEmbedding(userInput);

        return prompts.values().stream()
                .map(prompt -> new AbstractMap.SimpleEntry<>(prompt, cosineSimilarity(prompt.getEmbedding(), inputEmbedding)))
                .filter(entry -> entry.getValue() >= MINIMUM_SIMILARITY_THRESHOLD)
                .max(Comparator.comparingDouble(Map.Entry::getValue))
                .map(entry -> entry.getKey().getName())
                .orElse(defaultPrompt.getName()); // fallback to default if no match
    }



//    Caching
    public void initFile() throws IOException {
        final ClassPathResource resource = new ClassPathResource("prompts");
        //Read from the database
        Files.list(Path.of(resource.getFile().getAbsolutePath()))
                .filter(Files::isRegularFile)
                .forEach(path -> {
                    final String content = FileUtils.readAsString(path);
                    final String name = path.getFileName().toString().replace(".prompt", "");
                    final PromptDefinition def = parsePrompt(name, content);
                    prompts.put(def.getName(), def);
                });

        defaultPrompt = prompts.get("default");
        if (defaultPrompt == null) {
            throw new IllegalStateException("Missing required default.prompt file in prompts folder.");
        }
    }

    public void initDatabase() throws IOException {
        Map<String, PromptDefinition> generalChat = promptRetrieverService.getPromptsInDatabase("generalChat");
        if (generalChat.isEmpty()) {
            throw new IllegalStateException("No prompts found in the database for 'generalChat'.");
        }

        generalChat.entrySet().forEach(entry -> {
            PromptDefinition prompt = entry.getValue();
            // Ensure the prompt has an embedding
            if (prompt.getEmbedding() == null || prompt.getEmbedding().isEmpty()) {
                String fullText = prompt.getDescription() + " " + String.join(" ", prompt.getKeywords());
                List<Float> embedding = createEmbedding(fullText);
                prompt.setEmbedding(embedding);
                prompts.put(prompt.getName(), prompt);
            }
        });
    }

    private PromptDefinition parsePrompt(String name, String content) {
        final String[] parts = content.split("---", 3);
        if (parts.length < 3) {
            throw new RuntimeException("Invalid prompt file: " + name);
        }

        final Map<String, String> meta = parseMetadata(parts[1]);
        final String description = meta.getOrDefault("description", "");
        final String keywords = meta.getOrDefault("keywords", "");
        final String boostersForContent = meta.getOrDefault("boosts", "");
        final String agentCategory = meta.getOrDefault("agent_category", "");

        final String fullText = description + " " + keywords;
        final List<Float> embedding = createEmbedding(fullText);

        return PromptDefinition.builder()
                .name(name)
                .description(description)
                .keywords(Arrays.asList(keywords.split(",")))
                .boosts(Arrays.asList(boostersForContent.split(",")))
                .agentCategory(agentCategory)
                .content(parts[2].trim())
                .embedding(embedding)
                .build();
    }

    private Map<String, String> parseMetadata(String metadata) {
        final Map<String, String> map = new HashMap<>();
        Arrays.stream(metadata.split("\n"))
                .forEach(line -> {
                    final String[] kv = line.split(":", 2);
                    if (kv.length == 2) {
                        map.put(kv[0].trim(), kv[1].trim());
                    }
                });
        return map;
    }

    private List<Float> createEmbedding(String text) {
        try {
            final Response<Embedding> response = embeddingModel.embed(text);
            return response.content().vectorAsList();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create embedding for text: " + text, e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
            initFile();
            initDatabase();
    }
}

