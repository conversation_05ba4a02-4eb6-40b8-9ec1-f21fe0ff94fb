spring:
  cloud:
    config:
      enabled: false
  config:
    use-legacy-processing: true
  main:
    allow-bean-definition-overriding: true
  datasource:
    type: org.apache.tomcat.jdbc.pool.DataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:chat
    username: sa
    password:
  jpa:
    properties:
      javax:
        persistence:
          sharedCache:
            mode: ENABLE_SELECTIVE
      generate_statistics: true
      hibernate:
        show_sql: false
        dialect: org.hibernate.dialect.H2Dialect
        id:
          new_generator_mappings: true
        current_session_context_class: thread
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: none
  flyway:
    enabled: true
    locations:
      - classpath:database/db/migration
      - classpath:database/db/migration-data
  web:
    resources:
      static-locations: classpath:/static/browser
      cache:
        period: 0
  mvc:
    view:
      prefix: /
      suffix: .html
    static-path-pattern: /**
  h2:
    console:
      enabled: true


server:
  port: 34000
  servlet:
    context-path: /v3/assistant

openapi:
  servers: http://localhost:34000/v3/assistant

postgres:
  dimension: 384
  host: localhost
  port: 5433
  database: vectordb
  table: benefit_segments
  user: vectoruser
  password: vectorpass

elasticsearch:
  host: localhost
  port: 9200
  table: benefit_segments

vertex:
  project: encoded-aspect-172113
  location: us-central1
  model: gemini-2.0-flash-001
  credentials:
    type: service_account
    projectId: hs-ai-chat-test
    privateKeyId: bfa66072e1b542d524ab93f2f422ea22d2737704
    privateKey: "test"
    clientEmail: <EMAIL>
    clientId: "100725217112964160298"
    authUri: https://accounts.google.com/o/oauth2/auth
    tokenUri: https://oauth2.googleapis.com/token
    authProviderX509CertUrl: https://www.googleapis.com/oauth2/v1/certs
    clientX509CertUrl: https://www.googleapis.com/robot/v1/metadata/x509/tornike-vertex-ai%40hs-ai-chat-test.iam.gserviceaccount.com
    universeDomain: googleapis.com
  allowed-dns:
    - CN=paca.discovery.holdings.co.za
    - CN=WE2,O=Google Trust Services,C=US
  temperature: 0.1
  max-output-tokens: 5000
  top-k: 40
  top-p: 0.95
  seed: 1234
