import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

buildscript {
    ext {
        springCloudVersion = "2023.0.3"
        springIntegationVersion = '6.3.3'
        springIntegationKafkaVersion = '6.3.4'
    }

    dependencies {
        classpath "org.glassfish.jaxb:jaxb-runtime:2.3.2"
    }
}

plugins {
    id 'java'
    id 'idea'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'com.google.cloud.tools.jib'
    id "org.openapi.generator"
    id 'com.github.node-gradle.node' version '3.3.0'
    id 'productivity-plugin'
    id 'za.co.discovery.publisher.aws-codeartifact-publisher'
    id "jacoco"
    id "org.sonarqube" version "3.5.0.2730"
}

sourceCompatibility = JavaVersion.VERSION_21
description = 'hs-chat-orchestrator Application'
group = 'com.vitality.ai.chat'
version = '0.0.1-SNAPSHOT'

idea {
    module { generatedSourceDirs += file('src/generated/java') }
    module { generatedSourceDirs += file('build/generated/src/main/java') }
    module { generatedSourceDirs += file('build/generated/sources/annotationProcessor/java') }
}

sourceSets {
    main { java { srcDirs += file('src/generated/java') } }
    main { java { srcDirs += file('build/generated/src/main/java') } }
    main { java { srcDirs += file('build/generated/sources/annotationProcessor/java') } }
}

test {
    maxHeapSize = "2048m"
    useJUnitPlatform()
}

def angularDir = "${projectDir}/src/main/angular/chat-ui"


node {
    version = '18.19.0'
    download = true
    workDir = file("${project.buildDir}/nodejs")
    npmWorkDir = file("${project.buildDir}/npm")
    nodeProjectDir = file("${angularDir}")
}
// Use the plugin's npmInstall task
npmInstall {
    // Input/output specifications for better incremental build support
    inputs.file("${angularDir}/package.json")
    outputs.dir("${angularDir}/node_modules")
}


task buildAngular(type: com.github.gradle.node.npm.task.NpmTask) {
    description = 'Builds the Angular application'
    workingDir = file("${angularDir}")
    args = ['run', 'build-spring']
    dependsOn npmInstall
    inputs.dir file("${angularDir}/src")
    outputs.dir file("${angularDir}/dist")
}

//task copyAngularToStatic(type: Copy) {
//    dependsOn buildAngular
//    from file("${angularDir}/dist/chatui")
//    into file("${projectDir}/src/main/resources/static")
//}
//
//processResources.dependsOn 'copyAngularToStatic'

tasks.register("activity-bff", GenerateTask) {
    generatorName = "java"
    skipValidateSpec = true
    generateApiTests  = false
    generateModelTests = false
    generateApiDocumentation = false
    inputSpec = "$projectDir/src/main/resources/openapi/hs-activity-bff.json".toString()
    outputDir = "$projectDir/build/generated".toString()
    modelPackage = "com.vitality.ai.chat.activity.model"
    apiPackage = "com.vitality.ai.chat.activity.api"
    configOptions = [
            enumUnknownDefaultCase: "true",
            useNullForUnknownEnumValue: "true",
            disableHtmlEscaping: "true",
            serializationLibrary: "jackson",
            hideGenerationTimestamp: "true",
            annotationLibrary : "none",
            dateLibrary       : "java8",
            java8             : "false",
            useTags           : "false",
            library           : "resttemplate",
            initializeCollections: "true"

    ]
    ignoreFileOverride = "$projectDir/.openapi-generator-ignore"
}


configurations {
    compileOnly { extendsFrom annotationProcessor }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

dependencies {
    implementation platform(project(':hs-platform-ai'))
    implementation platform('com.google.cloud:libraries-bom:26.61.0') // <-- UPDATE THIS TO THE LATEST STABLE BOM
    implementation project(":hs-common-ai-starter")

    implementation('org.springframework.boot:spring-boot-starter-data-jpa') { exclude group: 'com.zaxxer', module: 'HikariCP' }
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    //Spring
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.boot:spring-boot-starter'

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'org.apache.commons:commons-text'
    implementation 'commons-validator:commons-validator'
    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
//    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'commons-validator:commons-validator'

    implementation 'org.apache.httpcomponents:httpclient'
    //Database
    implementation 'javax.cache:cache-api'
    implementation 'org.ehcache:ehcache'
    // mapstruct
    implementation 'org.mapstruct:mapstruct:1.4.2.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.2.Final'
    //logging
    //To send logging to a json file or to a TCP port (on kubernetes):
    implementation "net.logstash.logback:logstash-logback-encoder"
    //Conditionals in the logback configs:
    implementation 'org.codehaus.janino:janino'

    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    implementation 'dev.langchain4j:langchain4j-elasticsearch-spring-boot-starter:1.3.0-beta9'

    //Swagger
    implementation 'org.openapitools:jackson-databind-nullable'
    implementation 'za.co.discovery.health.hs:hs-starter-swagger'

    annotationProcessor 'jakarta.annotation:jakarta.annotation-api'

    implementation 'io.swagger:swagger-annotations'
    annotationProcessor 'jakarta.persistence:jakarta.persistence-api'
    implementation 'jakarta.persistence:jakarta.persistence-api'
    implementation 'javax.annotation:javax.annotation-api'

    //Database

    implementation "org.flywaydb:flyway-core"
    implementation 'org.apache.tomcat:tomcat-jdbc'
    implementation 'com.h2database:h2'
    implementation "com.sap.cloud.db.jdbc:ngdbc"
    implementation 'commons-io:commons-io'
    implementation 'org.hibernate.validator:hibernate-validator'

    testImplementation 'za.co.discovery.health.hs:hs-starter-test:20250714'
    testAnnotationProcessor 'org.projectlombok:lombok'
}

springBoot {
    buildInfo {
        properties {
            time = null
        }
    }
}

bootJar {
    manifest {
        attributes 'Start-Class': 'com.vitality.ai.chat.ChatOrchestrationApplication'
    }
}

productivityGeneratorTask {

    productivitySetting {
        rootPackageName = 'com.vitality.ai.chat.database'
        generatedDestination = 'build/generated/src/main/java'
        profiles = ['database']
        applicationName = 'hs-chat-orchestrator'

        databaseSetting {
            cascadeDetails = "ALL"
            foreignKeyCollectionInverse = true
            foreignKeyCollectionLazy = true
            //removes both instances of collections, FK - PK - FK
            excludeForeignKeyAsCollection = false
            //Removed the Object References
            excludeForeignKeyAsManytoOne = false
        }
    }
}

compileJava.dependsOn productivityGeneratorTask, tasks."activity-bff"
productivityGeneratorTask.dependsOn "activity-bff"

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-ai-chat-orchestrator:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['34000']
        jvmFlags = [
                '-XX:MinRAMPercentage=60.0',
                '-XX:MaxRAMPercentage=80.0',
                '-XX:+PrintFlagsFinal',
                '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.ai.chat.ChatOrchestrationApplication'
    }
    allowInsecureRegistries = true
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        failOnViolation = true
        rule {
            limit {
                minimum = 0.1
            }
        }
    }
}
