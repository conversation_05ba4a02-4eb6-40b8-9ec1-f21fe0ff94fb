package com.vitality.ai.chat.config;

import com.vitality.ai.chat.ApiClient;
import com.vitality.ai.chat.api.ChatControllerApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Configuration for wiring the REST API client that talks to the Chat Orchestrator service.
 */
@Slf4j
@Configuration
public class ChatOrchestratorConfig {

    @Autowired
    private WebClient webClient;

    @Value("${integration.service.url.hs-chat-orchestrator}")
    private String orchestratorBaseUrl;

    @Bean
    public ChatControllerApi chatControllerApi() {
        final ApiClient apiClient = new ApiClient(webClient)
            .setBasePath(orchestratorBaseUrl);

        return new ChatController<PERSON><PERSON>(apiClient);
    }
}
