package com.vitality.ai.chat.config;

import com.vitality.ai.chat.client.ChatClient;
import com.vitality.ai.chat.controller.ChatWebSocketHandler;
import com.vitality.ai.internal.interceptor.AuthHandshakeInterceptor;
import com.vitality.ai.internal.service.JwtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private JwtService jwtService;

    @Autowired
    private ChatClient client;

    @Bean
    public ChatWebSocketHandler chatWebSocketHandler() {
        return new ChatWebSocket<PERSON>andler(client);
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(chatWebSocketHandler(), "/chat")
                .addInterceptors(new AuthHandshakeInterceptor(jwtService))
                .setAllowedOrigins("*");
    }
}
