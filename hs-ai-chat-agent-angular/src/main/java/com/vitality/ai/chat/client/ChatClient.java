package com.vitality.ai.chat.client;

import com.vitality.ai.chat.api.ChatControllerApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ChatClient {

    @Autowired
    private ChatControllerApi chatControllerApi;

    public String callAssistant(final String userRequest, final String sessionId) {
        return chatControllerApi.chatter(sessionId, userRequest)
            .block();
    }

}
