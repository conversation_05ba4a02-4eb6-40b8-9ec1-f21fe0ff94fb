package com.vitality.ai.chat.controller;

import com.vitality.ai.chat.client.ChatClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
public class ChatWebSocketHandler extends TextWebSocketHandler {

    // Store active WebSocket sessions
    private final CopyOnWriteArrayList<WebSocketSession> sessions = new CopyOnWriteArrayList<>();
    private final ChatClient chatService;

    public ChatWebSocketHandler(final ChatClient chatService) {
        this.chatService = chatService;
    }

    @Override
    public void afterConnectionEstablished(final WebSocketSession session) throws Exception {
        sessions.add(session);
        session.sendMessage(new TextMessage("Hi, I am <PERSON> how can I help you?"));
    }

    @Override
    protected void handleTextMessage(final WebSocketSession session, final TextMessage message) throws Exception {
        final Authentication authentication = (Authentication) session.getAttributes().get("authentication");

        if (authentication != null) {
            final String username = authentication.getName();
            log.info("Authenticated user: {}", username);
            final String userMessage = message.getPayload();
            final String botReply = chatService.callAssistant(userMessage, username);
            session.sendMessage(new TextMessage(botReply));
        } else {
            log.error("No authenticated user found!");
        }
    }


    @Override
    public void afterConnectionClosed(final WebSocketSession session, final org.springframework.web.socket.CloseStatus status) {
        sessions.remove(session);
    }
}
