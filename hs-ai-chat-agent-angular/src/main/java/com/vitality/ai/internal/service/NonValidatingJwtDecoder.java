package com.vitality.ai.internal.service;

import com.nimbusds.jwt.SignedJWT;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;

import java.text.ParseException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

public class NonValidatingJwtDecoder implements JwtDecoder {

    @Override
    public Jwt decode(String token) throws JwtException {
        try {
            // Parse the JWT using Nimbus
            SignedJWT signedJWT = SignedJWT.parse(token);

            // Extract headers
            Map<String, Object> headers = signedJWT.getHeader().toJSONObject();

            // Extract claims and convert dates to Instant
            Map<String, Object> claims = new HashMap<>(signedJWT.getJWTClaimsSet().getClaims());
            claims.replace("iat", toInstant(claims.get("iat")));
            claims.replace("exp", toInstant(claims.get("exp")));
            claims.replace("nbf", toInstant(claims.get("nbf")));

            // Build the Jwt object
            return Jwt.withTokenValue(token)
                    .headers(h -> h.putAll(headers))
                    .claims(c -> c.putAll(claims))
                    .build();
        } catch (ParseException e) {
            throw new JwtException("Invalid JWT token", e);
        }
    }

    /**
     * Converts a value to an Instant if it is of type java.util.Date.
     */
    private Instant toInstant(Object value) {
        if (value instanceof java.util.Date) {
            return ((java.util.Date) value).toInstant();
        }
        return (Instant) value; // Assume it's already an Instant
    }
}
