package com.vitality.ai.internal.service;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class JwtService {

    private final JwtDecoder jwtDecoder;

    public JwtService(JwtDecoder jwtDecoder) {
        this.jwtDecoder = jwtDecoder;
    }

    /**
     * Extract and authenticate the user from a JWT.
     * @param token the JWT token
     * @return Authentication object representing the user
     * @throws JwtException if the token is invalid
     */
    public Authentication extractAuthentication(String token) throws JwtException {
        // Decode the JWT
        Jwt jwt = jwtDecoder.decode(token);

        // Extract user details (subject is usually the username)
        String username = jwt.getSubject();

        // (Optional) Extract roles or authorities if included in the token
        // This assumes the token has a "roles" claim with a list of roles
        var authorities = jwt.getClaimAsStringList("roles");
        if (authorities == null) {
            authorities = Collections.emptyList();
        }

        // Convert roles to Spring Security authorities
        var grantedAuthorities = authorities.stream()
                .map(SimpleGrantedAuthority::new)
                .toList();

        // Return an Authentication object
        return new UsernamePasswordAuthenticationToken(username, null, grantedAuthorities);
    }
}
