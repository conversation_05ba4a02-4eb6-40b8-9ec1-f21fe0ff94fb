package com.vitality.ai.internal.interceptor;

import com.vitality.ai.internal.service.JwtService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.List;
import java.util.Map;

public class AuthHandshakeInterceptor implements HandshakeInterceptor {

    private final JwtService jwtService;

    public AuthHandshakeInterceptor(JwtService jwtService) {
        this.jwtService = jwtService;
    }

    @Override
    public boolean beforeHandshake(ServerHttpRequest request,
                                   ServerHttpResponse response,
                                   WebSocketHandler wsHandler,
                                   Map<String, Object> attributes) throws Exception {
        // Extract the JWT token from query parameters
        String token = getTokenFromCookies(request);

        if (token != null) {
            Authentication authentication = jwtService.extractAuthentication(token);
            attributes.put("authentication", authentication); // Store token in WebSocket session attributes
            return true; // Proceed with the handshake
        }

        return false; // Reject handshake if token is missing or invalid
    }

    @Override
    public void afterHandshake(ServerHttpRequest request,
                               ServerHttpResponse response,
                               WebSocketHandler wsHandler,
                               Exception exception) {
        // No additional actions needed after the handshake
    }

    private String getTokenFromCookies(ServerHttpRequest request) {
        HttpHeaders headers = request.getHeaders();

        // Extract "Cookie" header
        List<String> cookies = headers.get(HttpHeaders.COOKIE);
        if (cookies != null) {
            for (String cookieHeader : cookies) {
                String[] cookiePairs = cookieHeader.split(";"); // Split cookies by semicolon
                for (String cookiePair : cookiePairs) {
                    String[] keyValue = cookiePair.trim().split("=");
                    if (keyValue.length == 2 && "__Secure-Authorization-Bearer".equals(keyValue[0])) { // Replace "jwt" with your cookie name
                        return keyValue[1];
                    }
                }
            }
        }
        return null;
    }

    /**
     * Utility to extract the JWT from the WebSocket handshake request.
     */
    private String getTokenFromRequest(ServerHttpRequest request) {
        var uri = request.getURI();
        var query = uri.getQuery();

        if (query != null) {
            for (String param : query.split("&")) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && "token".equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }
        return null;
    }
}
