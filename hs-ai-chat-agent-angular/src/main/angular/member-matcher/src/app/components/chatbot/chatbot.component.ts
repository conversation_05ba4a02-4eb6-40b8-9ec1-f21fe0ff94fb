// src/app/components/chatbot/chatbot.component.ts
import { Component, OnInit, ViewChild, ElementRef, AfterViewChecked, On<PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { tap } from 'rxjs/operators';
import { AppState } from '../../store';
import { Message } from '../../models/message.model';
import * as ChatActions from '../../store/chat/chat.actions';
import * as fromChat from '../../store/chat/chat.selectors';
import { WebSocketService } from '../../services/websocket.service';
import { AsyncPipe, NgClass, NgForOf, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-chatbot',
  standalone: true, // Add this property for standalone components
  imports: [
    Ng<PERSON>lass,
    FormsModule,
    NgIf,
    NgForOf
  ],
  templateUrl: './chatbot.component.html',
  styleUrls: ['./chatbot.component.scss']
})
export class ChatbotComponent implements OnInit, AfterViewChecked, OnDestroy {
  userMessage = '';
  messages: Message[] = [];
  isChatVisible = true;
  isConnected = false;

  private messagesSubscription: Subscription | undefined;
  private visibilitySubscription: Subscription | undefined;
  private connectionSubscription: Subscription | undefined;

  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;

  constructor(
    private store: Store<AppState>,
    private webSocketService: WebSocketService,
    private cdr: ChangeDetectorRef
  ) {
    console.log('ChatbotComponent constructor');

    // Check initial store state
    this.store.select(state => state).subscribe(state => {
      console.log('Current full store state:', state);
    });
  }

  ngOnInit() {
    console.log('ChatbotComponent: initializing component');

    // Reset messages first
    this.webSocketService.clearMessages();

    // Subscribe to messages updates from store
    this.messagesSubscription = this.store.select(fromChat.selectAllMessages)
      .pipe(
        tap(messages => console.log('ChatbotComponent: Received messages update, count:', messages?.length || 0))
      )
      .subscribe({
        next: (messages) => {
          if (messages) {
            console.log('Messages from store:', messages);
            this.messages = [...messages];
            // this.cdr.markForCheck();
            // this.cdr.detectChanges();

            // Log after update
            console.log('Component messages array after update:', this.messages);
          } else {
            console.warn('Messages selector returned undefined or null');
          }
        },
        error: (err) => console.error('Error in messages subscription:', err)
      });

    // Subscribe to chat visibility updates
    this.visibilitySubscription = this.store.select(fromChat.selectIsChatVisible)
      .subscribe({
        next: (visible) => {
          console.log('ChatbotComponent: Chat visibility update:', visible);
          if (visible !== undefined) {
            this.isChatVisible = visible;
            this.cdr.detectChanges();
          }
        },
        error: (err) => console.error('Error in visibility subscription:', err)
      });

    // Subscribe to connection status - directly from WebSocketService
    this.connectionSubscription = this.webSocketService.isConnected$
      .subscribe({
        next: (connected) => {
          console.log('ChatbotComponent: Connection status update:', connected);
          this.isConnected = connected;
          this.cdr.detectChanges();
        },
        error: (err) => console.error('Error in connection subscription:', err)
      });

    // Connect to WebSocket
    // this.store.dispatch(ChatActions.connectWebSocket());

    // Debug: Add a test message after a delay
    // setTimeout(() => this.addTestMessage(), 1000);
  }

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  ngOnDestroy() {
    console.log('ChatbotComponent: destroying component, cleaning up subscriptions');

    if (this.messagesSubscription) {
      this.messagesSubscription.unsubscribe();
    }

    if (this.visibilitySubscription) {
      this.visibilitySubscription.unsubscribe();
    }

    if (this.connectionSubscription) {
      this.connectionSubscription.unsubscribe();
    }
  }

  scrollToBottom(): void {
    try {
      if (this.messagesContainer) {
        this.messagesContainer.nativeElement.scrollTop =
          this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  sendMessage() {
    if (!this.userMessage.trim()) return;

    const messageText = this.userMessage.trim();
    console.log('ChatbotComponent: Sending message:', messageText);

    // Let the WebSocketService handle both sending and store updates
    this.webSocketService.sendMessage(messageText);

    // Clear input
    this.userMessage = '';
  }

  toggleChat() {
    console.log('ChatbotComponent: Toggling chat visibility');
    this.store.dispatch(ChatActions.toggleChatVisibility());
  }

  // Add a method to manually test state updates
  testStateUpdate() {
    const testMsg: Message = {
      id: 'test-' + Date.now(),
      text: 'This is a test message to verify state updates',
      sender: 'bot',
      timestamp: Date.now()
    };

    console.log('Dispatching test message to store:', testMsg);
    this.store.dispatch(ChatActions.receiveMessage({ message: testMsg }));
  }

  // Helper method to add a test message
  private addTestMessage() {
    const testMsg: Message = {
      id: 'auto-test-' + Date.now(),
      text: 'This is an automatic test message',
      sender: 'bot',
      timestamp: Date.now()
    };

    console.log('Adding automatic test message:', testMsg);
    this.store.dispatch(ChatActions.receiveMessage({ message: testMsg }));
  }

  // TrackBy function for ngFor optimization
  trackByFn(index: number, message: Message) {
    return message.id;
  }
}
