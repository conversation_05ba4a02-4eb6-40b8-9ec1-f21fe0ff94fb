.chatbot-container {
  width: 400px;
  height: 600px;
  background: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  display: none;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  bottom: 100px;
  right: 50px;
  z-index: 1000;

  &.visible {
    display: flex !important;
  }
}

.chatbot-header {
  background-color: #f41c5e;
  color: #ffffff;
  text-align: center;
  padding: 15px;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.connection-status {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 10px;
  height: 10px;
  border-radius: 50%;

  &.connected {
    color: #2ecc71;
  }

  &.disconnected {
    color: #e74c3c;
  }
}

.chatbot-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 15px;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  position: relative;
  display: inline-block;

  &.user {
    background-color: #ffd3e1; // light pink for user
    align-self: flex-end;
    border-bottom-right-radius: 4px;


  }

  &.bot {
    background-color: #f8b8cc; // darker pink for bot
    align-self: flex-start;
    border-bottom-left-radius: 4px;


  }
}

.chatbot-input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ddd;
  background-color: #ffffff;

  input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
    font-family: Roboto, sans-serif;
  }

  button {
    margin-left: 10px;
    padding: 10px 20px;
    background-color: #f41c5e;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-family: Roboto, sans-serif;

    &:hover {
      background-color: #c0174c; // darker version on hover
    }
  }
}

.floating-icon {
  width: 60px;
  height: 60px;
  background-color: #f41c5e;
  color: white;
  font-size: 30px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  position: fixed;
  bottom: 30px;
  right: 30px;
  cursor: pointer;
  //box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease-in-out, background-color 0.3s;

  &:hover {
    transform: scale(1.1);
    background-color: #c0174c; // darker on hover
  }
}

.no-messages {
  text-align: center;
  color: #888;
  margin-top: 20px;
  font-style: italic;
}

// src/styles.scss
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Roboto, sans-serif;
  background-color: #f4f4f9;
  height: 100vh;
}
