<!-- src/app/components/chatbot/chatbot.component.html -->
<div class="chatbot-container" [class.visible]="isChatVisible">
  <div class="chatbot-header">
    <span>Ask <PERSON></span>
    <span *ngIf="isConnected" class="connection-status connected">●</span>
    <span *ngIf="!isConnected" class="connection-status disconnected">●</span>
  </div>

  <div #messagesContainer class="chatbot-messages">
    <!-- Simplified conditional rendering -->
    <div *ngIf="messages.length === 0" class="no-messages">
      No messages yet. Start a conversation!
    </div>

    <!-- Direct iteration without nested containers -->
    <div *ngFor="let message of messages; trackBy: trackByFn"
         class="message"
         [ngClass]="message.sender">
      {{ message.text }}
    </div>
  </div>

  <div class="chatbot-input">
    <input type="text"
           [(ngModel)]="userMessage"
           placeholder="Type your message..."
           (keyup.enter)="sendMessage()">
    <button (click)="sendMessage()">Send</button>
  </div>
</div>

<div class="floating-icon" (click)="toggleChat()">
  <img src="stanley.png" alt="Chat Icon" width="64" height="64"/>
</div>
