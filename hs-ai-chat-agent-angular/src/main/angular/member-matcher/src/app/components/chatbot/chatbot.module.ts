// src/app/features/dashboard/dashboard.module.ts
import {NgModule, Provider} from '@angular/core';
import {AsyncPipe, CommonModule, NgClass} from '@angular/common';
import { StoreModule } from '@ngrx/store';
import {Actions, EffectsModule} from '@ngrx/effects';
import {ChatbotComponent} from './chatbot.component';
import {ChatEffects} from '../../store/chat/chat.effects';
import {reducers} from '../../store';
import {FormsModule} from '@angular/forms';


@NgModule({
    declarations: [

    ],
    imports: [
        CommonModule,
        StoreModule.forFeature('chatbot', reducers),
        EffectsModule.forFeature([ChatEffects]),
      ChatbotComponent,
      FormsModule
    ],
    providers: [
      ChatEffects,
        Actions,
    ],
  exports:[
    ChatbotComponent
  ]
})
export class ChatBotModule { }
