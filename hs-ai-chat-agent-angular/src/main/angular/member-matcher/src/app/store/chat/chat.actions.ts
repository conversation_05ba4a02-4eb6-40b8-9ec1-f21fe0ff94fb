import { createAction, props } from '@ngrx/store';
import { Message } from '../../models/message.model';

export const sendMessage = createAction(
  '[Chat] Send Message',
  props<{ message: Message }>()
);

export const addTypingIndicator = createAction(
  '[Chat] Add Typing Indicator'
);

export const removeTypingIndicator = createAction(
  '[Chat] Remove Typing Indicator'
);

export const receiveMessage = createAction(
  '[Chat] Receive Message',
  props<{ message: Message }>()
);

export const clearMessages = createAction(
  '[Chat] Clear Messages'
);

export const connectWebSocket = createAction(
  '[Chat] Connect WebSocket'
);

export const webSocketConnected = createAction(
  '[Chat] WebSocket Connected'
);

export const webSocketDisconnected = createAction(
  '[Chat] WebSocket Disconnected'
);

export const webSocketError = createAction(
  '[Chat] WebSocket Error',
  props<{ error: any }>()
);

export const toggleChatVisibility = createAction(
  '[Chat] Toggle Chat Visibility'
);
