import { ActionReducerMap, MetaReducer } from '@ngrx/store';
import { environment } from '../../environment/environment';
import * as fromChat from './chat/chat.reducer';

// Define the feature state interface
export interface ChatbotState {
  chat: fromChat.ChatState;
}

// Define the overall app state interface that will include the feature state
export interface AppState {
  chatbot: ChatbotState;
}

// Register all reducers in the ActionReducerMap for the feature store
export const reducers: ActionReducerMap<ChatbotState> = {
  chat: fromChat.chatReducer
};

// Optional: Add metaReducers for debugging if needed
export const metaReducers: MetaReducer<ChatbotState>[] = !environment.production
  ? [debugReducer]
  : [];

// Debug reducer for logging state changes
export function debugReducer(reducer: any) {
  return function(state: any, action: any) {
    console.log('state before', state);
    console.log('action', action);

    // Call the next reducer
    const nextState = reducer(state, action);

    console.log('state after', nextState);
    return nextState;
  };
}
