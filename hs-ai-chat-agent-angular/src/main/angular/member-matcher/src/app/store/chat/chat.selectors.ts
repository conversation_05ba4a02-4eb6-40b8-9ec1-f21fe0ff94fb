// src/app/store/chat/chat.selectors.ts
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { AppState, ChatbotState } from '../index';
import { ChatState, adapter } from './chat.reducer';

// First get the chatbot feature state from the overall app state
export const selectChatbotFeatureState = createFeatureSelector<AppState, ChatbotState>('chatbot');

// Then get the chat state from within the chatbot feature state
export const selectChatState = createSelector(
  selectChatbotFeatureState,
  (state: ChatbotState) => state.chat
);

// Create the entity selectors, based on the chat state selector
export const {
  selectIds: selectMessageIds,
  selectEntities: selectMessageEntities,
  selectAll: selectAllMessages,
  selectTotal: selectTotalMessages
} = adapter.getSelectors(selectChatState);

// Create additional selectors for other state properties
export const selectIsConnected = createSelector(
  selectChatState,
  (state: ChatState) => state.isConnected
);

export const selectIsChatVisible = createSelector(
  selectChatState,
  (state: ChatState) => state.isChatVisible
);

export const selectConnectionError = createSelector(
  selectChatState,
  (state: ChatState) => state.connectionError
);

export const selectTypingIndicatorId = createSelector(
  selectChatState,
  (state: ChatState) => state.typingIndicatorId
);

export const selectHasTypingIndicator = createSelector(
  selectTypingIndicatorId,
  (id: string | null) => id !== null
);
