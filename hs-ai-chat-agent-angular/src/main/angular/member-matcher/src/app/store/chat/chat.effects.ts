// src/app/store/chat/chat.effects.ts
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of, EMPTY } from 'rxjs';
import { map, mergeMap, catchError, withLatestFrom, tap, switchMap } from 'rxjs/operators';

import { AppState } from '../index'; // Import AppState with nested feature state
import * as ChatActions from './chat.actions';
import { WebSocketService } from '../../services/websocket.service';
import { Message } from '../../models/message.model';
import * as fromChat from './chat.selectors';

@Injectable()
export class ChatEffects {
  // Declare effects but don't initialize yet
  connectWebSocket$;
  sendMessage$;
  // listenToMessages$;

  constructor(
    actions$: Actions,
    store: Store<AppState>, // Use the AppState with nested feature state
    webSocketService: WebSocketService
  ) {
    console.log('ChatEffects constructor - is actions$ defined?', !!actions$);
    console.log('ChatEffects constructor - is webSocketService defined?', !!webSocketService);

    // Initialize the connectWebSocket$ effect in the constructor
    this.connectWebSocket$ = createEffect(() =>
      actions$.pipe(
        ofType(ChatActions.connectWebSocket),
        // Use the updated selector for the nested feature state
        withLatestFrom(store.select(fromChat.selectIsConnected)),
        switchMap(([_, isConnected]) => {
          console.log('Effect: ConnectWebSocket triggered', isConnected);

          if (isConnected) {
            return EMPTY;
          }

          return webSocketService.connect().pipe(
            map(() => {
              console.log('Effect: WebSocket connected successfully');
              return ChatActions.webSocketConnected();
            }),
            catchError((error) => {
              console.error('Effect: WebSocket connection error', error);
              return of(ChatActions.webSocketError({ error }));
            })
          );
        })
      )
    );

    // Initialize the sendMessage$ effect in the constructor
    this.sendMessage$ = createEffect(() =>
        actions$.pipe(
          ofType(ChatActions.sendMessage),
          tap(({ message }) => {
            console.log('Effect: SendMessage action received', message);

            // Add typing indicator right after sending a message

            // Send the message through WebSocket
          }),
          // Return EMPTY to not dispatch any actions
          mergeMap(() => EMPTY)
        ),
      { dispatch: false } // Important: specify that this effect doesn't dispatch actions
    );

    // Initialize the listenToMessages$ effect in the constructor
    // this.listenToMessages$ = createEffect(() =>
    //   webSocketService.messages$.pipe(
    //     tap(message => console.log('Effect: Message received from WebSocket:', message)),
    //     map((text: string) => {
    //       // Create a message object
    //       const message: Message = {
    //         id: 'bot-' + Date.now(),
    //         text,
    //         sender: 'bot',
    //         timestamp: Date.now()
    //       };
    //
    //       console.log('Effect: Creating bot message:', message);
    //
    //       // Return an action to be dispatched, don't dispatch directly
    //       return ChatActions.receiveMessage({ message });
    //     })
    //   )
    // );

    // Debug: Log the current state to verify feature structure
    // store.select(state => state).subscribe(state => {
    //   console.log('Effects - Current full store state:', state);
    // });

    // Initialize WebSocket connection to ensure messages$ is available
    // webSocketService.connect().subscribe({
    //   next: (connected) => console.log('Initial WebSocket connection status:', connected),
    //   error: (err) => console.error('Initial connection failed:', err)
    // });
  }
}
