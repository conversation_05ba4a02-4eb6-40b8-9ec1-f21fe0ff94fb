import { createReducer, on } from '@ngrx/store';
import { EntityState, EntityAdapter, createEntityAdapter } from '@ngrx/entity';
import { Message } from '../../models/message.model';
import * as ChatActions from './chat.actions';

// Define the Chat State interface with EntityState
export interface ChatState extends EntityState<Message> {
  typingIndicatorId: string | null;
  isConnected: boolean;
  isChatVisible: boolean;
  connectionError: any | null;
}

// Create the entity adapter with an explicit ID selector function
export const adapter: EntityAdapter<Message> = createEntityAdapter<Message>({
  selectId: (message: Message) => message.id,
  sortComparer: (a: Message, b: Message) => a.timestamp - b.timestamp
});

// Create the initial state using the adapter's getInitialState method
// This is CRITICAL - it properly initializes the entity state
export const initialState: ChatState = adapter.getInitialState({
  typingIndicatorId: null,
  isConnected: false,
  isChatVisible: true,
  connectionError: null
});

// Create the reducer function
export const chatReducer = createReducer(
  initialState,

  on(ChatActions.sendMessage, (state, { message }) => {
    console.log('Reducer: sendMessage', message);
    return adapter.addOne(message, state);
  }),

  on(ChatActions.addTypingIndicator, (state) => {
    console.log('Reducer: addTypingIndicator');
    // Create a typing indicator message
    const typingIndicator: Message = {
      id: 'typing-indicator-' + Date.now(),
      text: '...',
      sender: 'bot',
      timestamp: Date.now()
    };

    return {
      ...adapter.addOne(typingIndicator, state),
      typingIndicatorId: typingIndicator.id
    };
  }),

  on(ChatActions.removeTypingIndicator, (state) => {
    console.log('Reducer: removeTypingIndicator', state.typingIndicatorId);
    if (!state.typingIndicatorId) {
      return state;
    }

    return {
      ...adapter.removeOne(state.typingIndicatorId, state),
      typingIndicatorId: null
    };
  }),

  on(ChatActions.receiveMessage, (state, { message }) => {
    console.log('Reducer: receiveMessage', message);
    return adapter.addOne(message, state);
  }),

  on(ChatActions.clearMessages, (state) => {
    console.log('Reducer: clearMessages');
    return adapter.removeAll(state);
  }),

  on(ChatActions.webSocketConnected, (state) => {
    console.log('Reducer: webSocketConnected');
    return {
      ...state,
      isConnected: true,
      connectionError: null
    };
  }),

  on(ChatActions.webSocketDisconnected, (state) => {
    console.log('Reducer: webSocketDisconnected');
    return {
      ...state,
      isConnected: false
    };
  }),

  on(ChatActions.webSocketError, (state, { error }) => {
    console.log('Reducer: webSocketError', error);
    return {
      ...state,
      connectionError: error
    };
  }),

  on(ChatActions.toggleChatVisibility, (state) => {
    console.log('Reducer: toggleChatVisibility', !state.isChatVisible);
    return {
      ...state,
      isChatVisible: !state.isChatVisible
    };
  })
);
