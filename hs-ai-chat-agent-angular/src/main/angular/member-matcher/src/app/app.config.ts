import { ApplicationConfig, isDevMode, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import {environment} from '../environment/environment';

const providers = [
  provideHttpClient(),
  importProvidersFrom(
    StoreModule.forRoot({}),
    EffectsModule.forRoot([])
  )
];

// Add DevTools only in non-production
if (!environment.production) {
  providers.push(importProvidersFrom(
    StoreDevtoolsModule.instrument({
      maxAge: 25,
      logOnly: environment.production
    })
  ));
}

export const appConfig: ApplicationConfig = {
  providers
};
