// src/app/services/websocket.service.ts - Updated with loop prevention
import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { environment } from '../../environment/environment';
import { Store } from '@ngrx/store';
import { AppState } from '../store';
import * as ChatActions from '../store/chat/chat.actions';
import { Message } from '../models/message.model';

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private messagesSubject = new Subject<string>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: any = null;
  private closeIntentionally = false;
  private destroy$ = new Subject<void>();
  private connectionStatus$ = new BehaviorSubject<boolean>(false);

  // Track sent messages to prevent loops
  private recentlySentMessages: Set<string> = new Set();
  private processingMessage = false;

  // Public observables
  public messages$ = this.messagesSubject.asObservable();
  public isConnected$ = this.connectionStatus$.asObservable();

  constructor(private store: Store<AppState>) {
    // Setup auto-reconnect mechanism
    this.isConnected$
      .pipe(
        filter(connected => !connected && !this.closeIntentionally),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      });
  }

  public connect(): Observable<boolean> {
    // Reset intentional close flag
    this.closeIntentionally = false;

    return new Observable<boolean>(observer => {
      try {
        // Close existing connection if exists
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
          this.closeIntentionally = true;
          this.socket.close();
        }

        // Create new WebSocket connection
        this.socket = new WebSocket(environment.websocketUrl);

        // Setup event handlers
        this.socket.onopen = () => {
          console.log('WebSocket connection established');
          this.reconnectAttempts = 0;
          this.connectionStatus$.next(true);

          // Dispatch connected action
          this.store.dispatch(ChatActions.webSocketConnected());

          observer.next(true);
          observer.complete();

        };

        this.socket.onmessage = (event) => {
          // console.log('Received WebSocket message:', event.data);

            // IMPORTANT: Check if we're already processing a message to prevent loops
            if (this.processingMessage) {
              // console.log('Ignoring message received while processing another message');
              return;
            }

            // Set processing flag
            this.processingMessage = true;

            // Check if this is a message we just sent (to prevent loops)
            if (this.recentlySentMessages.has(event.data)) {
              // console.log('Ignoring echo of our own message:', event.data);
              this.processingMessage = false;
              return;
            }

            // Forward the message to subscribers
            this.messagesSubject.next(event.data);

            // Create a message object
            const message: Message = {
              id: 'bot-' + Date.now(),
              text: event.data,
              sender: 'bot',
              timestamp: Date.now()
            };

            // console.log('Dispatching received message to store:', message);

            // Dispatch action to update store
            this.store.dispatch(ChatActions.receiveMessage({ message }));

            // Reset processing flag
            this.processingMessage = false;
        };

        this.socket.onclose = (event) => {
          console.log('WebSocket connection closed:', event);

          // Update connection status
          this.connectionStatus$.next(false);

          // If closed intentionally, don't reconnect
          if (!this.closeIntentionally) {
            if (this.reconnectAttempts >= this.maxReconnectAttempts) {
              console.error('Maximum reconnect attempts reached');
              const errorMessage = 'Connection to server lost. Please refresh the page to try again.';
              this.messagesSubject.next(errorMessage);
            }
          }
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);
          observer.error(error);
        };
      } catch (error) {
        console.error('Error initializing WebSocket:', error);
        observer.error(error);
      }

      // Return a teardown function for cleanup
    });
  }

  private scheduleReconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    const timeout = Math.min(30000, Math.pow(2, this.reconnectAttempts) * 1000);
    console.log(`Attempting to reconnect in ${timeout/1000} seconds...`);

    this.reconnectTimeout = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect().subscribe({
        error: (err) => console.error('Reconnect failed:', err)
      });
    }, timeout);
  }

  // Clean up old messages from the tracking set
  private cleanupSentMessages() {
    // Keep the set from growing too large - limit to last 20 messages
    if (this.recentlySentMessages.size > 20) {
      // Convert to array, slice to keep only the most recent 10, and create a new set
      const messagesArray = Array.from(this.recentlySentMessages);
      this.recentlySentMessages = new Set(messagesArray.slice(-10));
    }
  }

  public sendMessage(message: string): void {
    // Check if already processing to prevent loops
    if (this.processingMessage) {
      console.log('Ignoring send request while processing another message');
      return;
    }

    console.log('WebSocketService: Sending message:', message);

    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      // Track this message to prevent loops
      this.recentlySentMessages.add(message);
      this.cleanupSentMessages();

      // Create a message object for the store
      const userMessage: Message = {
        id: 'user-' + Date.now(),
        text: message,
        sender: 'user',
        timestamp: Date.now()
      };

      console.log('Dispatching sent message to store:', userMessage);

      // Update the store with the user message
      this.store.dispatch(ChatActions.sendMessage({ message: userMessage }));

      // Send the message to the WebSocket server
      this.socket.send(message);

      // Add typing indicator
      // this.store.dispatch(ChatActions.addTypingIndicator());

      // Auto-remove message from tracking after 5 seconds
      setTimeout(() => {
        this.recentlySentMessages.delete(message);
      }, 5000);
    } else {
      console.warn('WebSocket is not connected');

      // In production, inform the user about connection issues
      const errorMsg: Message = {
        id: 'error-' + Date.now(),
        text: 'Cannot send message. Connection to server lost. Trying to reconnect...',
        sender: 'bot',
        timestamp: Date.now()
      };

      this.store.dispatch(ChatActions.receiveMessage({ message: errorMsg }));

      // Try to reconnect
      this.connect().subscribe({
        error: (err) => console.error('Connection failed:', err)
      });
    }
  }

  public disconnect(): void {
    this.closeIntentionally = true;

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }


    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    // Clean up all subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Clear the message tracking
    this.recentlySentMessages.clear();
    this.processingMessage = false;
  }

  // Add a method to clear all messages (useful for testing)
  public clearMessages(): void {
    this.store.dispatch(ChatActions.clearMessages());
  }
}
