{"name": "member-matcher", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json", "build": "ng build", "build-spring": "ng build --configuration=spring", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "@ngrx/effects": "^19.0.1", "@ngrx/entity": "^19.0.1", "@ngrx/router-store": "^19.0.1", "@ngrx/store": "^19.0.1", "@ngrx/store-devtools": "^19.0.1", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.4", "@angular/cli": "^19.2.4", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}