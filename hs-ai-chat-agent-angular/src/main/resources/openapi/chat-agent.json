{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://127.0.0.1:34000/v3/assistant", "description": "Generated server url"}], "paths": {"/api/prompts/{id}": {"get": {"tags": ["prompt-controller"], "operationId": "getPromptById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PromptDTO"}}}}}}, "put": {"tags": ["prompt-controller"], "operationId": "updatePrompt", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromptDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PromptDTO"}}}}}}, "delete": {"tags": ["prompt-controller"], "operationId": "deletePrompt", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/files/content/{fileName}": {"get": {"tags": ["file-management-controller"], "operationId": "getFileContent", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}, "put": {"tags": ["file-management-controller"], "operationId": "updateFileContent", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/chat/chat": {"post": {"tags": ["chat-controller"], "operationId": "chatter", "parameters": [{"name": "sessionId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/prompts": {"get": {"tags": ["prompt-controller"], "operationId": "getAllPrompts", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromptDTO"}}}}}}}, "post": {"tags": ["prompt-controller"], "operationId": "createPrompt", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromptDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PromptDTO"}}}}}}}, "/api/prompts/upload": {"post": {"tags": ["prompt-controller"], "operationId": "process", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/api/files/validate": {"post": {"tags": ["file-management-controller"], "operationId": "validateContent", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/files/upload": {"post": {"tags": ["file-management-controller"], "operationId": "uploadPromptFile", "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/removeAll": {"get": {"tags": ["content-controller"], "operationId": "removeAll", "responses": {"200": {"description": "OK"}}}}, "/api/prompts/tools": {"get": {"tags": ["prompt-controller"], "operationId": "getAllTools", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ToolDTO"}}}}}}}}, "/api/prompts/search": {"get": {"tags": ["prompt-controller"], "operationId": "searchPrompts", "parameters": [{"name": "title", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sourceTool", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "keyword", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PromptDTO"}}}}}}}}, "/api/prompts/download": {"get": {"tags": ["prompt-controller"], "operationId": "download", "parameters": [{"name": "promptId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/load": {"get": {"tags": ["content-controller"], "operationId": "loadContent", "responses": {"200": {"description": "OK"}}}}, "/api/files/stats": {"get": {"tags": ["file-management-controller"], "operationId": "getDirectoryStats", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/files/list": {"get": {"tags": ["file-management-controller"], "operationId": "listPromptFiles", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileInfo"}}}}}}}}, "/api/files/{fileName}": {"delete": {"tags": ["file-management-controller"], "operationId": "deleteFile", "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}, "components": {"schemas": {"PromptDTO": {"required": ["title"], "type": "object", "properties": {"id": {"type": "string"}, "title": {"maxLength": 255, "minLength": 0, "type": "string"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}, "sourceTool": {"maxLength": 100, "minLength": 0, "type": "string"}, "context": {"type": "string"}, "data": {"type": "string"}, "rules": {"type": "string"}, "output": {"type": "string"}, "unbreakableCompliance": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ToolDTO": {"type": "object", "properties": {"category": {"type": "string"}, "documentation": {"type": "string"}}}, "FileInfo": {"type": "object", "properties": {"fileName": {"type": "string"}, "filePath": {"type": "string"}, "size": {"type": "integer", "format": "int64"}, "created": {"type": "string", "format": "date-time"}, "modified": {"type": "string", "format": "date-time"}}}}}}