server:
  port: 9900
  servlet:
    context-path: /internal

spring:
  application:
    name: internal-service
  cloud:
    config:
      enabled: false
  web:
    resources:
      static-locations: classpath:/static/browser
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8082/realms/master
          jwk-set-uri: http://localhost:8082/realms/master/protocol/openid-connect/certs

push-gateway:
  credentials:
    enabled: true
    username: admin
    password: XnK4tuFhMrAGcbsdkX2G7tf2q8

integration:
  service:
    url:
      hs-chat-orchestrator: http://localhost:34000
