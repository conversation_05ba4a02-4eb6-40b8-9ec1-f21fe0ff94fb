var Hg=Object.defineProperty,zg=Object.defineProperties;var Gg=Object.getOwnPropertyDescriptors;var Eu=Object.getOwnPropertySymbols;var Wg=Object.prototype.hasOwnProperty,qg=Object.prototype.propertyIsEnumerable;var bu=(e,t,n)=>t in e?Hg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,I=(e,t)=>{for(var n in t||={})Wg.call(t,n)&&bu(e,n,t[n]);if(Eu)for(var n of Eu(t))qg.call(t,n)&&bu(e,n,t[n]);return e},A=(e,t)=>zg(e,Gg(t));function Cu(e,t){return Object.is(e,t)}var K=null,$r=!1,Xi=1,Me=Symbol("SIGNAL");function j(e){let t=K;return K=e,t}function Iu(){return K}var Wt={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function es(e){if($r)throw new Error("");if(K===null)return;K.consumerOnSignalRead(e);let t=K.nextProducerIndex++;if(qr(K),t<K.producerNode.length&&K.producerNode[t]!==e&&Ln(K)){let n=K.producerNode[t];Wr(n,K.producerIndexOfThis[t])}K.producerNode[t]!==e&&(K.producerNode[t]=e,K.producerIndexOfThis[t]=Ln(K)?Tu(e,K,t):0),K.producerLastReadVersion[t]=e.version}function Zg(){Xi++}function _u(e){if(!(Ln(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Xi)){if(!e.producerMustRecompute(e)&&!Gr(e)){wu(e);return}e.producerRecomputeValue(e),wu(e)}}function Mu(e){if(e.liveConsumerNode===void 0)return;let t=$r;$r=!0;try{for(let n of e.liveConsumerNode)n.dirty||Yg(n)}finally{$r=t}}function Su(){return K?.consumerAllowSignalWrites!==!1}function Yg(e){e.dirty=!0,Mu(e),e.consumerMarkedDirty?.(e)}function wu(e){e.dirty=!1,e.lastCleanEpoch=Xi}function Vn(e){return e&&(e.nextProducerIndex=0),j(e)}function zr(e,t){if(j(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Ln(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Wr(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Gr(e){qr(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(_u(n),r!==n.version))return!0}return!1}function jn(e){if(qr(e),Ln(e))for(let t=0;t<e.producerNode.length;t++)Wr(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Tu(e,t,n){if(xu(e),e.liveConsumerNode.length===0&&Au(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Tu(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Wr(e,t){if(xu(e),e.liveConsumerNode.length===1&&Au(e))for(let r=0;r<e.producerNode.length;r++)Wr(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];qr(o),o.producerIndexOfThis[r]=t}}function Ln(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function qr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function xu(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Au(e){return e.producerNode!==void 0}function Nu(e,t){let n=Object.create(Qg);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(_u(n),es(n),n.value===Hr)throw n.error;return n.value};return r[Me]=n,r}var Ki=Symbol("UNSET"),Ji=Symbol("COMPUTING"),Hr=Symbol("ERRORED"),Qg=A(I({},Wt),{value:Ki,dirty:!0,error:null,equal:Cu,kind:"computed",producerMustRecompute(e){return e.value===Ki||e.value===Ji},producerRecomputeValue(e){if(e.value===Ji)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Ji;let n=Vn(e),r,o=!1;try{r=e.computation(),j(null),o=t!==Ki&&t!==Hr&&r!==Hr&&e.equal(t,r)}catch(i){r=Hr,e.error=i}finally{zr(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Kg(){throw new Error}var Ru=Kg;function Ou(e){Ru(e)}function Fu(e){Ru=e}var Jg=null;function ku(e,t){let n=Object.create(Lu);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(es(n),n.value);return r[Me]=n,r}function ts(e,t){Su()||Ou(e),e.equal(e.value,t)||(e.value=t,Xg(e))}function Pu(e,t){Su()||Ou(e),ts(e,t(e.value))}var Lu=A(I({},Wt),{equal:Cu,value:void 0,kind:"signal"});function Xg(e){e.version++,Zg(),Mu(e),Jg?.()}function Vu(e){let t=j(null);try{return e()}finally{j(t)}}var ns;function Bn(){return ns}function qe(e){let t=ns;return ns=e,t}var rs=Symbol("NotFound");function F(e){return typeof e=="function"}function qt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Zr=qt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function mt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var te=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(F(r))try{r()}catch(i){t=i instanceof Zr?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{ju(i)}catch(s){t=t??[],s instanceof Zr?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Zr(t)}}add(t){var n;if(t&&t!==this)if(this.closed)ju(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&mt(n,t)}remove(t){let{_finalizers:n}=this;n&&mt(n,t),t instanceof e&&t._removeParent(this)}};te.EMPTY=(()=>{let e=new te;return e.closed=!0,e})();var os=te.EMPTY;function Yr(e){return e instanceof te||e&&"closed"in e&&F(e.remove)&&F(e.add)&&F(e.unsubscribe)}function ju(e){F(e)?e():e.unsubscribe()}var Se={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Zt={setTimeout(e,t,...n){let{delegate:r}=Zt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Zt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Qr(e){Zt.setTimeout(()=>{let{onUnhandledError:t}=Se;if(t)t(e);else throw e})}function Ze(){}var Bu=is("C",void 0,void 0);function Uu(e){return is("E",void 0,e)}function $u(e){return is("N",e,void 0)}function is(e,t,n){return{kind:e,value:t,error:n}}var yt=null;function Yt(e){if(Se.useDeprecatedSynchronousErrorHandling){let t=!yt;if(t&&(yt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=yt;if(yt=null,n)throw r}}else e()}function Hu(e){Se.useDeprecatedSynchronousErrorHandling&&yt&&(yt.errorThrown=!0,yt.error=e)}var vt=class extends te{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Yr(t)&&t.add(this)):this.destination=nm}static create(t,n,r){return new Ye(t,n,r)}next(t){this.isStopped?as($u(t),this):this._next(t)}error(t){this.isStopped?as(Uu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?as(Bu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},em=Function.prototype.bind;function ss(e,t){return em.call(e,t)}var cs=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Kr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Kr(r)}else Kr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Kr(n)}}},Ye=class extends vt{constructor(t,n,r){super();let o;if(F(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Se.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&ss(t.next,i),error:t.error&&ss(t.error,i),complete:t.complete&&ss(t.complete,i)}):o=t}this.destination=new cs(o)}};function Kr(e){Se.useDeprecatedSynchronousErrorHandling?Hu(e):Qr(e)}function tm(e){throw e}function as(e,t){let{onStoppedNotification:n}=Se;n&&Zt.setTimeout(()=>n(e,t))}var nm={closed:!0,next:Ze,error:tm,complete:Ze};var Qt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Fe(e){return e}function zu(e){return e.length===0?Fe:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var k=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=om(n)?n:new Ye(n,r,o);return Yt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Gu(r),new r((o,i)=>{let s=new Ye({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Qt](){return this}pipe(...n){return zu(n)(this)}toPromise(n){return n=Gu(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Gu(e){var t;return(t=e??Se.Promise)!==null&&t!==void 0?t:Promise}function rm(e){return e&&F(e.next)&&F(e.error)&&F(e.complete)}function om(e){return e&&e instanceof vt||rm(e)&&Yr(e)}function im(e){return F(e?.lift)}function N(e){return t=>{if(im(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function R(e,t,n,r,o){return new Un(e,t,n,r,o)}var Un=class extends vt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};var Wu=qt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Y=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Jr(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Wu}next(n){Yt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Yt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Yt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?os:(this.currentObservers=null,i.push(n),new te(()=>{this.currentObservers=null,mt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new k;return n.source=this,n}}return e.create=(t,n)=>new Jr(t,n),e})(),Jr=class extends Y{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:os}};var Te=class extends Y{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var $n={now(){return($n.delegate||Date).now()},delegate:void 0};var Hn=class extends Y{constructor(t=1/0,n=1/0,r=$n){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Xr=class extends te{constructor(t,n){super()}schedule(t,n=0){return this}};var zn={setInterval(e,t,...n){let{delegate:r}=zn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=zn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Kt=class extends Xr{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return zn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&zn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,mt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Jt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Jt.now=$n.now;var Xt=class extends Jt{constructor(t,n=Jt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var eo=new Xt(Kt);var to=class extends Kt{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}schedule(t,n=0){return n>0?super.schedule(t,n):(this.delay=n,this.state=t,this.scheduler.flush(this),this)}execute(t,n){return n>0||this.closed?super.execute(t,n):this._execute(t,n)}requestAsyncId(t,n,r=0){return r!=null&&r>0||r==null&&this.delay>0?super.requestAsyncId(t,n,r):(t.flush(this),0)}};var no=class extends Xt{};var Gn=new no(to);var me=new k(e=>e.complete());function qu(e){return e&&F(e.schedule)}function us(e){return e[e.length-1]}function ro(e){return F(us(e))?e.pop():void 0}function oo(e){return qu(us(e))?e.pop():void 0}function Zu(e,t){return typeof us(e)=="number"?e.pop():t}function Qu(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function Yu(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Dt(e){return this instanceof Dt?(this.v=e,this):new Dt(e)}function Ku(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(p){return Promise.resolve(p).then(f,d)}}function a(f,p){r[f]&&(o[f]=function(g){return new Promise(function(v,m){i.push([f,g,v,m])>1||c(f,g)})},p&&(o[f]=p(o[f])))}function c(f,p){try{u(r[f](p))}catch(g){h(i[0][3],g)}}function u(f){f.value instanceof Dt?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,p){f(p),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Ju(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Yu=="function"?Yu(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var io=e=>e&&typeof e.length=="number"&&typeof e!="function";function so(e){return F(e?.then)}function ao(e){return F(e[Qt])}function co(e){return Symbol.asyncIterator&&F(e?.[Symbol.asyncIterator])}function uo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function sm(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var lo=sm();function fo(e){return F(e?.[lo])}function ho(e){return Ku(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Dt(n.read());if(o)return yield Dt(void 0);yield yield Dt(r)}}finally{n.releaseLock()}})}function po(e){return F(e?.getReader)}function U(e){if(e instanceof k)return e;if(e!=null){if(ao(e))return am(e);if(io(e))return cm(e);if(so(e))return um(e);if(co(e))return Xu(e);if(fo(e))return lm(e);if(po(e))return dm(e)}throw uo(e)}function am(e){return new k(t=>{let n=e[Qt]();if(F(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function cm(e){return new k(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function um(e){return new k(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Qr)})}function lm(e){return new k(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Xu(e){return new k(t=>{fm(e,t).catch(n=>t.error(n))})}function dm(e){return Xu(ho(e))}function fm(e,t){var n,r,o,i;return Qu(this,void 0,void 0,function*(){try{for(n=Ju(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ce(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Qe(e,t=0){return N((n,r)=>{n.subscribe(R(r,o=>ce(r,e,()=>r.next(o),t),()=>ce(r,e,()=>r.complete(),t),o=>ce(r,e,()=>r.error(o),t)))})}function go(e,t=0){return N((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function el(e,t){return U(e).pipe(go(t),Qe(t))}function tl(e,t){return U(e).pipe(go(t),Qe(t))}function nl(e,t){return new k(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function rl(e,t){return new k(n=>{let r;return ce(n,t,()=>{r=e[lo](),ce(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>F(r?.return)&&r.return()})}function mo(e,t){if(!e)throw new Error("Iterable cannot be null");return new k(n=>{ce(n,t,()=>{let r=e[Symbol.asyncIterator]();ce(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function ol(e,t){return mo(ho(e),t)}function il(e,t){if(e!=null){if(ao(e))return el(e,t);if(io(e))return nl(e,t);if(so(e))return tl(e,t);if(co(e))return mo(e,t);if(fo(e))return rl(e,t);if(po(e))return ol(e,t)}throw uo(e)}function Ke(e,t){return t?il(e,t):U(e)}function xe(...e){let t=oo(e);return Ke(e,t)}function sl(e,t){let n=F(e)?e:()=>e,r=o=>o.error(n());return new k(t?o=>t.schedule(r,0,o):r)}var st=class e{constructor(t,n,r){this.kind=t,this.value=n,this.error=r,this.hasValue=t==="N"}observe(t){return ls(this,t)}do(t,n,r){let{kind:o,value:i,error:s}=this;return o==="N"?t?.(i):o==="E"?n?.(s):r?.()}accept(t,n,r){var o;return F((o=t)===null||o===void 0?void 0:o.next)?this.observe(t):this.do(t,n,r)}toObservable(){let{kind:t,value:n,error:r}=this,o=t==="N"?xe(n):t==="E"?sl(()=>r):t==="C"?me:0;if(!o)throw new TypeError(`Unexpected notification kind ${t}`);return o}static createNext(t){return new e("N",t)}static createError(t){return new e("E",void 0,t)}static createComplete(){return e.completeNotification}};st.completeNotification=new st("C");function ls(e,t){var n,r,o;let{kind:i,value:s,error:a}=e;if(typeof i!="string")throw new TypeError('Invalid notification, missing "kind"');i==="N"?(n=t.next)===null||n===void 0||n.call(t,s):i==="E"?(r=t.error)===null||r===void 0||r.call(t,a):(o=t.complete)===null||o===void 0||o.call(t)}function al(e){return e instanceof Date&&!isNaN(e)}var hm=qt(e=>function(n=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n});function ds(e,t){let{first:n,each:r,with:o=pm,scheduler:i=t??eo,meta:s=null}=al(e)?{first:e}:typeof e=="number"?{each:e}:e;if(n==null&&r==null)throw new TypeError("No timeout provided.");return N((a,c)=>{let u,l,d=null,h=0,f=p=>{l=ce(c,i,()=>{try{u.unsubscribe(),U(o({meta:s,lastValue:d,seen:h})).subscribe(c)}catch(g){c.error(g)}},p)};u=a.subscribe(R(c,p=>{l?.unsubscribe(),h++,c.next(d=p),r>0&&f(r)},void 0,void 0,()=>{l?.closed||l?.unsubscribe(),d=null})),!h&&f(n!=null?typeof n=="number"?n:+n-i.now():r)})}function pm(e){throw new hm(e)}function B(e,t){return N((n,r)=>{let o=0;n.subscribe(R(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:gm}=Array;function mm(e,t){return gm(t)?e(...t):e(t)}function cl(e){return B(t=>mm(e,t))}var{isArray:ym}=Array,{getPrototypeOf:vm,prototype:Dm,keys:Em}=Object;function ul(e){if(e.length===1){let t=e[0];if(ym(t))return{args:t,keys:null};if(bm(t)){let n=Em(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function bm(e){return e&&typeof e=="object"&&vm(e)===Dm}function ll(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function dl(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&t.complete()},f=g=>u<r?p(g):c.push(g),p=g=>{i&&t.next(g),u++;let v=!1;U(n(g,l++)).subscribe(R(t,m=>{o?.(m),i?f(m):t.next(m)},()=>{v=!0},void 0,()=>{if(v)try{for(u--;c.length&&u<r;){let m=c.shift();s?ce(t,s,()=>p(m)):p(m)}h()}catch(m){t.error(m)}}))};return e.subscribe(R(t,f,()=>{d=!0,h()})),()=>{a?.()}}function ye(e,t,n=1/0){return F(t)?ye((r,o)=>B((i,s)=>t(r,i,o,s))(U(e(r,o))),n):(typeof t=="number"&&(n=t),N((r,o)=>dl(r,o,e,n)))}function fl(e=1/0){return ye(Fe,e)}function fs(...e){let t=ro(e),{args:n,keys:r}=ul(e),o=new k(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;U(n[l]).subscribe(R(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?ll(r,a):a),i.complete())}))}});return t?o.pipe(cl(t)):o}function Et(...e){let t=oo(e),n=Zu(e,1/0),r=e;return r.length?r.length===1?U(r[0]):fl(n)(Ke(r,t)):me}function ne(e,t){return N((n,r)=>{let o=0;n.subscribe(R(r,i=>e.call(t,i,o++)&&r.next(i)))})}function at(e){return N((t,n)=>{let r=null,o=!1,i;r=t.subscribe(R(n,void 0,void 0,s=>{i=U(e(s,at(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function hl(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(R(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function en(e,t){return F(t)?ye(e,t,1):ye(e,1)}function hs(e,t=eo){return N((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(R(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function Wn(e){return e<=0?()=>me:N((t,n)=>{let r=0;t.subscribe(R(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function ps(){return N((e,t)=>{e.subscribe(R(t,Ze))})}function gs(){return N((e,t)=>{e.subscribe(R(t,n=>ls(n,t)))})}function ms(e,t=Fe){return e=e??wm,N((n,r)=>{let o,i=!0;n.subscribe(R(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function wm(e,t){return e===t}function yo(e,t){return t?n=>n.pipe(yo((r,o)=>U(e(r,o)).pipe(B((i,s)=>t(r,i,o,s))))):N((n,r)=>{let o=0,i=null,s=!1;n.subscribe(R(r,a=>{i||(i=R(r,void 0,()=>{i=null,s&&r.complete()}),U(e(a,o++)).subscribe(i))},()=>{s=!0,!i&&r.complete()}))})}function vo(e){return N((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Do(e,t,n,r){return N((o,i)=>{let s;!t||typeof t=="function"?s=t:{duration:n,element:s,connector:r}=t;let a=new Map,c=p=>{a.forEach(p),p(i)},u=p=>c(g=>g.error(p)),l=0,d=!1,h=new Un(i,p=>{try{let g=e(p),v=a.get(g);if(!v){a.set(g,v=r?r():new Y);let m=f(g,v);if(i.next(m),n){let D=R(v,()=>{v.complete(),D?.unsubscribe()},void 0,void 0,()=>a.delete(g));h.add(U(n(m)).subscribe(D))}}v.next(s?s(p):p)}catch(g){u(g)}},()=>c(p=>p.complete()),u,()=>a.clear(),()=>(d=!0,l===0));o.subscribe(h);function f(p,g){let v=new k(m=>{l++;let D=g.subscribe(m);return()=>{D.unsubscribe(),--l===0&&d&&h.unsubscribe()}});return v.key=p,v}})}function ys(){return N((e,t)=>{e.subscribe(R(t,n=>{t.next(st.createNext(n))},()=>{t.next(st.createComplete()),t.complete()},n=>{t.next(st.createError(n)),t.complete()}))})}function vs(...e){let t=e.length;if(t===0)throw new Error("list of properties cannot be empty.");return B(n=>{let r=n;for(let o=0;o<t;o++){let i=r?.[e[o]];if(typeof i<"u")r=i;else return}return r})}function qn(e,t){return N(hl(e,t,arguments.length>=2,!0))}function Es(e={}){let{connector:t=()=>new Y,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,d=!1,h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=c=void 0,l=d=!1},p=()=>{let g=s;f(),g?.unsubscribe()};return N((g,v)=>{u++,!d&&!l&&h();let m=c=c??t();v.add(()=>{u--,u===0&&!d&&!l&&(a=Ds(p,o))}),m.subscribe(v),!s&&u>0&&(s=new Ye({next:D=>m.next(D),error:D=>{d=!0,h(),a=Ds(f,n,D),m.error(D)},complete:()=>{l=!0,h(),a=Ds(f,r),m.complete()}}),U(g).subscribe(s))})(i)}}function Ds(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ye({next:()=>{r.unsubscribe(),e()}});return U(t(...n)).subscribe(r)}function bs(e){return ne((t,n)=>e<=n)}function ct(e,t){return N((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(R(r,c=>{o?.unsubscribe();let u=0,l=i++;U(e(c,l)).subscribe(o=R(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function bt(e){return N((t,n)=>{U(e).subscribe(R(n,()=>n.complete(),Ze)),!n.closed&&t.subscribe(n)})}function tn(e,t,n){let r=F(e)||t||n?{next:e,error:t,complete:n}:e;return r?N((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(R(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):Fe}function wt(...e){let t=ro(e);return N((n,r)=>{let o=e.length,i=new Array(o),s=e.map(()=>!1),a=!1;for(let c=0;c<o;c++)U(e[c]).subscribe(R(r,u=>{i[c]=u,!a&&!s[c]&&(s[c]=!0,(a=s.every(Fe))&&(s=null))},Ze));n.subscribe(R(r,c=>{if(a){let u=[c,...i];r.next(t?t(...u):u)}}))})}var Cm="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",T=class extends Error{code;constructor(t,n){super(Jo(t,n)),this.code=t}};function Jo(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function ar(e){return{toString:e}.toString()}var Eo="__parameters__";function Im(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Ta(e,t,n){return ar(()=>{let r=Im(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(Eo)?c[Eo]:Object.defineProperty(c,Eo,{value:[]})[Eo];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}function W(e){for(let t in e)if(e[t]===W)return t;throw Error("Could not find renamed property on target object.")}function _m(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function be(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(be).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function pl(e,t){return e?t?`${e} ${t}`:e:t||""}var Mm=W({__forward_ref__:W});function cr(e){return e.__forward_ref__=cr,e.toString=function(){return be(this())},e}function ie(e){return ed(e)?e():e}function ed(e){return typeof e=="function"&&e.hasOwnProperty(Mm)&&e.__forward_ref__===cr}function x(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function J(e){return{providers:e.providers||[],imports:e.imports||[]}}function xa(e){return gl(e,td)||gl(e,nd)}function gl(e,t){return e.hasOwnProperty(t)?e[t]:null}function Sm(e){let t=e&&(e[td]||e[nd]);return t||null}function ml(e){return e&&(e.hasOwnProperty(yl)||e.hasOwnProperty(Tm))?e[yl]:null}var td=W({\u0275prov:W}),yl=W({\u0275inj:W}),nd=W({ngInjectableDef:W}),Tm=W({ngInjectorDef:W}),b=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=x({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function rd(e){return e&&!!e.\u0275providers}var xm=W({\u0275cmp:W}),Am=W({\u0275dir:W}),Nm=W({\u0275pipe:W});var To=W({\u0275fac:W}),Kn=W({__NG_ELEMENT_ID__:W}),vl=W({__NG_ENV_ID__:W});function od(e){return typeof e=="string"?e:e==null?"":String(e)}function Rm(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():od(e)}function id(e,t){throw new T(-200,e)}function Aa(e,t){throw new T(-201,!1)}var L=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(L||{}),Ps;function sd(){return Ps}function ve(e){let t=Ps;return Ps=e,t}function ad(e,t,n){let r=xa(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&L.Optional)return null;if(t!==void 0)return t;Aa(e,"Injector")}var Om={},Ct=Om,Ls="__NG_DI_FLAG__",xo=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?rs:Ct,r)}},Ao="ngTempTokenPath",Fm="ngTokenPath",km=/\n/gm,Pm="\u0275",Dl="__source";function Lm(e,t=L.Default){if(Bn()===void 0)throw new T(-203,!1);if(Bn()===null)return ad(e,void 0,t);{let n=Bn(),r;return n instanceof xo?r=n.injector:r=n,r.get(e,t&L.Optional?null:void 0,t)}}function E(e,t=L.Default){return(sd()||Lm)(ie(e),t)}function M(e,t=L.Default){return E(e,Xo(t))}function Xo(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Vs(e){let t=[];for(let n=0;n<e.length;n++){let r=ie(e[n]);if(Array.isArray(r)){if(r.length===0)throw new T(900,!1);let o,i=L.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Vm(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(E(o,i))}else t.push(E(r))}return t}function Na(e,t){return e[Ls]=t,e.prototype[Ls]=t,e}function Vm(e){return e[Ls]}function jm(e,t,n,r){let o=e[Ao];throw t[Dl]&&o.unshift(t[Dl]),e.message=Bm(`
`+e.message,o,n,r),e[Fm]=o,e[Ao]=null,e}function Bm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Pm?e.slice(2):e;let o=be(t);if(Array.isArray(t))o=t.map(be).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):be(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(km,`
  `)}`}var Ra=Na(Ta("Inject",e=>({token:e})),-1),cd=Na(Ta("Optional"),8);var Um=Na(Ta("SkipSelf"),4);function an(e,t){let n=e.hasOwnProperty(To);return n?e[To]:null}function $m(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Hm(e){return e.flat(Number.POSITIVE_INFINITY)}function Oa(e,t){e.forEach(n=>Array.isArray(n)?Oa(n,t):t(n))}function ud(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function No(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function zm(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Gm(e,t,n){let r=ur(e,t);return r>=0?e[r|1]=n:(r=~r,zm(e,r,t,n)),r}function ws(e,t){let n=ur(e,t);if(n>=0)return e[n|1]}function ur(e,t){return Wm(e,t,1)}function Wm(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var _t={},De=[],Mt=new b(""),ld=new b("",-1),dd=new b(""),Ro=class{get(t,n=Ct){if(n===Ct){let r=new Error(`NullInjectorError: No provider for ${be(t)}!`);throw r.name="NullInjectorError",r}return n}};function Jn(e){return e[xm]||null}function qm(e){return e[Am]||null}function Zm(e){return e[Nm]||null}function Pt(e){return{\u0275providers:e}}function ei(...e){return{\u0275providers:fd(!0,e),\u0275fromNgModule:!0}}function fd(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Oa(t,s=>{let a=s;js(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&hd(o,i),n}function hd(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Fa(o,i=>{t(i,r)})}}function js(e,t,n,r){if(e=ie(e),!e)return!1;let o=null,i=ml(e),s=!i&&Jn(e);if(!i&&!s){let c=e.ngModule;if(i=ml(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)js(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Oa(i.imports,l=>{js(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&hd(u,t)}if(!a){let u=an(o)||(()=>new o);t({provide:o,useFactory:u,deps:De},o),t({provide:dd,useValue:o,multi:!0},o),t({provide:Mt,useValue:()=>E(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Fa(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Fa(e,t){for(let n of e)rd(n)&&(n=n.\u0275providers),Array.isArray(n)?Fa(n,t):t(n)}var Ym=W({provide:String,useValue:W});function pd(e){return e!==null&&typeof e=="object"&&Ym in e}function Qm(e){return!!(e&&e.useExisting)}function Km(e){return!!(e&&e.useFactory)}function cn(e){return typeof e=="function"}function Jm(e){return!!e.useClass}var ti=new b(""),wo={},El={},Cs;function ka(){return Cs===void 0&&(Cs=new Ro),Cs}var Pe=class{},Xn=class extends Pe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Us(t,s=>this.processProvider(s)),this.records.set(ld,nn(void 0,this)),o.has("environment")&&this.records.set(Pe,nn(void 0,this));let i=this.records.get(ti);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(dd,De,L.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?rs:Ct,r)}destroy(){Yn(this),this._destroyed=!0;let t=j(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),j(t)}}onDestroy(t){return Yn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Yn(this);let n=qe(this),r=ve(void 0),o;try{return t()}finally{qe(n),ve(r)}}get(t,n=Ct,r=L.Default){if(Yn(this),t.hasOwnProperty(vl))return t[vl](this);r=Xo(r);let o,i=qe(this),s=ve(void 0);try{if(!(r&L.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=ry(t)&&xa(t);u&&this.injectableDefInScope(u)?c=nn(Bs(t),wo):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&L.Self?ka():this.parent;return n=r&L.Optional&&n===Ct?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Ao]=a[Ao]||[]).unshift(be(t)),i)throw a;return jm(a,t,"R3InjectorError",this.source)}else throw a}finally{ve(s),qe(i)}}resolveInjectorInitializers(){let t=j(null),n=qe(this),r=ve(void 0),o;try{let i=this.get(Mt,De,L.Self);for(let s of i)s()}finally{qe(n),ve(r),j(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(be(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ie(t);let n=cn(t)?t:ie(t&&t.provide),r=ey(t);if(!cn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=nn(void 0,wo,!0),o.factory=()=>Vs(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=j(null);try{return n.value===El?id(be(t)):n.value===wo&&(n.value=El,n.value=n.factory()),typeof n.value=="object"&&n.value&&ny(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{j(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ie(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Bs(e){let t=xa(e),n=t!==null?t.factory:an(e);if(n!==null)return n;if(e instanceof b)throw new T(204,!1);if(e instanceof Function)return Xm(e);throw new T(204,!1)}function Xm(e){if(e.length>0)throw new T(204,!1);let n=Sm(e);return n!==null?()=>n.factory(e):()=>new e}function ey(e){if(pd(e))return nn(void 0,e.useValue);{let t=gd(e);return nn(t,wo)}}function gd(e,t,n){let r;if(cn(e)){let o=ie(e);return an(o)||Bs(o)}else if(pd(e))r=()=>ie(e.useValue);else if(Km(e))r=()=>e.useFactory(...Vs(e.deps||[]));else if(Qm(e))r=()=>E(ie(e.useExisting));else{let o=ie(e&&(e.useClass||e.provide));if(ty(e))r=()=>new o(...Vs(e.deps));else return an(o)||Bs(o)}return r}function Yn(e){if(e.destroyed)throw new T(205,!1)}function nn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function ty(e){return!!e.deps}function ny(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function ry(e){return typeof e=="function"||typeof e=="object"&&e instanceof b}function Us(e,t){for(let n of e)Array.isArray(n)?Us(n,t):n&&rd(n)?Us(n.\u0275providers,t):t(n)}function ni(e,t){let n;e instanceof Xn?(Yn(e),n=e):n=new xo(e);let r,o=qe(n),i=ve(void 0);try{return t()}finally{qe(o),ve(i)}}function md(){return sd()!==void 0||Bn()!=null}function ri(e){if(!md())throw new T(-203,!1)}function oy(e){return typeof e=="function"}var tt=0,P=1,O=2,re=3,Ne=4,Re=5,Oo=6,Fo=7,fe=8,er=9,dt=10,ee=11,tr=12,bl=13,lr=14,Le=15,St=16,rn=17,Je=18,oi=19,yd=20,ut=21,Is=22,Tt=23,we=24,_s=25,Xe=26,vd=1;var xt=7,ko=8,un=9,de=10;function lt(e){return Array.isArray(e)&&typeof e[vd]=="object"}function nt(e){return Array.isArray(e)&&e[vd]===!0}function Dd(e){return(e.flags&4)!==0}function hn(e){return e.componentOffset>-1}function Pa(e){return(e.flags&1)===1}function Ve(e){return!!e.template}function Po(e){return(e[O]&512)!==0}function dr(e){return(e[O]&256)===256}var $s=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Ed(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var La=(()=>{let e=()=>bd;return e.ngInherit=!0,e})();function bd(e){return e.type.prototype.ngOnChanges&&(e.setInput=sy),iy}function iy(){let e=Cd(this),t=e?.current;if(t){let n=e.previous;if(n===_t)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function sy(e,t,n,r,o){let i=this.declaredInputs[r],s=Cd(e)||ay(e,{previous:_t,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new $s(u&&u.currentValue,n,c===_t),Ed(e,t,o,n)}var wd="__ngSimpleChanges__";function Cd(e){return e[wd]||null}function ay(e,t){return e[wd]=t}var wl=null;var z=function(e,t=null,n){wl?.(e,t,n)},cy="svg",uy="math";function je(e){for(;Array.isArray(e);)e=e[tt];return e}function Id(e,t){return je(t[e])}function rt(e,t){return je(t[e.index])}function _d(e,t){return e.data[t]}function Be(e,t){let n=t[e];return lt(n)?n:n[tt]}function ly(e){return(e[O]&4)===4}function Va(e){return(e[O]&128)===128}function dy(e){return nt(e[re])}function Lo(e,t){return t==null?null:e[t]}function Md(e){e[rn]=0}function Sd(e){e[O]&1024||(e[O]|=1024,Va(e)&&fr(e))}function ii(e){return!!(e[O]&9216||e[we]?.dirty)}function Hs(e){e[dt].changeDetectionScheduler?.notify(8),e[O]&64&&(e[O]|=1024),ii(e)&&fr(e)}function fr(e){e[dt].changeDetectionScheduler?.notify(0);let t=At(e);for(;t!==null&&!(t[O]&8192||(t[O]|=8192,!Va(t)));)t=At(t)}function Td(e,t){if(dr(e))throw new T(911,!1);e[ut]===null&&(e[ut]=[]),e[ut].push(t)}function fy(e,t){if(e[ut]===null)return;let n=e[ut].indexOf(t);n!==-1&&e[ut].splice(n,1)}function At(e){let t=e[re];return nt(t)?t[re]:t}function xd(e){return e[Fo]??=[]}function Ad(e){return e.cleanup??=[]}function hy(e,t,n,r){let o=xd(t);o.push(n),e.firstCreatePass&&Ad(e).push(r,o.length-1)}var V={lFrame:Vd(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var zs=!1;function py(){return V.lFrame.elementDepthCount}function gy(){V.lFrame.elementDepthCount++}function my(){V.lFrame.elementDepthCount--}function Nd(){return V.bindingsEnabled}function yy(){return V.skipHydrationRootTNode!==null}function vy(e){return V.skipHydrationRootTNode===e}function Dy(){V.skipHydrationRootTNode=null}function G(){return V.lFrame.lView}function ue(){return V.lFrame.tView}function hr(e){return V.lFrame.contextLView=e,e[fe]}function pr(e){return V.lFrame.contextLView=null,e}function he(){let e=Rd();for(;e!==null&&e.type===64;)e=e.parent;return e}function Rd(){return V.lFrame.currentTNode}function Ey(){let e=V.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function gr(e,t){let n=V.lFrame;n.currentTNode=e,n.isParent=t}function Od(){return V.lFrame.isParent}function by(){V.lFrame.isParent=!1}function Fd(){return zs}function Vo(e){let t=zs;return zs=e,t}function wy(e){return V.lFrame.bindingIndex=e}function ja(){return V.lFrame.bindingIndex++}function Cy(e){let t=V.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Iy(){return V.lFrame.inI18n}function _y(e,t){let n=V.lFrame;n.bindingIndex=n.bindingRootIndex=e,Gs(t)}function My(){return V.lFrame.currentDirectiveIndex}function Gs(e){V.lFrame.currentDirectiveIndex=e}function Sy(e){let t=V.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function kd(){return V.lFrame.currentQueryIndex}function Ba(e){V.lFrame.currentQueryIndex=e}function Ty(e){let t=e[P];return t.type===2?t.declTNode:t.type===1?e[Re]:null}function Pd(e,t,n){if(n&L.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&L.Host);)if(o=Ty(i),o===null||(i=i[lr],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=V.lFrame=Ld();return r.currentTNode=t,r.lView=e,!0}function Ua(e){let t=Ld(),n=e[P];V.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ld(){let e=V.lFrame,t=e===null?null:e.child;return t===null?Vd(e):t}function Vd(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function jd(){let e=V.lFrame;return V.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Bd=jd;function $a(){let e=jd();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function pn(){return V.lFrame.selectedIndex}function Nt(e){V.lFrame.selectedIndex=e}function Ud(){let e=V.lFrame;return _d(e.tView,e.selectedIndex)}function xy(){return V.lFrame.currentNamespace}var $d=!0;function Ha(){return $d}function za(e){$d=e}function Ay(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=bd(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Hd(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Co(e,t,n){zd(e,t,3,n)}function Io(e,t,n,r){(e[O]&3)===n&&zd(e,t,n,r)}function Ms(e,t){let n=e[O];(n&3)===t&&(n&=16383,n+=1,e[O]=n)}function zd(e,t,n,r){let o=r!==void 0?e[rn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[rn]+=65536),(a<i||i==-1)&&(Ny(e,n,t,c),e[rn]=(e[rn]&**********)+c+2),c++}function Cl(e,t){z(4,e,t);let n=j(null);try{t.call(e)}finally{j(n),z(5,e,t)}}function Ny(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[O]>>14<e[rn]>>16&&(e[O]&3)===t&&(e[O]+=16384,Cl(a,i)):Cl(a,i)}var sn=-1,Rt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Ry(e){return(e.flags&8)!==0}function Oy(e){return(e.flags&16)!==0}function Fy(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Py(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function ky(e){return e===3||e===4||e===6}function Py(e){return e.charCodeAt(0)===64}function nr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Il(e,n,o,null,t[++r]):Il(e,n,o,null,null))}}return e}function Il(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}var Ss={},Ws=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Xo(r);let o=this.injector.get(t,Ss,r);return o!==Ss||n===Ss?o:this.parentInjector.get(t,n,r)}};function Gd(e){return e!==sn}function jo(e){return e&32767}function Ly(e){return e>>16}function Bo(e,t){let n=Ly(e),r=t;for(;n>0;)r=r[lr],n--;return r}var qs=!0;function _l(e){let t=qs;return qs=e,t}var Vy=256,Wd=Vy-1,qd=5,jy=0,ke={};function By(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Kn)&&(r=n[Kn]),r==null&&(r=n[Kn]=jy++);let o=r&Wd,i=1<<o;t.data[e+(o>>qd)]|=i}function Uo(e,t){let n=Zd(e,t);if(n!==-1)return n;let r=t[P];r.firstCreatePass&&(e.injectorIndex=t.length,Ts(r.data,e),Ts(t,null),Ts(r.blueprint,null));let o=Ga(e,t),i=e.injectorIndex;if(Gd(o)){let s=jo(o),a=Bo(o,t),c=a[P].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Ts(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Zd(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ga(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Xd(o),r===null)return sn;if(n++,o=o[lr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return sn}function Zs(e,t,n){By(e,t,n)}function Yd(e,t,n){if(n&L.Optional||e!==void 0)return e;Aa(t,"NodeInjector")}function Qd(e,t,n,r){if(n&L.Optional&&r===void 0&&(r=null),(n&(L.Self|L.Host))===0){let o=e[er],i=ve(void 0);try{return o?o.get(t,r,n&L.Optional):ad(t,r,n&L.Optional)}finally{ve(i)}}return Yd(r,t,n)}function Kd(e,t,n,r=L.Default,o){if(e!==null){if(t[O]&2048&&!(r&L.Self)){let s=zy(e,t,n,r,ke);if(s!==ke)return s}let i=Jd(e,t,n,r,ke);if(i!==ke)return i}return Qd(t,n,r,o)}function Jd(e,t,n,r,o){let i=$y(n);if(typeof i=="function"){if(!Pd(t,e,r))return r&L.Host?Yd(o,n,r):Qd(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&L.Optional))Aa(n);else return s}finally{Bd()}}else if(typeof i=="number"){let s=null,a=Zd(e,t),c=sn,u=r&L.Host?t[Le][Re]:null;for((a===-1||r&L.SkipSelf)&&(c=a===-1?Ga(e,t):t[a+8],c===sn||!Sl(r,!1)?a=-1:(s=t[P],a=jo(c),t=Bo(c,t)));a!==-1;){let l=t[P];if(Ml(i,a,l.data)){let d=Uy(a,t,n,s,r,u);if(d!==ke)return d}c=t[a+8],c!==sn&&Sl(r,t[P].data[a+8]===u)&&Ml(i,a,t)?(s=l,a=jo(c),t=Bo(c,t)):a=-1}}return o}function Uy(e,t,n,r,o,i){let s=t[P],a=s.data[e+8],c=r==null?hn(a)&&qs:r!=s&&(a.type&3)!==0,u=o&L.Host&&i===a,l=_o(a,s,n,c,u);return l!==null?rr(t,s,l,a):ke}function _o(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let p=s[f];if(f<c&&n===p||f>=c&&p.type===n)return f}if(o){let f=s[c];if(f&&Ve(f)&&f.type===n)return c}return null}function rr(e,t,n,r){let o=e[n],i=t.data;if(o instanceof Rt){let s=o;s.resolving&&id(Rm(i[n]));let a=_l(s.canSeeViewProviders);s.resolving=!0;let c,u=s.injectImpl?ve(s.injectImpl):null,l=Pd(e,r,L.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Ay(n,i[n],t)}finally{u!==null&&ve(u),_l(a),s.resolving=!1,Bd()}}return o}function $y(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Kn)?e[Kn]:void 0;return typeof t=="number"?t>=0?t&Wd:Hy:t}function Ml(e,t,n){let r=1<<e;return!!(n[t+(e>>qd)]&r)}function Sl(e,t){return!(e&L.Self)&&!(e&L.Host&&t)}var It=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Kd(this._tNode,this._lView,t,Xo(r),n)}};function Hy(){return new It(he(),G())}function gn(e){return ar(()=>{let t=e.prototype.constructor,n=t[To]||Ys(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[To]||Ys(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ys(e){return ed(e)?()=>{let t=Ys(ie(e));return t&&t()}:an(e)}function zy(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[O]&2048&&!Po(s);){let a=Jd(i,s,n,r|L.Self,ke);if(a!==ke)return a;let c=i.parent;if(!c){let u=s[yd];if(u){let l=u.get(n,ke,r);if(l!==ke)return l}c=Xd(s),s=s[lr]}i=c}return o}function Xd(e){let t=e[P],n=t.type;return n===2?t.declTNode:n===1?e[Re]:null}function Tl(e,t=null,n=null,r){let o=Gy(e,t,n,r);return o.resolveInjectorInitializers(),o}function Gy(e,t=null,n=null,r,o=new Set){let i=[n||De,ei(e)];return r=r||(typeof e=="object"?void 0:be(e)),new Xn(i,t||ka(),r||null,o)}var oe=class e{static THROW_IF_NOT_FOUND=Ct;static NULL=new Ro;static create(t,n){if(Array.isArray(t))return Tl({name:""},n,t,"");{let r=t.name??"";return Tl({name:r},t.parent,t.providers,r)}}static \u0275prov=x({token:e,providedIn:"any",factory:()=>E(ld)});static __NG_ELEMENT_ID__=-1};var Wy=new b("");Wy.__NG_ELEMENT_ID__=e=>{let t=he();if(t===null)throw new T(204,!1);if(t.type&2)return t.value;if(e&L.Optional)return null;throw new T(204,!1)};var ef=!1,mn=(()=>{class e{static __NG_ELEMENT_ID__=qy;static __NG_ENV_ID__=n=>n}return e})(),$o=class extends mn{_lView;constructor(t){super(),this._lView=t}onDestroy(t){return Td(this._lView,t),()=>fy(this._lView,t)}};function qy(){return new $o(G())}var ln=class{},Wa=new b("",{providedIn:"root",factory:()=>!1});var tf=new b(""),nf=new b(""),yn=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Te(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}return e})();var Qs=class extends Y{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,md()&&(this.destroyRef=M(mn,{optional:!0})??void 0,this.pendingTasks=M(yn,{optional:!0})??void 0)}emit(t){let n=j(null);try{super.next(t)}finally{j(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof te&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},Ee=Qs;function or(...e){}function rf(e){let t,n;function r(){e=or;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function xl(e){return queueMicrotask(()=>e()),()=>{e=or}}var qa="isAngularZone",Ho=qa+"_ID",Zy=0,Q=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Ee(!1);onMicrotaskEmpty=new Ee(!1);onStable=new Ee(!1);onError=new Ee(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=ef}=t;if(typeof Zone>"u")throw new T(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Ky(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(qa)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new T(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new T(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Yy,or,or);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Yy={};function Za(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Qy(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){rf(()=>{e.callbackScheduled=!1,Ks(e),e.isCheckStableRunning=!0,Za(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Ks(e)}function Ky(e){let t=()=>{Qy(e)},n=Zy++;e._inner=e._inner.fork({name:"angular",properties:{[qa]:!0,[Ho]:n,[Ho+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Jy(c))return r.invokeTask(i,s,a,c);try{return Al(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Nl(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return Al(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Xy(c)&&t(),Nl(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Ks(e),Za(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Ks(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Al(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Nl(e){e._nesting--,Za(e)}var Js=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Ee;onMicrotaskEmpty=new Ee;onStable=new Ee;onError=new Ee;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Jy(e){return of(e,"__ignore_ng_zone__")}function Xy(e){return of(e,"__scheduler_tick__")}function of(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Ce=class{_console=console;handleError(t){this._console.error("ERROR",t)}},ev=new b("",{providedIn:"root",factory:()=>{let e=M(Q),t=M(Ce);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function tv(){return vn(he(),G())}function vn(e,t){return new $e(rt(e,t))}var $e=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=tv}return e})();function nv(e){return e instanceof $e?e.nativeElement:e}function rv(e){return typeof e=="function"&&e[Me]!==void 0}function ft(e,t){let n=ku(e,t?.equal),r=n[Me];return n.set=o=>ts(r,o),n.update=o=>Pu(r,o),n.asReadonly=ov.bind(n),n}function ov(){let e=this[Me];if(e.readonlyFn===void 0){let t=()=>this();t[Me]=e,e.readonlyFn=t}return e.readonlyFn}function sf(e){return rv(e)&&typeof e.set=="function"}function iv(){return this._results[Symbol.iterator]()}var Xs=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new Y}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Hm(t);(this._changesDetected=!$m(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=iv};function af(e){return(e.flags&128)===128}var cf=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(cf||{}),uf=new Map,sv=0;function av(){return sv++}function cv(e){uf.set(e[oi],e)}function ea(e){uf.delete(e[oi])}var Rl="__ngContext__";function mr(e,t){lt(t)?(e[Rl]=t[oi],cv(t)):e[Rl]=t}function lf(e){return ff(e[tr])}function df(e){return ff(e[Ne])}function ff(e){for(;e!==null&&!nt(e);)e=e[Ne];return e}var ta;function hf(e){ta=e}function uv(){if(ta!==void 0)return ta;if(typeof document<"u")return document;throw new T(210,!1)}var Ya=new b("",{providedIn:"root",factory:()=>lv}),lv="ng",Qa=new b(""),Lt=new b("",{providedIn:"platform",factory:()=>"unknown"});var Ka=new b("",{providedIn:"root",factory:()=>uv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var dv="h",fv="b";var pf=!1,hv=new b("",{providedIn:"root",factory:()=>pf});var gf=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(gf||{}),si=new b(""),Ol=new Set;function Ja(e){Ol.has(e)||(Ol.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var mf=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=pv}return e})();function pv(){return new mf(G(),he())}var gv=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}return e})();var mv=()=>null;function yf(e,t,n=!1){return mv(e,t,n)}function vf(e,t){let n=e.contentQueries;if(n!==null){let r=j(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ba(i),a.contentQueries(2,t[s],s)}}}finally{j(r)}}}function na(e,t,n){Ba(0);let r=j(null);try{t(e,n)}finally{j(r)}}function Df(e,t,n){if(Dd(t)){let r=j(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{j(r)}}}var Ue=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ue||{});var ra=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Cm})`}};function Xa(e){return e instanceof ra?e.changingThisBreaksApplicationSecurity:e}function yv(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Ef="ng-template";function vv(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&yv(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(ec(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function ec(e){return e.type===4&&e.value!==Ef}function Dv(e,t,n){let r=e.type===4&&!n?Ef:e.value;return t===r}function Ev(e,t,n){let r=4,o=e.attrs,i=o!==null?Cv(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ae(r)&&!Ae(c))return!1;if(s&&Ae(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Dv(e,c,n)||c===""&&t.length===1){if(Ae(r))return!1;s=!0}}else if(r&8){if(o===null||!vv(e,o,c,n)){if(Ae(r))return!1;s=!0}}else{let u=t[++a],l=bv(c,o,ec(e),n);if(l===-1){if(Ae(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(Ae(r))return!1;s=!0}}}}return Ae(r)||s}function Ae(e){return(e&1)===0}function bv(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Iv(t,e)}function wv(e,t,n=!1){for(let r=0;r<t.length;r++)if(Ev(e,t[r],n))return!0;return!1}function Cv(e){for(let t=0;t<e.length;t++){let n=e[t];if(ky(n))return t}return e.length}function Iv(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Fl(e,t){return e?":not("+t.trim()+")":t}function _v(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ae(s)&&(t+=Fl(i,o),o=""),r=s,i=i||!Ae(r);n++}return o!==""&&(t+=Fl(i,o)),t}function Mv(e){return e.map(_v).join(",")}function Sv(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ae(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Dn={};function Tv(e,t){return e.createText(t)}function xv(e,t,n){e.setValue(t,n)}function bf(e,t,n){return e.createElement(t,n)}function zo(e,t,n,r,o){e.insertBefore(t,n,r,o)}function wf(e,t,n){e.appendChild(t,n)}function kl(e,t,n,r,o){r!==null?zo(e,t,n,r,o):wf(e,t,n)}function Av(e,t,n){e.removeChild(null,t,n)}function Nv(e,t,n){e.setAttribute(t,"style",n)}function Rv(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Cf(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Fy(e,t,r),o!==null&&Rv(e,t,o),i!==null&&Nv(e,t,i)}function tc(e,t,n,r,o,i,s,a,c,u,l){let d=Xe+r,h=d+o,f=Ov(d,h),p=typeof u=="function"?u():u;return f[P]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:p,incompleteFirstPass:!1,ssrId:l}}function Ov(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Dn);return n}function Fv(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=tc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function nc(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[tt]=o,d[O]=r|4|128|8|64|1024,(u!==null||e&&e[O]&2048)&&(d[O]|=2048),Md(d),d[re]=d[lr]=e,d[fe]=n,d[dt]=s||e&&e[dt],d[ee]=a||e&&e[ee],d[er]=c||e&&e[er]||null,d[Re]=i,d[oi]=av(),d[Oo]=l,d[yd]=u,d[Le]=t.type==2?e[Le]:d,d}function kv(e,t,n){let r=rt(t,e),o=Fv(n),i=e[dt].rendererFactory,s=rc(e,nc(e,o,null,If(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function If(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function _f(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function rc(e,t){return e[tr]?e[bl][Ne]=t:e[tr]=t,e[bl]=t,t}function Vt(e=1){Mf(ue(),G(),pn()+e,!1)}function Mf(e,t,n,r){if(!r)if((t[O]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Co(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Io(t,i,0,n)}Nt(n)}var ai=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ai||{});function oa(e,t,n,r){let o=j(null);try{let[i,s,a]=e.inputs[n],c=null;(s&ai.SignalBased)!==0&&(c=t[i][Me]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Ed(t,c,i,r)}finally{j(o)}}function Sf(e,t,n,r,o){let i=pn(),s=r&2;try{Nt(-1),s&&t.length>Xe&&Mf(e,t,Xe,!1),z(s?2:0,o),n(r,o)}finally{Nt(i),z(s?3:1,o)}}function oc(e,t,n){Uv(e,t,n),(n.flags&64)===64&&$v(e,t,n)}function Tf(e,t,n=rt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Pv(e,t,n,r){let i=r.get(hv,pf)||n===Ue.ShadowDom,s=e.selectRootElement(t,i);return Lv(s),s}function Lv(e){Vv(e)}var Vv=()=>null;function jv(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function xf(e,t,n,r,o,i,s,a){if(!a&&ic(t,e,n,r,o)){hn(t)&&Bv(n,t.index);return}if(t.type&3){let c=rt(t,n);r=jv(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function Bv(e,t){let n=Be(t,e);n[O]&16||(n[O]|=64)}function Uv(e,t,n){let r=n.directiveStart,o=n.directiveEnd;hn(n)&&kv(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Uo(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=rr(t,e,s,n);if(mr(c,t),i!==null&&zv(t,s-r,c,a,n,i),Ve(a)){let u=Be(n.index,t);u[fe]=rr(t,e,s,n)}}}function $v(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=My();try{Nt(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];Gs(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Hv(c,u)}}finally{Nt(-1),Gs(s)}}function Hv(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Af(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];wv(t,i.selectors,!1)&&(r??=[],Ve(i)?r.unshift(i):r.push(i))}return r}function zv(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];oa(r,n,c,u)}}function Nf(e,t){let n=e[er],r=n?n.get(Ce,null):null;r&&r.handleError(t)}function ic(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=t.data[u];oa(d,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];oa(l,u,r,o),a=!0}return a}function Gv(e,t){let n=Be(t,e),r=n[P];Wv(r,n);let o=n[tt];o!==null&&n[Oo]===null&&(n[Oo]=yf(o,n[er])),z(18),sc(r,n,n[fe]),z(19,n[fe])}function Wv(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function sc(e,t,n){Ua(t);try{let r=e.viewQuery;r!==null&&na(1,r,n);let o=e.template;o!==null&&Sf(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Je]?.finishViewCreation(e),e.staticContentQueries&&vf(e,t),e.staticViewQueries&&na(2,e.viewQuery,n);let i=e.components;i!==null&&qv(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[O]&=-5,$a()}}function qv(e,t){for(let n=0;n<t.length;n++)Gv(e,t[n])}function Zv(e,t,n,r){let o=j(null);try{let i=t.tView,a=e[O]&4096?4096:16,c=nc(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[St]=u;let l=e[Je];return l!==null&&(c[Je]=l.createEmbeddedView(i)),sc(i,c,n),c}finally{j(o)}}function Pl(e,t){return!t||t.firstChild===null||af(e)}var Yv;function ac(e,t){return Yv(e,t)}var et=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(et||{});function Rf(e){return(e.flags&32)===32}function on(e,t,n,r,o){if(r!=null){let i,s=!1;nt(r)?i=r:lt(r)&&(s=!0,r=r[tt]);let a=je(r);e===0&&n!==null?o==null?wf(t,n,a):zo(t,n,a,o||null,!0):e===1&&n!==null?zo(t,n,a,o||null,!0):e===2?Av(t,a,s):e===3&&t.destroyNode(a),i!=null&&aD(t,e,i,n,o)}}function Qv(e,t){Of(e,t),t[tt]=null,t[Re]=null}function Kv(e,t,n,r,o,i){r[tt]=o,r[Re]=t,ci(e,r,n,1,o,i)}function Of(e,t){t[dt].changeDetectionScheduler?.notify(9),ci(e,t,t[ee],2,null,null)}function Jv(e){let t=e[tr];if(!t)return xs(e[P],e);for(;t;){let n=null;if(lt(t))n=t[tr];else{let r=t[de];r&&(n=r)}if(!n){for(;t&&!t[Ne]&&t!==e;)lt(t)&&xs(t[P],t),t=t[re];t===null&&(t=e),lt(t)&&xs(t[P],t),n=t&&t[Ne]}t=n}}function cc(e,t){let n=e[un],r=n.indexOf(t);n.splice(r,1)}function Ff(e,t){if(dr(t))return;let n=t[ee];n.destroyNode&&ci(e,t,n,3,null,null),Jv(t)}function xs(e,t){if(dr(t))return;let n=j(null);try{t[O]&=-129,t[O]|=256,t[we]&&jn(t[we]),eD(e,t),Xv(e,t),t[P].type===1&&t[ee].destroy();let r=t[St];if(r!==null&&nt(t[re])){r!==t[re]&&cc(r,t);let o=t[Je];o!==null&&o.detachView(e)}ea(t)}finally{j(n)}}function Xv(e,t){let n=e.cleanup,r=t[Fo];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Fo]=null);let o=t[ut];if(o!==null){t[ut]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Tt];if(i!==null){t[Tt]=null;for(let s of i)s.destroy()}}function eD(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Rt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];z(4,a,c);try{c.call(a)}finally{z(5,a,c)}}else{z(4,o,i);try{i.call(o)}finally{z(5,o,i)}}}}}function tD(e,t,n){return nD(e,t.parent,n)}function nD(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[tt];if(hn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ue.None||o===Ue.Emulated)return null}return rt(r,n)}function rD(e,t,n){return iD(e,t,n)}function oD(e,t,n){return e.type&40?rt(e,n):null}var iD=oD,Ll;function uc(e,t,n,r){let o=tD(e,r,t),i=t[ee],s=r.parent||t[Re],a=rD(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)kl(i,o,n[c],a,!1);else kl(i,o,n,a,!1);Ll!==void 0&&Ll(i,r,t,n,o)}function Qn(e,t){if(t!==null){let n=t.type;if(n&3)return rt(t,e);if(n&4)return ia(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Qn(e,r);{let o=e[t.index];return nt(o)?ia(-1,o):je(o)}}else{if(n&128)return Qn(e,t.next);if(n&32)return ac(t,e)()||je(e[t.index]);{let r=kf(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=At(e[Le]);return Qn(o,r)}else return Qn(e,t.next)}}}return null}function kf(e,t){if(t!==null){let r=e[Le][Re],o=t.projection;return r.projection[o]}return null}function ia(e,t){let n=de+e+1;if(n<t.length){let r=t[n],o=r[P].firstChild;if(o!==null)return Qn(r,o)}return t[xt]}function lc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&mr(je(a),r),n.flags|=2),!Rf(n))if(c&8)lc(e,t,n.child,r,o,i,!1),on(t,e,o,a,i);else if(c&32){let u=ac(n,r),l;for(;l=u();)on(t,e,o,l,i);on(t,e,o,a,i)}else c&16?sD(e,t,r,n,o,i):on(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ci(e,t,n,r,o,i){lc(n,r,e.firstChild,t,o,i,!1)}function sD(e,t,n,r,o,i){let s=n[Le],c=s[Re].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];on(t,e,o,l,i)}else{let u=c,l=s[re];af(r)&&(u.flags|=128),lc(e,t,u,l,o,i,!0)}}function aD(e,t,n,r,o){let i=n[xt],s=je(n);i!==s&&on(t,e,r,i,o);for(let a=de;a<n.length;a++){let c=n[a];ci(c[P],c,e,t,r,i)}}function cD(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:et.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=et.Important),e.setStyle(n,r,o,i))}}function Go(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(je(i)),nt(i)&&uD(i,r);let s=n.type;if(s&8)Go(e,t,n.child,r);else if(s&32){let a=ac(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=kf(t,n);if(Array.isArray(a))r.push(...a);else{let c=At(t[Le]);Go(c[P],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function uD(e,t){for(let n=de;n<e.length;n++){let r=e[n],o=r[P].firstChild;o!==null&&Go(r[P],r,o,t)}e[xt]!==e[tt]&&t.push(e[xt])}function Pf(e){if(e[_s]!==null){for(let t of e[_s])t.impl.addSequence(t);e[_s].length=0}}var Lf=[];function lD(e){return e[we]??dD(e)}function dD(e){let t=Lf.pop()??Object.create(hD);return t.lView=e,t}function fD(e){e.lView[we]!==e&&(e.lView=null,Lf.push(e))}var hD=A(I({},Wt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{fr(e.lView)},consumerOnSignalRead(){this.lView[we]=this}});function pD(e){let t=e[we]??Object.create(gD);return t.lView=e,t}var gD=A(I({},Wt),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=At(e.lView);for(;t&&!Vf(t[P]);)t=At(t);t&&Sd(t)},consumerOnSignalRead(){this.lView[we]=this}});function Vf(e){return e.type!==2}function jf(e){if(e[Tt]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Tt])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[O]&8192)}}var mD=100;function Bf(e,t=!0,n=0){let o=e[dt].rendererFactory,i=!1;i||o.begin?.();try{yD(e,n)}catch(s){throw t&&Nf(e,s),s}finally{i||o.end?.()}}function yD(e,t){let n=Fd();try{Vo(!0),sa(e,t);let r=0;for(;ii(e);){if(r===mD)throw new T(103,!1);r++,sa(e,1)}}finally{Vo(n)}}function vD(e,t,n,r){if(dr(t))return;let o=t[O],i=!1,s=!1;Ua(t);let a=!0,c=null,u=null;i||(Vf(e)?(u=lD(t),c=Vn(u)):Iu()===null?(a=!1,u=pD(t),c=Vn(u)):t[we]&&(jn(t[we]),t[we]=null));try{Md(t),wy(e.bindingStartIndex),n!==null&&Sf(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Co(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Io(t,f,0,null),Ms(t,0)}if(s||DD(t),jf(t),Uf(t,0),e.contentQueries!==null&&vf(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Co(t,f)}else{let f=e.contentHooks;f!==null&&Io(t,f,1),Ms(t,1)}bD(e,t);let d=e.components;d!==null&&Hf(t,d,0);let h=e.viewQuery;if(h!==null&&na(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Co(t,f)}else{let f=e.viewHooks;f!==null&&Io(t,f,2),Ms(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Is]){for(let f of t[Is])f();t[Is]=null}i||(Pf(t),t[O]&=-73)}catch(l){throw i||fr(t),l}finally{u!==null&&(zr(u,c),a&&fD(u)),$a()}}function Uf(e,t){for(let n=lf(e);n!==null;n=df(n))for(let r=de;r<n.length;r++){let o=n[r];$f(o,t)}}function DD(e){for(let t=lf(e);t!==null;t=df(t)){if(!(t[O]&2))continue;let n=t[un];for(let r=0;r<n.length;r++){let o=n[r];Sd(o)}}}function ED(e,t,n){z(18);let r=Be(t,e);$f(r,n),z(19,r[fe])}function $f(e,t){Va(e)&&sa(e,t)}function sa(e,t){let r=e[P],o=e[O],i=e[we],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Gr(i)),s||=!1,i&&(i.dirty=!1),e[O]&=-9217,s)vD(r,e,r.template,e[fe]);else if(o&8192){jf(e),Uf(e,1);let a=r.components;a!==null&&Hf(e,a,1),Pf(e)}}function Hf(e,t,n){for(let r=0;r<t.length;r++)ED(e,t[r],n)}function bD(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Nt(~o);else{let i=o,s=n[++r],a=n[++r];_y(s,i);let c=t[i];z(24,c),a(2,c),z(25,c)}}}finally{Nt(-1)}}function dc(e,t){let n=Fd()?64:1088;for(e[dt].changeDetectionScheduler?.notify(t);e;){e[O]|=n;let r=At(e);if(Po(e)&&!r)return e;e=r}return null}function zf(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function wD(e,t,n,r=!0){let o=t[P];if(CD(o,t,e,n),r){let s=ia(n,e),a=t[ee],c=a.parentNode(e[xt]);c!==null&&Kv(o,e[Re],a,t,c,s)}let i=t[Oo];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function aa(e,t){if(e.length<=de)return;let n=de+t,r=e[n];if(r){let o=r[St];o!==null&&o!==e&&cc(o,r),t>0&&(e[n-1][Ne]=r[Ne]);let i=No(e,de+t);Qv(r[P],r);let s=i[Je];s!==null&&s.detachView(i[P]),r[re]=null,r[Ne]=null,r[O]&=-129}return r}function CD(e,t,n,r){let o=de+r,i=n.length;r>0&&(n[o-1][Ne]=t),r<i-de?(t[Ne]=n[o],ud(n,de+r,t)):(n.push(t),t[Ne]=null),t[re]=n;let s=t[St];s!==null&&n!==s&&Gf(s,t);let a=t[Je];a!==null&&a.insertView(e),Hs(t),t[O]|=128}function Gf(e,t){let n=e[un],r=t[re];if(lt(r))e[O]|=2;else{let o=r[re][Le];t[Le]!==o&&(e[O]|=2)}n===null?e[un]=[t]:n.push(t)}var ir=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[P];return Go(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[fe]}set context(t){this._lView[fe]=t}get destroyed(){return dr(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[re];if(nt(t)){let n=t[ko],r=n?n.indexOf(this):-1;r>-1&&(aa(t,r),No(n,r))}this._attachedToViewContainer=!1}Ff(this._lView[P],this._lView)}onDestroy(t){Td(this._lView,t)}markForCheck(){dc(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[O]&=-129}reattach(){Hs(this._lView),this._lView[O]|=128}detectChanges(){this._lView[O]|=1024,Bf(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new T(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Po(this._lView),n=this._lView[St];n!==null&&!t&&cc(n,this._lView),Of(this._lView[P],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new T(902,!1);this._appRef=t;let n=Po(this._lView),r=this._lView[St];r!==null&&!n&&Gf(r,this._lView),Hs(this._lView)}};var Ot=(()=>{class e{static __NG_ELEMENT_ID__=MD}return e})(),ID=Ot,_D=class extends ID{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Zv(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new ir(o)}};function MD(){return fc(he(),G())}function fc(e,t){return e.type&4?new _D(t,e,vn(e,t)):null}function hc(e,t,n,r,o){let i=e.data[t];if(i===null)i=SD(e,t,n,r,o),Iy()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Ey();i.injectorIndex=s===null?-1:s.injectorIndex}return gr(i,!0),i}function SD(e,t,n,r,o){let i=Rd(),s=Od(),a=s?i:i&&i.parent,c=e.data[t]=xD(e,a,n,t,r,o);return TD(e,c,i,s),c}function TD(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function xD(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return yy()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var yA=new RegExp(`^(\\d+)*(${fv}|${dv})*(.*)`);var AD=()=>null;function Vl(e,t){return AD(e,t)}var ND=class{},Wf=class{},ca=class{resolveComponentFactory(t){throw Error(`No component factory found for ${be(t)}.`)}},pc=class{static NULL=new ca},dn=class{},En=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>RD()}return e})();function RD(){let e=G(),t=he(),n=Be(t.index,e);return(lt(n)?n:e)[ee]}var OD=(()=>{class e{static \u0275prov=x({token:e,providedIn:"root",factory:()=>null})}return e})();function jl(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=pl(o,a);else if(i==2){let c=a,u=t[++s];r=pl(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function H(e,t=L.Default){let n=G();if(n===null)return E(e,t);let r=he();return Kd(r,n,ie(e),t)}function qf(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=kD(s);l===null?a=s:[a,c,u]=l,VD(e,t,n,a,i,c,u)}i!==null&&r!==null&&FD(n,r,i)}function FD(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new T(-301,!1);r.push(t[o],i)}}function kD(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&Ve(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,PD(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function PD(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function LD(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function VD(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&Ve(f)&&(c=!0,LD(e,n,h)),Zs(Uo(n,t),e,f.type)}zD(n,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=_f(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(n.mergedAttrs=nr(n.mergedAttrs,f.hostAttrs),BD(e,n,t,d,f),HD(d,f,o),s!==null&&s.has(f)){let[g,v]=s.get(f);n.directiveToIndex.set(f.type,[d,g+n.directiveStart,v+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let p=f.type.prototype;!u&&(p.ngOnChanges||p.ngOnInit||p.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(p.ngOnChanges||p.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}jD(e,n,i)}function jD(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Bl(0,t,o,r),Bl(1,t,o,r),$l(t,r,!1);else{let i=n.get(o);Ul(0,t,i,r),Ul(1,t,i,r),$l(t,r,!0)}}}function Bl(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Zf(t,i)}}function Ul(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Zf(t,s)}}function Zf(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function $l(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||ec(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function BD(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=an(o.type,!0)),s=new Rt(i,Ve(o),H);e.blueprint[r]=s,n[r]=s,UD(e,t,r,_f(e,n,o.hostVars,Dn),o)}function UD(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;$D(s)!=a&&s.push(a),s.push(n,r,i)}}function $D(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function HD(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ve(t)&&(n[""]=e)}}function zD(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Yf(e,t,n,r,o,i,s,a){let c=t.consts,u=Lo(c,s),l=hc(t,e,2,r,u);return i&&qf(t,n,l,Lo(c,a),o),l.mergedAttrs=nr(l.mergedAttrs,l.attrs),l.attrs!==null&&jl(l,l.attrs,!1),l.mergedAttrs!==null&&jl(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function Qf(e,t){Hd(e,t),Dd(t)&&e.queries.elementEnd(t)}var ua=class extends pc{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Jn(t);return new Wo(n,this.ngModule)}};function GD(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&ai.SignalBased)!==0};return o&&(i.transform=o),i})}function WD(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function qD(e,t,n){let r=t instanceof Pe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Ws(n,r):n}function ZD(e){let t=e.get(dn,null);if(t===null)throw new T(407,!1);let n=e.get(OD,null),r=e.get(ln,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function YD(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return bf(t,n,n==="svg"?cy:n==="math"?uy:null)}var Wo=class extends Wf{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=GD(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=WD(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Mv(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){z(22);let i=j(null);try{let s=this.componentDef,a=r?["ng-version","19.2.3"]:Sv(this.componentDef.selectors[0]),c=tc(0,null,null,1,0,null,null,null,null,[a],null),u=qD(s,o||this.ngModule,t),l=ZD(u),d=l.rendererFactory.createRenderer(null,s),h=r?Pv(d,r,s.encapsulation,u):YD(s,d),f=nc(null,c,null,512|If(s),null,null,l,d,u,null,yf(h,u,!0));f[Xe]=h,Ua(f);let p=null;try{let g=Yf(Xe,c,f,"#host",()=>[this.componentDef],!0,0);h&&(Cf(d,h,g),mr(h,f)),oc(c,f,g),Df(c,g,f),Qf(c,g),n!==void 0&&QD(g,this.ngContentSelectors,n),p=Be(g.index,f),f[fe]=p[fe],sc(c,f,null)}catch(g){throw p!==null&&ea(p),ea(f),g}finally{z(23),$a()}return new la(this.componentType,f)}finally{j(i)}}},la=class extends ND{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=_d(n[P],Xe),this.location=vn(this._tNode,n),this.instance=Be(this._tNode.index,n)[fe],this.hostView=this.changeDetectorRef=new ir(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=ic(r,o[P],o,t,n);this.previousInputValues.set(t,n);let s=Be(r.index,o);dc(s,1)}get injector(){return new It(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function QD(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var bn=(()=>{class e{static __NG_ELEMENT_ID__=KD}return e})();function KD(){let e=he();return Jf(e,G())}var JD=bn,Kf=class extends JD{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return vn(this._hostTNode,this._hostLView)}get injector(){return new It(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ga(this._hostTNode,this._hostLView);if(Gd(t)){let n=Bo(t,this._hostLView),r=jo(t),o=n[P].data[r+8];return new It(o,n)}else return new It(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Hl(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-de}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Vl(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Pl(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!oy(t),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let c=s?t:new Wo(Jn(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let g=(s?u:this.parentInjector).get(Pe,null);g&&(i=g)}let l=Jn(c.componentType??{}),d=Vl(this._lContainer,l?.id??null),h=d?.firstChild??null,f=c.create(u,o,h,i);return this.insertImpl(f.hostView,a,Pl(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(dy(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[re],u=new Kf(c,c[Re],c[re]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return wD(s,o,i,r),t.attachToViewContainerRef(),ud(As(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Hl(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=aa(this._lContainer,n);r&&(No(As(this._lContainer),n),Ff(r[P],r))}detach(t){let n=this._adjustIndex(t,-1),r=aa(this._lContainer,n);return r&&No(As(this._lContainer),n)!=null?new ir(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Hl(e){return e[ko]}function As(e){return e[ko]||(e[ko]=[])}function Jf(e,t){let n,r=t[e.index];return nt(r)?n=r:(n=zf(r,t,null,e),t[e.index]=n,rc(t,n)),eE(n,t,e,r),new Kf(n,e,t)}function XD(e,t){let n=e[ee],r=n.createComment(""),o=rt(t,e),i=n.parentNode(o);return zo(n,i,r,n.nextSibling(o),!1),r}var eE=rE,tE=()=>!1;function nE(e,t,n){return tE(e,t,n)}function rE(e,t,n,r){if(e[xt])return;let o;n.type&8?o=je(r):o=XD(t,n),e[xt]=o}var da=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},fa=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)gc(t,n).matches!==null&&this.queries[n].setDirty()}},ha=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=dE(t):this.predicate=t}},pa=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},ga=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,oE(n,i)),this.matchTNodeWithReadOption(t,n,_o(n,t,i,!1,!1))}else r===Ot?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,_o(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===$e||o===bn||o===Ot&&n.type&4)this.addMatch(n.index,-2);else{let i=_o(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function oE(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function iE(e,t){return e.type&11?vn(e,t):e.type&4?fc(e,t):null}function sE(e,t,n,r){return n===-1?iE(t,e):n===-2?aE(e,t,r):rr(e,e[P],n,t)}function aE(e,t,n){if(n===$e)return vn(t,e);if(n===Ot)return fc(t,e);if(n===bn)return Jf(t,e)}function Xf(e,t,n,r){let o=t[Je].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(sE(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function ma(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Xf(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let d=de;d<l.length;d++){let h=l[d];h[St]===h[re]&&ma(h[P],h,u,r)}if(l[un]!==null){let d=l[un];for(let h=0;h<d.length;h++){let f=d[h];ma(f[P],f,u,r)}}}}}return r}function cE(e,t){return e[Je].queries[t].queryList}function uE(e,t,n){let r=new Xs((n&4)===4);return hy(e,t,r,r.destroy),(t[Je]??=new fa).queries.push(new da(r))-1}function lE(e,t,n){let r=ue();return r.firstCreatePass&&(fE(r,new ha(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),uE(r,G(),t)}function dE(e){return e.split(",").map(t=>t.trim())}function fE(e,t,n){e.queries===null&&(e.queries=new pa),e.queries.track(new ga(t,n))}function gc(e,t){return e.queries.getByIndex(t)}function hE(e,t){let n=e[P],r=gc(n,t);return r.crossesNgTemplate?ma(n,e,t,[]):Xf(n,e,r,t)}var qo=class{};var Zo=class extends qo{injector;componentFactoryResolver=new ua(this);instance=null;constructor(t){super();let n=new Xn([...t.providers,{provide:qo,useValue:this},{provide:pc,useValue:this.componentFactoryResolver}],t.parent||ka(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function pE(e,t,n=null){return new Zo({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var gE=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=fd(!1,n.type),o=r.length>0?pE([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=x({token:e,providedIn:"environment",factory:()=>new e(E(Pe))})}return e})();function ui(e){return ar(()=>{let t=eh(e),n=A(I({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===cf.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(gE).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ue.Emulated,styles:e.styles||De,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Ja("NgStandalone"),th(n);let r=e.dependencies;return n.directiveDefs=zl(r,!1),n.pipeDefs=zl(r,!0),n.id=EE(n),n})}function mE(e){return Jn(e)||qm(e)}function yE(e){return e!==null}function X(e){return ar(()=>({type:e.type,bootstrap:e.bootstrap||De,declarations:e.declarations||De,imports:e.imports||De,exports:e.exports||De,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function vE(e,t){if(e==null)return _t;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ai.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function DE(e){if(e==null)return _t;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function He(e){return ar(()=>{let t=eh(e);return th(t),t})}function eh(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||_t,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||De,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:vE(e.inputs,t),outputs:DE(e.outputs),debugInfo:null}}function th(e){e.features?.forEach(t=>t(e))}function zl(e,t){if(!e)return null;let n=t?Zm:mE;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(yE)}function EE(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function bE(e){return Object.getPrototypeOf(e.prototype).constructor}function wn(e){let t=bE(e.type),n=!0,r=[e];for(;t;){let o;if(Ve(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new T(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Ns(e.inputs),s.declaredInputs=Ns(e.declaredInputs),s.outputs=Ns(e.outputs);let a=o.hostBindings;a&&ME(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&IE(e,c),u&&_E(e,u),wE(e,o),_m(e.outputs,o.outputs),Ve(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===wn&&(n=!1)}}t=Object.getPrototypeOf(t)}CE(r)}function wE(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function CE(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=nr(o.hostAttrs,n=nr(n,o.hostAttrs))}}function Ns(e){return e===_t?{}:e===De?[]:e}function IE(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function _E(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function ME(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function nh(e){return TE(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function SE(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function TE(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function li(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function xE(e,t,n,r,o,i,s,a,c){let u=t.consts,l=hc(t,e,4,s||null,a||null);Nd()&&qf(t,n,l,Lo(u,c),Af),l.mergedAttrs=nr(l.mergedAttrs,l.attrs),Hd(t,l);let d=l.tView=tc(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function AE(e,t,n,r,o,i,s,a,c,u){let l=n+Xe,d=t.firstCreatePass?xE(l,t,e,r,o,i,s,a,c):t.data[l];gr(d,!1);let h=NE(t,e,d,n);Ha()&&uc(t,e,h,d),mr(h,e);let f=zf(h,e,h,d);return e[l]=f,rc(e,f),nE(f,d,e),Pa(d)&&oc(t,e,d),c!=null&&Tf(e,d,u),d}function di(e,t,n,r,o,i,s,a){let c=G(),u=ue(),l=Lo(u.consts,i);return AE(c,u,e,t,n,r,o,l,s,a),di}var NE=RE;function RE(e,t,n,r){return za(!0),t[ee].createComment("")}var rh=new b("");var oh=(()=>{class e{static \u0275prov=x({token:e,providedIn:"root",factory:()=>new ya})}return e})(),ya=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function yr(e){return!!e&&typeof e.then=="function"}function ih(e){return!!e&&typeof e.subscribe=="function"}var OE=new b("");var sh=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=M(OE,{optional:!0})??[];injector=M(oe);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=ni(this.injector,o);if(yr(i))n.push(i);else if(ih(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ah=new b("");function FE(){Fu(()=>{throw new T(600,!1)})}function kE(e){return e.isBoundToModule}var PE=10;var Ft=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=M(ev);afterRenderManager=M(gv);zonelessEnabled=M(Wa);rootEffectScheduler=M(oh);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Y;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=M(yn).hasPendingTasks.pipe(B(n=>!n));constructor(){M(si,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=M(Pe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){z(10);let o=n instanceof Wf;if(!this._injector.get(sh).done){let h="";throw new T(405,h)}let s;o?s=n:s=this._injector.get(pc).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=kE(s)?void 0:this._injector.get(qo),c=r||s.selector,u=s.create(oe.NULL,[],c,a),l=u.location.nativeElement,d=u.injector.get(rh,null);return d?.registerApplication(l),u.onDestroy(()=>{this.detachView(u.hostView),Mo(this.components,u),d?.unregisterApplication(l)}),this._loadComponent(u),z(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){z(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(gf.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new T(101,!1);let n=j(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,j(n),this.afterTick.next(),z(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(dn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<PE;)z(14),this.synchronizeOnce(),z(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)LE(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ii(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Mo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(ah,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Mo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new T(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Mo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function LE(e,t,n,r){if(!n&&!ii(e))return;Bf(e,t,n&&!r?0:1)}function VE(e,t,n,r){return li(e,ja(),n)?t+od(n)+r:Dn}function bo(e,t){return e<<17|t<<2}function kt(e){return e>>17&32767}function jE(e){return(e&2)==2}function BE(e,t){return e&131071|t<<17}function va(e){return e|2}function fn(e){return(e&131068)>>2}function Rs(e,t){return e&-131069|t<<2}function UE(e){return(e&1)===1}function Da(e){return e|1}function $E(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=kt(s),c=fn(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||ur(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=kt(e[a+1]);e[r+1]=bo(h,a),h!==0&&(e[h+1]=Rs(e[h+1],r)),e[a+1]=BE(e[a+1],r)}else e[r+1]=bo(a,0),a!==0&&(e[a+1]=Rs(e[a+1],r)),a=r;else e[r+1]=bo(c,0),a===0?a=r:e[c+1]=Rs(e[c+1],r),c=r;u&&(e[r+1]=va(e[r+1])),Gl(e,l,r,!0),Gl(e,l,r,!1),HE(t,l,e,r,i),s=bo(a,c),i?t.classBindings=s:t.styleBindings=s}function HE(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&ur(i,t)>=0&&(n[r+1]=Da(n[r+1]))}function Gl(e,t,n,r){let o=e[n+1],i=t===null,s=r?kt(o):fn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];zE(c,t)&&(a=!0,e[s+1]=r?Da(u):va(u)),s=r?kt(u):fn(u)}a&&(e[n+1]=r?va(o):Da(o))}function zE(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?ur(e,t)>=0:!1}function jt(e,t,n){let r=G(),o=ja();if(li(r,o,t)){let i=ue(),s=Ud();xf(i,s,r,e,t,r[ee],n,!1)}return jt}function Wl(e,t,n,r,o){ic(t,e,n,o?"class":"style",r)}function vr(e,t){return GE(e,t,null,!0),vr}function GE(e,t,n,r){let o=G(),i=ue(),s=Cy(2);if(i.firstUpdatePass&&qE(i,e,s,r),t!==Dn&&li(o,s,t)){let a=i.data[pn()];JE(i,a,o,o[ee],e,o[s+1]=XE(t,n),r,s)}}function WE(e,t){return t>=e.expandoStartIndex}function qE(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[pn()],s=WE(e,n);eb(i,r)&&t===null&&!s&&(t=!1),t=ZE(o,i,t,r),$E(o,i,t,n,s,r)}}function ZE(e,t,n,r){let o=Sy(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Os(null,e,t,n,r),n=sr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Os(o,e,t,n,r),i===null){let c=YE(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Os(null,e,t,c[1],r),c=sr(c,t.attrs,r),QE(e,t,r,c))}else i=KE(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function YE(e,t,n){let r=n?t.classBindings:t.styleBindings;if(fn(r)!==0)return e[kt(r)]}function QE(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[kt(o)]=r}function KE(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=sr(r,s,n)}return sr(r,t.attrs,n)}function Os(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=sr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function sr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Gm(e,s,n?!0:t[++i]))}return e===void 0?null:e}function JE(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=UE(u)?ql(c,t,n,o,fn(u),s):void 0;if(!Yo(l)){Yo(i)||jE(u)&&(i=ql(c,null,n,o,a,s));let d=Id(pn(),n);cD(r,s,d,o,i)}}function ql(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=n[o+1];h===Dn&&(h=d?De:void 0);let f=d?ws(h,r):l===r?h:void 0;if(u&&!Yo(f)&&(f=ws(c,r)),Yo(f)&&(a=f,s))return a;let p=e[o+1];o=s?kt(p):fn(p)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ws(c,r))}return a}function Yo(e){return e!==void 0}function XE(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=be(Xa(e)))),e}function eb(e,t){return(e.flags&(t?8:16))!==0}function le(e,t,n,r){let o=G(),i=ue(),s=Xe+e,a=o[ee],c=i.firstCreatePass?Yf(s,i,o,t,Af,Nd(),n,r):i.data[s],u=tb(i,o,c,a,t,e);o[s]=u;let l=Pa(c);return gr(c,!0),Cf(a,u,c),!Rf(c)&&Ha()&&uc(i,o,u,c),(py()===0||l)&&mr(u,o),gy(),l&&(oc(i,o,c),Df(i,c,o)),r!==null&&Tf(o,c),le}function se(){let e=he();Od()?by():(e=e.parent,gr(e,!1));let t=e;vy(t)&&Dy(),my();let n=ue();return n.firstCreatePass&&Qf(n,t),t.classesWithoutHost!=null&&Ry(t)&&Wl(n,t,G(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Oy(t)&&Wl(n,t,G(),t.stylesWithoutHost,!1),se}function Dr(e,t,n,r){return le(e,t,n,r),se(),Dr}var tb=(e,t,n,r,o,i)=>(za(!0),bf(r,o,xy()));function ch(){return G()}var Qo="en-US";var nb=Qo;function rb(e){typeof e=="string"&&(nb=e.toLowerCase().replace(/_/g,"-"))}var ob=(e,t,n)=>{};function Bt(e,t,n,r){let o=G(),i=ue(),s=he();return uh(i,o,o[ee],s,e,t,r),Bt}function ib(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Fo],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function uh(e,t,n,r,o,i,s){let a=Pa(r),u=e.firstCreatePass?Ad(e):null,l=t[fe],d=xd(t),h=!0;if(r.type&3||s){let f=rt(r,t),p=s?s(f):f,g=d.length,v=s?D=>s(je(D[r.index])):r.index,m=null;if(!s&&a&&(m=ib(e,t,o,r.index)),m!==null){let D=m.__ngLastListenerFn__||m;D.__ngNextListenerFn__=i,m.__ngLastListenerFn__=i,h=!1}else{i=Ql(r,t,l,i),ob(p,o,i);let D=n.listen(p,o,i);d.push(i,D),u&&u.push(o,v,g,g+1)}}else i=Ql(r,t,l,i);if(h){let f=r.outputs?.[o],p=r.hostDirectiveOutputs?.[o];if(p&&p.length)for(let g=0;g<p.length;g+=2){let v=p[g],m=p[g+1];Zl(r,e,t,v,m,o,i,d,u)}if(f&&f.length)for(let g of f)Zl(r,e,t,g,o,o,i,d,u)}}function Zl(e,t,n,r,o,i,s,a,c){let u=n[r],d=t.data[r].outputs[o],f=u[d].subscribe(s),p=a.length;a.push(s,f),c&&c.push(i,e.index,p,-(p+1))}function Yl(e,t,n,r){let o=j(null);try{return z(6,t,n),n(r)!==!1}catch(i){return Nf(e,i),!1}finally{z(7,t,n),j(o)}}function Ql(e,t,n,r){return function o(i){if(i===Function)return r;let s=hn(e)?Be(e.index,t):t;dc(s,5);let a=Yl(t,n,r,i),c=o.__ngNextListenerFn__;for(;c;)a=Yl(t,n,c,i)&&a,c=c.__ngNextListenerFn__;return a}}function lh(e,t,n){lE(e,t,n)}function dh(e){let t=G(),n=ue(),r=kd();Ba(r+1);let o=gc(n,r);if(e.dirty&&ly(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=hE(t,r);e.reset(i,nv),e.notifyOnChanges()}return!0}return!1}function fh(){return cE(G(),kd())}function Ut(e,t=""){let n=G(),r=ue(),o=e+Xe,i=r.firstCreatePass?hc(r,o,1,t,null):r.data[o],s=sb(r,n,i,t,e);n[o]=s,Ha()&&uc(r,n,s,i),gr(i,!1)}var sb=(e,t,n,r,o)=>(za(!0),Tv(t[ee],r));function mc(e,t,n){let r=G(),o=VE(r,e,t,n);return o!==Dn&&ab(r,pn(),o),mc}function ab(e,t,n){let r=Id(t,e);xv(e[ee],r,n)}function yc(e,t,n){sf(t)&&(t=t());let r=G(),o=ja();if(li(r,o,t)){let i=ue(),s=Ud();xf(i,s,r,e,t,r[ee],n,!1)}return yc}function hh(e,t){let n=sf(e);return n&&e.set(t),n}function vc(e,t){let n=G(),r=ue(),o=he();return uh(r,n,n[ee],o,e,t),vc}function cb(e,t,n){let r=ue();if(r.firstCreatePass){let o=Ve(e);Ea(n,r.data,r.blueprint,o,!0),Ea(t,r.data,r.blueprint,o,!1)}}function Ea(e,t,n,r,o){if(e=ie(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ea(e[i],t,n,r,o);else{let i=ue(),s=G(),a=he(),c=cn(e)?e:ie(e.provide),u=gd(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(cn(e)||!e.multi){let f=new Rt(u,o,H),p=ks(c,t,o?l:l+h,d);p===-1?(Zs(Uo(a,s),i,c),Fs(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[p]=f,s[p]=f)}else{let f=ks(c,t,l+h,d),p=ks(c,t,l,l+h),g=f>=0&&n[f],v=p>=0&&n[p];if(o&&!v||!o&&!g){Zs(Uo(a,s),i,c);let m=db(o?lb:ub,n.length,o,r,u);!o&&v&&(n[p].providerFactory=m),Fs(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(m),s.push(m)}else{let m=ph(n[o?p:f],u,!o&&r);Fs(i,e,f>-1?f:p,m)}!o&&r&&v&&n[p].componentProviders++}}}function Fs(e,t,n,r){let o=cn(t),i=Jm(t);if(o||i){let c=(i?ie(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function ph(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ks(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function ub(e,t,n,r){return ba(this.multi,[])}function lb(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=rr(n,n[P],this.providerFactory.index,r);i=a.slice(0,s),ba(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],ba(o,i);return i}function ba(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function db(e,t,n,r,o){let i=new Rt(e,n,H);return i.multi=[],i.index=t,i.componentProviders=0,ph(i,o,r&&!n),i}function Dc(e,t=[]){return n=>{n.providersResolver=(r,o)=>cb(r,o?o(e):e,t)}}var fb=(()=>{class e{zone=M(Q);changeDetectionScheduler=M(ln);applicationRef=M(Ft);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function hb({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new Q(A(I({},pb()),{scheduleInRootZone:n})),[{provide:Q,useFactory:e},{provide:Mt,multi:!0,useFactory:()=>{let r=M(fb,{optional:!0});return()=>r.initialize()}},{provide:Mt,multi:!0,useFactory:()=>{let r=M(gb);return()=>{r.initialize()}}},t===!0?{provide:tf,useValue:!0}:[],{provide:nf,useValue:n??ef}]}function pb(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var gb=(()=>{class e{subscription=new te;initialized=!1;zone=M(Q);pendingTasks=M(yn);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Q.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Q.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var mb=(()=>{class e{appRef=M(Ft);taskService=M(yn);ngZone=M(Q);zonelessEnabled=M(Wa);tracing=M(si,{optional:!0});disableScheduling=M(tf,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new te;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ho):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(M(nf,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Js||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?xl:rf;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ho+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,xl(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function yb(){return typeof $localize<"u"&&$localize.locale||Qo}var Ec=new b("",{providedIn:"root",factory:()=>M(Ec,L.Optional|L.SkipSelf)||yb()});var wa=new b(""),vb=new b("");function Zn(e){return!e.moduleRef}function Db(e){let t=Zn(e)?e.r3Injector:e.moduleRef.injector,n=t.get(Q);return n.run(()=>{Zn(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ce,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Zn(e)){let i=()=>t.destroy(),s=e.platformInjector.get(wa);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(wa);s.add(i),e.moduleRef.onDestroy(()=>{Mo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return bb(r,n,()=>{let i=t.get(sh);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Ec,Qo);if(rb(s||Qo),!t.get(vb,!0))return Zn(e)?t.get(Ft):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Zn(e)){let c=t.get(Ft);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return Eb(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function Eb(e,t){let n=e.injector.get(Ft);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new T(-403,!1);t.push(e)}function bb(e,t,n){try{let r=n();return yr(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var So=null;function wb(e=[],t){return oe.create({name:t,providers:[{provide:ti,useValue:"platform"},{provide:wa,useValue:new Set([()=>So=null])},...e]})}function Cb(e=[]){if(So)return So;let t=wb(e);return So=t,FE(),Ib(t),t}function Ib(e){let t=e.get(Qa,null);ni(e,()=>{t?.forEach(n=>n())})}function Er(){return!1}var Cn=(()=>{class e{static __NG_ELEMENT_ID__=_b}return e})();function _b(e){return Mb(he(),G(),(e&16)===16)}function Mb(e,t,n){if(hn(e)&&!n){let r=Be(e.index,t);return new ir(r,r)}else if(e.type&175){let r=t[Le];return new ir(r,t)}return null}var Ca=class{constructor(){}supports(t){return nh(t)}create(t){return new Ia(t)}},Sb=(e,t)=>t,Ia=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||Sb}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Kl(r,o,i)?n:r,a=Kl(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,p=f+h;l<=p&&p<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!nh(t))throw new T(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,SE(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new _a(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Ko),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ko),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},_a=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Ma=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Ko=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Ma,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Kl(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function Jl(){return new bc([new Ca])}var bc=(()=>{class e{factories;static \u0275prov=x({token:e,providedIn:"root",factory:Jl});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Jl()),deps:[[e,new Um,new cd]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new T(901,!1)}}return e})();function gh(e){z(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Cb(r),i=[hb({}),{provide:ln,useExisting:mb},...n||[]],s=new Zo({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Db({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{z(9)}}function wc(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Oe(e){return Vu(e)}function ot(e,t){return Nu(e,t?.equal)}var Sa=class{[Me];constructor(t){this[Me]=t}destroy(){this[Me].destroy()}};function fi(e,t){!t?.injector&&ri(fi);let n=t?.injector??M(oe),r=t?.manualCleanup!==!0?n.get(mn):null,o,i=n.get(mf,null,{optional:!0}),s=n.get(ln);return i!==null&&!t?.forceRoot?(o=Ab(i.view,s,e),r instanceof $o&&r._lView===i.view&&(r=null)):o=Nb(e,n.get(oh),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Sa(o)}var mh=A(I({},Wt),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:or,run(){if(this.dirty=!1,this.hasRun&&!Gr(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Vn(this),n=Vo(!1);try{this.maybeCleanup(),this.fn(e)}finally{Vo(n),zr(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),Tb=A(I({},mh),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){jn(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),xb=A(I({},mh),{consumerMarkedDirty(){this.view[O]|=8192,fr(this.view),this.notifier.notify(13)},destroy(){jn(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Tt]?.delete(this)}});function Ab(e,t,n){let r=Object.create(xb);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Tt]??=new Set,e[Tt].add(r),r.consumerMarkedDirty(r),r}function Nb(e,t,n){let r=Object.create(Tb);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}var bh=null;function _n(){return bh}function wh(e){bh??=e}var hi=class{};var ze=new b("");function pi(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Cc=/\s+/,yh=[],Ch=(()=>{class e{_ngEl;_renderer;initialClasses=yh;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(Cc):yh}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(Cc):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(Cc).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(H($e),H(En))};static \u0275dir=He({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Ic=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Ih=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ic(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),vh(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);vh(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(bn),H(Ot),H(bc))};static \u0275dir=He({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function vh(e,t){e.context.$implicit=t.item}var _h=(()=>{class e{_viewContainer;_context=new _c;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Dh(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Dh(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(bn),H(Ot))};static \u0275dir=He({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),_c=class{$implicit=null;ngIf=null};function Dh(e,t){if(e&&!e.createEmbeddedView)throw new T(2020,!1)}var Mc=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=X({type:e});static \u0275inj=J({})}return e})(),Mh="browser",Rb="server";function Sc(e){return e===Rb}var In=class{};function br(e,t){let n=!t?.manualCleanup;n&&!t?.injector&&ri(br);let r=n?t?.injector?.get(mn)??M(mn):null,o=Fb(t?.equal),i;t?.requireSync?i=ft({kind:0},{equal:o}):i=ft({kind:1,value:t?.initialValue},{equal:o});let s=e.subscribe({next:a=>i.set({kind:1,value:a}),error:a=>{if(t?.rejectErrors)throw a;i.set({kind:2,error:a})}});if(t?.requireSync&&i().kind===0)throw new T(601,!1);return r?.onDestroy(s.unsubscribe.bind(s)),ot(()=>{let a=i();switch(a.kind){case 1:return a.value;case 2:throw a.error;case 0:throw new T(601,!1)}},{equal:t?.equal})}function Fb(e=Object.is){return(t,n)=>t.kind===1&&n.kind===1&&e(t.value,n.value)}var Nc={};function pe(e,t){if(Nc[e]=(Nc[e]||0)+1,typeof t=="function")return Tc(e,(...r)=>A(I({},t(...r)),{type:e}));switch(t?t._as:"empty"){case"empty":return Tc(e,()=>({type:e}));case"props":return Tc(e,r=>A(I({},r),{type:e}));default:throw new Error("Unexpected config.")}}function yi(){return{_as:"props",_p:void 0}}function Tc(e,t){return Object.defineProperty(t,"type",{value:e,writable:!1})}function kb(e,t){if(e==null)throw new Error(`${t} must be defined.`)}var Cr="@ngrx/store/init",Ge=(()=>{class e extends Te{constructor(){super({type:Cr})}next(n){if(typeof n=="function")throw new TypeError(`
        Dispatch expected an object, instead it received a function.
        If you're using the createAction function, make sure to invoke the function
        before dispatching the action. For example, someAction should be someAction().`);if(typeof n>"u")throw new TypeError("Actions must be objects");if(typeof n.type>"u")throw new TypeError("Actions must have a type property");super.next(n)}complete(){}ngOnDestroy(){super.complete()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Pb=[Ge],$h=new b("@ngrx/store Internal Root Guard"),Sh=new b("@ngrx/store Internal Initial State"),Ir=new b("@ngrx/store Initial State"),Hh=new b("@ngrx/store Reducer Factory"),Th=new b("@ngrx/store Internal Reducer Factory Provider"),zh=new b("@ngrx/store Initial Reducers"),xc=new b("@ngrx/store Internal Initial Reducers"),xh=new b("@ngrx/store Store Features"),Ah=new b("@ngrx/store Internal Store Reducers"),Ac=new b("@ngrx/store Internal Feature Reducers"),Nh=new b("@ngrx/store Internal Feature Configs"),Gh=new b("@ngrx/store Internal Store Features"),Rh=new b("@ngrx/store Internal Feature Reducers Token"),Wh=new b("@ngrx/store Feature Reducers"),Oh=new b("@ngrx/store User Provided Meta Reducers"),gi=new b("@ngrx/store Meta Reducers"),Fh=new b("@ngrx/store Internal Resolved Meta Reducers"),kh=new b("@ngrx/store User Runtime Checks Config"),Ph=new b("@ngrx/store Internal User Runtime Checks Config"),wr=new b("@ngrx/store Internal Runtime Checks"),kc=new b("@ngrx/store Check if Action types are unique"),Lb=new b("@ngrx/store Root Store Provider"),Vb=new b("@ngrx/store Feature State Provider");function Pc(e,t={}){let n=Object.keys(e),r={};for(let i=0;i<n.length;i++){let s=n[i];typeof e[s]=="function"&&(r[s]=e[s])}let o=Object.keys(r);return function(s,a){s=s===void 0?t:s;let c=!1,u={};for(let l=0;l<o.length;l++){let d=o[l],h=r[d],f=s[d],p=h(f,a);u[d]=p,c=c||p!==f}return c?u:s}}function jb(e,t){return Object.keys(e).filter(n=>n!==t).reduce((n,r)=>Object.assign(n,{[r]:e[r]}),{})}function qh(...e){return function(t){if(e.length===0)return t;let n=e[e.length-1];return e.slice(0,-1).reduceRight((o,i)=>i(o),n(t))}}function Zh(e,t){return Array.isArray(t)&&t.length>0&&(e=qh.apply(null,[...t,e])),(n,r)=>{let o=e(n);return(i,s)=>(i=i===void 0?r:i,o(i,s))}}function Bb(e){let t=Array.isArray(e)&&e.length>0?qh(...e):n=>n;return(n,r)=>(n=t(n),(o,i)=>(o=o===void 0?r:o,n(o,i)))}var $t=class extends k{},Mn=class extends Ge{},vi="@ngrx/store/update-reducers",mi=(()=>{class e extends Te{get currentReducers(){return this.reducers}constructor(n,r,o,i){super(i(o,r)),this.dispatcher=n,this.initialState=r,this.reducers=o,this.reducerFactory=i}addFeature(n){this.addFeatures([n])}addFeatures(n){let r=n.reduce((o,{reducers:i,reducerFactory:s,metaReducers:a,initialState:c,key:u})=>{let l=typeof i=="function"?Bb(a)(i,c):Zh(s,a)(i,c);return o[u]=l,o},{});this.addReducers(r)}removeFeature(n){this.removeFeatures([n])}removeFeatures(n){this.removeReducers(n.map(r=>r.key))}addReducer(n,r){this.addReducers({[n]:r})}addReducers(n){this.reducers=I(I({},this.reducers),n),this.updateReducers(Object.keys(n))}removeReducer(n){this.removeReducers([n])}removeReducers(n){n.forEach(r=>{this.reducers=jb(this.reducers,r)}),this.updateReducers(n)}updateReducers(n){this.next(this.reducerFactory(this.reducers,this.initialState)),this.dispatcher.next({type:vi,features:n})}ngOnDestroy(){this.complete()}static{this.\u0275fac=function(r){return new(r||e)(E(Mn),E(Ir),E(zh),E(Hh))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Ub=[mi,{provide:$t,useExisting:mi},{provide:Mn,useExisting:Ge}],Ht=(()=>{class e extends Y{ngOnDestroy(){this.complete()}static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=gn(e)))(o||e)}})()}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),$b=[Ht],Sn=class extends k{},Lh=(()=>{class e extends Te{static{this.INIT=Cr}constructor(n,r,o,i){super(i);let a=n.pipe(Qe(Gn)).pipe(wt(r)),c={state:i},u=a.pipe(qn(Hb,c));this.stateSubscription=u.subscribe(({state:l,action:d})=>{this.next(l),o.next(d)}),this.state=br(this,{manualCleanup:!0,requireSync:!0})}ngOnDestroy(){this.stateSubscription.unsubscribe(),this.complete()}static{this.\u0275fac=function(r){return new(r||e)(E(Ge),E($t),E(Ht),E(Ir))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function Hb(e={state:void 0},[t,n]){let{state:r}=e;return{state:n(r,t),action:t}}var zb=[Lh,{provide:Sn,useExisting:Lh}],ge=(()=>{class e extends k{constructor(n,r,o,i){super(),this.actionsObserver=r,this.reducerManager=o,this.injector=i,this.source=n,this.state=n.state}select(n,...r){return Wb.call(null,n,...r)(this)}selectSignal(n,r){return ot(()=>n(this.state()),r)}lift(n){let r=new e(this,this.actionsObserver,this.reducerManager);return r.operator=n,r}dispatch(n,r){if(typeof n=="function")return this.processDispatchFn(n,r);this.actionsObserver.next(n)}next(n){this.actionsObserver.next(n)}error(n){this.actionsObserver.error(n)}complete(){this.actionsObserver.complete()}addReducer(n,r){this.reducerManager.addReducer(n,r)}removeReducer(n){this.reducerManager.removeReducer(n)}processDispatchFn(n,r){kb(this.injector,"Store Injector");let o=r?.injector??qb()??this.injector;return fi(()=>{let i=n();Oe(()=>this.dispatch(i))},{injector:o})}static{this.\u0275fac=function(r){return new(r||e)(E(Sn),E(Ge),E(mi),E(oe))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Gb=[ge];function Wb(e,t,...n){return function(o){let i;if(typeof e=="string"){let s=[t,...n].filter(Boolean);i=o.pipe(vs(e,...s))}else if(typeof e=="function")i=o.pipe(B(s=>e(s,t)));else throw new TypeError(`Unexpected type '${typeof e}' in select operator, expected 'string' or 'function'`);return i.pipe(ms())}}function qb(){try{return M(oe)}catch{return}}var Lc="https://ngrx.io/guide/store/configuration/runtime-checks";function Vh(e){return e===void 0}function jh(e){return e===null}function Yh(e){return Array.isArray(e)}function Zb(e){return typeof e=="string"}function Yb(e){return typeof e=="boolean"}function Qb(e){return typeof e=="number"}function Qh(e){return typeof e=="object"&&e!==null}function Kb(e){return Qh(e)&&!Yh(e)}function Jb(e){if(!Kb(e))return!1;let t=Object.getPrototypeOf(e);return t===Object.prototype||t===null}function Rc(e){return typeof e=="function"}function Xb(e){return Rc(e)&&e.hasOwnProperty("\u0275cmp")}function ew(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var tw=!1;function nw(){return tw}function Bh(e,t){return e===t}function rw(e,t,n){for(let r=0;r<e.length;r++)if(!n(e[r],t[r]))return!0;return!1}function Kh(e,t=Bh,n=Bh){let r=null,o=null,i;function s(){r=null,o=null}function a(l=void 0){i={result:l}}function c(){i=void 0}function u(){if(i!==void 0)return i.result;if(!r)return o=e.apply(null,arguments),r=arguments,o;if(!rw(arguments,r,t))return o;let l=e.apply(null,arguments);return r=arguments,n(o,l)?o:(o=l,l)}return{memoized:u,reset:s,setResult:a,clearResult:c}}function ae(...e){return iw(Kh)(...e)}function ow(e,t,n,r){if(n===void 0){let i=t.map(s=>s(e));return r.memoized.apply(null,i)}let o=t.map(i=>i(e,n));return r.memoized.apply(null,[...o,n])}function iw(e,t={stateFn:ow}){return function(...n){let r=n;if(Array.isArray(r[0])){let[l,...d]=r;r=[...l,...d]}else r.length===1&&sw(r[0])&&(r=aw(r[0]));let o=r.slice(0,r.length-1),i=r[r.length-1],s=o.filter(l=>l.release&&typeof l.release=="function"),a=e(function(...l){return i.apply(null,l)}),c=Kh(function(l,d){return t.stateFn.apply(null,[l,o,d,a])});function u(){c.reset(),a.reset(),s.forEach(l=>l.release())}return Object.assign(c.memoized,{release:u,projector:a.memoized,setResult:c.setResult,clearResult:c.clearResult})}}function Jh(e){return ae(t=>{let n=t[e];return!nw()&&Er()&&!(e in t)&&console.warn(`@ngrx/store: The feature name "${e}" does not exist in the state, therefore createFeatureSelector cannot access it.  Be sure it is imported in a loaded module using StoreModule.forRoot('${e}', ...) or StoreModule.forFeature('${e}', ...).  If the default state is intended to be undefined, as is the case with router state, this development-only warning message can be ignored.`),n},t=>t)}function sw(e){return!!e&&typeof e=="object"&&Object.values(e).every(t=>typeof t=="function")}function aw(e){let t=Object.values(e),n=Object.keys(e),r=(...o)=>n.reduce((i,s,a)=>A(I({},i),{[s]:o[a]}),{});return[...t,r]}function cw(e){return e instanceof b?M(e):e}function uw(e,t){return t.map((n,r)=>{if(e[r]instanceof b){let o=M(e[r]);return{key:n.key,reducerFactory:o.reducerFactory?o.reducerFactory:Pc,metaReducers:o.metaReducers?o.metaReducers:[],initialState:o.initialState}}return n})}function lw(e){return e.map(t=>t instanceof b?M(t):t)}function Xh(e){return typeof e=="function"?e():e}function dw(e,t){return e.concat(t)}function fw(){if(M(ge,{optional:!0,skipSelf:!0}))throw new TypeError("The root Store has been provided more than once. Feature modules should provide feature states instead.");return"guarded"}function hw(e,t){return function(n,r){let o=t.action(r)?Oc(r):r,i=e(n,o);return t.state()?Oc(i):i}}function Oc(e){Object.freeze(e);let t=Rc(e);return Object.getOwnPropertyNames(e).forEach(n=>{if(!n.startsWith("\u0275")&&ew(e,n)&&(!t||n!=="caller"&&n!=="callee"&&n!=="arguments")){let r=e[n];(Qh(r)||Rc(r))&&!Object.isFrozen(r)&&Oc(r)}}),e}function pw(e,t){return function(n,r){if(t.action(r)){let i=Fc(r);Uh(i,"action")}let o=e(n,r);if(t.state()){let i=Fc(o);Uh(i,"state")}return o}}function Fc(e,t=[]){return(Vh(e)||jh(e))&&t.length===0?{path:["root"],value:e}:Object.keys(e).reduce((r,o)=>{if(r)return r;let i=e[o];return Xb(i)?r:Vh(i)||jh(i)||Qb(i)||Yb(i)||Zb(i)||Yh(i)?!1:Jb(i)?Fc(i,[...t,o]):{path:[...t,o],value:i}},!1)}function Uh(e,t){if(e===!1)return;let n=e.path.join("."),r=new Error(`Detected unserializable ${t} at "${n}". ${Lc}#strict${t}serializability`);throw r.value=e.value,r.unserializablePath=n,r}function gw(e,t){return function(n,r){if(t.action(r)&&!Q.isInAngularZone())throw new Error(`Action '${r.type}' running outside NgZone. ${Lc}#strictactionwithinngzone`);return e(n,r)}}function mw(e){return Er()?I({strictStateSerializability:!1,strictActionSerializability:!1,strictStateImmutability:!0,strictActionImmutability:!0,strictActionWithinNgZone:!1,strictActionTypeUniqueness:!1},e):{strictStateSerializability:!1,strictActionSerializability:!1,strictStateImmutability:!1,strictActionImmutability:!1,strictActionWithinNgZone:!1,strictActionTypeUniqueness:!1}}function yw({strictActionSerializability:e,strictStateSerializability:t}){return n=>e||t?pw(n,{action:r=>e&&!Vc(r),state:()=>t}):n}function vw({strictActionImmutability:e,strictStateImmutability:t}){return n=>e||t?hw(n,{action:r=>e&&!Vc(r),state:()=>t}):n}function Vc(e){return e.type.startsWith("@ngrx")}function Dw({strictActionWithinNgZone:e}){return t=>e?gw(t,{action:n=>e&&!Vc(n)}):t}function Ew(e){return[{provide:Ph,useValue:e},{provide:kh,useFactory:bw,deps:[Ph]},{provide:wr,deps:[kh],useFactory:mw},{provide:gi,multi:!0,deps:[wr],useFactory:vw},{provide:gi,multi:!0,deps:[wr],useFactory:yw},{provide:gi,multi:!0,deps:[wr],useFactory:Dw}]}function ep(){return[{provide:kc,multi:!0,deps:[wr],useFactory:ww}]}function bw(e){return e}function ww(e){if(!e.strictActionTypeUniqueness)return;let t=Object.entries(Nc).filter(([,n])=>n>1).map(([n])=>n);if(t.length)throw new Error(`Action types are registered more than once, ${t.map(n=>`"${n}"`).join(", ")}. ${Lc}#strictactiontypeuniqueness`)}function Cw(e={},t={}){return[{provide:$h,useFactory:fw},{provide:Sh,useValue:t.initialState},{provide:Ir,useFactory:Xh,deps:[Sh]},{provide:xc,useValue:e},{provide:Ah,useExisting:e instanceof b?e:xc},{provide:zh,deps:[xc,[new Ra(Ah)]],useFactory:cw},{provide:Oh,useValue:t.metaReducers?t.metaReducers:[]},{provide:Fh,deps:[gi,Oh],useFactory:dw},{provide:Th,useValue:t.reducerFactory?t.reducerFactory:Pc},{provide:Hh,deps:[Th,Fh],useFactory:Zh},Pb,Ub,$b,zb,Gb,Ew(t.runtimeChecks),ep()]}function Iw(e,t,n={}){return[{provide:Nh,multi:!0,useValue:e instanceof Object?{}:n},{provide:xh,multi:!0,useValue:{key:e instanceof Object?e.name:e,reducerFactory:!(n instanceof b)&&n.reducerFactory?n.reducerFactory:Pc,metaReducers:!(n instanceof b)&&n.metaReducers?n.metaReducers:[],initialState:!(n instanceof b)&&n.initialState?n.initialState:void 0}},{provide:Gh,deps:[Nh,xh],useFactory:uw},{provide:Ac,multi:!0,useValue:e instanceof Object?e.reducer:t},{provide:Rh,multi:!0,useExisting:t instanceof b?t:Ac},{provide:Wh,multi:!0,deps:[Ac,[new Ra(Rh)]],useFactory:lw},ep()]}var _r=(()=>{class e{constructor(n,r,o,i,s,a){}static{this.\u0275fac=function(r){return new(r||e)(E(Ge),E($t),E(Ht),E(ge),E($h,8),E(kc,8))}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})(),Di=(()=>{class e{constructor(n,r,o,i,s){this.features=n,this.featureReducers=r,this.reducerManager=o;let a=n.map((c,u)=>{let d=r.shift()[u];return A(I({},c),{reducers:d,initialState:Xh(c.initialState)})});o.addFeatures(a)}ngOnDestroy(){this.reducerManager.removeFeatures(this.features)}static{this.\u0275fac=function(r){return new(r||e)(E(Gh),E(Wh),E(mi),E(_r),E(kc,8))}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})(),Ei=(()=>{class e{static forRoot(n,r){return{ngModule:_r,providers:[...Cw(n,r)]}}static forFeature(n,r,o={}){return{ngModule:Di,providers:[...Iw(n,r,o)]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})();function We(...e){let t=e.pop(),n=e.map(r=>r.type);return{reducer:t,types:n}}function tp(e,...t){let n=new Map;for(let r of t)for(let o of r.types){let i=n.get(o);if(i){let s=(a,c)=>r.reducer(i(a,c),c);n.set(o,s)}else n.set(o,r.reducer)}return function(r=e,o){let i=n.get(o.type);return i?i(r,o):r}}var _w={dispatch:!0,functional:!1,useEffectsErrorHandler:!0},wi="__@ngrx/effects_create__";function Bc(e,t={}){let n=t.functional?e:e(),r=I(I({},_w),t);return Object.defineProperty(n,wi,{value:r}),n}function Mw(e){return Object.getOwnPropertyNames(e).filter(r=>e[r]&&e[r].hasOwnProperty(wi)?e[r][wi].hasOwnProperty("dispatch"):!1).map(r=>{let o=e[r][wi];return I({propertyName:r},o)})}function Sw(e){return Mw(e)}function ip(e){return Object.getPrototypeOf(e)}function Tw(e){return!!e.constructor&&e.constructor.name!=="Object"&&e.constructor.name!=="Function"}function sp(e){return typeof e=="function"}function np(e){return e.filter(sp)}function xw(e){return e instanceof b||sp(e)}function Aw(e,t,n){let r=ip(e),i=!!r&&r.constructor.name!=="Object"?r.constructor.name:null,s=Sw(e).map(({propertyName:a,dispatch:c,useEffectsErrorHandler:u})=>{let l=typeof e[a]=="function"?e[a]():e[a],d=u?n(l,t):l;return c===!1?d.pipe(ps()):d.pipe(ys()).pipe(B(f=>({effect:e[a],notification:f,propertyName:a,sourceName:i,sourceInstance:e})))});return Et(...s)}var Nw=10;function ap(e,t,n=Nw){return e.pipe(at(r=>(t&&t.handleError(r),n<=1?e:ap(e,t,n-1))))}var Ci=(()=>{class e extends k{constructor(n){super(),n&&(this.source=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}static{this.\u0275fac=function(r){return new(r||e)(E(Ht))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Uc(...e){return ne(t=>e.some(n=>typeof n=="string"?n===t.type:n.type===t.type))}var cp=new b("@ngrx/effects Internal Root Guard"),bi=new b("@ngrx/effects User Provided Effects"),jc=new b("@ngrx/effects Internal Root Effects"),up=new b("@ngrx/effects Internal Root Effects Instances"),rp=new b("@ngrx/effects Internal Feature Effects"),lp=new b("@ngrx/effects Internal Feature Effects Instance Groups"),Rw=new b("@ngrx/effects Effects Error Handler",{providedIn:"root",factory:()=>ap}),dp="@ngrx/effects/init",IN=pe(dp);function Ow(e,t){if(e.notification.kind==="N"){let n=e.notification.value;!Fw(n)&&t.handleError(new Error(`Effect ${kw(e)} dispatched an invalid action: ${Pw(n)}`))}}function Fw(e){return typeof e!="function"&&e&&e.type&&typeof e.type=="string"}function kw({propertyName:e,sourceInstance:t,sourceName:n}){let r=typeof t[e]=="function";return!!n?`"${n}.${String(e)}${r?"()":""}"`:`"${String(e)}()"`}function Pw(e){try{return JSON.stringify(e)}catch{return e}}var Lw="ngrxOnIdentifyEffects";function Vw(e){return $c(e,Lw)}var jw="ngrxOnRunEffects";function Bw(e){return $c(e,jw)}var Uw="ngrxOnInitEffects";function $w(e){return $c(e,Uw)}function $c(e,t){return e&&t in e&&typeof e[t]=="function"}var fp=(()=>{class e extends Y{constructor(n,r){super(),this.errorHandler=n,this.effectsErrorHandler=r}addEffects(n){this.next(n)}toActions(){return this.pipe(Do(n=>Tw(n)?ip(n):n),ye(n=>n.pipe(Do(Hw))),ye(n=>{let r=n.pipe(yo(i=>zw(this.errorHandler,this.effectsErrorHandler)(i)),B(i=>(Ow(i,this.errorHandler),i.notification)),ne(i=>i.kind==="N"&&i.value!=null),gs()),o=n.pipe(Wn(1),ne($w),B(i=>i.ngrxOnInitEffects()));return Et(r,o)}))}static{this.\u0275fac=function(r){return new(r||e)(E(Ce),E(Rw))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Hw(e){return Vw(e)?e.ngrxOnIdentifyEffects():""}function zw(e,t){return n=>{let r=Aw(n,e,t);return Bw(n)?n.ngrxOnRunEffects(r):r}}var hp=(()=>{class e{get isStarted(){return!!this.effectsSubscription}constructor(n,r){this.effectSources=n,this.store=r,this.effectsSubscription=null}start(){this.effectsSubscription||(this.effectsSubscription=this.effectSources.toActions().subscribe(this.store))}ngOnDestroy(){this.effectsSubscription&&(this.effectsSubscription.unsubscribe(),this.effectsSubscription=null)}static{this.\u0275fac=function(r){return new(r||e)(E(fp),E(ge))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),pp=(()=>{class e{constructor(n,r,o,i,s,a,c){this.sources=n,r.start();for(let u of i)n.addEffects(u);o.dispatch({type:dp})}addEffects(n){this.sources.addEffects(n)}static{this.\u0275fac=function(r){return new(r||e)(E(fp),E(hp),E(ge),E(up),E(_r,8),E(Di,8),E(cp,8))}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})(),Gw=(()=>{class e{constructor(n,r,o,i){let s=r.flat();for(let a of s)n.addEffects(a)}static{this.\u0275fac=function(r){return new(r||e)(E(pp),E(lp),E(_r,8),E(Di,8))}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})(),Ii=(()=>{class e{static forFeature(...n){let r=n.flat(),o=np(r);return{ngModule:Gw,providers:[o,{provide:rp,multi:!0,useValue:r},{provide:bi,multi:!0,useValue:[]},{provide:lp,multi:!0,useFactory:op,deps:[rp,bi]}]}}static forRoot(...n){let r=n.flat(),o=np(r);return{ngModule:pp,providers:[o,{provide:jc,useValue:[r]},{provide:cp,useFactory:Ww},{provide:bi,multi:!0,useValue:[]},{provide:up,useFactory:op,deps:[jc,bi]}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})();function op(e,t){let n=[];for(let r of e)n.push(...r);for(let r of t)n.push(...r);return n.map(r=>xw(r)?M(r):r)}function Ww(){let e=M(hp,{optional:!0,skipSelf:!0}),t=M(jc,{self:!0});if(!(t.length===1&&t[0].length===0)&&e)throw new TypeError("EffectsModule.forRoot() called twice. Feature modules should use EffectsModule.forFeature() instead.");return"guarded"}var Tn=pe("[Chat] Send Message",yi()),gp=pe("[Chat] Add Typing Indicator"),mp=pe("[Chat] Remove Typing Indicator"),ht=pe("[Chat] Receive Message",yi()),_i=pe("[Chat] Clear Messages"),yp=pe("[Chat] Connect WebSocket"),xn=pe("[Chat] WebSocket Connected"),vp=pe("[Chat] WebSocket Disconnected"),Mi=pe("[Chat] WebSocket Error",yi()),Si=pe("[Chat] Toggle Chat Visibility");function Zw(){return{ids:[],entities:{}}}function Yw(){function e(t={}){return Object.assign(Zw(),t)}return{getInitialState:e}}function Qw(){function e(t){let n=s=>s.ids,r=s=>s.entities,o=ae(n,r,(s,a)=>s.map(c=>a[c])),i=ae(n,s=>s.length);return t?{selectIds:ae(t,n),selectEntities:ae(t,r),selectAll:ae(t,o),selectTotal:ae(t,i)}:{selectIds:n,selectEntities:r,selectAll:o,selectTotal:i}}return{getSelectors:e}}var S=function(e){return e[e.EntitiesOnly=0]="EntitiesOnly",e[e.Both=1]="Both",e[e.None=2]="None",e}(S||{});function q(e){return function(n,r){let o={ids:[...r.ids],entities:I({},r.entities)},i=e(n,o);return i===S.Both?Object.assign({},r,o):i===S.EntitiesOnly?A(I({},r),{entities:o.entities}):r}}function it(e,t){let n=t(e);return Er()&&n===void 0&&console.warn("@ngrx/entity: The entity passed to the `selectId` implementation returned undefined.","You should probably provide your own `selectId` implementation.","The entity that was passed:",e,"The `selectId` implementation:",t.toString()),n}function Dp(e){function t(v,m){let D=it(v,e);return D in m.entities?S.None:(m.ids.push(D),m.entities[D]=v,S.Both)}function n(v,m){let D=!1;for(let y of v)D=t(y,m)!==S.None||D;return D?S.Both:S.None}function r(v,m){return m.ids=[],m.entities={},n(v,m),S.Both}function o(v,m){let D=it(v,e);return D in m.entities?(m.entities[D]=v,S.EntitiesOnly):(m.ids.push(D),m.entities[D]=v,S.Both)}function i(v,m){let D=v.map(y=>o(y,m));switch(!0){case D.some(y=>y===S.Both):return S.Both;case D.some(y=>y===S.EntitiesOnly):return S.EntitiesOnly;default:return S.None}}function s(v,m){return a([v],m)}function a(v,m){let y=(v instanceof Array?v:m.ids.filter(w=>v(m.entities[w]))).filter(w=>w in m.entities).map(w=>delete m.entities[w]).length>0;return y&&(m.ids=m.ids.filter(w=>w in m.entities)),y?S.Both:S.None}function c(v){return Object.assign({},v,{ids:[],entities:{}})}function u(v,m,D){let y=D.entities[m.id],w=Object.assign({},y,m.changes),C=it(w,e),_=C!==m.id;return _&&(v[m.id]=C,delete D.entities[m.id]),D.entities[C]=w,_}function l(v,m){return d([v],m)}function d(v,m){let D={};return v=v.filter(w=>w.id in m.entities),v.length>0?v.filter(C=>u(D,C,m)).length>0?(m.ids=m.ids.map(C=>D[C]||C),S.Both):S.EntitiesOnly:S.None}function h(v,m){let y=m.ids.reduce((w,C)=>{let _=v(m.entities[C]);return _!==m.entities[C]&&w.push({id:C,changes:_}),w},[]).filter(({id:w})=>w in m.entities);return d(y,m)}function f({map:v,id:m},D){let y=D.entities[m];if(!y)return S.None;let w=v(y);return l({id:m,changes:w},D)}function p(v,m){return g([v],m)}function g(v,m){let D=[],y=[];for(let _ of v){let $=it(_,e);$ in m.entities?y.push({id:$,changes:_}):D.push(_)}let w=d(y,m),C=n(D,m);switch(!0){case(C===S.None&&w===S.None):return S.None;case(C===S.Both||w===S.Both):return S.Both;default:return S.EntitiesOnly}}return{removeAll:c,addOne:q(t),addMany:q(n),setAll:q(r),setOne:q(o),setMany:q(i),updateOne:q(l),updateMany:q(d),upsertOne:q(p),upsertMany:q(g),removeOne:q(s),removeMany:q(a),map:q(h),mapOne:q(f)}}function Kw(e,t){let{removeOne:n,removeMany:r,removeAll:o}=Dp(e);function i(D,y){return s([D],y)}function s(D,y){let w=D.filter(C=>!(it(C,e)in y.entities));return w.length===0?S.None:(m(w,y),S.Both)}function a(D,y){return y.entities={},y.ids=[],s(D,y),S.Both}function c(D,y){let w=it(D,e);return w in y.entities?(y.ids=y.ids.filter(C=>C!==w),m([D],y),S.Both):i(D,y)}function u(D,y){let w=D.map(C=>c(C,y));switch(!0){case w.some(C=>C===S.Both):return S.Both;case w.some(C=>C===S.EntitiesOnly):return S.EntitiesOnly;default:return S.None}}function l(D,y){return h([D],y)}function d(D,y,w){if(!(y.id in w.entities))return!1;let C=w.entities[y.id],_=Object.assign({},C,y.changes),$=it(_,e);return delete w.entities[y.id],D.push(_),$!==y.id}function h(D,y){let w=[],C=D.filter(_=>d(w,_,y)).length>0;if(w.length===0)return S.None;{let _=y.ids,$=[];return y.ids=y.ids.filter((Z,_e)=>Z in y.entities?!0:($.push(_e),!1)),m(w,y),!C&&$.every(Z=>y.ids[Z]===_[Z])?S.EntitiesOnly:S.Both}}function f(D,y){let w=y.ids.reduce((C,_)=>{let $=D(y.entities[_]);return $!==y.entities[_]&&C.push({id:_,changes:$}),C},[]);return h(w,y)}function p({map:D,id:y},w){let C=w.entities[y];if(!C)return S.None;let _=D(C);return l({id:y,changes:_},w)}function g(D,y){return v([D],y)}function v(D,y){let w=[],C=[];for(let Z of D){let _e=it(Z,e);_e in y.entities?C.push({id:_e,changes:Z}):w.push(Z)}let _=h(C,y),$=s(w,y);switch(!0){case($===S.None&&_===S.None):return S.None;case($===S.Both||_===S.Both):return S.Both;default:return S.EntitiesOnly}}function m(D,y){D.sort(t);let w=[],C=0,_=0;for(;C<D.length&&_<y.ids.length;){let $=D[C],Z=it($,e),_e=y.ids[_],$g=y.entities[_e];t($,$g)<=0?(w.push(Z),C++):(w.push(_e),_++)}C<D.length?y.ids=w.concat(D.slice(C).map(e)):y.ids=w.concat(y.ids.slice(_)),D.forEach(($,Z)=>{y.entities[e($)]=$})}return{removeOne:n,removeMany:r,removeAll:o,addOne:q(i),updateOne:q(l),upsertOne:q(g),setAll:q(a),setOne:q(c),setMany:q(u),addMany:q(s),updateMany:q(h),upsertMany:q(v),map:q(f),mapOne:q(p)}}function Ep(e={}){let{selectId:t,sortComparer:n}={selectId:e.selectId??(s=>s.id),sortComparer:e.sortComparer??!1},r=Yw(),o=Qw(),i=n?Kw(t,n):Dp(t);return I(I(I({selectId:t,sortComparer:n},r),o),i)}var pt=Ep({selectId:e=>e.id,sortComparer:(e,t)=>e.timestamp-t.timestamp}),Jw=pt.getInitialState({typingIndicatorId:null,isConnected:!1,isChatVisible:!0,connectionError:null}),bp=tp(Jw,We(Tn,(e,{message:t})=>(console.log("Reducer: sendMessage",t),pt.addOne(t,e))),We(gp,e=>{console.log("Reducer: addTypingIndicator");let t={id:"typing-indicator-"+Date.now(),text:"...",sender:"bot",timestamp:Date.now()};return A(I({},pt.addOne(t,e)),{typingIndicatorId:t.id})}),We(mp,e=>(console.log("Reducer: removeTypingIndicator",e.typingIndicatorId),e.typingIndicatorId?A(I({},pt.removeOne(e.typingIndicatorId,e)),{typingIndicatorId:null}):e)),We(ht,(e,{message:t})=>(console.log("Reducer: receiveMessage",t),pt.addOne(t,e))),We(_i,e=>(console.log("Reducer: clearMessages"),pt.removeAll(e))),We(xn,e=>(console.log("Reducer: webSocketConnected"),A(I({},e),{isConnected:!0,connectionError:null}))),We(vp,e=>(console.log("Reducer: webSocketDisconnected"),A(I({},e),{isConnected:!1}))),We(Mi,(e,{error:t})=>(console.log("Reducer: webSocketError",t),A(I({},e),{connectionError:t}))),We(Si,e=>(console.log("Reducer: toggleChatVisibility",!e.isChatVisible),A(I({},e),{isChatVisible:!e.isChatVisible}))));var eC=Jh("chatbot"),Sr=ae(eC,e=>e.chat),{selectIds:jN,selectEntities:BN,selectAll:wp,selectTotal:UN}=pt.getSelectors(Sr),Cp=ae(Sr,e=>e.isConnected),Ip=ae(Sr,e=>e.isChatVisible),$N=ae(Sr,e=>e.connectionError),tC=ae(Sr,e=>e.typingIndicatorId),HN=ae(tC,e=>e!==null);var Op=(()=>{class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,r){this._renderer=n,this._elementRef=r}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(r){return new(r||e)(H(En),H($e))};static \u0275dir=He({type:e})}return e})(),nC=(()=>{class e extends Op{static \u0275fac=(()=>{let n;return function(o){return(n||(n=gn(e)))(o||e)}})();static \u0275dir=He({type:e,features:[wn]})}return e})(),Fp=new b("");var rC={provide:Fp,useExisting:cr(()=>Oi),multi:!0};function oC(){let e=_n()?_n().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var iC=new b(""),Oi=(()=>{class e extends Op{_compositionMode;_composing=!1;constructor(n,r,o){super(n,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!oC())}writeValue(n){let r=n??"";this.setProperty("value",r)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(r){return new(r||e)(H(En),H($e),H(iC,8))};static \u0275dir=He({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&Bt("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Dc([rC]),wn]})}return e})();var sC=new b(""),aC=new b("");function kp(e){return e!=null}function Pp(e){return yr(e)?Ke(e):e}function Lp(e){let t={};return e.forEach(n=>{t=n!=null?I(I({},t),n):t}),Object.keys(t).length===0?null:t}function Vp(e,t){return t.map(n=>n(e))}function cC(e){return!e.validate}function jp(e){return e.map(t=>cC(t)?t:n=>t.validate(n))}function uC(e){if(!e)return null;let t=e.filter(kp);return t.length==0?null:function(n){return Lp(Vp(n,t))}}function Bp(e){return e!=null?uC(jp(e)):null}function lC(e){if(!e)return null;let t=e.filter(kp);return t.length==0?null:function(n){let r=Vp(n,t).map(Pp);return fs(r).pipe(B(Lp))}}function Up(e){return e!=null?lC(jp(e)):null}function Mp(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function dC(e){return e._rawValidators}function fC(e){return e._rawAsyncValidators}function Hc(e){return e?Array.isArray(e)?e:[e]:[]}function Ai(e,t){return Array.isArray(e)?e.includes(t):e===t}function Sp(e,t){let n=Hc(t);return Hc(e).forEach(o=>{Ai(n,o)||n.push(o)}),n}function Tp(e,t){return Hc(t).filter(n=>!Ai(e,n))}var Ni=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Bp(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=Up(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},zc=class extends Ni{name;get formDirective(){return null}get path(){return null}},Rr=class extends Ni{_parent=null;name=null;valueAccessor=null},Gc=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},hC={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},sR=A(I({},hC),{"[class.ng-submitted]":"isSubmitted"}),$p=(()=>{class e extends Gc{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(H(Rr,2))};static \u0275dir=He({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&vr("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[wn]})}return e})();var Tr="VALID",xi="INVALID",An="PENDING",xr="DISABLED",Rn=class{},Ri=class extends Rn{value;source;constructor(t,n){super(),this.value=t,this.source=n}},Ar=class extends Rn{pristine;source;constructor(t,n){super(),this.pristine=t,this.source=n}},Nr=class extends Rn{touched;source;constructor(t,n){super(),this.touched=t,this.source=n}},Nn=class extends Rn{status;source;constructor(t,n){super(),this.status=t,this.source=n}};function pC(e){return(Fi(e)?e.validators:e)||null}function gC(e){return Array.isArray(e)?Bp(e):e||null}function mC(e,t){return(Fi(t)?t.asyncValidators:e)||null}function yC(e){return Array.isArray(e)?Up(e):e||null}function Fi(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}var Wc=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,n){this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return Oe(this.statusReactive)}set status(t){Oe(()=>this.statusReactive.set(t))}_status=ot(()=>this.statusReactive());statusReactive=ft(void 0);get valid(){return this.status===Tr}get invalid(){return this.status===xi}get pending(){return this.status==An}get disabled(){return this.status===xr}get enabled(){return this.status!==xr}errors;get pristine(){return Oe(this.pristineReactive)}set pristine(t){Oe(()=>this.pristineReactive.set(t))}_pristine=ot(()=>this.pristineReactive());pristineReactive=ft(!0);get dirty(){return!this.pristine}get touched(){return Oe(this.touchedReactive)}set touched(t){Oe(()=>this.touchedReactive.set(t))}_touched=ot(()=>this.touchedReactive());touchedReactive=ft(!1);get untouched(){return!this.touched}_events=new Y;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Sp(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Sp(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Tp(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Tp(t,this._rawAsyncValidators))}hasValidator(t){return Ai(this._rawValidators,t)}hasAsyncValidator(t){return Ai(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(A(I({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new Nr(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new Nr(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(A(I({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new Ar(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new Ar(!0,r))}markAsPending(t={}){this.status=An;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Nn(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(A(I({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=xr,this.errors=null,this._forEachChild(o=>{o.disable(A(I({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Ri(this.value,r)),this._events.next(new Nn(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(A(I({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=Tr,this._forEachChild(r=>{r.enable(A(I({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(A(I({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Tr||this.status===An)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Ri(this.value,n)),this._events.next(new Nn(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(A(I({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?xr:Tr}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=An,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=Pp(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new Nn(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new Ee,this.statusChanges=new Ee}_calculateStatus(){return this._allControlsDisabled()?xr:this.errors?xi:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(An)?An:this._anyControlsHaveStatus(xi)?xi:Tr}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),o&&this._events.next(new Ar(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new Nr(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Fi(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=gC(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=yC(this._rawAsyncValidators)}};var Hp=new b("",{providedIn:"root",factory:()=>qc}),qc="always";function vC(e,t){return[...t.path,e]}function DC(e,t,n=qc){bC(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),wC(e,t),IC(e,t),CC(e,t),EC(e,t)}function xp(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function EC(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function bC(e,t){let n=dC(e);t.validator!==null?e.setValidators(Mp(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=fC(e);t.asyncValidator!==null?e.setAsyncValidators(Mp(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();xp(t._rawValidators,o),xp(t._rawAsyncValidators,o)}function wC(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&zp(e,t)})}function CC(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&zp(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function zp(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function IC(e,t){let n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function _C(e,t){if(!e.hasOwnProperty("model"))return!1;let n=e.model;return n.isFirstChange()?!0:!Object.is(t,n.currentValue)}function MC(e){return Object.getPrototypeOf(e.constructor)===nC}function SC(e,t){if(!t)return null;Array.isArray(t);let n,r,o;return t.forEach(i=>{i.constructor===Oi?n=i:MC(i)?r=i:o=i}),o||r||n||null}function Ap(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Np(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var TC=class extends Wc{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,n,r){super(pC(n),mC(r,n)),this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Fi(n)&&(n.nonNullable||n.initialValueIsDefault)&&(Np(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){Ap(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){Ap(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){Np(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var xC={provide:Rr,useExisting:cr(()=>Zc)},Rp=Promise.resolve(),Zc=(()=>{class e extends Rr{_changeDetectorRef;callSetDisabledState;control=new TC;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new Ee;constructor(n,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=SC(this,i)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),_C(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){DC(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){Rp.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let r=n.isDisabled.currentValue,o=r!==0&&wc(r);Rp.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?vC(n,this._parent):[n]}static \u0275fac=function(r){return new(r||e)(H(zc,9),H(sC,10),H(aC,10),H(Fp,10),H(Cn,8),H(Hp,8))};static \u0275dir=He({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Dc([xC]),wn,La]})}return e})();var AC=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=X({type:e});static \u0275inj=J({})}return e})();var ki=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Hp,useValue:n.callSetDisabledState??qc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=X({type:e});static \u0275inj=J({imports:[AC]})}return e})();var zt={production:!1,apiUrl:"http://localhost:8080",websocketUrl:"ws://localhost:8200/internal/chat",useDevFallback:!1};var On=class e{constructor(t){this.store=t;this.isConnected$.pipe(ne(n=>!n&&!this.closeIntentionally),bt(this.destroy$)).subscribe(()=>{this.reconnectAttempts<this.maxReconnectAttempts&&this.scheduleReconnect()})}socket=null;messagesSubject=new Y;reconnectAttempts=0;maxReconnectAttempts=5;reconnectTimeout=null;closeIntentionally=!1;destroy$=new Y;connectionStatus$=new Te(!1);recentlySentMessages=new Set;processingMessage=!1;messages$=this.messagesSubject.asObservable();isConnected$=this.connectionStatus$.asObservable();connect(){return this.closeIntentionally=!1,new k(t=>{try{this.socket&&this.socket.readyState!==WebSocket.CLOSED&&(this.closeIntentionally=!0,this.socket.close()),this.socket=new WebSocket(zt.websocketUrl),this.socket.onopen=()=>{console.log("WebSocket connection established"),this.reconnectAttempts=0,this.connectionStatus$.next(!0),this.store.dispatch(xn()),t.next(!0),t.complete()},this.socket.onmessage=n=>{if(this.processingMessage)return;if(this.processingMessage=!0,this.recentlySentMessages.has(n.data)){this.processingMessage=!1;return}this.messagesSubject.next(n.data);let r={id:"bot-"+Date.now(),text:n.data,sender:"bot",timestamp:Date.now()};this.store.dispatch(ht({message:r})),this.processingMessage=!1},this.socket.onclose=n=>{console.log("WebSocket connection closed:",n),this.connectionStatus$.next(!1),this.closeIntentionally||this.reconnectAttempts>=this.maxReconnectAttempts&&(console.error("Maximum reconnect attempts reached"),this.messagesSubject.next("Connection to server lost. Please refresh the page to try again."))},this.socket.onerror=n=>{console.error("WebSocket error:",n),t.error(n)}}catch(n){console.error("Error initializing WebSocket:",n),t.error(n)}})}scheduleReconnect(){this.reconnectTimeout&&clearTimeout(this.reconnectTimeout);let t=Math.min(3e4,Math.pow(2,this.reconnectAttempts)*1e3);console.log(`Attempting to reconnect in ${t/1e3} seconds...`),this.reconnectTimeout=setTimeout(()=>{this.reconnectAttempts++,this.connect().subscribe({error:n=>console.error("Reconnect failed:",n)})},t)}cleanupSentMessages(){if(this.recentlySentMessages.size>20){let t=Array.from(this.recentlySentMessages);this.recentlySentMessages=new Set(t.slice(-10))}}sendMessage(t){if(this.processingMessage){console.log("Ignoring send request while processing another message");return}if(console.log("WebSocketService: Sending message:",t),this.socket&&this.socket.readyState===WebSocket.OPEN){this.recentlySentMessages.add(t),this.cleanupSentMessages();let n={id:"user-"+Date.now(),text:t,sender:"user",timestamp:Date.now()};console.log("Dispatching sent message to store:",n),this.store.dispatch(Tn({message:n})),this.socket.send(t),setTimeout(()=>{this.recentlySentMessages.delete(t)},5e3)}else{console.warn("WebSocket is not connected");let n={id:"error-"+Date.now(),text:"Cannot send message. Connection to server lost. Trying to reconnect...",sender:"bot",timestamp:Date.now()};this.store.dispatch(ht({message:n})),this.connect().subscribe({error:r=>console.error("Connection failed:",r)})}}disconnect(){this.closeIntentionally=!0,this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket&&(this.socket.close(),this.socket=null),this.destroy$.next(),this.destroy$.complete(),this.recentlySentMessages.clear(),this.processingMessage=!1}clearMessages(){this.store.dispatch(_i())}static \u0275fac=function(n){return new(n||e)(E(ge))};static \u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})};var OC=["messagesContainer"];function FC(e,t){e&1&&(le(0,"span",13),Ut(1,"\u25CF"),se())}function kC(e,t){e&1&&(le(0,"span",14),Ut(1,"\u25CF"),se())}function PC(e,t){e&1&&(le(0,"div",15),Ut(1," No messages yet. Start a conversation! "),se())}function LC(e,t){if(e&1&&(le(0,"div",16),Ut(1),se()),e&2){let n=t.$implicit;jt("ngClass",n.sender),Vt(),mc(" ",n.text," ")}}var Fn=class e{constructor(t,n,r){this.store=t;this.webSocketService=n;this.cdr=r;console.log("ChatbotComponent constructor"),this.store.select(o=>o).subscribe(o=>{console.log("Current full store state:",o)})}userMessage="";messages=[];isChatVisible=!0;isConnected=!1;messagesSubscription;visibilitySubscription;connectionSubscription;messagesContainer;ngOnInit(){console.log("ChatbotComponent: initializing component"),this.webSocketService.clearMessages(),this.messagesSubscription=this.store.select(wp).pipe(tn(t=>console.log("ChatbotComponent: Received messages update, count:",t?.length||0))).subscribe({next:t=>{t?(console.log("Messages from store:",t),this.messages=[...t],console.log("Component messages array after update:",this.messages)):console.warn("Messages selector returned undefined or null")},error:t=>console.error("Error in messages subscription:",t)}),this.visibilitySubscription=this.store.select(Ip).subscribe({next:t=>{console.log("ChatbotComponent: Chat visibility update:",t),t!==void 0&&(this.isChatVisible=t,this.cdr.detectChanges())},error:t=>console.error("Error in visibility subscription:",t)}),this.connectionSubscription=this.webSocketService.isConnected$.subscribe({next:t=>{console.log("ChatbotComponent: Connection status update:",t),this.isConnected=t,this.cdr.detectChanges()},error:t=>console.error("Error in connection subscription:",t)})}ngAfterViewChecked(){this.scrollToBottom()}ngOnDestroy(){console.log("ChatbotComponent: destroying component, cleaning up subscriptions"),this.messagesSubscription&&this.messagesSubscription.unsubscribe(),this.visibilitySubscription&&this.visibilitySubscription.unsubscribe(),this.connectionSubscription&&this.connectionSubscription.unsubscribe()}scrollToBottom(){try{this.messagesContainer&&(this.messagesContainer.nativeElement.scrollTop=this.messagesContainer.nativeElement.scrollHeight)}catch(t){console.error("Error scrolling to bottom:",t)}}sendMessage(){if(!this.userMessage.trim())return;let t=this.userMessage.trim();console.log("ChatbotComponent: Sending message:",t),this.webSocketService.sendMessage(t),this.userMessage=""}toggleChat(){console.log("ChatbotComponent: Toggling chat visibility"),this.store.dispatch(Si())}testStateUpdate(){let t={id:"test-"+Date.now(),text:"This is a test message to verify state updates",sender:"bot",timestamp:Date.now()};console.log("Dispatching test message to store:",t),this.store.dispatch(ht({message:t}))}addTestMessage(){let t={id:"auto-test-"+Date.now(),text:"This is an automatic test message",sender:"bot",timestamp:Date.now()};console.log("Adding automatic test message:",t),this.store.dispatch(ht({message:t}))}trackByFn(t,n){return n.id}static \u0275fac=function(n){return new(n||e)(H(ge),H(On),H(Cn))};static \u0275cmp=ui({type:e,selectors:[["app-chatbot"]],viewQuery:function(n,r){if(n&1&&lh(OC,5),n&2){let o;dh(o=fh())&&(r.messagesContainer=o.first)}},decls:16,vars:8,consts:[["messagesContainer",""],[1,"chatbot-container"],[1,"chatbot-header"],["class","connection-status connected",4,"ngIf"],["class","connection-status disconnected",4,"ngIf"],[1,"chatbot-messages"],["class","no-messages",4,"ngIf"],["class","message",3,"ngClass",4,"ngFor","ngForOf","ngForTrackBy"],[1,"chatbot-input"],["type","text","placeholder","Type your message...",3,"ngModelChange","keyup.enter","ngModel"],[3,"click"],[1,"floating-icon",3,"click"],["src","stanley.png","alt","Chat Icon","width","64","height","64"],[1,"connection-status","connected"],[1,"connection-status","disconnected"],[1,"no-messages"],[1,"message",3,"ngClass"]],template:function(n,r){if(n&1){let o=ch();le(0,"div",1)(1,"div",2)(2,"span"),Ut(3,"Ask Stanley"),se(),di(4,FC,2,0,"span",3)(5,kC,2,0,"span",4),se(),le(6,"div",5,0),di(8,PC,2,0,"div",6)(9,LC,2,2,"div",7),se(),le(10,"div",8)(11,"input",9),vc("ngModelChange",function(s){return hr(o),hh(r.userMessage,s)||(r.userMessage=s),pr(s)}),Bt("keyup.enter",function(){return hr(o),pr(r.sendMessage())}),se(),le(12,"button",10),Bt("click",function(){return hr(o),pr(r.sendMessage())}),Ut(13,"Send"),se()()(),le(14,"div",11),Bt("click",function(){return hr(o),pr(r.toggleChat())}),Dr(15,"img",12),se()}n&2&&(vr("visible",r.isChatVisible),Vt(4),jt("ngIf",r.isConnected),Vt(),jt("ngIf",!r.isConnected),Vt(3),jt("ngIf",r.messages.length===0),Vt(),jt("ngForOf",r.messages)("ngForTrackBy",r.trackByFn),Vt(2),yc("ngModel",r.userMessage))},dependencies:[Ch,ki,Oi,$p,Zc,_h,Ih],styles:[".chatbot-container[_ngcontent-%COMP%]{width:400px;height:600px;background:#fff;box-shadow:0 4px 10px #0003;border-radius:10px;display:none;flex-direction:column;overflow:hidden;position:fixed;bottom:100px;right:50px;z-index:1000}.chatbot-container.visible[_ngcontent-%COMP%]{display:flex!important}.chatbot-header[_ngcontent-%COMP%]{background-color:#f41c5e;color:#fff;text-align:center;padding:15px;font-size:18px;font-weight:700;display:flex;justify-content:center;align-items:center;position:relative}.connection-status[_ngcontent-%COMP%]{position:absolute;top:15px;right:15px;width:10px;height:10px;border-radius:50%}.connection-status.connected[_ngcontent-%COMP%]{color:#2ecc71}.connection-status.disconnected[_ngcontent-%COMP%]{color:#e74c3c}.chatbot-messages[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:15px;background-color:#f8f9fa;display:flex;flex-direction:column;gap:10px}.message[_ngcontent-%COMP%]{max-width:70%;padding:10px 15px;border-radius:15px;font-size:14px;line-height:1.4;word-break:break-word;position:relative;display:inline-block}.message.user[_ngcontent-%COMP%]{background-color:#ffd3e1;align-self:flex-end;border-bottom-right-radius:4px}.message.bot[_ngcontent-%COMP%]{background-color:#f8b8cc;align-self:flex-start;border-bottom-left-radius:4px}.chatbot-input[_ngcontent-%COMP%]{display:flex;padding:10px;border-top:1px solid #ddd;background-color:#fff}.chatbot-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;padding:10px;border:1px solid #ddd;border-radius:5px;outline:none;font-family:Roboto,sans-serif}.chatbot-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:10px;padding:10px 20px;background-color:#f41c5e;color:#fff;border:none;border-radius:5px;cursor:pointer;font-size:14px;font-family:Roboto,sans-serif}.chatbot-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#c0174c}.floating-icon[_ngcontent-%COMP%]{width:60px;height:60px;background-color:#f41c5e;color:#fff;font-size:30px;font-weight:700;display:flex;justify-content:center;align-items:center;border-radius:50%;position:fixed;bottom:30px;right:30px;cursor:pointer;transition:transform .2s ease-in-out,background-color .3s}.floating-icon[_ngcontent-%COMP%]:hover{transform:scale(1.1);background-color:#c0174c}.no-messages[_ngcontent-%COMP%]{text-align:center;color:#888;margin-top:20px;font-style:italic}*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box}body[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;background-color:#f4f4f9;height:100vh}"]})};var Or=class e{connectWebSocket$;sendMessage$;constructor(t,n,r){console.log("ChatEffects constructor - is actions$ defined?",!!t),console.log("ChatEffects constructor - is webSocketService defined?",!!r),this.connectWebSocket$=Bc(()=>t.pipe(Uc(yp),wt(n.select(Cp)),ct(([o,i])=>(console.log("Effect: ConnectWebSocket triggered",i),i?me:r.connect().pipe(B(()=>(console.log("Effect: WebSocket connected successfully"),xn())),at(s=>(console.error("Effect: WebSocket connection error",s),xe(Mi({error:s}))))))))),this.sendMessage$=Bc(()=>t.pipe(Uc(Tn),tn(({message:o})=>{console.log("Effect: SendMessage action received",o)}),ye(()=>me)),{dispatch:!1})}static \u0275fac=function(n){return new(n||e)(E(Ci),E(ge),E(On))};static \u0275prov=x({token:e,factory:e.\u0275fac})};var Wp={chat:bp},CR=zt.production?[]:[jC];function jC(e){return function(t,n){console.log("state before",t),console.log("action",n);let r=e(t,n);return console.log("state after",r),r}}var Pi=class e{static \u0275fac=function(n){return new(n||e)};static \u0275mod=X({type:e});static \u0275inj=J({providers:[Or,Ci],imports:[Mc,Ei.forFeature("chatbot",Wp),Ii.forFeature([Or]),Fn,ki]})};var Li=class e{title="angular-chatbot";static \u0275fac=function(n){return new(n||e)};static \u0275cmp=ui({type:e,selectors:[["app-root"]],decls:2,vars:0,consts:[[1,"app-container"]],template:function(n,r){n&1&&(le(0,"div",0),Dr(1,"app-chatbot"),se())},dependencies:[Pi,Fn],styles:[".app-container[_ngcontent-%COMP%]{height:100vh;display:flex;justify-content:center;align-items:center;background-color:#f4f4f9}"]})};var kr=class{},ji=class{},Gt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Qc=class{encodeKey(t){return qp(t)}encodeValue(t){return qp(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function BC(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var UC=/%(\d[a-f0-9])/gi,$C={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function qp(e){return encodeURIComponent(e).replace(UC,(t,n)=>$C[n]??t)}function Vi(e){return`${e}`}var gt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Qc,t.fromString){if(t.fromObject)throw new T(2805,!1);this.map=BC(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Vi):[Vi(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Vi(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Vi(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Kc=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function HC(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Zp(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Yp(e){return typeof Blob<"u"&&e instanceof Blob}function Qp(e){return typeof FormData<"u"&&e instanceof FormData}function zC(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Kp="Content-Type",Jp="Accept",tg="X-Request-URL",ng="text/plain",rg="application/json",GC=`${rg}, ${ng}, */*`,Fr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(HC(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Gt,this.context??=new Kc,!this.params)this.params=new gt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Zp(this.body)||Yp(this.body)||Qp(this.body)||zC(this.body)?this.body:this.body instanceof gt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Qp(this.body)?null:Yp(this.body)?this.body.type||null:Zp(this.body)?null:typeof this.body=="string"?ng:this.body instanceof gt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?rg:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,f)=>h.set(f,t.setHeaders[f]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,f)=>h.set(f,t.setParams[f]),l)),new e(n,r,s,{params:l,headers:u,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},kn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(kn||{}),Pr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Gt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Jc=class e extends Pr{constructor(t={}){super(t)}type=kn.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Bi=class e extends Pr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=kn.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Ui=class extends Pr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},WC=200,qC=204;function Yc(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var ZC=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Fr)i=n;else{let c;o.headers instanceof Gt?c=o.headers:c=new Gt(o.headers);let u;o.params&&(o.params instanceof gt?u=o.params:u=new gt({fromObject:o.params})),i=new Fr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=xe(i).pipe(en(c=>this.handler.handle(c)));if(n instanceof Fr||o.observe==="events")return s;let a=s.pipe(ne(c=>c instanceof Bi));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(B(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new T(2806,!1);return c.body}));case"blob":return a.pipe(B(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new T(2807,!1);return c.body}));case"text":return a.pipe(B(c=>{if(c.body!==null&&typeof c.body!="string")throw new T(2808,!1);return c.body}));case"json":default:return a.pipe(B(c=>c.body))}case"response":return a;default:throw new T(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new gt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Yc(o,r))}post(n,r,o={}){return this.request("POST",n,Yc(o,r))}put(n,r,o={}){return this.request("PUT",n,Yc(o,r))}static \u0275fac=function(r){return new(r||e)(E(kr))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})();var YC=new b("");function QC(e,t){return t(e)}function KC(e,t,n){return(r,o)=>ni(n,()=>t(r,i=>e(i,o)))}var og=new b(""),JC=new b(""),XC=new b("",{providedIn:"root",factory:()=>!0});var Xp=(()=>{class e extends kr{backend;injector;chain=null;pendingTasks=M(yn);contributeToStability=M(XC);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(og),...this.injector.get(JC,[])]));this.chain=r.reduceRight((o,i)=>KC(o,i,this.injector),QC)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(vo(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(E(ji),E(Pe))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})();var eI=/^\)\]\}',?\n/,tI=RegExp(`^${tg}:`,"m");function nI(e){return"responseURL"in e&&e.responseURL?e.responseURL:tI.test(e.getAllResponseHeaders())?e.getResponseHeader(tg):null}var eg=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new T(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?Ke(r.\u0275loadImpl()):xe(null)).pipe(ct(()=>new k(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((g,v)=>s.setRequestHeader(g,v.join(","))),n.headers.has(Jp)||s.setRequestHeader(Jp,GC),!n.headers.has(Kp)){let g=n.detectContentTypeHeader();g!==null&&s.setRequestHeader(Kp,g)}if(n.responseType){let g=n.responseType.toLowerCase();s.responseType=g!=="json"?g:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let g=s.statusText||"OK",v=new Gt(s.getAllResponseHeaders()),m=nI(s)||n.url;return c=new Jc({headers:v,status:s.status,statusText:g,url:m}),c},l=()=>{let{headers:g,status:v,statusText:m,url:D}=u(),y=null;v!==qC&&(y=typeof s.response>"u"?s.responseText:s.response),v===0&&(v=y?WC:0);let w=v>=200&&v<300;if(n.responseType==="json"&&typeof y=="string"){let C=y;y=y.replace(eI,"");try{y=y!==""?JSON.parse(y):null}catch(_){y=C,w&&(w=!1,y={error:_,text:y})}}w?(i.next(new Bi({body:y,headers:g,status:v,statusText:m,url:D||void 0})),i.complete()):i.error(new Ui({error:y,headers:g,status:v,statusText:m,url:D||void 0}))},d=g=>{let{url:v}=u(),m=new Ui({error:g,status:s.status||0,statusText:s.statusText||"Unknown Error",url:v||void 0});i.error(m)},h=!1,f=g=>{h||(i.next(u()),h=!0);let v={type:kn.DownloadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),n.responseType==="text"&&s.responseText&&(v.partialText=s.responseText),i.next(v)},p=g=>{let v={type:kn.UploadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),i.next(v)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:kn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(E(In))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),ig=new b(""),rI="XSRF-TOKEN",oI=new b("",{providedIn:"root",factory:()=>rI}),iI="X-XSRF-TOKEN",sI=new b("",{providedIn:"root",factory:()=>iI}),$i=class{},aI=(()=>{class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=pi(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(E(ze),E(Lt),E(oI))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})();function cI(e,t){let n=e.url.toLowerCase();if(!M(ig)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=M($i).getToken(),o=M(sI);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}function sg(...e){let t=[ZC,eg,Xp,{provide:kr,useExisting:Xp},{provide:ji,useFactory:()=>M(YC,{optional:!0})??M(eg)},{provide:og,useValue:cI,multi:!0},{provide:ig,useValue:!0},{provide:$i,useClass:aI}];for(let n of e)t.push(...n.\u0275providers);return Pt(t)}var eu=class extends hi{supportsDOMEvents=!0},tu=class e extends eu{static makeCurrent(){wh(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=uI();return n==null?null:lI(n)}resetBaseElement(){Lr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return pi(document.cookie,t)}},Lr=null;function uI(){return Lr=Lr||document.querySelector("base"),Lr?Lr.getAttribute("href"):null}function lI(e){return new URL(e,document.baseURI).pathname}var dI=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),nu=new b(""),fg=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new T(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(E(nu),E(Q))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),zi=class{_doc;constructor(t){this._doc=t}manager},Hi="ng-app-id";function ag(e){for(let t of e)t.remove()}function cg(e,t){let n=t.createElement("style");return n.textContent=e,n}function fI(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Hi}="${t}"],link[${Hi}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Hi),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function ru(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var hg=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Sc(i),fI(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,cg);r?.forEach(o=>this.addUsage(o,this.external,ru))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(ag(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])ag(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,cg(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,ru(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Hi,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(E(ze),E(Ya),E(Ka,8),E(Lt))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),Xc={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},iu=/%COMP%/g;var pg="%COMP%",hI=`_nghost-${pg}`,pI=`_ngcontent-${pg}`,gI=!0,mI=new b("",{providedIn:"root",factory:()=>gI});function yI(e){return pI.replace(iu,e)}function vI(e){return hI.replace(iu,e)}function gg(e,t){return t.map(n=>n.replace(iu,e))}var ug=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Sc(a),this.defaultRenderer=new Vr(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ue.ShadowDom&&(r=A(I({},r),{encapsulation:Ue.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Gi?o.applyToHost(n):o instanceof jr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Ue.Emulated:i=new Gi(c,u,r,this.appId,l,s,a,d,h);break;case Ue.ShadowDom:return new ou(c,u,n,r,s,a,this.nonce,d,h);default:i=new jr(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(E(fg),E(hg),E(Ya),E(mI),E(ze),E(Lt),E(Q),E(Ka),E(si,8))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),Vr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Xc[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(lg(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(lg(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new T(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Xc[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Xc[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(et.DashCase|et.Important)?t.style.setProperty(n,r,o&et.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&et.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=_n().getGlobalEventTarget(this.doc,t),!t))throw new T(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function lg(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var ou=class extends Vr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=gg(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=ru(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},jr=class extends Vr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?gg(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Gi=class extends jr{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=yI(l),this.hostAttr=vI(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},DI=(()=>{class e extends zi{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(E(ze))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),dg=["alt","control","meta","shift"],EI={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},bI={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},wI=(()=>{class e extends zi{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>_n().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),dg.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=EI[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),dg.forEach(s=>{if(s!==o){let a=bI[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(E(ze))};static \u0275prov=x({token:e,factory:e.\u0275fac})}return e})();function mg(e,t){return gh(I({rootComponent:e},CI(t)))}function CI(e){return{appProviders:[...TI,...e?.providers??[]],platformProviders:SI}}function II(){tu.makeCurrent()}function _I(){return new Ce}function MI(){return hf(document),document}var SI=[{provide:Lt,useValue:Mh},{provide:Qa,useValue:II,multi:!0},{provide:ze,useFactory:MI,deps:[]}];var TI=[{provide:ti,useValue:"root"},{provide:Ce,useFactory:_I,deps:[]},{provide:nu,useClass:DI,multi:!0,deps:[ze]},{provide:nu,useClass:wI,multi:!0,deps:[ze]},ug,hg,fg,{provide:dn,useExisting:ug},{provide:In,useClass:dI,deps:[]},[]];var Ur="PERFORM_ACTION",xI="REFRESH",wg="RESET",Cg="ROLLBACK",Ig="COMMIT",_g="SWEEP",Mg="TOGGLE_ACTION",AI="SET_ACTIONS_ACTIVE",Sg="JUMP_TO_STATE",Tg="JUMP_TO_ACTION",vu="IMPORT_STATE",xg="LOCK_CHANGES",Ag="PAUSE_RECORDING",Pn=class{constructor(t,n){if(this.action=t,this.timestamp=n,this.type=Ur,typeof t.type>"u")throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?')}},su=class{constructor(){this.type=xI}},au=class{constructor(t){this.timestamp=t,this.type=wg}},cu=class{constructor(t){this.timestamp=t,this.type=Cg}},uu=class{constructor(t){this.timestamp=t,this.type=Ig}},lu=class{constructor(){this.type=_g}},du=class{constructor(t){this.id=t,this.type=Mg}};var fu=class{constructor(t){this.index=t,this.type=Sg}},hu=class{constructor(t){this.actionId=t,this.type=Tg}},pu=class{constructor(t){this.nextLiftedState=t,this.type=vu}},gu=class{constructor(t){this.status=t,this.type=xg}},mu=class{constructor(t){this.status=t,this.type=Ag}};var Yi=new b("@ngrx/store-devtools Options"),yg=new b("@ngrx/store-devtools Initial Config");function Ng(){return null}var NI="NgRx Store DevTools";function RI(e){let t={maxAge:!1,monitor:Ng,actionSanitizer:void 0,stateSanitizer:void 0,name:NI,serialize:!1,logOnly:!1,autoPause:!1,trace:!1,traceLimit:75,features:{pause:!0,lock:!0,persist:!0,export:!0,import:"custom",jump:!0,skip:!0,reorder:!0,dispatch:!0,test:!0},connectInZone:!1},n=typeof e=="function"?e():e,r=n.logOnly?{pause:!0,export:!0,test:!0}:!1,o=n.features||r||t.features;o.import===!0&&(o.import="custom");let i=Object.assign({},t,{features:o},n);if(i.maxAge&&i.maxAge<2)throw new Error(`Devtools 'maxAge' cannot be less than 2, got ${i.maxAge}`);return i}function vg(e,t){return e.filter(n=>t.indexOf(n)<0)}function Rg(e){let{computedStates:t,currentStateIndex:n}=e;if(n>=t.length){let{state:o}=t[t.length-1];return o}let{state:r}=t[n];return r}function Br(e){return new Pn(e,+Date.now())}function OI(e,t){return Object.keys(t).reduce((n,r)=>{let o=Number(r);return n[o]=Og(e,t[o],o),n},{})}function Og(e,t,n){return A(I({},t),{action:e(t.action,n)})}function FI(e,t){return t.map((n,r)=>({state:Fg(e,n.state,r),error:n.error}))}function Fg(e,t,n){return e(t,n)}function kg(e){return e.predicate||e.actionsSafelist||e.actionsBlocklist}function kI(e,t,n,r){let o=[],i={},s=[];return e.stagedActionIds.forEach((a,c)=>{let u=e.actionsById[a];u&&(c&&Du(e.computedStates[c],u,t,n,r)||(i[a]=u,o.push(a),s.push(e.computedStates[c])))}),A(I({},e),{stagedActionIds:o,actionsById:i,computedStates:s})}function Du(e,t,n,r,o){let i=n&&!n(e,t.action),s=r&&!t.action.type.match(r.map(c=>Dg(c)).join("|")),a=o&&t.action.type.match(o.map(c=>Dg(c)).join("|"));return i||s||a}function Dg(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Pg(e){return{ngZone:e?M(Q):null,connectInZone:e}}var Qi=(()=>{class e extends Ge{static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=gn(e)))(o||e)}})()}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Wi={START:"START",DISPATCH:"DISPATCH",STOP:"STOP",ACTION:"ACTION"},yu=new b("@ngrx/store-devtools Redux Devtools Extension"),Lg=(()=>{class e{constructor(n,r,o){this.config=r,this.dispatcher=o,this.zoneConfig=Pg(this.config.connectInZone),this.devtoolsExtension=n,this.createActionStreams()}notify(n,r){if(this.devtoolsExtension)if(n.type===Ur){if(r.isLocked||r.isPaused)return;let o=Rg(r);if(kg(this.config)&&Du(o,n,this.config.predicate,this.config.actionsSafelist,this.config.actionsBlocklist))return;let i=this.config.stateSanitizer?Fg(this.config.stateSanitizer,o,r.currentStateIndex):o,s=this.config.actionSanitizer?Og(this.config.actionSanitizer,n,r.nextActionId):n;this.sendToReduxDevtools(()=>this.extensionConnection.send(s,i))}else{let o=A(I({},r),{stagedActionIds:r.stagedActionIds,actionsById:this.config.actionSanitizer?OI(this.config.actionSanitizer,r.actionsById):r.actionsById,computedStates:this.config.stateSanitizer?FI(this.config.stateSanitizer,r.computedStates):r.computedStates});this.sendToReduxDevtools(()=>this.devtoolsExtension.send(null,o,this.getExtensionConfig(this.config)))}}createChangesObservable(){return this.devtoolsExtension?new k(n=>{let r=this.zoneConfig.connectInZone?this.zoneConfig.ngZone.runOutsideAngular(()=>this.devtoolsExtension.connect(this.getExtensionConfig(this.config))):this.devtoolsExtension.connect(this.getExtensionConfig(this.config));return this.extensionConnection=r,r.init(),r.subscribe(o=>n.next(o)),r.unsubscribe}):me}createActionStreams(){let n=this.createChangesObservable().pipe(Es()),r=n.pipe(ne(u=>u.type===Wi.START)),o=n.pipe(ne(u=>u.type===Wi.STOP)),i=n.pipe(ne(u=>u.type===Wi.DISPATCH),B(u=>this.unwrapAction(u.payload)),en(u=>u.type===vu?this.dispatcher.pipe(ne(l=>l.type===vi),ds(1e3),hs(1e3),B(()=>u),at(()=>xe(u)),Wn(1)):xe(u))),a=n.pipe(ne(u=>u.type===Wi.ACTION),B(u=>this.unwrapAction(u.payload))).pipe(bt(o)),c=i.pipe(bt(o));this.start$=r.pipe(bt(o)),this.actions$=this.start$.pipe(ct(()=>a)),this.liftedActions$=this.start$.pipe(ct(()=>c))}unwrapAction(n){return typeof n=="string"?(0,eval)(`(${n})`):n}getExtensionConfig(n){let r={name:n.name,features:n.features,serialize:n.serialize,autoPause:n.autoPause??!1,trace:n.trace??!1,traceLimit:n.traceLimit??75};return n.maxAge!==!1&&(r.maxAge=n.maxAge),r}sendToReduxDevtools(n){try{n()}catch(r){console.warn("@ngrx/store-devtools: something went wrong inside the redux devtools",r)}}static{this.\u0275fac=function(r){return new(r||e)(E(yu),E(Yi),E(Qi))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})(),Zi={type:Cr},PI="@ngrx/store-devtools/recompute",LI={type:PI};function Vg(e,t,n,r,o){if(r)return{state:n,error:"Interrupted by an error up the chain"};let i=n,s;try{i=e(n,t)}catch(a){s=a.toString(),o.handleError(a)}return{state:i,error:s}}function qi(e,t,n,r,o,i,s,a,c){if(t>=e.length&&e.length===i.length)return e;let u=e.slice(0,t),l=i.length-(c?1:0);for(let d=t;d<l;d++){let h=i[d],f=o[h].action,p=u[d-1],g=p?p.state:r,v=p?p.error:void 0,D=s.indexOf(h)>-1?p:Vg(n,f,g,v,a);u.push(D)}return c&&u.push(e[e.length-1]),u}function VI(e,t){return{monitorState:t(void 0,{}),nextActionId:1,actionsById:{0:Br(Zi)},stagedActionIds:[0],skippedActionIds:[],committedState:e,currentStateIndex:0,computedStates:[],isLocked:!1,isPaused:!1}}function jI(e,t,n,r,o={}){return i=>(s,a)=>{let{monitorState:c,actionsById:u,nextActionId:l,stagedActionIds:d,skippedActionIds:h,committedState:f,currentStateIndex:p,computedStates:g,isLocked:v,isPaused:m}=s||t;s||(u=Object.create(u));function D(C){let _=C,$=d.slice(1,_+1);for(let Z=0;Z<$.length;Z++)if(g[Z+1].error){_=Z,$=d.slice(1,_+1);break}else delete u[$[Z]];h=h.filter(Z=>$.indexOf(Z)===-1),d=[0,...d.slice(_+1)],f=g[_].state,g=g.slice(_),p=p>_?p-_:0}function y(){u={0:Br(Zi)},l=1,d=[0],h=[],f=g[p].state,p=0,g=[]}let w=0;switch(a.type){case xg:{v=a.status,w=1/0;break}case Ag:{m=a.status,m?(d=[...d,l],u[l]=new Pn({type:"@ngrx/devtools/pause"},+Date.now()),l++,w=d.length-1,g=g.concat(g[g.length-1]),p===d.length-2&&p++,w=1/0):y();break}case wg:{u={0:Br(Zi)},l=1,d=[0],h=[],f=e,p=0,g=[];break}case Ig:{y();break}case Cg:{u={0:Br(Zi)},l=1,d=[0],h=[],p=0,g=[];break}case Mg:{let{id:C}=a;h.indexOf(C)===-1?h=[C,...h]:h=h.filter($=>$!==C),w=d.indexOf(C);break}case AI:{let{start:C,end:_,active:$}=a,Z=[];for(let _e=C;_e<_;_e++)Z.push(_e);$?h=vg(h,Z):h=[...h,...Z],w=d.indexOf(C);break}case Sg:{p=a.index,w=1/0;break}case Tg:{let C=d.indexOf(a.actionId);C!==-1&&(p=C),w=1/0;break}case _g:{d=vg(d,h),h=[],p=Math.min(p,d.length-1);break}case Ur:{if(v)return s||t;if(m||s&&Du(s.computedStates[p],a,o.predicate,o.actionsSafelist,o.actionsBlocklist)){let _=g[g.length-1];g=[...g.slice(0,-1),Vg(i,a.action,_.state,_.error,n)],w=1/0;break}o.maxAge&&d.length===o.maxAge&&D(1),p===d.length-1&&p++;let C=l++;u[C]=a,d=[...d,C],w=d.length-1;break}case vu:{({monitorState:c,actionsById:u,nextActionId:l,stagedActionIds:d,skippedActionIds:h,committedState:f,currentStateIndex:p,computedStates:g,isLocked:v,isPaused:m}=a.nextLiftedState);break}case Cr:{w=0,o.maxAge&&d.length>o.maxAge&&(g=qi(g,w,i,f,u,d,h,n,m),D(d.length-o.maxAge),w=1/0);break}case vi:{if(g.filter(_=>_.error).length>0)w=0,o.maxAge&&d.length>o.maxAge&&(g=qi(g,w,i,f,u,d,h,n,m),D(d.length-o.maxAge),w=1/0);else{if(!m&&!v){p===d.length-1&&p++;let _=l++;u[_]=new Pn(a,+Date.now()),d=[...d,_],w=d.length-1,g=qi(g,w,i,f,u,d,h,n,m)}g=g.map(_=>A(I({},_),{state:i(_.state,LI)})),p=d.length-1,o.maxAge&&d.length>o.maxAge&&D(d.length-o.maxAge),w=1/0}break}default:{w=1/0;break}}return g=qi(g,w,i,f,u,d,h,n,m),c=r(c,a),{monitorState:c,actionsById:u,nextActionId:l,stagedActionIds:d,skippedActionIds:h,committedState:f,currentStateIndex:p,computedStates:g,isLocked:v,isPaused:m}}}var Eg=(()=>{class e{constructor(n,r,o,i,s,a,c,u){let l=VI(c,u.monitor),d=jI(c,l,a,u.monitor,u),h=Et(Et(r.asObservable().pipe(bs(1)),i.actions$).pipe(B(Br)),n,i.liftedActions$).pipe(Qe(Gn)),f=o.pipe(B(d)),p=Pg(u.connectInZone),g=new Hn(1);this.liftedStateSubscription=h.pipe(wt(f),bg(p),qn(({state:D},[y,w])=>{let C=w(D,y);return y.type!==Ur&&kg(u)&&(C=kI(C,u.predicate,u.actionsSafelist,u.actionsBlocklist)),i.notify(y,C),{state:C,action:y}},{state:l,action:null})).subscribe(({state:D,action:y})=>{if(g.next(D),y.type===Ur){let w=y.action;s.next(w)}}),this.extensionStartSubscription=i.start$.pipe(bg(p)).subscribe(()=>{this.refresh()});let v=g.asObservable(),m=v.pipe(B(Rg));Object.defineProperty(m,"state",{value:br(m,{manualCleanup:!0,requireSync:!0})}),this.dispatcher=n,this.liftedState=v,this.state=m}ngOnDestroy(){this.liftedStateSubscription.unsubscribe(),this.extensionStartSubscription.unsubscribe()}dispatch(n){this.dispatcher.next(n)}next(n){this.dispatcher.next(n)}error(n){}complete(){}performAction(n){this.dispatch(new Pn(n,+Date.now()))}refresh(){this.dispatch(new su)}reset(){this.dispatch(new au(+Date.now()))}rollback(){this.dispatch(new cu(+Date.now()))}commit(){this.dispatch(new uu(+Date.now()))}sweep(){this.dispatch(new lu)}toggleAction(n){this.dispatch(new du(n))}jumpToAction(n){this.dispatch(new hu(n))}jumpToState(n){this.dispatch(new fu(n))}importState(n){this.dispatch(new pu(n))}lockChanges(n){this.dispatch(new gu(n))}pauseRecording(n){this.dispatch(new mu(n))}static{this.\u0275fac=function(r){return new(r||e)(E(Qi),E(Ge),E($t),E(Lg),E(Ht),E(Ce),E(Ir),E(Yi))}}static{this.\u0275prov=x({token:e,factory:e.\u0275fac})}}return e})();function bg({ngZone:e,connectInZone:t}){return n=>t?new k(r=>n.subscribe({next:o=>e.run(()=>r.next(o)),error:o=>e.run(()=>r.error(o)),complete:()=>e.run(()=>r.complete())})):n}var BI=new b("@ngrx/store-devtools Is Devtools Extension or Monitor Present");function UI(e,t){return!!e||t.monitor!==Ng}function $I(){let e="__REDUX_DEVTOOLS_EXTENSION__";return typeof window=="object"&&typeof window[e]<"u"?window[e]:null}function HI(e){return e.state}function zI(e={}){return Pt([Lg,Qi,Eg,{provide:yg,useValue:e},{provide:BI,deps:[yu,Yi],useFactory:UI},{provide:yu,useFactory:$I},{provide:Yi,deps:[yg],useFactory:RI},{provide:Sn,deps:[Eg],useFactory:HI},{provide:Mn,useExisting:Qi}])}var jg=(()=>{class e{static instrument(n={}){return{ngModule:e,providers:[zI(n)]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=X({type:e})}static{this.\u0275inj=J({})}}return e})();var Bg=[sg(),ei(Ei.forRoot({}),Ii.forRoot([]))];zt.production||Bg.push(ei(jg.instrument({maxAge:25,logOnly:zt.production})));var Ug={providers:Bg};mg(Li,Ug).catch(e=>console.error("Error bootstrapping app:",e));
