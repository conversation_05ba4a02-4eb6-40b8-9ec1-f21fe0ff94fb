import org.openapitools.generator.gradle.plugin.tasks.GenerateTask


buildscript {
    repositories {
        mavenLocal()
        mavenCentral()
        maven { url 'https://repo.spring.io/release' }
    }

    ext {
        springCloudVersion = "2023.0.3"
        springIntegationVersion = '6.3.3'
        springIntegationKafkaVersion = '6.3.4'
    }
}

plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'java'
    id 'com.google.cloud.tools.jib'
    id 'idea'
    id 'com.github.node-gradle.node'
    id 'za.co.discovery.publisher.aws-codeartifact-publisher'
    id "org.openapi.generator" version "6.6.0"
    id "jacoco"
    id "org.sonarqube" version "3.5.0.2730"
}

sourceCompatibility = JavaVersion.VERSION_21
description = 'hs-chat-agent-angular'
group = 'za.co.discovery.health.masterpatientindex'
version = '0.0.1-SNAPSHOT'

idea {
    module { generatedSourceDirs += file('src/generated/java') }
    module { generatedSourceDirs += file('build/generated/src/main/java') }
}

sourceSets {
    main { java { srcDirs += file('src/generated/java') } }
    main { java { srcDirs += file('build/generated/src/main/java') } }
}

tasks.register("chat-service", GenerateTask) {
    generatorName = "java"
    skipValidateSpec = true
    generateApiTests = false
    generateModelTests = false
    generateApiDocumentation = false
    inputSpec = "$projectDir/src/main/resources/openapi/chat-agent.json".toString()
    outputDir = "$projectDir/build/generated".toString()
    modelPackage = "com.vitality.ai.chat.model"
    apiPackage = "com.vitality.ai.chat.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "webclient",
        "useJakartaEe"    : "true"
    ]
    ignoreFileOverride = "$projectDir/.openapi-generator-ignore"
}

jar {
    enabled = true
}

bootJar {
    manifest {
        attributes 'Start-Class': 'za.co.discovery.health.ChatAgentApp'
    }
    dependsOn 'buildAngular'
}

def angularDir = "${projectDir}/src/main/angular/member-matcher"


node {
    version = '18.19.0'
    download = true
    workDir = file("${project.buildDir}/nodejs")
    npmWorkDir = file("${project.buildDir}/npm")
    nodeProjectDir = file("${angularDir}")
}
// Use the plugin's npmInstall task
npmInstall {
    // Input/output specifications for better incremental build support
    inputs.file("${angularDir}/package.json")
    outputs.dir("${angularDir}/node_modules")
}


task buildAngular(type: com.github.gradle.node.npm.task.NpmTask) {
    description = 'Builds the Angular application'
    workingDir = file("${angularDir}")
    args = ['run', 'build-spring']
    dependsOn npmInstall
    inputs.dir file("${angularDir}/src")
    outputs.dir file("${angularDir}/dist")
}

processResources.dependsOn 'buildAngular'


configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    maven {
        name = 'd2hp-v1-d2hp-v1-artifacts'
        url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
        credentials {
            username "aws"
            password System.env.CODEARTIFACT_AUTH_TOKEN ?: System.getProperty("codeArtifactAuthToken")
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

dependencies {
    implementation platform(project(':hs-platform-ai'))
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.security:spring-security-oauth2-resource-server'
    implementation 'org.springframework.security:spring-security-oauth2-jose'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'jakarta.servlet:jakarta.servlet-api'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'com.github.loki4j:loki-logback-appender'
    implementation 'org.springframework.retry:spring-retry'
    implementation 'org.projectlombok:lombok'
    implementation 'javax.annotation:javax.annotation-api'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    implementation 'org.hibernate.validator:hibernate-validator'
    implementation 'org.openapitools:jackson-databind-nullable:0.2.3'
    implementation 'za.co.discovery.health.hs:hs-starter-swagger'
    implementation 'za.co.discovery.health.hs:hs-starter-webclient-sb3:20250731'

    testImplementation 'za.co.discovery.health.hs:hs-starter-test-light:20250714'
    testAnnotationProcessor 'org.projectlombok:lombok'
}

test {
    useJUnitPlatform()
}

springBoot {
    mainClass = 'com.vitality.ai.InternalServiceApplication'
}

tasks.withType(Checkstyle).configureEach {
    reports {
        xml.enabled false
        html.enabled true
    }
}

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-ai-chat-agent-angular:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['9900']
        jvmFlags = [
            '-XX:MinRAMPercentage=60.0',
            '-XX:MaxRAMPercentage=80.0',
            '-XX:+PrintFlagsFinal',
            '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.ai.InternalServiceApplication'
    }
    allowInsecureRegistries = true
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        failOnViolation = true
        rule {
            limit {
                minimum = 0.1
            }
        }
    }
}

compileJava.dependsOn tasks."chat-service"
