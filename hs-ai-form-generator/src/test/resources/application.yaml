# Application configuration
spring:
  application:
    name: hs-ai-form-generator
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      enabled: false
server:
  port: 9900
  servlet:
    context-path: /v3/ai-form-generator

integration:
  survey:
    url: http://hs-survey-management:33000/v3/survey
  entity-data-service:
    url: http://hs-entity-data-service:32000/v3/entity

hs:
  oauth2:
    #url: http://keycloak:8080/v3-auth/realms/vhs/protocol/openid-connect/token
    url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: BACKE<PERSON>_TO_BACKEND_CLIENT_ID
      secret: BACKEND_TO_BACKEND_CLIENT_SECRET

form:
  templates:
    path: templates/
