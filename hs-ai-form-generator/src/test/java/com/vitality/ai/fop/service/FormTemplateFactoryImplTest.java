package com.vitality.ai.fop.service;

import com.vitality.ai.fop.domain.FormTemplate;
import com.vitality.ai.fop.domain.FormType;
import com.vitality.ai.fop.service.mock.MockSurveyTemplateProvider;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import za.co.discovery.health.survey.domain.SurveyTemplate;

@Slf4j
class FormTemplateFactoryImplTest {

    private final MockSurveyTemplateProvider mockSurveyTemplateProvider = new MockSurveyTemplateProvider();
    private final FormTemplateFactoryImpl formTemplateFactory = new FormTemplateFactoryImpl();

    @Test
    void testCreateFormTemplate() {
        SurveyTemplate surveyTemplate = mockSurveyTemplateProvider.getSurveyTemplate(FormType.HRA.getSurveyTemplateName());
        FormTemplate formTemplate = formTemplateFactory.createTemplate(surveyTemplate);

        Assertions.assertNotNull(formTemplate);

        log.info("Form Template ID: {}", formTemplate.getFormId());
        formTemplate.getQuestions().forEach(question -> {
            log.info("{} {} {}", question.getText(), question.getType(), question.getOptions());
        });
    }
}
