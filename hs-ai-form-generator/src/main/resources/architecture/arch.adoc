= OCR Pipeline Architecture: Java and Spring Implementation
:toc: left
:toclevels: 3
:sectnums:
:icons: font
:source-highlighter: highlight.js

== Executive Summary

This document outlines the architecture for an Optical Character Recognition (OCR) pipeline built with Java and Spring. The pipeline provides a comprehensive solution for document processing, from image acquisition to text extraction, with capabilities for training custom models and deploying them in various environments. The architecture follows modern software engineering principles, with a focus on modularity, scalability, and maintainability.

== Introduction

This architecture provides a framework for a specialized OCR pipeline designed to process structured forms with the following characteristics:

Each form contains a QR code at the top for document identification and classification
The form follows a scantron-style layout for multiple choice questions
Each question is identified by a mini QR code that encodes the question number or identifier
The system combines traditional OCR capabilities with specialized computer vision techniques for QR code detection, bubble/checkbox recognition, and form alignment. This specialized approach ensures high accuracy for structured form processing while leveraging Java and Spring for enterprise-grade deployment and integration capabilities.

[plantuml]
....
@startuml
skinparam Monochrome true

rectangle "Structured Form OCR Pipeline" {
  [Document Capture] as DC
  [Form Alignment & Normalization] as FAN
  [QR Code Detection] as QCD
  [Form Classification] as FC
  [Question QR Detection] as QQD
  [Bubble/Checkbox Recognition] as BCR
  [Answer Extraction] as AE
  [Result Validation] as RV
  [Output Generation] as OG

  DC -right-> FAN
  FAN -right-> QCD
  QCD -right-> FC
  FC -right-> QQD
  QQD -right-> BCR
  BCR -right-> AE
  AE -right-> RV
  RV -right-> OG
}
@enduml
....

== System Architecture

=== High-Level Components

[plantuml]
....
@startuml
skinparam Monochrome true

package "Structured Form OCR System" {
  component "Document Acquisition Service" as DAS
  component "Form Processing Service" as FPS
  component "QR Code Detection Service" as QCDS
  component "Form Template Registry" as FTR
  component "Bubble Recognition Engine" as BRE
  component "Answer Extraction Service" as AES
  component "Result Validation Service" as RVS
  component "Output Generation Service" as OGS
  component "Pipeline Orchestrator" as PO
  component "Administration UI" as AUI
  component "Template Training Module" as TTM

  database "Document Repository" as DR
  database "Template Repository" as TR
  database "Form Definition Repository" as FDR
  database "Training Data Repository" as TDR

  DAS --> DR : stores
  FPS --> DR : reads/writes
  QCDS --> DR : reads
  QCDS --> FTR : identifies form
  FTR --> TR : uses
  FTR --> FDR : uses
  BRE --> DR : reads
  BRE --> FDR : uses layout
  BRE --> AES : sends to
  AES --> RVS : sends to
  RVS --> OGS : sends to

  TTM --> TR : updates
  TTM --> FDR : updates
  TTM --> TDR : reads

  PO --> DAS : orchestrates
  PO --> FPS : orchestrates
  PO --> QCDS : orchestrates
  PO --> BRE : orchestrates
  PO --> AES : orchestrates
  PO --> RVS : orchestrates
  PO --> OGS : orchestrates

  AUI --> PO : configures
  AUI --> TTM : manages
  AUI --> FTR : manages
}
@enduml
....

=== Component Details

==== Document Acquisition Service
* Responsible for acquiring form images from various sources (scanner, file upload, API)
* Validates document formats and quality
* Implements image quality checks specific to form scanning
* Registers documents in the repository for processing

==== Form Processing Service
* Image enhancement (contrast adjustment, noise reduction)
* Deskewing and orientation correction based on form edges or markers
* Form alignment and normalization to ensure consistent processing
* Binarization and thresholding optimized for bubbles/checkboxes

==== QR Code Detection Service
* Specialized detection of QR codes at the top of forms
* Extraction and decoding of form identification QR codes
* Detection and decoding of per-question mini QR codes
* QR code validation and error correction

==== Form Template Registry
* Maintains form templates and specifications
* Maps QR codes to specific form types
* Provides form layout information for bubble recognition
* Manages versioning of form templates

==== Bubble Recognition Engine
* Specialized detection of filled bubbles and checkboxes
* Grid alignment based on form templates
* Discrimination between filled, partially filled, and empty bubbles
* Confidence scoring for bubble recognition
* Multiple selection detection for questions allowing multiple answers

==== Answer Extraction Service
* Maps bubble positions to answer choices
* Aggregates answers from all questions based on mini QR identifiers
* Applies form-specific business rules to extracted answers
* Generates structured response data

==== Result Validation Service
* Validates extracted answers against form business rules
* Flags inconsistent or invalid answer patterns
* Identifies forms requiring manual review
* Quality assurance checks based on form templates

==== Template Training Module
* Form template definition and management
* Training data preparation for bubble recognition
* Template validation and performance metrics
* Template versioning and deployment

==== Output Generation Service
* Structured data formatting (JSON, XML)
* Integration with downstream systems
* Results visualization and reporting
* Answer aggregation and analytics

==== Pipeline Orchestrator
* Workflow management and execution
* Error handling and recovery
* Monitoring and reporting
* Scalability management

==== Administration UI
* Form template management
* Pipeline configuration
* Training data management
* System health dashboard
* Manual review interface for flagged forms

=== Data Flow

[plantuml]
....
@startuml
skinparam Monochrome true

actor "User" as USER
participant "Document Acquisition\nService" as DAS
participant "Form Processing\nService" as FPS
participant "QR Code Detection\nService" as QCDS
participant "Form Template\nRegistry" as FTR
participant "Bubble Recognition\nEngine" as BRE
participant "Answer Extraction\nService" as AES
participant "Result Validation\nService" as RVS
participant "Output Generation\nService" as OGS
database "Document\nRepository" as DR
database "Template\nRepository" as TR
database "Form Definition\nRepository" as FDR

USER -> DAS : Submit form
activate DAS
DAS -> DR : Store original form
DAS -> FPS : Forward for processing
deactivate DAS

activate FPS
FPS -> DR : Retrieve form
FPS -> FPS : Apply preprocessing\n(alignment & normalization)
FPS -> DR : Store preprocessed form
FPS -> QCDS : Send for QR detection
deactivate FPS

activate QCDS
QCDS -> DR : Retrieve preprocessed form
QCDS -> QCDS : Detect main QR code
QCDS -> FTR : Identify form template
activate FTR
FTR -> TR : Lookup template
FTR -> FDR : Retrieve form definition
FTR -> QCDS : Return form specification
deactivate FTR
QCDS -> QCDS : Detect question QR codes
QCDS -> BRE : Send form with QR info
deactivate QCDS

activate BRE
BRE -> DR : Retrieve form with QR data
BRE -> FDR : Get bubble positions
BRE -> BRE : Detect filled bubbles
BRE -> AES : Send bubble recognition results
deactivate BRE

activate AES
AES -> AES : Map bubbles to answers
AES -> AES : Extract structured responses
AES -> RVS : Send extracted answers
deactivate AES

activate RVS
RVS -> FDR : Get validation rules
RVS -> RVS : Validate answers
RVS -> RVS : Flag inconsistencies
RVS -> OGS : Send validated results
deactivate RVS

activate OGS
OGS -> OGS : Generate structured output
OGS -> DR : Store processed results
OGS -> USER : Return results
deactivate OGS
@enduml
....

== Technology Stack

=== Core Technologies

[cols="2,5", options="header"]
|===
| Technology | Purpose
| Java 17+ | Primary programming language
| Spring Boot | Application framework
| Spring Data | Data access and persistence
| Spring Cloud | Microservices support (if needed)
| ZXing | QR code detection and decoding
| JavaCV/OpenCV | Image processing and bubble detection
| DL4J (DeepLearning4J) | Java-based deep learning for custom model training
| OpenCV (with Java bindings) | Image preprocessing and form alignment
| Project Lombok | Boilerplate code reduction
| Swagger/OpenAPI | API documentation
| JUnit 5 | Testing framework
|===

=== Storage

[cols="2,5", options="header"]
|===
| Technology | Purpose
| MongoDB | Document storage (original and processed)
| PostgreSQL | Structured data and metadata
| MinIO or S3 | Large binary object storage
| Redis | Caching and temporary storage
|===

=== Build and Deployment

[cols="2,5", options="header"]
|===
| Technology | Purpose
| Maven | Dependency management and build
| Docker | Containerization
| Kubernetes | Container orchestration (production)
| GitHub Actions or Jenkins | CI/CD pipeline
|===

== Implementation Details

=== Spring Configuration

[source,java]
----
@Configuration
@EnableConfigurationProperties(OcrProperties.class)
public class OcrConfiguration {

    @Bean
    public DocumentProcessor documentProcessor(PreprocessorService preprocessorService) {
        return new DocumentProcessorImpl(preprocessorService);
    }

    @Bean
    public OcrEngine ocrEngine(OcrProperties properties, ModelRepository modelRepository) {
        return new TesseractEngine(properties.getTesseractDataPath(), modelRepository);
    }

    @Bean
    public PipelineOrchestrator pipelineOrchestrator(
            DocumentAcquisitionService acquisitionService,
            PreprocessingService preprocessingService,
            OcrEngine ocrEngine,
            PostProcessingService postProcessingService,
            OutputGenerationService outputService) {

        return new PipelineOrchestratorImpl(
                acquisitionService,
                preprocessingService,
                ocrEngine,
                postProcessingService,
                outputService);
    }
}
----

=== Key Interfaces

[source,java]
----
public interface OcrEngine {
    RecognitionResult recognize(ProcessedDocument document);
    double getConfidenceScore(RecognitionResult result);
    void setModel(OcrModel model);
    OcrModel getActiveModel();
}

public interface DocumentProcessor {
    ProcessedDocument process(RawDocument document);
    List<TextRegion> detectTextRegions(ProcessedDocument document);
}

public interface PipelineOrchestrator {
    void startPipeline(String documentId);
    void stopPipeline(String pipelineId);
    PipelineStatus getPipelineStatus(String pipelineId);
    List<PipelineExecution> getRecentExecutions();
}

public interface ModelTrainer {
    TrainingResult trainModel(TrainingDataset dataset, ModelParameters parameters);
    void validateModel(OcrModel model, ValidationDataset dataset);
    OcrModel exportModel(String modelId, ModelFormat format);
}
----

=== Document Entity Model

[plantuml]
....
@startuml
skinparam Monochrome true

class Document {
  +String id
  +String name
  +DocumentType type
  +DocumentStatus status
  +Date createdAt
  +Date updatedAt
  +List<Page> pages
}

class Page {
  +String id
  +int pageNumber
  +int width
  +int height
  +List<TextRegion> textRegions
}

class TextRegion {
  +String id
  +Rectangle bounds
  +List<TextLine> lines
  +float confidenceScore
}

class TextLine {
  +String id
  +Rectangle bounds
  +List<Word> words
  +String text
  +float confidenceScore
}

class Word {
  +String id
  +Rectangle bounds
  +String text
  +float confidenceScore
  +List<RecognitionCandidate> alternativeCandidates
}

class RecognitionCandidate {
  +String text
  +float confidenceScore
}

Document "1" *-- "many" Page
Page "1" *-- "many" TextRegion
TextRegion "1" *-- "many" TextLine
TextLine "1" *-- "many" Word
Word "1" *-- "many" RecognitionCandidate
@enduml
....

=== Model Training Pipeline

[plantuml]
....
@startuml
skinparam Monochrome true

package "Model Training Pipeline" {
  [Data Collection] as DC
  [Data Labeling] as DL
  [Training Data Preparation] as TDP
  [Feature Engineering] as FE
  [Model Training] as MT
  [Model Validation] as MV
  [Model Export] as ME
  [Model Deployment] as MD

  DC -right-> DL
  DL -right-> TDP
  TDP -right-> FE
  FE -right-> MT
  MT -right-> MV
  MV -right-> ME
  ME -right-> MD
}
@enduml
....

==== Training Data Collection

Integration with scanning devices or document repositories
Batch import of labeled datasets
Web interface for manual document uploads
Data quality checks and format validation
==== Data Annotation

[source,java]
----
@Service
@RequiredArgsConstructor
public class DataAnnotationService {

    private final AnnotationRepository repository;
    private final ImageProcessingService imageProcessor;

    public AnnotatedDocument annotateAutomatically(Document document) {
        // Perform automatic annotation using existing models
        ProcessedDocument processed = imageProcessor.process(document);
        List<TextRegion> regions = imageProcessor.detectTextRegions(processed);

        AnnotatedDocument annotated = new AnnotatedDocument(document);
        annotated.setTextRegions(regions);

        return repository.save(annotated);
    }

    public AnnotatedDocument updateAnnotation(String documentId, AnnotationUpdate update) {
        // Update annotations based on user feedback
        AnnotatedDocument document = repository.findById(documentId)
            .orElseThrow(() -> new DocumentNotFoundException(documentId));

        update.getRegionUpdates().forEach(regionUpdate -> {
            TextRegion region = document.findRegionById(regionUpdate.getRegionId());
            region.setBounds(regionUpdate.getBounds());
            region.setText(regionUpdate.getText());
        });

        return repository.save(document);
    }
}
----

==== Model Training Implementation

[source,java]
----
@Service
@RequiredArgsConstructor
public class Dl4jModelTrainer implements ModelTrainer {

    private final ModelRepository modelRepository;
    private final DatasetRepository datasetRepository;

    @Override
    public TrainingResult trainModel(TrainingDataset dataset, ModelParameters parameters) {
        // Initialize DL4J configuration
        MultiLayerConfiguration conf = new NeuralNetConfiguration.Builder()
            .seed(parameters.getSeed())
            .optimizationAlgo(OptimizationAlgorithm.STOCHASTIC_GRADIENT_DESCENT)
            .updater(new Adam(parameters.getLearningRate()))
            .list()
            .layer(0, new ConvolutionLayer.Builder()
                .kernelSize(5, 5)
                .stride(1, 1)
                .nIn(1)
                .nOut(32)
                .activation(Activation.RELU)
                .build())
            .layer(1, new SubsamplingLayer.Builder()
                .kernelSize(2, 2)
                .stride(2, 2)
                .poolingType(SubsamplingLayer.PoolingType.MAX)
                .build())
            // Additional layers...
            .layer(7, new OutputLayer.Builder()
                .nOut(dataset.getNumClasses())
                .activation(Activation.SOFTMAX)
                .lossFunction(LossFunctions.LossFunction.NEGATIVELOGLIKELIHOOD)
                .build())
            .setInputType(InputType.convolutionalFlat(
                parameters.getImageHeight(),
                parameters.getImageWidth(),
                parameters.getChannels()))
            .build();

        // Initialize model
        MultiLayerNetwork model = new MultiLayerNetwork(conf);
        model.init();

        // Train model with dataset
        DataSetIterator trainIter = createDataSetIterator(dataset);
        for (int i = 0; i < parameters.getEpochs(); i++) {
            model.fit(trainIter);
            // Log training metrics
        }

        // Save model
        OcrModel ocrModel = new OcrModel();
        ocrModel.setName(parameters.getModelName());
        ocrModel.setDescription(parameters.getDescription());
        ocrModel.setCreatedAt(new Date());

        // Save model to byte array
        ByteArrayOutputStream modelBytes = new ByteArrayOutputStream();
        ModelSerializer.writeModel(model, modelBytes, true);
        ocrModel.setModelData(modelBytes.toByteArray());

        // Save model metadata
        modelRepository.save(ocrModel);

        return new TrainingResult(ocrModel.getId(), calculatePerformanceMetrics(model, dataset));
    }

    @Override
    public void validateModel(OcrModel model, ValidationDataset dataset) {
        // Load model
        MultiLayerNetwork network = ModelSerializer.restoreMultiLayerNetwork(
            new ByteArrayInputStream(model.getModelData()));

        // Validate against test dataset
        DataSetIterator testIter = createDataSetIterator(dataset);
        Evaluation eval = network.evaluate(testIter);

        // Update model with validation results
        model.setAccuracy(eval.accuracy());
        model.setPrecision(eval.precision());
        model.setRecall(eval.recall());
        model.setF1Score(eval.f1());
        model.setLastValidated(new Date());

        modelRepository.save(model);
    }

    @Override
    public OcrModel exportModel(String modelId, ModelFormat format) {
        OcrModel model = modelRepository.findById(modelId)
            .orElseThrow(() -> new ModelNotFoundException(modelId));

        // Convert to required format
        switch (format) {
            case ONNX:
                // Convert to ONNX format
                break;
            case TENSORFLOW:
                // Convert to TensorFlow format
                break;
            default:
                // Return as is
                break;
        }

        return model;
    }

    private DataSetIterator createDataSetIterator(Dataset dataset) {
        // Implementation to create DL4J DataSetIterator from our dataset
        // This would handle image loading, preprocessing, and batching
    }

    private Map<String, Double> calculatePerformanceMetrics(MultiLayerNetwork model, Dataset dataset) {
        // Calculate and return model performance metrics
    }
}
----

== Deployment Architecture

=== Monolithic Deployment

[plantuml]
....
@startuml
skinparam Monochrome true

node "Application Server" {
  [Spring Boot Application] as SBA
  [Embedded Tomcat] as ET

  component "OCR Pipeline" {
    [Document Acquisition] as DA
    [Preprocessing] as PP
    [OCR Engine] as OCR
    [Post-Processing] as POP
    [Output Generation] as OG

    DA -down-> PP
    PP -down-> OCR
    OCR -down-> POP
    POP -down-> OG
  }

  SBA -down-> ET
  ET -down-> [OCR Pipeline]
}

database "Database" as DB
[OCR Pipeline] -down-> DB
@enduml
....

=== Microservices Deployment

[plantuml]
....
@startuml
skinparam Monochrome true

node "Kubernetes Cluster" {
  component "API Gateway" as AG

  component "Document Service" as DS {
    [Document Controller] as DC
    [Document Repository] as DR
    DC -down-> DR
  }

  component "Preprocessing Service" as PS {
    [Preprocessing Controller] as PC
    [Image Processing] as IP
    PC -down-> IP
  }

  component "OCR Service" as OS {
    [OCR Controller] as OC
    [Tesseract Engine] as TE
    [Custom Model Engine] as CME
    OC -down-> TE
    OC -down-> CME
  }

  component "Post-Processing Service" as PPS {
    [Post-Processing Controller] as PPC
    [Text Correction] as TC
    PPC -down-> TC
  }

  component "Output Service" as OPS {
    [Output Controller] as OPC
    [Format Conversion] as FC
    OPC -down-> FC
  }

  component "Training Service" as TS {
    [Training Controller] as TC
    [Model Training] as MT
    TC -down-> MT
  }

  AG -down-> DS
  AG -down-> PS
  AG -down-> OS
  AG -down-> PPS
  AG -down-> OPS
  AG -down-> TS

  DS -right-> PS : REST
  PS -right-> OS : REST
  OS -right-> PPS : REST
  PPS -right-> OPS : REST
  TS -up-> OS : Model updates
}

database "MongoDB" as MDB
database "PostgreSQL" as PG
database "MinIO" as MIO

DS -down-> MDB
DS -down-> MIO
PS -down-> MIO
OS -down-> PG
OS -down-> MIO
TS -down-> MDB
TS -down-> MIO
@enduml
....

=== Hybrid Deployment

[plantuml]
....
@startuml
skinparam Monochrome true

node "Spring Boot Application" as SBA {
  [Document Processing API] as DPA
  [Web UI] as WUI

  component "Core Pipeline" {
    [Document Manager] as DM
    [Pipeline Orchestrator] as PO
    [Result Handler] as RH

    DPA -down-> PO
    WUI -down-> PO
    PO -down-> DM
    PO -down-> RH
  }
}

node "Worker Nodes" {
  component "Preprocessing Worker" as PW
  component "OCR Worker" as OW
  component "Post-Processing Worker" as PPW
}

node "Training Server" {
  [Model Training Service] as MTS
  [Model Repository] as MR

  MTS -down-> MR
}

database "Shared Database" as SD

SBA -down-> SD
PW -down-> SD
OW -down-> SD
PPW -down-> SD
MTS -down-> SD

SBA -right-> PW : Messages
SBA -right-> OW : Messages
SBA -right-> PPW : Messages
MTS -up-> OW : Model updates
@enduml
....

== Performance Considerations

=== Resource Requirements

[cols="2,3,2", options="header"]
|===
| Component | CPU/Memory Requirements | Scalability Approach
| Document Acquisition | Medium CPU, Medium Memory | Horizontal scaling for high volume
| Preprocessing | High CPU, Medium Memory | GPU acceleration for image processing
| OCR Engine | High CPU, High Memory | Worker pool with load balancing
| Model Training | Very High CPU, Very High Memory | Dedicated training servers with GPUs
| Post-Processing | Medium CPU, Medium Memory | Horizontal scaling
| Output Generation | Medium CPU, Medium Memory | Horizontal scaling
|===

=== Optimization Strategies

Use native bindings for Tesseract and OpenCV for improved performance
Implement caching for frequently accessed documents and model results
Batch processing for large document sets
Asynchronous processing with message queues
Pipeline parallelization for multi-page documents
== Monitoring and Operations

=== Key Metrics

[cols="2,5", options="header"]
|===
| Metric | Description
| Document Processing Time | End-to-end time from submission to result
| OCR Accuracy | Character and word recognition accuracy
| Model Performance | Precision, recall, and F1 score of trained models
| Queue Sizes | Number of documents waiting for processing
| Error Rates | Percentage of failed document processing attempts
| Resource Utilization | CPU, memory, and disk usage across components
|===

=== Logging Strategy

[source,yaml]
----
logging:
  level:
    root: INFO
    com.example.ocr: DEBUG
    org.springframework: INFO
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${logging.file.path}/ocr-pipeline.log
    path: /var/log/ocr-pipeline
----

=== Health Checks

[source,java]
----
@Component
public class OcrEngineHealthIndicator implements HealthIndicator {

    private final OcrEngine ocrEngine;

    public OcrEngineHealthIndicator(OcrEngine ocrEngine) {
        this.ocrEngine = ocrEngine;
    }

    @Override
    public Health health() {
        try {
            boolean engineStatus = ocrEngine.checkStatus();

            if (engineStatus) {
                return Health.up()
                    .withDetail("activeModel", ocrEngine.getActiveModel().getName())
                    .withDetail("engineVersion", ocrEngine.getVersion())
                    .build();
            } else {
                return Health.down()
                    .withDetail("error", "OCR engine failed status check")
                    .build();
            }
        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}
----

== Security Considerations

=== Data Security

Encryption of documents in transit and at rest
Access control for document repositories
Secure API endpoints with OAuth2/JWT authentication
Regular security audits and penetration testing
=== Model Security

Versioning and access control for trained models
Validation of model inputs to prevent adversarial attacks
Monitoring for abnormal model behavior
Regular model performance audits
== Implementation Plan

=== Phase 1: Core OCR Pipeline

Document acquisition and repository setup
Basic preprocessing capabilities
Integration with Tesseract OCR
Simple post-processing and output generation
Basic UI for document submission and result viewing
=== Phase 2: Model Training Capabilities

Training data collection and annotation tools
Model training pipeline with DL4J
Model validation and performance metrics
Model deployment process
=== Phase 3: Advanced Features and Optimization

Enhanced preprocessing for different document types
Language-specific post-processing
Structured data extraction (forms, tables)
Performance optimization and scaling
Advanced monitoring and operations
== Conclusion

This architectural document provides a comprehensive blueprint for implementing an OCR pipeline using Java and Spring. By leveraging open-source technologies and following a modular design, the system provides flexibility for both using existing OCR engines and training custom models. The architecture supports various deployment models, from monolithic applications to distributed microservices, allowing for scalability as processing needs grow.

The implementation details, including Spring configuration, key interfaces, and component designs, offer a practical starting point for development while the performance considerations and monitoring strategies ensure operational success.

== References

[bibliography]
* [[[smith2020ocr]]] Smith, J. (2020). "Modern OCR Techniques in Document Processing." Journal of Computer Vision Applications. Vol. 12, pp. 45-58.

[[[tesseract2022]]] Tesseract OCR. (2022). "Tesseract Documentation." https://tesseract-ocr.github.io/

[[[spring2022]]] Spring Framework. (2022). "Spring Framework Documentation." https://spring.io/projects/spring-framework

[[[dl4j2022]]] DeepLearning4J. (2022). "DL4J Documentation." https://deeplearning4j.konduit.ai/

[[[opencv2022]]] OpenCV. (2022). "OpenCV Java Tutorials." https://opencv-java-tutorials.readthedocs.io/

[[[breuel2017]]] Breuel, T. M. (2017). "High Performance Text Recognition Using a Hybrid Convolutional-LSTM Implementation." IEEE International Conference on Document Analysis and Recognition (ICDAR). pp. 11-16.

[[[newman2021microservices]]] Newman, S. (2021). "Building Microservices: Designing Fine-Grained Systems." O'Reilly Media.

[[[walls2022spring]]] Walls, C. (2022). "Spring Boot in Action." Manning Publications.

