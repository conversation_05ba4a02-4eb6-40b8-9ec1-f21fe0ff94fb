<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">

    <!-- Root template -->
    <xsl:template match="/">
        <fo:root>
            <fo:layout-master-set>
                <fo:simple-page-master master-name="A4"
                                       page-height="29.7cm"
                                       page-width="21cm"
                                       margin-top="1cm"
                                       margin-bottom="1cm"
                                       margin-left="1.5cm"
                                       margin-right="1.5cm">
                    <fo:region-body margin-top="4.5cm" margin-bottom="2.5cm"/>
                    <fo:region-before extent="4cm"/>
                    <fo:region-after extent="2cm"/>
                </fo:simple-page-master>

                <!-- Create a page sequence master to handle automatic page breaks -->
                <fo:page-sequence-master master-name="document">
                    <fo:repeatable-page-master-reference master-reference="A4"/>
                </fo:page-sequence-master>
            </fo:layout-master-set>

            <fo:page-sequence master-reference="document">
                <!-- Header with logo, form title, and patient info -->
                <fo:static-content flow-name="xsl-region-before">
                    <!-- Logo and Title Row -->
                    <fo:table width="100%" table-layout="fixed" margin-bottom="0.3cm">
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="60%"/>
                        <fo:table-column column-width="20%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <!-- Logo in left column -->
                                <fo:table-cell display-align="center">
                                    <fo:block>
                                        <xsl:if test="formData/formStyle/logoUrl and string-length(formData/formStyle/logoUrl) > 0">
                                            <!--                                            <fo:external-graphic src="url({formData/formStyle/logoUrl})" content-width="2cm" content-height="scale-to-fit" alt="Logo"/>-->
                                        </xsl:if>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Title in middle column - Changed to black font -->
                                <fo:table-cell display-align="center">
                                    <fo:block font-size="16pt" font-weight="bold" color="#000000" text-align="center">
                                        <xsl:value-of select="formData/formTemplate/title"/>
                                    </fo:block>
                                    <fo:block font-size="9pt" color="#000000" text-align="center">
                                        Form ID: <xsl:value-of select="formData/formTemplate/formId"/>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Empty right column (QR code removed) -->
                                <fo:table-cell display-align="center">
                                    <fo:block>&#160;</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- Patient information section -->
                    <fo:table width="100%" table-layout="fixed" border="1pt solid #cccccc" background-color="#f5f5f5" margin-bottom="0.5cm">
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-body>
                            <!-- Row 1: Name and DOB -->
                            <fo:table-row>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Patient Name:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/name">
                                                <xsl:value-of select="formData/patientInfo/name"/>
                                            </xsl:when>
                                            <xsl:otherwise>John Doe</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Date of Birth:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/dateOfBirth">
                                                <xsl:value-of select="formData/patientInfo/dateOfBirth"/>
                                            </xsl:when>
                                            <xsl:otherwise>01/01/1980</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <!-- Row 2: Patient ID and Date -->
                            <fo:table-row>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Patient ID:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/id">
                                                <xsl:value-of select="formData/patientInfo/id"/>
                                            </xsl:when>
                                            <xsl:otherwise>PT-12345678</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Assessment Date:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/assessmentDate">
                                                <xsl:value-of select="formData/assessmentDate"/>
                                            </xsl:when>
                                            <xsl:otherwise>DD/MM/YYYY</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- Border line at bottom of header -->
                    <fo:block border-bottom="2pt solid #336699"></fo:block>
                </fo:static-content>

                <!-- Footer -->
                <fo:static-content flow-name="xsl-region-after">
                    <fo:table width="100%" table-layout="fixed" border-top="1pt solid #cccccc">
                        <fo:table-column column-width="33%"/>
                        <fo:table-column column-width="34%"/>
                        <fo:table-column column-width="33%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" padding-top="5pt">
                                        <xsl:value-of select="formData/formTemplate/formId"/>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" text-align="center" padding-top="5pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/name">
                                                <xsl:value-of select="formData/patientInfo/name"/>
                                            </xsl:when>
                                            <xsl:otherwise>John Doe</xsl:otherwise>
                                        </xsl:choose>
                                        -
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/id">
                                                <xsl:value-of select="formData/patientInfo/id"/>
                                            </xsl:when>
                                            <xsl:otherwise>PT-12345678</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" text-align="right" padding-top="5pt">
                                        Page <fo:page-number/> of <fo:page-number-citation-last ref-id="end-of-document"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:static-content>

                <!-- Main content -->
                <fo:flow flow-name="xsl-region-body" reference-orientation="0">
                    <!-- Description -->
                    <xsl:if test="formData/formTemplate/description">
                        <fo:block font-size="10pt" margin-bottom="6pt" space-after="0.2cm" font-style="italic">
                            <xsl:value-of select="formData/formTemplate/description"/>
                        </fo:block>
                    </xsl:if>

                    <!-- Dynamically set maximum questions per page based on available space -->
                    <fo:block>
                        <!-- Process each question -->
                        <xsl:for-each select="formData/formTemplate/questions/questions">
                            <xsl:variable name="questionNumber" select="position()"/>
                            <xsl:variable name="questionType" select="type"/>
                            <!-- Count actual options for this question -->
                            <xsl:variable name="optionCount" select="count(options/options)"/>

                            <!-- Question box with keep-together attribute to prevent page breaks within questions -->
                            <fo:block-container margin-bottom="4pt"
                                                padding="4pt"
                                                border="1pt solid #cccccc"
                                                background-color="#f9f9f9"
                                                keep-together.within-page="always">

                                <!-- Question heading - QR code removed -->
                                <fo:table width="100%" table-layout="fixed">
                                    <fo:table-column column-width="100%"/>
                                    <fo:table-body>
                                        <fo:table-row>
                                            <!-- Question text -->
                                            <fo:table-cell>
                                                <fo:block font-size="10pt" font-weight="bold" margin-bottom="2pt">
                                                    <xsl:value-of select="$questionNumber"/>. <xsl:value-of select="text"/>
                                                </fo:block>

                                                <!-- Paragraph of text below question title -->
                                                <xsl:if test="description">
                                                    <fo:block font-size="9pt" margin-bottom="3pt" margin-top="1pt" margin-left="2pt" font-style="italic">
                                                        <xsl:value-of select="description"/>
                                                    </fo:block>
                                                </xsl:if>
                                            </fo:table-cell>
                                        </fo:table-row>
                                    </fo:table-body>
                                </fo:table>

                                <!-- Question options based on type -->
                                <xsl:choose>
                                    <!-- Multiple choice options -->
                                    <xsl:when test="$questionType = 'MULTIPLE_CHOICE'">
                                        <fo:block margin-top="2pt">
                                            <xsl:for-each select="options/options">
                                                <fo:table width="100%" table-layout="fixed" margin-bottom="1pt">
                                                    <fo:table-column column-width="15pt"/>
                                                    <fo:table-column column-width="proportional-column-width(1)"/>
                                                    <fo:table-body>
                                                        <fo:table-row>
                                                            <!-- Answer box -->
                                                            <fo:table-cell display-align="center">
                                                                <fo:block>
                                                                    <fo:instream-foreign-object>
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                                                                            <rect x="0.5" y="0.5" width="9" height="9" fill="white" stroke="#000000" stroke-width="1"/>
                                                                        </svg>
                                                                    </fo:instream-foreign-object>
                                                                </fo:block>
                                                            </fo:table-cell>
                                                            <!-- Option text -->
                                                            <fo:table-cell>
                                                                <fo:block font-size="9pt" margin-left="3pt">
                                                                    <xsl:value-of select="."/>
                                                                </fo:block>
                                                            </fo:table-cell>
                                                        </fo:table-row>
                                                    </fo:table-body>
                                                </fo:table>
                                            </xsl:for-each>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- LENGTH question type - added support -->
                                    <xsl:when test="$questionType = 'LENGTH'">
                                        <fo:block margin-top="2pt">
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="70%"/>
                                                <fo:table-column column-width="30%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="2pt">
                                                            <fo:block border="1pt solid #336699" padding="3pt" background-color="white">
                                                                <!-- Input field -->
                                                                <fo:block>&#160;</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="2pt">
                                                            <fo:block font-size="9pt" font-style="italic">
                                                                <xsl:choose>
                                                                    <xsl:when test="options/options[1]">
                                                                        <xsl:value-of select="options/options[1]"/>
                                                                    </xsl:when>
                                                                    <xsl:otherwise>units</xsl:otherwise>
                                                                </xsl:choose>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- Rating scale - FIXED VERSION -->
                                    <xsl:when test="$questionType = 'RATING_SCALE'">
                                        <fo:block margin-top="2pt">
                                            <!-- Get the actual number of options -->
                                            <xsl:variable name="optionCount" select="count(options/options)"/>

                                            <fo:table width="100%" table-layout="fixed">
                                                <!-- Create the exact number of columns needed - one per option -->
                                                <xsl:for-each select="options/options">
                                                    <fo:table-column column-width="proportional-column-width(1)"/>
                                                </xsl:for-each>

                                                <fo:table-body>
                                                    <!-- Scale labels row -->
                                                    <fo:table-row>
                                                        <!-- Only try to create the Low and High labels if we have options -->
                                                        <xsl:if test="$optionCount > 0">
                                                            <!-- First cell - Low label -->
                                                            <fo:table-cell>
                                                                <fo:block font-size="8pt" text-align="center">Low</fo:block>
                                                            </fo:table-cell>

                                                            <!-- Middle cells - generate n-2 empty cells if we have more than 2 options -->
                                                            <xsl:if test="$optionCount > 2">
                                                                <xsl:for-each select="options/options[position() > 1 and position() &lt; $optionCount]">
                                                                    <fo:table-cell>
                                                                        <fo:block>&#160;</fo:block>
                                                                    </fo:table-cell>
                                                                </xsl:for-each>
                                                            </xsl:if>

                                                            <!-- Last cell - High label (only if we have more than one option) -->
                                                            <xsl:if test="$optionCount > 1">
                                                                <fo:table-cell>
                                                                    <fo:block font-size="8pt" text-align="center">High</fo:block>
                                                                </fo:table-cell>
                                                            </xsl:if>
                                                        </xsl:if>
                                                    </fo:table-row>

                                                    <!-- Numbers row -->
                                                    <fo:table-row>
                                                        <xsl:for-each select="options/options">
                                                            <fo:table-cell text-align="center">
                                                                <fo:block font-size="9pt">
                                                                    <xsl:value-of select="."/>
                                                                </fo:block>
                                                            </fo:table-cell>
                                                        </xsl:for-each>
                                                    </fo:table-row>

                                                    <!-- Checkbox row -->
                                                    <fo:table-row>
                                                        <xsl:for-each select="options/options">
                                                            <fo:table-cell text-align="center" padding="2pt">
                                                                <fo:block>
                                                                    <fo:instream-foreign-object>
                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                                                                            <rect x="0.5" y="0.5" width="9" height="9" fill="white" stroke="#000000" stroke-width="1"/>
                                                                        </svg>
                                                                    </fo:instream-foreign-object>
                                                                </fo:block>
                                                            </fo:table-cell>
                                                        </xsl:for-each>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- Clinical measure -->
                                    <xsl:when test="$questionType = 'CLINICAL_MEASURE'">
                                        <fo:block margin-top="2pt">
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="70%"/>
                                                <fo:table-column column-width="30%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="2pt">
                                                            <fo:block border="1pt solid #336699" padding="3pt" background-color="white">
                                                                <!-- Input field -->
                                                                <fo:block>&#160;</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="2pt">
                                                            <fo:block font-size="9pt" font-style="italic">
                                                                <xsl:choose>
                                                                    <xsl:when test="options/options[1]">
                                                                        <xsl:value-of select="options/options[1]"/>
                                                                    </xsl:when>
                                                                    <xsl:otherwise>units</xsl:otherwise>
                                                                </xsl:choose>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- Default case when type is not recognized -->
                                    <xsl:otherwise>
                                        <fo:block margin-top="2pt">
                                            <xsl:choose>
                                                <!-- If it's a paragraph type, use a multi-line text area -->
                                                <xsl:when test="$questionType = 'PARAGRAPH'">
                                                    <fo:table width="100%" table-layout="fixed">
                                                        <fo:table-column column-width="100%"/>
                                                        <fo:table-body>
                                                            <fo:table-row>
                                                                <fo:table-cell padding="2pt">
                                                                    <fo:block border="1pt solid #336699" padding="3pt" background-color="white" height="4em">
                                                                        <!-- Paragraph input field with more height -->
                                                                        <fo:block>&#160;</fo:block>
                                                                    </fo:block>
                                                                </fo:table-cell>
                                                            </fo:table-row>
                                                        </fo:table-body>
                                                    </fo:table>
                                                </xsl:when>
                                                <!-- Otherwise use a default input field -->
                                                <xsl:otherwise>
                                                    <fo:table width="100%" table-layout="fixed">
                                                        <fo:table-column column-width="100%"/>
                                                        <fo:table-body>
                                                            <fo:table-row>
                                                                <fo:table-cell padding="2pt">
                                                                    <fo:block border="1pt solid #336699" padding="3pt" background-color="white">
                                                                        <!-- Default input field -->
                                                                        <fo:block>&#160;</fo:block>
                                                                    </fo:block>
                                                                </fo:table-cell>
                                                            </fo:table-row>
                                                        </fo:table-body>
                                                    </fo:table>
                                                </xsl:otherwise>
                                            </xsl:choose>
                                        </fo:block>
                                    </xsl:otherwise>
                                </xsl:choose>
                            </fo:block-container>

                            <!-- Add manual page breaks based on space considerations -->
                            <xsl:choose>
                                <!-- Force page break after rating scale questions or when we have more than 3 long questions in a row -->
                                <xsl:when test="($questionType = 'RATING_SCALE' and position() mod 3 = 0) or
                                           (position() mod 3 = 0 and $optionCount > 5) or
                                           position() mod 5 = 0">
                                    <fo:block break-after="page"/>
                                </xsl:when>
                                <!-- Otherwise just add minimal space between questions -->
                                <xsl:otherwise>
                                    <fo:block space-after="0.1cm"/>
                                </xsl:otherwise>
                            </xsl:choose>
                        </xsl:for-each>
                    </fo:block>
                    <!-- Signature section -->
                    <fo:block-container margin-top="6pt" margin-bottom="6pt">
                        <fo:table width="100%" table-layout="fixed">
                            <fo:table-column column-width="33%"/>
                            <fo:table-column column-width="34%"/>
                            <fo:table-column column-width="33%"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell padding="4pt">
                                        <fo:block font-size="9pt" font-weight="bold">Patient Signature:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="15pt">&#160;</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding="4pt">
                                        <fo:block font-size="9pt" font-weight="bold">Provider Signature:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="15pt">&#160;</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding="4pt">
                                        <fo:block font-size="9pt" font-weight="bold">Date:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="15pt">&#160;</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block-container>

                    <fo:block id="end-of-document"/>
                </fo:flow>
            </fo:page-sequence>
        </fo:root>
    </xsl:template>

    <!-- These templates are now unused but kept for reference -->
    <!-- Helper template for creating empty cells for the middle of the rating scale -->
    <xsl:template name="create-empty-cells">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <xsl:if test="$current &lt;= $count">
            <fo:table-cell>
                <fo:block>&#160;</fo:block>
            </fo:table-cell>

            <xsl:call-template name="create-empty-cells">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>

    <!-- Helper template for creating table columns -->
    <xsl:template name="create-columns">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <fo:table-column column-width="proportional-column-width(1)"/>

        <xsl:if test="$current &lt; $count">
            <xsl:call-template name="create-columns">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>

    <!-- Helper template for creating number cells -->
    <xsl:template name="create-number-cells">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <fo:table-cell text-align="center">
            <fo:block font-size="9pt">
                <xsl:value-of select="$current"/>
            </fo:block>
        </fo:table-cell>

        <xsl:if test="$current &lt; $count">
            <xsl:call-template name="create-number-cells">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>

    <!-- Helper template for creating proper rating boxes -->
    <xsl:template name="create-rating-boxes">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <fo:table-cell text-align="center" padding="2pt">
            <fo:block>
                <fo:instream-foreign-object>
                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                        <rect x="0.5" y="0.5" width="9" height="9" fill="white" stroke="#000000" stroke-width="1"/>
                    </svg>
                </fo:instream-foreign-object>
            </fo:block>
        </fo:table-cell>

        <xsl:if test="$current &lt; $count">
            <xsl:call-template name="create-rating-boxes">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>
</xsl:stylesheet>