<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">

  <!-- ===== COMMON ATTRIBUTE SETS ===== -->

  <!-- Base font family for consistency -->
  <xsl:attribute-set name="base-font">
    <xsl:attribute name="font-family">Arial, Helvetica, sans-serif</xsl:attribute>
  </xsl:attribute-set>

  <!-- Spacing utilities -->
  <xsl:attribute-set name="padding-xs" use-attribute-sets="base-font">
    <xsl:attribute name="padding">2pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-sm" use-attribute-sets="base-font">
    <xsl:attribute name="padding">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-md" use-attribute-sets="base-font">
    <xsl:attribute name="padding">6pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-lg" use-attribute-sets="base-font">
    <xsl:attribute name="padding">10pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-bottom-sm">
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-bottom-md">
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-top-sm">
    <xsl:attribute name="margin-top">4pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Typography hierarchy -->
  <xsl:attribute-set name="text-title" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">14pt</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
    <xsl:attribute name="color">#1a365d</xsl:attribute>
    <xsl:attribute name="text-align">center</xsl:attribute>
    <xsl:attribute name="line-height">1.4</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-question-title" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">10pt</xsl:attribute>
    <xsl:attribute name="font-weight">600</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
    <xsl:attribute name="line-height">1.3</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-question-description" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="color">#4a5568</xsl:attribute>
    <xsl:attribute name="font-style">italic</xsl:attribute>
    <xsl:attribute name="margin-bottom">3pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-label" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="font-weight">600</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-body" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
    <xsl:attribute name="line-height">1.2</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-small" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="color">#4a5568</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-footer" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-header-body" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">11pt</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
    <xsl:attribute name="line-height">1.2</xsl:attribute>
  </xsl:attribute-set>

  <!-- Layout components -->
  <xsl:attribute-set name="header-table">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="divider-container">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="patient-info-container">
    <xsl:attribute name="border">1pt solid #e2e8f0</xsl:attribute>
    <xsl:attribute name="background-color">#f7fafc</xsl:attribute>
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
    <xsl:attribute name="padding">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="patient-info-table">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="question-container">
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
    <xsl:attribute name="padding">8pt</xsl:attribute>
    <xsl:attribute name="border">1pt solid #e2e8f0</xsl:attribute>
    <xsl:attribute name="background-color">#ffffff</xsl:attribute>
    <xsl:attribute name="keep-together.within-page">always</xsl:attribute>
  </xsl:attribute-set>

  <!-- Input field styles -->
  <xsl:attribute-set name="input-base">
    <xsl:attribute name="border">1pt solid #cbd5e0</xsl:attribute>
    <xsl:attribute name="background-color">#ffffff</xsl:attribute>
    <xsl:attribute name="padding">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="input-text" use-attribute-sets="input-base">
    <xsl:attribute name="height">1.2em</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="input-textarea" use-attribute-sets="input-base">
    <xsl:attribute name="height">4em</xsl:attribute>
  </xsl:attribute-set>

  <!-- Table styles -->
  <xsl:attribute-set name="table-full">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="table-options">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="margin-top">4pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Alignment utilities -->
  <xsl:attribute-set name="align-center">
    <xsl:attribute name="text-align">center</xsl:attribute>
    <xsl:attribute name="display-align">center</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="align-right">
    <xsl:attribute name="text-align">right</xsl:attribute>
  </xsl:attribute-set>

  <!-- ===== REUSABLE TEMPLATES ===== -->

  <!-- Enhanced checkbox with better styling -->
  <xsl:template name="checkbox">
    <fo:instream-foreign-object>
      <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
        <rect x="0.5" y="0.5" width="9" height="9"
              fill="#ffffff"
              stroke="#4a5568"
              stroke-width="1"
              rx="1"/>
      </svg>
    </fo:instream-foreign-object>
  </xsl:template>

  <!-- Option row template for multiple choice -->
  <xsl:template name="option-row">
    <xsl:param name="option-text"/>
    <fo:table xsl:use-attribute-sets="table-full">
      <fo:table-column column-width="16pt"/>
      <fo:table-column column-width="proportional-column-width(1)"/>
      <fo:table-body>
        <fo:table-row>
          <fo:table-cell xsl:use-attribute-sets="align-center">
            <fo:block>
              <xsl:call-template name="checkbox"/>
            </fo:block>
          </fo:table-cell>
          <fo:table-cell xsl:use-attribute-sets="padding-xs">
            <fo:block xsl:use-attribute-sets="text-body">
              <xsl:value-of select="$option-text"/>
            </fo:block>
          </fo:table-cell>
        </fo:table-row>
      </fo:table-body>
    </fo:table>
  </xsl:template>

  <!-- Rating scale template -->
  <xsl:template name="rating-scale">
    <xsl:param name="options"/>
    <xsl:variable name="optionCount" select="count($options)"/>

    <fo:table xsl:use-attribute-sets="table-options">
      <!-- Dynamic columns based on option count -->
      <xsl:for-each select="$options">
        <fo:table-column column-width="proportional-column-width(1)"/>
      </xsl:for-each>

      <fo:table-body>
        <!-- Scale labels -->
        <fo:table-row>
          <fo:table-cell>
            <fo:block xsl:use-attribute-sets="text-small align-center">Low</fo:block>
          </fo:table-cell>
          <xsl:for-each select="$options[position() > 1 and position() &lt; $optionCount]">
            <fo:table-cell>
              <fo:block>&#160;</fo:block>
            </fo:table-cell>
          </xsl:for-each>
          <xsl:if test="$optionCount > 1">
            <fo:table-cell>
              <fo:block xsl:use-attribute-sets="text-small align-center">High</fo:block>
            </fo:table-cell>
          </xsl:if>
        </fo:table-row>

        <!-- Numbers -->
        <fo:table-row>
          <xsl:for-each select="$options">
            <fo:table-cell xsl:use-attribute-sets="align-center padding-xs">
              <fo:block xsl:use-attribute-sets="text-body">
                <xsl:value-of select="."/>
              </fo:block>
            </fo:table-cell>
          </xsl:for-each>
        </fo:table-row>

        <!-- Checkboxes -->
        <fo:table-row>
          <xsl:for-each select="$options">
            <fo:table-cell xsl:use-attribute-sets="align-center padding-xs">
              <fo:block>
                <xsl:call-template name="checkbox"/>
              </fo:block>
            </fo:table-cell>
          </xsl:for-each>
        </fo:table-row>
      </fo:table-body>
    </fo:table>
  </xsl:template>

  <!-- ===== MAIN TEMPLATE ===== -->
  <xsl:template match="/">
    <fo:root>
      <fo:layout-master-set>
        <!-- First page with header -->
        <fo:simple-page-master master-name="first-page"
                               page-height="29.7cm"
                               page-width="21cm"
                               margin-top="1cm"
                               margin-bottom="1cm"
                               margin-left="1.5cm"
                               margin-right="1.5cm">
          <fo:region-body margin-top="4cm" margin-bottom="2.5cm"/>
          <fo:region-before extent="4cm" region-name="first-header"/>
          <fo:region-after extent="2cm"/>
        </fo:simple-page-master>

        <!-- Subsequent pages without header -->
        <fo:simple-page-master master-name="other-pages"
                               page-height="29.7cm"
                               page-width="21cm"
                               margin-top="1cm"
                               margin-bottom="1cm"
                               margin-left="1.5cm"
                               margin-right="1.5cm">
          <fo:region-body margin-top="1cm" margin-bottom="2.5cm"/>
          <fo:region-before extent="1cm" region-name="other-header"/>
          <fo:region-after extent="2cm"/>
        </fo:simple-page-master>

        <fo:page-sequence-master master-name="document">
          <fo:single-page-master-reference master-reference="first-page"/>
          <fo:repeatable-page-master-reference master-reference="other-pages"/>
        </fo:page-sequence-master>
      </fo:layout-master-set>

      <fo:page-sequence master-reference="document">
        <!-- ===== FIRST PAGE HEADER ===== -->
        <fo:static-content flow-name="first-header">
          <!-- Title section -->
          <fo:table xsl:use-attribute-sets="header-table">
            <fo:table-column column-width="20%"/>
            <fo:table-column column-width="60%"/>
            <fo:table-column column-width="20%"/>
            <fo:table-body>
              <fo:table-row>
                <fo:table-cell>
                  <fo:block>&#160;</fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="align-center">
                  <fo:block xsl:use-attribute-sets="text-title">
                    <xsl:value-of select="formData/formTemplate/title"/>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="align-right">
                  <fo:block>
                    <!-- SVG Vitality Logo from /hs-ai-ops/hs-ai-form-generator/src/main/resources/templates/Vitality3.0Pink.svg-->
                    <fo:instream-foreign-object content-height="1cm" content-width="scale-to-fit">
                      <svg width="77" height="40" viewBox="0 0 77 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M15.3818 23.1649C15.7664 21.4932 16.1505 19.8239 16.1505 19.5684C16.1402 18.9692 15.9284 18.391 15.5493 17.9269C15.1702 17.4627 14.6459 17.1398 14.0608 17.0101C11.3164 20.122 10.8458 25.0359 10.6336 27.2514C10.6151 27.4446 10.5986 27.6172 10.5827 27.7664C10.4792 29.9596 12.6695 30.02 12.6695 30.02C15.1962 29.3991 14.8599 27.4531 14.8599 27.4531C14.7673 27.3686 14.6963 27.2632 14.6528 27.1457C14.6093 27.0282 14.5945 26.902 14.6098 26.7776C14.6098 26.52 14.9961 24.8412 15.3818 23.1649ZM51.3313 15.5671C51.3313 16.3116 50.1643 17.2056 49.6411 17.2056C47.8733 17.2056 47.1547 14.4691 47.1547 13.624C47.1547 13.1094 47.8503 12.4943 48.3476 12.4943C50.0636 12.4943 51.3313 15.2366 51.3313 15.5671ZM18.338 14.15C18.338 14.8916 17.3722 15.2423 16.849 15.2423C14.262 15.2423 14.0809 12.5403 14.0694 11.6894C14.0694 11.1749 14.765 10.5626 15.2623 10.5626C16.9784 10.5626 18.338 13.8166 18.338 14.15ZM4.59514 23.0868C12.4051 -1.51017 21.454 -0.48685 22.1582 0.295009L22.1467 0.309381C11.5715 5.2851 7.04707 29.3071 7.04707 29.3071C6.36294 30.0717 3.1349 30.2413 2.00811 29.7584C0.128197 27.2835 -0.596172 8.28606 0.547871 5.93761C0.547871 5.93761 3.06017 4.98903 3.44535 7.94975C3.4816 8.25531 3.47291 8.97261 3.4607 9.98076C3.42546 12.8894 3.36089 18.2189 4.2617 23.0666C4.26899 23.1042 4.28856 23.1383 4.31732 23.1635C4.34609 23.1887 4.38243 23.2036 4.42062 23.2059C4.45881 23.2082 4.49668 23.1978 4.52827 23.1762C4.55986 23.1546 4.58339 23.1232 4.59514 23.0868ZM70.323 18.1685C70.323 18.1685 70.4609 17.8811 70.6909 17.8811V17.861C71.0675 17.861 72.8496 19.0337 72.8496 19.3557C72.814 21.288 72.5975 23.2127 72.2029 25.1046C71.9873 26.1423 71.6998 27.1656 71.4124 28.1545C70.5012 31.2761 68.2332 35.2516 65.1547 37.853C62.4929 40.1008 58.6928 40.2934 58.6152 39.6208C60.8142 39.0919 66.0371 36.6773 68.7506 27.019C68.7612 26.9841 68.7593 26.9466 68.7454 26.9129C68.7314 26.8791 68.7062 26.8513 68.6741 26.834C68.6419 26.8168 68.6048 26.8111 68.569 26.8182C68.5331 26.8252 68.5008 26.8443 68.4776 26.8724C66.7615 28.8788 64.7005 29.7757 62.9758 29.9999C62.5842 30.0446 62.1888 30.0446 61.7973 29.9999C59.9519 29.8332 59.2275 28.577 59.2419 26.3608C59.2557 24.7201 60.9315 21.2145 61.3093 20.4241C61.3234 20.3947 61.3357 20.369 61.346 20.3474C61.3503 20.34 61.3523 20.3314 61.3518 20.3229C61.3512 20.3143 61.3481 20.3061 61.3428 20.2993C61.3399 20.2954 61.3363 20.2921 61.3322 20.2895C61.3292 20.2875 61.3258 20.2859 61.3223 20.2847C61.3141 20.282 61.3054 20.2818 61.2971 20.2841C60.1911 20.4415 59.0735 20.5021 57.957 20.4652C57.957 20.4652 56.6203 22.371 56.1115 27.6514C56.0656 28.2378 54.2173 30.204 53.766 30.0286C53.5064 24.6159 54.0051 22.2127 54.2158 21.1974C54.2687 20.9424 54.3035 20.7749 54.3035 20.6693C54.3035 20.3423 53.8536 20.0087 53.364 19.6455C53.0661 19.4245 52.7534 19.1926 52.5184 18.9446C52.6667 18.0602 53.1866 17.9645 53.7569 17.8595C54.1262 17.7915 54.5167 17.7196 54.841 17.4269C54.9531 16.967 56.5801 10.8357 59.1355 6.33142C60.7251 6.74534 61.665 7.19088 61.665 8.47003C61.665 8.98456 59.6385 14.7019 58.667 17.4298C59.9563 17.6119 61.225 17.918 62.4555 18.3439C62.491 18.3568 62.5297 18.3585 62.5662 18.3488C62.6027 18.339 62.6353 18.3182 62.6596 18.2892C62.7572 18.1593 62.8691 18.0407 62.9931 17.9357C63.076 17.9859 63.1534 18.0199 63.2235 18.0507C63.4304 18.1415 63.5738 18.2045 63.6082 18.5738C63.6641 19.1649 63.3468 20.4116 62.9977 21.7827C62.5575 23.5119 62.0669 25.439 62.2112 26.4988C62.3003 26.9817 62.5504 27.2289 63.0017 27.2001C65.1087 27.042 67.5865 23.1069 70.323 18.1685ZM52.5041 28.7121H52.5989L52.5874 28.7207C52.7018 28.7021 52.819 28.7276 52.9153 28.7921C53.0116 28.8566 53.0798 28.9552 53.106 29.0681C53.1323 29.181 53.1148 29.2996 53.0569 29.4C52.9991 29.5004 52.9052 29.5751 52.7944 29.6089C52.0558 29.8757 51.2797 30.0242 50.4948 30.0487C48.4771 30.0487 48.0965 27.7804 47.7291 25.5908C47.638 25.0479 47.5477 24.5098 47.4335 24.0123C47.4214 23.9604 47.3943 23.9133 47.3555 23.8767C47.3167 23.8402 47.268 23.816 47.2155 23.807C47.1629 23.7981 47.1089 23.805 47.0603 23.8267C47.0116 23.8484 46.9705 23.8839 46.942 23.929C45.4731 26.2659 42.8228 30.0516 40.2358 30.0516C39.3648 30.0516 38.4536 29.0887 38.0023 28.5741C37.9809 28.5499 37.952 28.5336 37.9201 28.5279C37.8883 28.5221 37.8555 28.5273 37.827 28.5425C37.7324 28.5944 37.6168 28.6609 37.4844 28.7369C36.6636 29.2088 35.2025 30.0487 34.1678 30.0487C33.465 30.0675 32.7797 29.828 32.2416 29.3757C31.7034 28.9233 31.3497 28.2895 31.2473 27.5939C31.2412 27.5367 31.2181 27.4826 31.1809 27.4387C31.1437 27.3948 31.0942 27.363 31.0388 27.3475C30.9834 27.332 30.9246 27.3335 30.87 27.3518C30.8154 27.3702 30.7676 27.4044 30.7328 27.4502C29.8532 28.4304 28.0106 30.0372 26.2112 30.0372C25.1764 30.0401 23.3626 29.0743 23.3367 27.3496C23.2821 23.1126 25.8318 20.9136 28.5108 18.6486C28.5195 18.6408 28.5254 18.6305 28.5278 18.6191C28.5302 18.6077 28.5289 18.5959 28.524 18.5853C28.5192 18.5747 28.5112 18.566 28.501 18.5603C28.4909 18.5546 28.4792 18.5523 28.4677 18.5537C26.6337 18.7911 24.7865 18.9111 22.9372 18.913C22.9372 18.913 22.0748 21.5662 21.2096 27.3007C21.1148 27.9389 19.62 30.1235 18.2403 30.0401C17.9349 24.0595 18.4935 20.9597 18.7232 19.6853C18.7788 19.3764 18.8152 19.1747 18.8152 19.0654C18.8152 18.7923 18.5757 18.4878 18.3301 18.1755C18.0715 17.8469 17.8062 17.5096 17.8062 17.1912C18.1096 16.2558 18.5234 16.0491 18.9037 15.8592C19.082 15.7702 19.2529 15.6848 19.4016 15.5297C19.5252 15.0181 21.842 8.48727 24.6389 3.55179C26.3722 4.02033 27.2144 4.85393 27.1109 5.71628C27.0881 5.90005 26.8044 6.65092 26.4068 7.7031C25.6043 9.82731 24.3377 13.1796 23.8168 15.5729C24.0642 15.6064 24.5933 15.6222 25.2947 15.6433C27.6003 15.7123 31.7681 15.8372 33.9148 16.829C34.0846 16.9103 34.2193 17.0502 34.2943 17.2228C34.3132 17.2894 34.3176 17.3593 34.3072 17.4277C34.2968 17.4962 34.2718 17.5616 34.2339 17.6195C33.8327 18.0752 33.4005 18.5026 32.9404 18.8986C27.8985 20.5687 26.1365 24.9868 26.8637 26.5879C26.8927 26.653 26.9364 26.7105 26.9916 26.7557C27.0467 26.8009 27.1116 26.8326 27.1811 26.8483C27.2507 26.8639 27.3229 26.8631 27.3921 26.8458C27.4613 26.8285 27.5254 26.7953 27.5795 26.7488C28.2089 26.1983 29.4155 24.8059 30.5702 23.4734C31.8992 21.9397 33.1594 20.4853 33.3917 20.4853C34.461 20.4853 35.8781 22.4055 35.8781 22.7389C35.8781 23.0327 35.5096 24.1382 35.1816 25.1224C34.9382 25.8526 34.717 26.5161 34.6852 26.7316C34.4064 28.6661 36.7721 27.821 37.4274 27.5336C37.4757 27.5135 37.5165 27.4789 37.5443 27.4346C37.5721 27.3904 37.5855 27.3386 37.5827 27.2864C37.1946 18.9992 41.0838 8.59076 43.4006 5.31098C44.4613 5.42021 46.5826 6.16757 46.1026 7.71979C46.0937 7.748 46.0297 7.89412 45.9222 8.1397C44.8788 10.5217 39.7368 22.2607 41.0579 26.516C41.0828 26.5917 41.127 26.6595 41.1862 26.7128C41.2454 26.7661 41.3175 26.803 41.3954 26.8198C41.4732 26.8367 41.5541 26.8329 41.6301 26.8089C41.706 26.7848 41.7743 26.7413 41.8283 26.6827C43.6971 24.7073 45.3577 22.5449 46.7839 20.2295C46.8257 20.1587 46.8864 20.101 46.9592 20.0628C47.3013 19.8788 48.3361 19.3471 49.0087 19.2579C51.1759 18.9646 51.1852 21.7751 51.1939 24.4064C51.2013 26.6228 51.2082 28.7121 52.5041 28.7121ZM76.7791 20.2353C76.8693 20.0195 76.9152 19.7879 76.9142 19.554L76.9228 19.5626C76.927 19.3272 76.883 19.0934 76.7934 18.8756C76.7122 18.6717 76.591 18.486 76.437 18.3295C76.2808 18.1765 76.0963 18.0554 75.8937 17.9731C75.6709 17.8873 75.4339 17.8444 75.1952 17.8466C74.7338 17.8429 74.2896 18.0217 73.9592 18.3439C73.7985 18.5051 73.6713 18.6966 73.585 18.9073C73.4986 19.1179 73.4548 19.3436 73.4562 19.5713C73.4529 19.8082 73.4958 20.0435 73.5826 20.264C73.6646 20.4651 73.7859 20.6477 73.9394 20.8012C74.0929 20.9547 74.2756 21.076 74.4766 21.158C74.6969 21.2458 74.9322 21.2897 75.1693 21.2873C75.4041 21.2886 75.6368 21.2427 75.8535 21.1522C76.0616 21.0671 76.251 20.9421 76.4111 20.7843C76.5671 20.626 76.692 20.4397 76.7791 20.2353ZM76.4226 19.0136C76.4929 19.197 76.5271 19.3922 76.5232 19.5885H76.529C76.5305 19.7682 76.4984 19.9467 76.4341 20.1145C76.3736 20.2748 76.2818 20.4214 76.1639 20.5457C76.0434 20.6685 75.8998 20.7662 75.7414 20.8332C75.5654 20.9089 75.3753 20.9462 75.1837 20.9424C75.0037 20.9447 74.8249 20.9115 74.6577 20.8447C74.4996 20.777 74.3561 20.6794 74.2351 20.5572C74.1092 20.4316 74.0112 20.2807 73.9477 20.1145C73.8757 19.9316 73.8406 19.7362 73.8442 19.5396C73.8425 19.3596 73.8756 19.181 73.9419 19.0136C74.0031 18.8528 74.0949 18.7054 74.2122 18.5796C74.3336 18.4579 74.4769 18.3604 74.6347 18.2921C74.8101 18.2217 74.9976 18.1865 75.1866 18.1886C75.3682 18.1868 75.5486 18.219 75.7184 18.2835C75.8762 18.3486 76.0182 18.4466 76.1352 18.5709C76.2617 18.6961 76.3598 18.8471 76.4226 19.0136ZM76.0058 20.4336C76.0084 20.4223 76.0084 20.4105 76.0058 20.3991L76.0145 20.402C76.0095 20.3586 75.9989 20.3161 75.9828 20.2755L75.8765 19.9881C75.8437 19.8928 75.7897 19.8063 75.7184 19.7351C75.6608 19.6808 75.5922 19.6408 75.5184 19.6184C75.4446 19.596 75.3675 19.5918 75.2922 19.6061C75.2169 19.6204 75.1456 19.6529 75.0836 19.7014C75.0216 19.7499 74.9706 19.8131 74.9344 19.8862C74.8982 19.9593 74.8778 20.0403 74.8748 20.1228C74.8718 20.2053 74.8862 20.2873 74.9169 20.3632C74.9476 20.4391 74.9938 20.5069 75.0522 20.5622C75.1106 20.6175 75.1798 20.6589 75.2551 20.6838C75.3304 20.7087 75.4099 20.7165 75.4883 20.7067C75.5667 20.6969 75.6421 20.6697 75.7096 20.6269C75.7771 20.5841 75.8351 20.5267 75.8796 20.4584L76.0058 20.4336Z"
                              fill="#E91E63"/>
                      </svg>
                    </fo:instream-foreign-object>
                  </fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>

          <!-- Patient information -->
          <fo:block xsl:use-attribute-sets="patient-info-container">
            <fo:table xsl:use-attribute-sets="patient-info-table">
              <fo:table-column column-width="33%"/>
              <fo:table-column column-width="33%"/>
              <fo:table-column column-width="34%"/>
              <fo:table-body>
                <fo:table-row>
                  <fo:table-cell xsl:use-attribute-sets="padding-sm align-center">
                    <fo:block xsl:use-attribute-sets="text-label">Patient Name:</fo:block>
                    <fo:block xsl:use-attribute-sets="text-header-body" margin-top="2pt">
                      <xsl:value-of select="formData/patientInfo/name"/>
                    </fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="padding-sm align-center">
                    <fo:block xsl:use-attribute-sets="text-label">Date of Birth:</fo:block>
                    <fo:block xsl:use-attribute-sets="text-header-body" margin-top="2pt">
                      <xsl:value-of select="formData/patientInfo/dateOfBirth"/>
                    </fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="padding-sm align-center">
                    <fo:block xsl:use-attribute-sets="text-label">Policy Card:</fo:block>
                    <fo:block xsl:use-attribute-sets="text-header-body" margin-top="2pt">
                      <xsl:value-of select="formData/patientInfo/policyCardNo"/>
                    </fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block>

          <fo:block border-bottom="2pt solid #1a365d" margin-top="12pt" margin-bottom="4pt" padding-left="8pt" padding-right="8pt"/>
        </fo:static-content>

        <!-- ===== OTHER PAGES HEADER (EMPTY) ===== -->
        <fo:static-content flow-name="other-header">
          <fo:block>&#160;</fo:block>
        </fo:static-content>

        <!-- ===== FOOTER ===== -->
        <fo:static-content flow-name="xsl-region-after">
          <fo:block border-top="1pt solid #e2e8f0" padding-top="8pt"/>
          <fo:block padding="8pt">
            <fo:table xsl:use-attribute-sets="table-full">
              <fo:table-column column-width="33%"/>
              <fo:table-column column-width="34%"/>
              <fo:table-column column-width="33%"/>
              <fo:table-body>
                <fo:table-row>
                  <fo:table-cell>
                    <fo:block xsl:use-attribute-sets="text-footer">
                      <xsl:value-of select="formData/formTemplate/formId"/>
                    </fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="align-center">
                    <fo:block xsl:use-attribute-sets="text-footer">
                      <xsl:value-of select="formData/patientInfo/name"/> -
                      <xsl:value-of select="formData/patientInfo/policyCardNo"/>
                    </fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="align-right">
                    <fo:block xsl:use-attribute-sets="text-footer">
                      Page
                      <fo:page-number/>
                      of
                      <fo:page-number-citation-last ref-id="end-of-document"/>
                    </fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block>
        </fo:static-content>

        <!-- ===== MAIN CONTENT ===== -->
        <fo:flow flow-name="xsl-region-body">
          <!-- Form description -->
          <xsl:if test="formData/formTemplate/description">
            <fo:block xsl:use-attribute-sets="text-body margin-bottom-md"
                      font-style="italic"
                      color="#4a5568">
              <xsl:value-of select="formData/formTemplate/description"/>
            </fo:block>
          </xsl:if>

          <!-- Questions -->
          <xsl:for-each select="formData/formTemplate/questions/questions">
            <xsl:variable name="questionNumber" select="position()"/>
            <xsl:variable name="questionType" select="type"/>

            <fo:block-container xsl:use-attribute-sets="question-container">
              <!-- Question title -->
              <fo:block xsl:use-attribute-sets="text-question-title margin-bottom-sm">
                <xsl:value-of select="$questionNumber"/>.
                <xsl:value-of select="text"/>
              </fo:block>

              <!-- Question description -->
              <xsl:if test="description and string-length(description) > 0">
                <fo:block xsl:use-attribute-sets="text-question-description">
                  <xsl:value-of select="description"/>
                </fo:block>
              </xsl:if>

              <!-- Question content based on type -->
              <xsl:choose>
                <!-- Multiple Choice -->
                <xsl:when test="$questionType = 'MULTIPLE_CHOICE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options">
                      <fo:table-column column-width="50%"/>
                      <fo:table-column column-width="50%"/>
                      <fo:table-body>
                        <xsl:for-each select="options/options[position() mod 2 = 1]">
                          <xsl:variable name="pos" select="position() * 2 - 1"/>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-xs">
                              <xsl:call-template name="option-row">
                                <xsl:with-param name="option-text" select="."/>
                              </xsl:call-template>
                            </fo:table-cell>
                            <fo:table-cell xsl:use-attribute-sets="padding-xs">
                              <xsl:choose>
                                <xsl:when test="../options[$pos + 1]">
                                  <xsl:call-template name="option-row">
                                    <xsl:with-param name="option-text" select="../options[$pos + 1]"/>
                                  </xsl:call-template>
                                </xsl:when>
                                <xsl:otherwise>
                                  <fo:block>&#160;</fo:block>
                                </xsl:otherwise>
                              </xsl:choose>
                            </fo:table-cell>
                          </fo:table-row>
                        </xsl:for-each>
                      </fo:table-body>
                    </fo:table>
                  </fo:block>
                </xsl:when>

                <!-- Rating Scale -->
                <xsl:when test="$questionType = 'RATING_SCALE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <xsl:call-template name="rating-scale">
                      <xsl:with-param name="options" select="options/options"/>
                    </xsl:call-template>
                  </fo:block>
                </xsl:when>

                <!-- Clinical Measure -->
                <xsl:when test="$questionType = 'CLINICAL_MEASURE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options">
                      <fo:table-column column-width="25%"/>
                      <fo:table-column column-width="75%"/>
                      <fo:table-body>
                        <fo:table-row>
                          <fo:table-cell xsl:use-attribute-sets="padding-xs">
                            <fo:block xsl:use-attribute-sets="input-text">&#160;</fo:block>
                          </fo:table-cell>
                          <fo:table-cell xsl:use-attribute-sets="padding-sm">
                            <fo:block xsl:use-attribute-sets="text-small" font-style="italic">
                              <xsl:choose>
                                <xsl:when test="options/options[1]">
                                  <xsl:value-of select="options/options[1]"/>
                                </xsl:when>
                                <xsl:otherwise>units</xsl:otherwise>
                              </xsl:choose>
                            </fo:block>
                          </fo:table-cell>
                        </fo:table-row>
                      </fo:table-body>
                    </fo:table>
                  </fo:block>
                </xsl:when>

                <!-- Text Input -->
                <xsl:when test="$questionType = 'TEXT'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options margin-bottom-sm">
                      <fo:table-column column-width="30%"/>
                      <fo:table-column column-width="70%"/>
                      <fo:table-body>
                        <fo:table-row>
                          <fo:table-cell xsl:use-attribute-sets="padding-xs">
                            <fo:block xsl:use-attribute-sets="input-text">&#160;</fo:block>
                          </fo:table-cell>
                          <fo:table-cell xsl:use-attribute-sets="padding-sm">
                            <fo:block xsl:use-attribute-sets="text-small" font-style="italic">
                              <xsl:choose>
                                <xsl:when test="suffixText">
                                  <xsl:value-of select="suffixText"/>
                                </xsl:when>
                                <xsl:when test="options/options[1]">
                                  <xsl:value-of select="options/options[1]"/>
                                </xsl:when>
                                <xsl:otherwise>&#160;</xsl:otherwise>
                              </xsl:choose>
                            </fo:block>
                          </fo:table-cell>
                        </fo:table-row>
                      </fo:table-body>
                    </fo:table>
                    <!-- "I am not sure" option -->
                    <xsl:call-template name="option-row">
                      <xsl:with-param name="option-text">I am not sure</xsl:with-param>
                    </xsl:call-template>
                  </fo:block>
                </xsl:when>

                <!-- Date Input -->
                <xsl:when test="$questionType = 'DATE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options">
                      <fo:table-column column-width="30%"/>
                      <fo:table-column column-width="70%"/>
                      <fo:table-body>
                        <fo:table-row>
                          <fo:table-cell xsl:use-attribute-sets="padding-xs">
                            <fo:block xsl:use-attribute-sets="input-text align-center">
                              DD/MM/YYYY
                            </fo:block>
                          </fo:table-cell>
                          <fo:table-cell>
                            <fo:block>&#160;</fo:block>
                          </fo:table-cell>
                        </fo:table-row>
                      </fo:table-body>
                    </fo:table>
                  </fo:block>
                </xsl:when>

                <!-- Paragraph/Textarea -->
                <xsl:when test="$questionType = 'PARAGRAPH'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:block xsl:use-attribute-sets="input-textarea">&#160;</fo:block>
                  </fo:block>
                </xsl:when>

                <!-- Default fallback -->
                <xsl:otherwise>
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:block xsl:use-attribute-sets="input-text">&#160;</fo:block>
                  </fo:block>
                </xsl:otherwise>
              </xsl:choose>
            </fo:block-container>
          </xsl:for-each>

          <!-- Signature section positioned at bottom of last page -->
          <fo:block space-after.optimum="100%" space-after.minimum="16pt">
            <!--            border="1pt solid #e2e8f0"-->
            <!--            background-color="#f7fafc"-->
            <fo:block-container padding="8pt"
                                keep-together="always">
              <fo:table xsl:use-attribute-sets="table-full">
                <fo:table-column column-width="33%"/>
                <fo:table-column column-width="34%"/>
                <fo:table-column column-width="33%"/>
                <fo:table-body>
                  <fo:table-row>
                    <fo:table-cell xsl:use-attribute-sets="padding-sm">
                      <fo:block xsl:use-attribute-sets="text-label">Patient Signature:</fo:block>
                      <fo:block border-bottom="1pt solid #4a5568"
                                padding-top="16pt"
                                margin-top="4pt">&#160;
                      </fo:block>
                    </fo:table-cell>
                    <fo:table-cell xsl:use-attribute-sets="padding-sm">
                      <fo:block xsl:use-attribute-sets="text-label">Provider Signature:</fo:block>
                      <fo:block border-bottom="1pt solid #4a5568"
                                padding-top="16pt"
                                margin-top="4pt">&#160;
                      </fo:block>
                    </fo:table-cell>
                    <fo:table-cell xsl:use-attribute-sets="padding-sm">
                      <fo:block xsl:use-attribute-sets="text-label">Date:</fo:block>
                      <fo:block border-bottom="1pt solid #4a5568"
                                padding-top="16pt"
                                margin-top="4pt">&#160;
                      </fo:block>
                    </fo:table-cell>
                  </fo:table-row>
                </fo:table-body>
              </fo:table>
            </fo:block-container>
          </fo:block>

          <fo:block id="end-of-document"/>
        </fo:flow>
      </fo:page-sequence>
    </fo:root>
  </xsl:template>
</xsl:stylesheet>
