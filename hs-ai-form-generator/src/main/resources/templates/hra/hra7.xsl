<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">

  <!-- Common attribute sets for style reuse -->

  <!-- Padding styles -->
  <xsl:attribute-set name="padding-1pt">
    <xsl:attribute name="padding">1pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-2pt">
    <xsl:attribute name="padding">2pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-3pt">
    <xsl:attribute name="padding">3pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-4pt">
    <xsl:attribute name="padding">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-10pt">
    <xsl:attribute name="padding">10pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Font styles -->
  <xsl:attribute-set name="font-title">
    <xsl:attribute name="font-size">12pt</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
    <xsl:attribute name="color">#000000</xsl:attribute>
    <xsl:attribute name="text-align">center</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-question-title">
    <xsl:attribute name="font-size">10pt</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
    <xsl:attribute name="margin-bottom">1pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-question-description">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="margin-bottom">1pt</xsl:attribute>
    <xsl:attribute name="font-style">italic</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-label-bold">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-regular">
    <xsl:attribute name="font-size">10pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-small">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-small-italic">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="font-style">italic</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-extra-small">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-extra-small-bold">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-tiny">
    <xsl:attribute name="font-size">7pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-footer">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="color">#666666</xsl:attribute>
    <xsl:attribute name="padding-top">5pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-footer-center">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="color">#666666</xsl:attribute>
    <xsl:attribute name="text-align">center</xsl:attribute>
    <xsl:attribute name="padding-top">5pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="font-footer-right">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="color">#666666</xsl:attribute>
    <xsl:attribute name="text-align">right</xsl:attribute>
    <xsl:attribute name="padding-top">5pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Table and container styles -->
  <xsl:attribute-set name="patient-info-table">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="border">1pt solid #cccccc</xsl:attribute>
    <xsl:attribute name="background-color">#f5f5f5</xsl:attribute>
    <xsl:attribute name="margin-bottom">0.5cm</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="question-container">
    <xsl:attribute name="margin-bottom">2pt</xsl:attribute>
    <xsl:attribute name="padding-top">6pt</xsl:attribute>
    <xsl:attribute name="padding-bottom">4pt</xsl:attribute>
    <xsl:attribute name="padding-left">8pt</xsl:attribute>
    <xsl:attribute name="padding-right">8pt</xsl:attribute>
    <xsl:attribute name="border">1pt solid #cccccc</xsl:attribute>
    <xsl:attribute name="background-color">#f9f9f9</xsl:attribute>
    <xsl:attribute name="keep-together.within-page">always</xsl:attribute>
  </xsl:attribute-set>

  <!-- Input field styles -->
  <xsl:attribute-set name="input-field">
    <xsl:attribute name="border">1pt solid #336699</xsl:attribute>
    <xsl:attribute name="padding">2pt</xsl:attribute>
    <xsl:attribute name="background-color">white</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="input-field-multiline">
    <xsl:attribute name="border">1pt solid #336699</xsl:attribute>
    <xsl:attribute name="padding">2pt</xsl:attribute>
    <xsl:attribute name="background-color">white</xsl:attribute>
    <xsl:attribute name="height">4em</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="date-box">
    <xsl:attribute name="border">0.5pt solid black</xsl:attribute>
    <xsl:attribute name="padding">2pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Common table styles -->
  <xsl:attribute-set name="table-full-width">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="table-full-width-bordered">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="border-top">1pt solid #cccccc</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="table-header">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="table-option">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="margin-bottom">1pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Text alignment -->
  <xsl:attribute-set name="text-center">
    <xsl:attribute name="text-align">center</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="display-center">
    <xsl:attribute name="display-align">center</xsl:attribute>
  </xsl:attribute-set>

  <!-- Margins -->
  <xsl:attribute-set name="margin-top-1pt">
    <xsl:attribute name="margin-top">1pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-top-2pt">
    <xsl:attribute name="margin-top">2pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-top-4pt">
    <xsl:attribute name="margin-top">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-bottom-4pt">
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-left-2pt">
    <xsl:attribute name="margin-left">2pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Signature section -->
  <xsl:attribute-set name="signature-line">
    <xsl:attribute name="border-bottom">1pt solid #000000</xsl:attribute>
    <xsl:attribute name="padding-top">12pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Common templates -->
  <xsl:template name="checkbox">
    <fo:instream-foreign-object>
      <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8">
        <rect x="0.5" y="0.5" width="7" height="7" fill="white" stroke="#000000" stroke-width="1"/>
      </svg>
    </fo:instream-foreign-object>
  </xsl:template>

  <!-- Root template -->
  <xsl:template match="/">
    <fo:root>
      <fo:layout-master-set>
        <fo:simple-page-master master-name="A4"
                               page-height="29.7cm"
                               page-width="21cm"
                               margin-top="1cm"
                               margin-bottom="1cm"
                               margin-left="1.5cm"
                               margin-right="1.5cm">
          <fo:region-body margin-top="3.5cm" margin-bottom="2.5cm"/>
          <fo:region-before extent="4cm"/>
          <fo:region-after extent="2cm"/>
        </fo:simple-page-master>

        <!-- Create a page sequence master to handle automatic page breaks -->
        <fo:page-sequence-master master-name="document">
          <fo:repeatable-page-master-reference master-reference="A4"/>
        </fo:page-sequence-master>
      </fo:layout-master-set>

      <fo:page-sequence master-reference="document">
        <!-- Header with logo, form title, and patient info -->
        <fo:static-content flow-name="xsl-region-before">
          <!-- Logo and Title Row -->
          <fo:table xsl:use-attribute-sets="table-header">
            <fo:table-column column-width="20%"/>
            <fo:table-column column-width="60%"/>
            <fo:table-column column-width="20%"/>
            <fo:table-body>
              <fo:table-row>
                <!-- Logo in left column -->
                <fo:table-cell xsl:use-attribute-sets="display-center">
                  <fo:block>
                    <xsl:if test="formData/formStyle/logoUrl and string-length(formData/formStyle/logoUrl) > 0">
                      <!--    <fo:external-graphic src="url({formData/formStyle/logoUrl})" content-width="2cm" content-height="scale-to-fit" alt="Logo"/>-->
                    </xsl:if>
                    <!-- Empty block to ensure cell is valid -->
                    <fo:block>&#160;</fo:block>
                  </fo:block>
                </fo:table-cell>

                <!-- Title in middle column - Changed to black font -->
                <fo:table-cell xsl:use-attribute-sets="display-center">
                  <fo:block xsl:use-attribute-sets="font-title">
                    <xsl:value-of select="formData/formTemplate/title"/>
                  </fo:block>
                </fo:table-cell>

                <!-- Empty right column (QR code removed) -->
                <fo:table-cell xsl:use-attribute-sets="display-center">
                  <fo:block>&#160;</fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>

          <!-- Patient information section -->
          <fo:table xsl:use-attribute-sets="patient-info-table">
            <fo:table-column column-width="20%"/>
            <fo:table-column column-width="30%"/>
            <fo:table-column column-width="20%"/>
            <fo:table-column column-width="30%"/>
            <fo:table-body>
              <!-- Row 1: Name and DOB -->
              <fo:table-row>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-label-bold">Patient Name:</fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-regular">
                    <xsl:choose>
                      <xsl:when test="formData/patientInfo/name">
                        <xsl:value-of select="formData/patientInfo/name"/>
                      </xsl:when>
                    </xsl:choose>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-label-bold">Date of Birth:</fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-regular">
                    <xsl:choose>
                      <xsl:when test="formData/patientInfo/dateOfBirth">
                        <xsl:value-of select="formData/patientInfo/dateOfBirth"/>
                      </xsl:when>
                    </xsl:choose>
                  </fo:block>
                </fo:table-cell>
              </fo:table-row>

              <!-- Row 2: Patient ID and Date -->
              <fo:table-row>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-label-bold">Patient ID:</fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-regular">
                    <xsl:choose>
                      <xsl:when test="formData/patientInfo/policyCardNo">
                        <xsl:value-of select="formData/patientInfo/policyCardNo"/>
                      </xsl:when>
                    </xsl:choose>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block xsl:use-attribute-sets="font-label-bold">Assessment Date:</fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="padding-4pt">
                  <fo:block/>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>

          <!-- Border line at bottom of header -->
          <fo:block border-bottom="1pt solid #000000"/>
        </fo:static-content>

        <!-- Footer -->
        <fo:static-content flow-name="xsl-region-after">
          <fo:table xsl:use-attribute-sets="table-full-width-bordered">
            <fo:table-column column-width="33%"/>
            <fo:table-column column-width="34%"/>
            <fo:table-column column-width="33%"/>
            <fo:table-body>
              <fo:table-row>
                <fo:table-cell>
                  <fo:block xsl:use-attribute-sets="font-footer">
                    <xsl:value-of select="formData/formTemplate/formId"/>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block xsl:use-attribute-sets="font-footer-center">
                    <xsl:choose>
                      <xsl:when test="formData/patientInfo/name">
                        <xsl:value-of select="formData/patientInfo/name"/>
                      </xsl:when>
                    </xsl:choose>
                    -
                    <xsl:choose>
                      <xsl:when test="formData/patientInfo/policyCardNo">
                        <xsl:value-of select="formData/patientInfo/policyCardNo"/>
                      </xsl:when>
                    </xsl:choose>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell>
                  <fo:block xsl:use-attribute-sets="font-footer-right">
                    Page
                    <fo:page-number/>
                    of
                    <fo:page-number-citation-last ref-id="end-of-document"/>
                  </fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>
        </fo:static-content>

        <!-- Main content - Optimized for space -->
        <fo:flow flow-name="xsl-region-body" reference-orientation="0">
          <!-- Description -->
          <xsl:if test="formData/formTemplate/description">
            <fo:block xsl:use-attribute-sets="font-regular" margin-bottom="4pt" font-style="italic">
              <xsl:value-of select="formData/formTemplate/description"/>
            </fo:block>
          </xsl:if>

          <!-- Main question container -->
          <fo:block>
            <!-- Process each question with optimized layout -->
            <xsl:for-each select="formData/formTemplate/questions/questions">
              <xsl:variable name="questionNumber" select="position()"/>
              <xsl:variable name="questionType" select="type"/>
              <xsl:variable name="optionCount" select="count(options/options)"/>

              <!-- Question container - Reduced spacing and margin -->
              <!-- IMPORTANT: keep-together.within-page set to "always" for all questions -->
              <fo:block-container xsl:use-attribute-sets="question-container">

                <!-- Question title - Optimized spacing -->
                <fo:block xsl:use-attribute-sets="font-question-title">
                  <xsl:value-of select="$questionNumber"/>.
                  <xsl:value-of select="text"/>
                </fo:block>

                <!-- Question description if present -->
                <xsl:if test="description">
                  <fo:block xsl:use-attribute-sets="font-question-description">
                    <xsl:value-of select="description"/>
                  </fo:block>
                </xsl:if>

                <!-- Question options based on type - with optimized layouts -->
                <xsl:choose>
                  <!-- Multiple choice options - Optimized for 2 columns for ALL multiple choice questions -->
                  <xsl:when test="$questionType = 'MULTIPLE_CHOICE'">
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <!-- ALWAYS use 2-column layout regardless of option count or text length -->
                      <fo:table xsl:use-attribute-sets="table-full-width">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="50%"/>
                        <fo:table-body>
                          <!-- Process options in two columns -->
                          <xsl:for-each select="options/options[position() mod 2 = 1]">
                            <xsl:variable name="pos" select="position() * 2 - 1"/>
                            <fo:table-row>
                              <!-- First option in pair -->
                              <fo:table-cell>
                                <fo:table xsl:use-attribute-sets="table-option">
                                  <fo:table-column column-width="12pt"/>
                                  <fo:table-column column-width="proportional-column-width(1)"/>
                                  <fo:table-body>
                                    <fo:table-row>
                                      <fo:table-cell xsl:use-attribute-sets="display-center">
                                        <fo:block>
                                          <xsl:call-template name="checkbox"/>
                                        </fo:block>
                                      </fo:table-cell>
                                      <fo:table-cell>
                                        <fo:block xsl:use-attribute-sets="font-small margin-left-2pt padding-2pt">
                                          <xsl:value-of select="."/>
                                        </fo:block>
                                      </fo:table-cell>
                                    </fo:table-row>
                                  </fo:table-body>
                                </fo:table>
                              </fo:table-cell>

                              <!-- Second option in pair (if exists) -->
                              <fo:table-cell>
                                <xsl:choose>
                                  <xsl:when test="../options[$pos + 1]">
                                    <fo:table xsl:use-attribute-sets="table-option">
                                      <fo:table-column column-width="12pt"/>
                                      <fo:table-column column-width="proportional-column-width(1)"/>
                                      <fo:table-body>
                                        <fo:table-row>
                                          <fo:table-cell xsl:use-attribute-sets="display-center">
                                            <fo:block>
                                              <xsl:call-template name="checkbox"/>
                                            </fo:block>
                                          </fo:table-cell>
                                          <fo:table-cell>
                                            <fo:block xsl:use-attribute-sets="font-small margin-left-2pt padding-2pt">
                                              <xsl:value-of select="../options[$pos + 1]"/>
                                            </fo:block>
                                          </fo:table-cell>
                                        </fo:table-row>
                                      </fo:table-body>
                                    </fo:table>
                                  </xsl:when>
                                  <xsl:otherwise>
                                    <!-- Empty block to ensure cell is valid -->
                                    <fo:block>&#160;</fo:block>
                                  </xsl:otherwise>
                                </xsl:choose>
                              </fo:table-cell>
                            </fo:table-row>
                          </xsl:for-each>
                        </fo:table-body>
                      </fo:table>
                    </fo:block>
                  </xsl:when>

                  <!-- Rating scale - Compact version -->
                  <xsl:when test="$questionType = 'RATING_SCALE'">
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <xsl:variable name="optionCount" select="count(options/options)"/>

                      <xsl:choose>
                        <!-- Only create the table if there are options -->
                        <xsl:when test="$optionCount > 0">
                          <fo:table xsl:use-attribute-sets="table-full-width">
                            <!-- Create columns -->
                            <xsl:for-each select="options/options">
                              <fo:table-column column-width="proportional-column-width(1)"/>
                            </xsl:for-each>

                            <fo:table-body>
                              <!-- Scale labels in one compact row -->
                              <fo:table-row>
                                <fo:table-cell>
                                  <fo:block xsl:use-attribute-sets="font-tiny text-center">Low</fo:block>
                                </fo:table-cell>

                                <xsl:for-each select="options/options[position() > 1 and position() &lt; $optionCount]">
                                  <fo:table-cell>
                                    <fo:block>&#160;</fo:block>
                                  </fo:table-cell>
                                </xsl:for-each>

                                <xsl:if test="$optionCount > 1">
                                  <fo:table-cell>
                                    <fo:block xsl:use-attribute-sets="font-tiny text-center">High</fo:block>
                                  </fo:table-cell>
                                </xsl:if>
                              </fo:table-row>

                              <!-- Numbers row -->
                              <fo:table-row>
                                <xsl:for-each select="options/options">
                                  <fo:table-cell xsl:use-attribute-sets="text-center">
                                    <fo:block xsl:use-attribute-sets="font-extra-small">
                                      <xsl:value-of select="."/>
                                    </fo:block>
                                  </fo:table-cell>
                                </xsl:for-each>
                              </fo:table-row>

                              <!-- Checkbox row -->
                              <fo:table-row>
                                <xsl:for-each select="options/options">
                                  <fo:table-cell xsl:use-attribute-sets="text-center padding-1pt">
                                    <fo:block>
                                      <xsl:call-template name="checkbox"/>
                                    </fo:block>
                                  </fo:table-cell>
                                </xsl:for-each>
                              </fo:table-row>
                            </fo:table-body>
                          </fo:table>
                        </xsl:when>
                        <xsl:otherwise>
                          <!-- Fallback if no options are present -->
                          <fo:block>No rating options available</fo:block>
                        </xsl:otherwise>
                      </xsl:choose>
                    </fo:block>
                  </xsl:when>

                  <!-- CLINICAL_MEASURE type - with input field and units -->
                  <xsl:when test="$questionType = 'CLINICAL_MEASURE'">
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <fo:table xsl:use-attribute-sets="table-full-width">
                        <fo:table-column column-width="25%"/>
                        <fo:table-column column-width="75%"/>
                        <fo:table-body>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-1pt">
                              <fo:block xsl:use-attribute-sets="input-field">
                                <!-- Input field -->
                                <fo:block>&#160;</fo:block>
                              </fo:block>
                            </fo:table-cell>
                            <fo:table-cell xsl:use-attribute-sets="padding-1pt">
                              <fo:block xsl:use-attribute-sets="font-small-italic">
                                <xsl:choose>
                                  <xsl:when test="options/options[1]">
                                    <xsl:value-of select="options/options[1]"/>
                                  </xsl:when>
                                  <xsl:otherwise>units</xsl:otherwise>
                                </xsl:choose>
                              </fo:block>
                            </fo:table-cell>
                          </fo:table-row>
                        </fo:table-body>
                      </fo:table>
                    </fo:block>
                  </xsl:when>

                  <!-- TEXT input type -->

                  <xsl:when test="$questionType = 'TEXT'">
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <!-- Main input field with suffix text -->
                      <fo:table xsl:use-attribute-sets="table-full-width" margin-bottom="2pt">
                        <fo:table-column column-width="120pt"/>
                        <fo:table-column column-width="proportional-column-width(1)"/>
                        <fo:table-body>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-1pt">
                              <fo:block xsl:use-attribute-sets="input-field">
                                <!-- Single line text input field -->
                                <fo:block>&#160;</fo:block>
                              </fo:block>
                            </fo:table-cell>
                            <fo:table-cell xsl:use-attribute-sets="padding-10pt">
                              <fo:block xsl:use-attribute-sets="font-small-italic">
                                <!-- Display suffix text if available -->
                                <xsl:choose>
                                  <xsl:when test="suffixText">
                                    <xsl:value-of select="suffixText"/>
                                  </xsl:when>
                                  <xsl:when test="options/options[1]">
                                    <xsl:value-of select="options/options[1]"/>
                                  </xsl:when>
                                  <xsl:otherwise>
                                    <!-- No placeholder if no suffix text available -->
                                    <fo:block>&#160;</fo:block>
                                  </xsl:otherwise>
                                </xsl:choose>
                              </fo:block>
                            </fo:table-cell>
                          </fo:table-row>
                        </fo:table-body>
                      </fo:table>

                      <!-- "I am not sure" checkbox option -->
                      <fo:table xsl:use-attribute-sets="table-full-width">
                        <fo:table-column column-width="12pt"/>
                        <fo:table-column column-width="proportional-column-width(1)"/>
                        <fo:table-body>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="display-center">
                              <fo:block>
                                <xsl:call-template name="checkbox"/>
                              </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                              <fo:block xsl:use-attribute-sets="font-small margin-left-2pt padding-2pt">I am not sure</fo:block>
                            </fo:table-cell>
                          </fo:table-row>
                        </fo:table-body>
                      </fo:table>
                    </fo:block>
                  </xsl:when>

                  <!-- DATE input type -->
                  <xsl:when test="$questionType = 'DATE'">
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <fo:table xsl:use-attribute-sets="table-full-width">
                        <fo:table-column column-width="100%"/>
                        <fo:table-body>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-1pt">
                              <fo:block xsl:use-attribute-sets="input-field">
                                <!-- Date input field -->
                                <fo:block>DD/MM/YYYY</fo:block>
                              </fo:block>
                            </fo:table-cell>
                          </fo:table-row>
                        </fo:table-body>
                      </fo:table>
                    </fo:block>
                  </xsl:when>

                  <!-- PARAGRAPH type (for multi-line text) -->
                  <xsl:when test="$questionType = 'PARAGRAPH'">
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <fo:table xsl:use-attribute-sets="table-full-width">
                        <fo:table-column column-width="100%"/>
                        <fo:table-body>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-1pt">
                              <fo:block xsl:use-attribute-sets="input-field-multiline">
                                <!-- Multi-line text area -->
                                <fo:block>&#160;</fo:block>
                              </fo:block>
                            </fo:table-cell>
                          </fo:table-row>
                        </fo:table-body>
                      </fo:table>
                    </fo:block>
                  </xsl:when>

                  <!-- Default case for any other question types -->
                  <xsl:otherwise>
                    <fo:block xsl:use-attribute-sets="margin-top-2pt">
                      <fo:table xsl:use-attribute-sets="table-full-width">
                        <fo:table-column column-width="100%"/>
                        <fo:table-body>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-1pt">
                              <fo:block xsl:use-attribute-sets="input-field">
                                <!-- Default input field -->
                                <fo:block>&#160;</fo:block>
                              </fo:block>
                            </fo:table-cell>
                          </fo:table-row>
                        </fo:table-body>
                      </fo:table>
                    </fo:block>
                  </xsl:otherwise>
                </xsl:choose>
              </fo:block-container>

              <!-- Empty block for spacing between questions -->
              <fo:block>&#160;</fo:block>
            </xsl:for-each>
          </fo:block>

          <!-- Signature section - Compressed -->
          <fo:block-container xsl:use-attribute-sets="margin-top-4pt margin-bottom-4pt">
            <fo:table xsl:use-attribute-sets="table-full-width">
              <fo:table-column column-width="33%"/>
              <fo:table-column column-width="34%"/>
              <fo:table-column column-width="33%"/>
              <fo:table-body>
                <fo:table-row>
                  <fo:table-cell xsl:use-attribute-sets="padding-3pt">
                    <fo:block xsl:use-attribute-sets="font-extra-small-bold">Patient Signature:</fo:block>
                    <fo:block xsl:use-attribute-sets="signature-line">&#160;</fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="padding-3pt">
                    <fo:block xsl:use-attribute-sets="font-extra-small-bold">Provider Signature:</fo:block>
                    <fo:block xsl:use-attribute-sets="signature-line">&#160;</fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="padding-3pt">
                    <fo:block xsl:use-attribute-sets="font-extra-small-bold">Date:</fo:block>
                    <fo:block xsl:use-attribute-sets="signature-line">&#160;</fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block-container>

          <fo:block id="end-of-document"/>
        </fo:flow>
      </fo:page-sequence>
    </fo:root>
  </xsl:template>
</xsl:stylesheet>
