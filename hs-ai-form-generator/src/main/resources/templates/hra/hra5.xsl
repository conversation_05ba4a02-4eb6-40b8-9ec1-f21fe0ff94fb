<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">

    <!-- Root template -->
    <xsl:template match="/">
        <fo:root>
            <fo:layout-master-set>
                <fo:simple-page-master master-name="A4"
                                       page-height="29.7cm"
                                       page-width="21cm"
                                       margin-top="1cm"
                                       margin-bottom="1cm"
                                       margin-left="1.5cm"
                                       margin-right="1.5cm">
                    <fo:region-body margin-top="4.5cm" margin-bottom="2.5cm"/>
                    <fo:region-before extent="4cm"/>
                    <fo:region-after extent="2cm"/>
                </fo:simple-page-master>

                <!-- Create a page sequence master to handle automatic page breaks -->
                <fo:page-sequence-master master-name="document">
                    <fo:repeatable-page-master-reference master-reference="A4"/>
                </fo:page-sequence-master>
            </fo:layout-master-set>

            <fo:page-sequence master-reference="document">
                <!-- Header with logo, form title, and patient info -->
                <fo:static-content flow-name="xsl-region-before">
                    <!-- Logo and Title Row -->
                    <fo:table width="100%" table-layout="fixed" margin-bottom="0.3cm">
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="60%"/>
                        <fo:table-column column-width="20%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <!-- Logo in left column -->
                                <fo:table-cell display-align="center">
                                    <fo:block>
                                        <xsl:if test="formData/formStyle/logoUrl and string-length(formData/formStyle/logoUrl) > 0">
                                            <!--                                            <fo:external-graphic src="url({formData/formStyle/logoUrl})" content-width="2cm" content-height="scale-to-fit" alt="Logo"/>-->
                                        </xsl:if>
                                        <!-- Empty block to ensure cell is valid -->
                                        <fo:block>&#160;</fo:block>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Title in middle column - Changed to black font -->
                                <fo:table-cell display-align="center">
                                    <fo:block font-size="16pt" font-weight="bold" color="#000000" text-align="center">
                                        <xsl:value-of select="formData/formTemplate/title"/>
                                    </fo:block>
                                    <fo:block font-size="9pt" color="#000000" text-align="center">
                                        Form ID: <xsl:value-of select="formData/formTemplate/formId"/>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Empty right column (QR code removed) -->
                                <fo:table-cell display-align="center">
                                    <fo:block>&#160;</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- Patient information section -->
                    <fo:table width="100%" table-layout="fixed" border="1pt solid #cccccc" background-color="#f5f5f5" margin-bottom="0.5cm">
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-body>
                            <!-- Row 1: Name and DOB -->
                            <fo:table-row>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Patient Name:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/name">
                                                <xsl:value-of select="formData/patientInfo/name"/>
                                            </xsl:when>
                                            <xsl:otherwise>John Doe</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Date of Birth:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/dateOfBirth">
                                                <xsl:value-of select="formData/patientInfo/dateOfBirth"/>
                                            </xsl:when>
                                            <xsl:otherwise>01/01/1980</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <!-- Row 2: Patient ID and Date -->
                            <fo:table-row>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Patient ID:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/id">
                                                <xsl:value-of select="formData/patientInfo/id"/>
                                            </xsl:when>
                                            <xsl:otherwise>PT-12345678</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Assessment Date:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/assessmentDate">
                                                <xsl:value-of select="formData/assessmentDate"/>
                                            </xsl:when>
                                            <xsl:otherwise>DD/MM/YYYY</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- Border line at bottom of header -->
                    <fo:block border-bottom="2pt solid #336699"></fo:block>
                </fo:static-content>

                <!-- Footer -->
                <fo:static-content flow-name="xsl-region-after">
                    <fo:table width="100%" table-layout="fixed" border-top="1pt solid #cccccc">
                        <fo:table-column column-width="33%"/>
                        <fo:table-column column-width="34%"/>
                        <fo:table-column column-width="33%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" padding-top="5pt">
                                        <xsl:value-of select="formData/formTemplate/formId"/>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" text-align="center" padding-top="5pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/name">
                                                <xsl:value-of select="formData/patientInfo/name"/>
                                            </xsl:when>
                                            <xsl:otherwise>John Doe</xsl:otherwise>
                                        </xsl:choose>
                                        -
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/id">
                                                <xsl:value-of select="formData/patientInfo/id"/>
                                            </xsl:when>
                                            <xsl:otherwise>PT-12345678</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" text-align="right" padding-top="5pt">
                                        Page <fo:page-number/> of <fo:page-number-citation-last ref-id="end-of-document"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:static-content>

                <!-- Main content - Optimized for space -->
                <fo:flow flow-name="xsl-region-body" reference-orientation="0">
                    <!-- Description -->
                    <xsl:if test="formData/formTemplate/description">
                        <fo:block font-size="10pt" margin-bottom="4pt" font-style="italic">
                            <xsl:value-of select="formData/formTemplate/description"/>
                        </fo:block>
                    </xsl:if>

                    <!-- Main question container -->
                    <fo:block>
                        <!-- Process each question with optimized layout -->
                        <xsl:for-each select="formData/formTemplate/questions/questions">
                            <xsl:variable name="questionNumber" select="position()"/>
                            <xsl:variable name="questionType" select="type"/>
                            <xsl:variable name="optionCount" select="count(options/options)"/>

                            <!-- Question container - Reduced spacing and margin -->
                            <!-- IMPORTANT: keep-together.within-page set to "always" for all questions -->
                            <fo:block-container margin-bottom="2pt"
                                                padding="3pt"
                                                border="1pt solid #cccccc"
                                                background-color="#f9f9f9"
                                                keep-together.within-page="always">

                                <!-- Question title - Optimized spacing -->
                                <fo:block font-size="10pt" font-weight="bold" margin-bottom="1pt">
                                    <xsl:value-of select="$questionNumber"/>. <xsl:value-of select="text"/>
                                </fo:block>

                                <!-- Question description if present -->
                                <xsl:if test="description">
                                    <fo:block font-size="9pt" margin-bottom="1pt" font-style="italic">
                                        <xsl:value-of select="description"/>
                                    </fo:block>
                                </xsl:if>

                                <!-- Question options based on type - with optimized layouts -->
                                <xsl:choose>
                                    <!-- Multiple choice options - Optimized for 2 columns for ALL multiple choice questions -->
                                    <xsl:when test="$questionType = 'MULTIPLE_CHOICE'">
                                        <fo:block margin-top="1pt">
                                            <!-- ALWAYS use 2-column layout regardless of option count or text length -->
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="50%"/>
                                                <fo:table-column column-width="50%"/>
                                                <fo:table-body>
                                                    <!-- Process options in two columns -->
                                                    <xsl:for-each select="options/options[position() mod 2 = 1]">
                                                        <xsl:variable name="pos" select="position() * 2 - 1"/>
                                                        <fo:table-row>
                                                            <!-- First option in pair -->
                                                            <fo:table-cell>
                                                                <fo:table width="100%" table-layout="fixed" margin-bottom="1pt">
                                                                    <fo:table-column column-width="12pt"/>
                                                                    <fo:table-column column-width="proportional-column-width(1)"/>
                                                                    <fo:table-body>
                                                                        <fo:table-row>
                                                                            <fo:table-cell display-align="center">
                                                                                <fo:block>
                                                                                    <fo:instream-foreign-object>
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8">
                                                                                            <rect x="0.5" y="0.5" width="7" height="7" fill="white" stroke="#000000" stroke-width="1"/>
                                                                                        </svg>
                                                                                    </fo:instream-foreign-object>
                                                                                </fo:block>
                                                                            </fo:table-cell>
                                                                            <fo:table-cell>
                                                                                <fo:block font-size="9pt" margin-left="2pt">
                                                                                    <xsl:value-of select="."/>
                                                                                </fo:block>
                                                                            </fo:table-cell>
                                                                        </fo:table-row>
                                                                    </fo:table-body>
                                                                </fo:table>
                                                            </fo:table-cell>

                                                            <!-- Second option in pair (if exists) -->
                                                            <fo:table-cell>
                                                                <xsl:choose>
                                                                    <xsl:when test="../options[$pos + 1]">
                                                                        <fo:table width="100%" table-layout="fixed" margin-bottom="1pt">
                                                                            <fo:table-column column-width="12pt"/>
                                                                            <fo:table-column column-width="proportional-column-width(1)"/>
                                                                            <fo:table-body>
                                                                                <fo:table-row>
                                                                                    <fo:table-cell display-align="center">
                                                                                        <fo:block>
                                                                                            <fo:instream-foreign-object>
                                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8">
                                                                                                    <rect x="0.5" y="0.5" width="7" height="7" fill="white" stroke="#000000" stroke-width="1"/>
                                                                                                </svg>
                                                                                            </fo:instream-foreign-object>
                                                                                        </fo:block>
                                                                                    </fo:table-cell>
                                                                                    <fo:table-cell>
                                                                                        <fo:block font-size="9pt" margin-left="2pt">
                                                                                            <xsl:value-of select="../options[$pos + 1]"/>
                                                                                        </fo:block>
                                                                                    </fo:table-cell>
                                                                                </fo:table-row>
                                                                            </fo:table-body>
                                                                        </fo:table>
                                                                    </xsl:when>
                                                                    <xsl:otherwise>
                                                                        <!-- Empty block to ensure cell is valid -->
                                                                        <fo:block>&#160;</fo:block>
                                                                    </xsl:otherwise>
                                                                </xsl:choose>
                                                            </fo:table-cell>
                                                        </fo:table-row>
                                                    </xsl:for-each>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- Rating scale - Compact version -->
                                    <xsl:when test="$questionType = 'RATING_SCALE'">
                                        <fo:block margin-top="1pt">
                                            <xsl:variable name="optionCount" select="count(options/options)"/>

                                            <xsl:choose>
                                                <!-- Only create the table if there are options -->
                                                <xsl:when test="$optionCount > 0">
                                                    <fo:table width="100%" table-layout="fixed">
                                                        <!-- Create columns -->
                                                        <xsl:for-each select="options/options">
                                                            <fo:table-column column-width="proportional-column-width(1)"/>
                                                        </xsl:for-each>

                                                        <fo:table-body>
                                                            <!-- Scale labels in one compact row -->
                                                            <fo:table-row>
                                                                <fo:table-cell>
                                                                    <fo:block font-size="7pt" text-align="center">Low</fo:block>
                                                                </fo:table-cell>

                                                                <xsl:for-each select="options/options[position() > 1 and position() &lt; $optionCount]">
                                                                    <fo:table-cell>
                                                                        <fo:block>&#160;</fo:block>
                                                                    </fo:table-cell>
                                                                </xsl:for-each>

                                                                <xsl:if test="$optionCount > 1">
                                                                    <fo:table-cell>
                                                                        <fo:block font-size="7pt" text-align="center">High</fo:block>
                                                                    </fo:table-cell>
                                                                </xsl:if>
                                                            </fo:table-row>

                                                            <!-- Numbers row -->
                                                            <fo:table-row>
                                                                <xsl:for-each select="options/options">
                                                                    <fo:table-cell text-align="center">
                                                                        <fo:block font-size="8pt">
                                                                            <xsl:value-of select="."/>
                                                                        </fo:block>
                                                                    </fo:table-cell>
                                                                </xsl:for-each>
                                                            </fo:table-row>

                                                            <!-- Checkbox row -->
                                                            <fo:table-row>
                                                                <xsl:for-each select="options/options">
                                                                    <fo:table-cell text-align="center" padding="1pt">
                                                                        <fo:block>
                                                                            <fo:instream-foreign-object>
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8">
                                                                                    <rect x="0.5" y="0.5" width="7" height="7" fill="white" stroke="#000000" stroke-width="1"/>
                                                                                </svg>
                                                                            </fo:instream-foreign-object>
                                                                        </fo:block>
                                                                    </fo:table-cell>
                                                                </xsl:for-each>
                                                            </fo:table-row>
                                                        </fo:table-body>
                                                    </fo:table>
                                                </xsl:when>
                                                <xsl:otherwise>
                                                    <!-- Fallback if no options are present -->
                                                    <fo:block>No rating options available</fo:block>
                                                </xsl:otherwise>
                                            </xsl:choose>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- CLINICAL_MEASURE type - with input field and units -->
                                    <xsl:when test="$questionType = 'CLINICAL_MEASURE'">
                                        <fo:block margin-top="1pt">
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="70%"/>
                                                <fo:table-column column-width="30%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1pt">
                                                            <fo:block border="1pt solid #336699" padding="2pt" background-color="white">
                                                                <!-- Input field -->
                                                                <fo:block>&#160;</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1pt">
                                                            <fo:block font-size="9pt" font-style="italic">
                                                                <xsl:choose>
                                                                    <xsl:when test="options/options[1]">
                                                                        <xsl:value-of select="options/options[1]"/>
                                                                    </xsl:when>
                                                                    <xsl:otherwise>units</xsl:otherwise>
                                                                </xsl:choose>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- TEXT input type -->

                                    <xsl:when test="$questionType = 'TEXT'">
                                        <fo:block margin-top="1pt">
                                            <!-- Main input field with suffix text -->
                                            <fo:table width="100%" table-layout="fixed" margin-bottom="2pt">
                                                <fo:table-column column-width="70%"/>
                                                <fo:table-column column-width="30%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1pt">
                                                            <fo:block border="1pt solid #336699" padding="2pt" background-color="white">
                                                                <!-- Single line text input field -->
                                                                <fo:block>&#160;</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="10pt">
                                                            <fo:block font-size="9pt" font-style="italic">
                                                                <!-- Display suffix text if available -->
                                                                <xsl:choose>
                                                                    <xsl:when test="suffixText">
                                                                        <xsl:value-of select="suffixText"/>
                                                                    </xsl:when>
                                                                    <xsl:when test="options/options[1]">
                                                                        <xsl:value-of select="options/options[1]"/>
                                                                    </xsl:when>
                                                                    <xsl:otherwise>
                                                                        <!-- No placeholder if no suffix text available -->
                                                                        <fo:block>&#160;</fo:block>
                                                                    </xsl:otherwise>
                                                                </xsl:choose>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>

                                            <!-- "I am not sure" checkbox option -->
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="12pt"/>
                                                <fo:table-column column-width="proportional-column-width(1)"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell display-align="center">
                                                            <fo:block>
                                                                <fo:instream-foreign-object>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 8 8">
                                                                        <rect x="0.5" y="0.5" width="7" height="7" fill="white" stroke="#000000" stroke-width="1"/>
                                                                    </svg>
                                                                </fo:instream-foreign-object>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell>
                                                            <fo:block font-size="9pt" margin-left="2pt">I am not sure</fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- DATE input type -->
                                    <xsl:when test="$questionType = 'DATE'">
                                        <fo:block margin-top="1pt">
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="100%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1pt">
                                                            <fo:block border="1pt solid #336699" padding="2pt" background-color="white">
                                                                <!-- Date input field -->
                                                                <fo:block>DD/MM/YYYY</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- PARAGRAPH type (for multi-line text) -->
                                    <xsl:when test="$questionType = 'PARAGRAPH'">
                                        <fo:block margin-top="1pt">
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="100%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1pt">
                                                            <fo:block border="1pt solid #336699" padding="2pt" background-color="white" height="4em">
                                                                <!-- Multi-line text area -->
                                                                <fo:block>&#160;</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:when>

                                    <!-- Default case for any other question types -->
                                    <xsl:otherwise>
                                        <fo:block margin-top="1pt">
                                            <fo:table width="100%" table-layout="fixed">
                                                <fo:table-column column-width="100%"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1pt">
                                                            <fo:block border="1pt solid #336699" padding="2pt" background-color="white">
                                                                <!-- Default input field -->
                                                                <fo:block>&#160;</fo:block>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </xsl:otherwise>
                                </xsl:choose>
                            </fo:block-container>

                            <!-- Empty block for spacing between questions -->
                            <fo:block>&#160;</fo:block>
                        </xsl:for-each>
                    </fo:block>

                    <!-- Signature section - Compressed -->
                    <fo:block-container margin-top="4pt" margin-bottom="4pt">
                        <fo:table width="100%" table-layout="fixed">
                            <fo:table-column column-width="33%"/>
                            <fo:table-column column-width="34%"/>
                            <fo:table-column column-width="33%"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell padding="3pt">
                                        <fo:block font-size="8pt" font-weight="bold">Patient Signature:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="12pt">&#160;</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding="3pt">
                                        <fo:block font-size="8pt" font-weight="bold">Provider Signature:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="12pt">&#160;</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding="3pt">
                                        <fo:block font-size="8pt" font-weight="bold">Date:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="12pt">&#160;</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block-container>

                    <fo:block id="end-of-document"/>
                </fo:flow>
            </fo:page-sequence>
        </fo:root>
    </xsl:template>
</xsl:stylesheet>