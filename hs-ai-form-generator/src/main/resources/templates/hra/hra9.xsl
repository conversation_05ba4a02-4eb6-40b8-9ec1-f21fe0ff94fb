<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">

  <!-- ===== COMMON ATTRIBUTE SETS ===== -->

  <!-- Base font family for consistency -->
  <xsl:attribute-set name="base-font">
    <xsl:attribute name="font-family">Arial, Helvetica, sans-serif</xsl:attribute>
  </xsl:attribute-set>

  <!-- Spacing utilities -->
  <xsl:attribute-set name="padding-xs" use-attribute-sets="base-font">
    <xsl:attribute name="padding">2pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-sm" use-attribute-sets="base-font">
    <xsl:attribute name="padding">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-md" use-attribute-sets="base-font">
    <xsl:attribute name="padding">6pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="padding-lg" use-attribute-sets="base-font">
    <xsl:attribute name="padding">10pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-bottom-sm">
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-bottom-md">
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="margin-top-sm">
    <xsl:attribute name="margin-top">4pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Typography hierarchy -->
  <xsl:attribute-set name="text-title" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">14pt</xsl:attribute>
    <xsl:attribute name="font-weight">bold</xsl:attribute>
    <xsl:attribute name="color">#1a365d</xsl:attribute>
    <xsl:attribute name="text-align">center</xsl:attribute>
    <xsl:attribute name="line-height">1.4</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-question-title" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">10pt</xsl:attribute>
    <xsl:attribute name="font-weight">600</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
    <xsl:attribute name="line-height">1.3</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-question-description" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="color">#4a5568</xsl:attribute>
    <xsl:attribute name="font-style">italic</xsl:attribute>
    <xsl:attribute name="margin-bottom">3pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-label" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="font-weight">600</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-body" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">9pt</xsl:attribute>
    <xsl:attribute name="color">#2d3748</xsl:attribute>
    <xsl:attribute name="line-height">1.2</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-small" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="color">#4a5568</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="text-footer" use-attribute-sets="base-font">
    <xsl:attribute name="font-size">8pt</xsl:attribute>
    <xsl:attribute name="color">#718096</xsl:attribute>
  </xsl:attribute-set>

  <!-- Layout components -->
  <xsl:attribute-set name="header-table">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="divider-container">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="patient-info-container">
    <xsl:attribute name="border">1pt solid #e2e8f0</xsl:attribute>
    <xsl:attribute name="background-color">#f7fafc</xsl:attribute>
    <xsl:attribute name="margin-bottom">4pt</xsl:attribute>
    <xsl:attribute name="padding">8pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="patient-info-table">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="question-container">
    <xsl:attribute name="margin-bottom">8pt</xsl:attribute>
    <xsl:attribute name="padding">8pt</xsl:attribute>
    <xsl:attribute name="border">1pt solid #e2e8f0</xsl:attribute>
    <xsl:attribute name="background-color">#ffffff</xsl:attribute>
    <xsl:attribute name="keep-together.within-page">always</xsl:attribute>
  </xsl:attribute-set>

  <!-- Input field styles -->
  <xsl:attribute-set name="input-base">
    <xsl:attribute name="border">1pt solid #cbd5e0</xsl:attribute>
    <xsl:attribute name="background-color">#ffffff</xsl:attribute>
    <xsl:attribute name="padding">4pt</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="input-text" use-attribute-sets="input-base">
    <xsl:attribute name="height">1.2em</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="input-textarea" use-attribute-sets="input-base">
    <xsl:attribute name="height">4em</xsl:attribute>
  </xsl:attribute-set>

  <!-- Table styles -->
  <xsl:attribute-set name="table-full">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="table-options">
    <xsl:attribute name="width">100%</xsl:attribute>
    <xsl:attribute name="table-layout">fixed</xsl:attribute>
    <xsl:attribute name="margin-top">4pt</xsl:attribute>
  </xsl:attribute-set>

  <!-- Alignment utilities -->
  <xsl:attribute-set name="align-center">
    <xsl:attribute name="text-align">center</xsl:attribute>
    <xsl:attribute name="display-align">center</xsl:attribute>
  </xsl:attribute-set>

  <xsl:attribute-set name="align-right">
    <xsl:attribute name="text-align">right</xsl:attribute>
  </xsl:attribute-set>

  <!-- ===== REUSABLE TEMPLATES ===== -->

  <!-- Enhanced checkbox with better styling -->
  <xsl:template name="checkbox">
    <fo:instream-foreign-object>
      <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
        <rect x="0.5" y="0.5" width="9" height="9"
              fill="#ffffff"
              stroke="#4a5568"
              stroke-width="1"
              rx="1"/>
      </svg>
    </fo:instream-foreign-object>
  </xsl:template>

  <!-- Option row template for multiple choice -->
  <xsl:template name="option-row">
    <xsl:param name="option-text"/>
    <fo:table xsl:use-attribute-sets="table-full">
      <fo:table-column column-width="16pt"/>
      <fo:table-column column-width="proportional-column-width(1)"/>
      <fo:table-body>
        <fo:table-row>
          <fo:table-cell xsl:use-attribute-sets="align-center">
            <fo:block>
              <xsl:call-template name="checkbox"/>
            </fo:block>
          </fo:table-cell>
          <fo:table-cell xsl:use-attribute-sets="padding-xs">
            <fo:block xsl:use-attribute-sets="text-body">
              <xsl:value-of select="$option-text"/>
            </fo:block>
          </fo:table-cell>
        </fo:table-row>
      </fo:table-body>
    </fo:table>
  </xsl:template>

  <!-- Rating scale template -->
  <xsl:template name="rating-scale">
    <xsl:param name="options"/>
    <xsl:variable name="optionCount" select="count($options)"/>

    <fo:table xsl:use-attribute-sets="table-options">
      <!-- Dynamic columns based on option count -->
      <xsl:for-each select="$options">
        <fo:table-column column-width="proportional-column-width(1)"/>
      </xsl:for-each>

      <fo:table-body>
        <!-- Scale labels -->
        <fo:table-row>
          <fo:table-cell>
            <fo:block xsl:use-attribute-sets="text-small align-center">Low</fo:block>
          </fo:table-cell>
          <xsl:for-each select="$options[position() > 1 and position() &lt; $optionCount]">
            <fo:table-cell>
              <fo:block>&#160;</fo:block>
            </fo:table-cell>
          </xsl:for-each>
          <xsl:if test="$optionCount > 1">
            <fo:table-cell>
              <fo:block xsl:use-attribute-sets="text-small align-center">High</fo:block>
            </fo:table-cell>
          </xsl:if>
        </fo:table-row>

        <!-- Numbers -->
        <fo:table-row>
          <xsl:for-each select="$options">
            <fo:table-cell xsl:use-attribute-sets="align-center padding-xs">
              <fo:block xsl:use-attribute-sets="text-body">
                <xsl:value-of select="."/>
              </fo:block>
            </fo:table-cell>
          </xsl:for-each>
        </fo:table-row>

        <!-- Checkboxes -->
        <fo:table-row>
          <xsl:for-each select="$options">
            <fo:table-cell xsl:use-attribute-sets="align-center padding-xs">
              <fo:block>
                <xsl:call-template name="checkbox"/>
              </fo:block>
            </fo:table-cell>
          </xsl:for-each>
        </fo:table-row>
      </fo:table-body>
    </fo:table>
  </xsl:template>

  <!-- Patient info row template -->
  <xsl:template name="patient-info-row">
    <xsl:param name="label1"/>
    <xsl:param name="value1"/>
    <xsl:param name="label2"/>
    <xsl:param name="value2"/>

    <fo:table-row>
      <fo:table-cell xsl:use-attribute-sets="padding-sm">
        <fo:block xsl:use-attribute-sets="text-label">
          <xsl:value-of select="$label1"/>
        </fo:block>
      </fo:table-cell>
      <fo:table-cell xsl:use-attribute-sets="padding-sm">
        <fo:block xsl:use-attribute-sets="text-body">
          <xsl:value-of select="$value1"/>
        </fo:block>
      </fo:table-cell>
      <fo:table-cell xsl:use-attribute-sets="padding-sm">
        <fo:block xsl:use-attribute-sets="text-label">
          <xsl:value-of select="$label2"/>
        </fo:block>
      </fo:table-cell>
      <fo:table-cell xsl:use-attribute-sets="padding-sm">
        <fo:block xsl:use-attribute-sets="text-body">
          <xsl:value-of select="$value2"/>
        </fo:block>
      </fo:table-cell>
    </fo:table-row>
  </xsl:template>

  <!-- ===== MAIN TEMPLATE ===== -->
  <xsl:template match="/">
    <fo:root>
      <fo:layout-master-set>
        <fo:simple-page-master master-name="A4"
                               page-height="29.7cm"
                               page-width="21cm"
                               margin-top="1cm"
                               margin-bottom="1cm"
                               margin-left="1.5cm"
                               margin-right="1.5cm">
          <fo:region-body margin-top="4cm" margin-bottom="2.5cm"/>
          <fo:region-before extent="4cm"/>
          <fo:region-after extent="2cm"/>
        </fo:simple-page-master>

        <fo:page-sequence-master master-name="document">
          <fo:repeatable-page-master-reference master-reference="A4"/>
        </fo:page-sequence-master>
      </fo:layout-master-set>

      <fo:page-sequence master-reference="document">
        <!-- ===== HEADER ===== -->
        <fo:static-content flow-name="xsl-region-before">
          <!-- Title section -->
          <fo:table xsl:use-attribute-sets="header-table">
            <fo:table-column column-width="20%"/>
            <fo:table-column column-width="60%"/>
            <fo:table-column column-width="20%"/>
            <fo:table-body>
              <fo:table-row>
                <fo:table-cell xsl:use-attribute-sets="align-center">
                  <fo:block>
                    <xsl:if test="formData/formStyle/logoUrl and string-length(formData/formStyle/logoUrl) > 0">
                      <fo:external-graphic src="url({formData/formStyle/logoUrl})"
                                           content-width="2.5cm"
                                           content-height="scale-to-fit"
                                           alt="Logo"/>
                    </xsl:if>
                    <fo:block>&#160;</fo:block>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="align-center">
                  <fo:block xsl:use-attribute-sets="text-title">
                    <xsl:value-of select="formData/formTemplate/title"/>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="align-center">
                  <fo:block>&#160;</fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>

          <!-- Patient information -->
          <fo:block xsl:use-attribute-sets="patient-info-container">
            <fo:table xsl:use-attribute-sets="patient-info-table">
              <fo:table-column column-width="15%"/>
              <fo:table-column column-width="35%"/>
              <fo:table-column column-width="20%"/>
              <fo:table-column column-width="30%"/>
              <fo:table-body>
                <xsl:call-template name="patient-info-row">
                  <xsl:with-param name="label1">Patient Name:</xsl:with-param>
                  <xsl:with-param name="value1" select="formData/patientInfo/name"/>
                  <xsl:with-param name="label2">Date of Birth:</xsl:with-param>
                  <xsl:with-param name="value2" select="formData/patientInfo/dateOfBirth"/>
                </xsl:call-template>
                <xsl:call-template name="patient-info-row">
                  <xsl:with-param name="label1">Patient ID:</xsl:with-param>
                  <xsl:with-param name="value1" select="formData/patientInfo/policyCardNo"/>
                  <xsl:with-param name="label2">Assessment Date:</xsl:with-param>
                  <xsl:with-param name="value2">&#160;</xsl:with-param>
                </xsl:call-template>
              </fo:table-body>
            </fo:table>
          </fo:block>

          <fo:block border-bottom="2pt solid #1a365d" margin-top="12pt" margin-bottom="4pt" padding-left="8pt" padding-right="8pt"/>
        </fo:static-content>

        <!-- ===== FOOTER ===== -->
        <fo:static-content flow-name="xsl-region-after">
          <fo:block border-top="1pt solid #e2e8f0" padding-top="8pt"/>
          <fo:table xsl:use-attribute-sets="table-full">
            <fo:table-column column-width="33%"/>
            <fo:table-column column-width="34%"/>
            <fo:table-column column-width="33%"/>
            <fo:table-body>
              <fo:table-row>
                <fo:table-cell>
                  <fo:block xsl:use-attribute-sets="text-footer">
                    <xsl:value-of select="formData/formTemplate/formId"/>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="align-center">
                  <fo:block xsl:use-attribute-sets="text-footer">
                    <xsl:value-of select="formData/patientInfo/name"/> -
                    <xsl:value-of select="formData/patientInfo/policyCardNo"/>
                  </fo:block>
                </fo:table-cell>
                <fo:table-cell xsl:use-attribute-sets="align-right">
                  <fo:block xsl:use-attribute-sets="text-footer">
                    Page
                    <fo:page-number/>
                    of
                    <fo:page-number-citation-last ref-id="end-of-document"/>
                  </fo:block>
                </fo:table-cell>
              </fo:table-row>
            </fo:table-body>
          </fo:table>
        </fo:static-content>

        <!-- ===== MAIN CONTENT ===== -->
        <fo:flow flow-name="xsl-region-body">
          <!-- Form description -->
          <xsl:if test="formData/formTemplate/description">
            <fo:block xsl:use-attribute-sets="text-body margin-bottom-md"
                      font-style="italic"
                      color="#4a5568">
              <xsl:value-of select="formData/formTemplate/description"/>
            </fo:block>
          </xsl:if>

          <!-- Questions -->
          <xsl:for-each select="formData/formTemplate/questions/questions">
            <xsl:variable name="questionNumber" select="position()"/>
            <xsl:variable name="questionType" select="type"/>

            <fo:block-container xsl:use-attribute-sets="question-container">
              <!-- Question title -->
              <fo:block xsl:use-attribute-sets="text-question-title margin-bottom-sm">
                <xsl:value-of select="$questionNumber"/>.
                <xsl:value-of select="text"/>
              </fo:block>

              <!-- Question description -->
              <xsl:if test="description and string-length(description) > 0">
                <fo:block xsl:use-attribute-sets="text-question-description">
                  <xsl:value-of select="description"/>
                </fo:block>
              </xsl:if>

              <!-- Question content based on type -->
              <xsl:choose>
                <!-- Multiple Choice -->
                <xsl:when test="$questionType = 'MULTIPLE_CHOICE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options">
                      <fo:table-column column-width="50%"/>
                      <fo:table-column column-width="50%"/>
                      <fo:table-body>
                        <xsl:for-each select="options/options[position() mod 2 = 1]">
                          <xsl:variable name="pos" select="position() * 2 - 1"/>
                          <fo:table-row>
                            <fo:table-cell xsl:use-attribute-sets="padding-xs">
                              <xsl:call-template name="option-row">
                                <xsl:with-param name="option-text" select="."/>
                              </xsl:call-template>
                            </fo:table-cell>
                            <fo:table-cell xsl:use-attribute-sets="padding-xs">
                              <xsl:choose>
                                <xsl:when test="../options[$pos + 1]">
                                  <xsl:call-template name="option-row">
                                    <xsl:with-param name="option-text" select="../options[$pos + 1]"/>
                                  </xsl:call-template>
                                </xsl:when>
                                <xsl:otherwise>
                                  <fo:block>&#160;</fo:block>
                                </xsl:otherwise>
                              </xsl:choose>
                            </fo:table-cell>
                          </fo:table-row>
                        </xsl:for-each>
                      </fo:table-body>
                    </fo:table>
                  </fo:block>
                </xsl:when>

                <!-- Rating Scale -->
                <xsl:when test="$questionType = 'RATING_SCALE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <xsl:call-template name="rating-scale">
                      <xsl:with-param name="options" select="options/options"/>
                    </xsl:call-template>
                  </fo:block>
                </xsl:when>

                <!-- Clinical Measure -->
                <xsl:when test="$questionType = 'CLINICAL_MEASURE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options">
                      <fo:table-column column-width="25%"/>
                      <fo:table-column column-width="75%"/>
                      <fo:table-body>
                        <fo:table-row>
                          <fo:table-cell xsl:use-attribute-sets="padding-xs">
                            <fo:block xsl:use-attribute-sets="input-text">&#160;</fo:block>
                          </fo:table-cell>
                          <fo:table-cell xsl:use-attribute-sets="padding-sm">
                            <fo:block xsl:use-attribute-sets="text-small" font-style="italic">
                              <xsl:choose>
                                <xsl:when test="options/options[1]">
                                  <xsl:value-of select="options/options[1]"/>
                                </xsl:when>
                                <xsl:otherwise>units</xsl:otherwise>
                              </xsl:choose>
                            </fo:block>
                          </fo:table-cell>
                        </fo:table-row>
                      </fo:table-body>
                    </fo:table>
                  </fo:block>
                </xsl:when>

                <!-- Text Input -->
                <xsl:when test="$questionType = 'TEXT'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options margin-bottom-sm">
                      <fo:table-column column-width="30%"/>
                      <fo:table-column column-width="70%"/>
                      <fo:table-body>
                        <fo:table-row>
                          <fo:table-cell xsl:use-attribute-sets="padding-xs">
                            <fo:block xsl:use-attribute-sets="input-text">&#160;</fo:block>
                          </fo:table-cell>
                          <fo:table-cell xsl:use-attribute-sets="padding-sm">
                            <fo:block xsl:use-attribute-sets="text-small" font-style="italic">
                              <xsl:choose>
                                <xsl:when test="suffixText">
                                  <xsl:value-of select="suffixText"/>
                                </xsl:when>
                                <xsl:when test="options/options[1]">
                                  <xsl:value-of select="options/options[1]"/>
                                </xsl:when>
                                <xsl:otherwise>&#160;</xsl:otherwise>
                              </xsl:choose>
                            </fo:block>
                          </fo:table-cell>
                        </fo:table-row>
                      </fo:table-body>
                    </fo:table>
                    <!-- "I am not sure" option -->
                    <xsl:call-template name="option-row">
                      <xsl:with-param name="option-text">I am not sure</xsl:with-param>
                    </xsl:call-template>
                  </fo:block>
                </xsl:when>

                <!-- Date Input -->
                <xsl:when test="$questionType = 'DATE'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:table xsl:use-attribute-sets="table-options">
                      <fo:table-column column-width="30%"/>
                      <fo:table-column column-width="70%"/>
                      <fo:table-body>
                        <fo:table-row>
                          <fo:table-cell xsl:use-attribute-sets="padding-xs">
                            <fo:block xsl:use-attribute-sets="input-text align-center">
                              DD/MM/YYYY
                            </fo:block>
                          </fo:table-cell>
                          <fo:table-cell>
                            <fo:block>&#160;</fo:block>
                          </fo:table-cell>
                        </fo:table-row>
                      </fo:table-body>
                    </fo:table>
                  </fo:block>
                </xsl:when>

                <!-- Paragraph/Textarea -->
                <xsl:when test="$questionType = 'PARAGRAPH'">
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:block xsl:use-attribute-sets="input-textarea">&#160;</fo:block>
                  </fo:block>
                </xsl:when>

                <!-- Default fallback -->
                <xsl:otherwise>
                  <fo:block xsl:use-attribute-sets="margin-top-sm">
                    <fo:block xsl:use-attribute-sets="input-text">&#160;</fo:block>
                  </fo:block>
                </xsl:otherwise>
              </xsl:choose>
            </fo:block-container>
          </xsl:for-each>

          <!-- Signature section -->
          <fo:block-container margin-top="16pt"
                              padding="12pt"
                              border="1pt solid #e2e8f0"
                              background-color="#f7fafc">
            <fo:table xsl:use-attribute-sets="table-full">
              <fo:table-column column-width="33%"/>
              <fo:table-column column-width="34%"/>
              <fo:table-column column-width="33%"/>
              <fo:table-body>
                <fo:table-row>
                  <fo:table-cell xsl:use-attribute-sets="padding-sm">
                    <fo:block xsl:use-attribute-sets="text-label">Patient Signature:</fo:block>
                    <fo:block border-bottom="1pt solid #4a5568"
                              padding-top="16pt"
                              margin-top="4pt">&#160;
                    </fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="padding-sm">
                    <fo:block xsl:use-attribute-sets="text-label">Provider Signature:</fo:block>
                    <fo:block border-bottom="1pt solid #4a5568"
                              padding-top="16pt"
                              margin-top="4pt">&#160;
                    </fo:block>
                  </fo:table-cell>
                  <fo:table-cell xsl:use-attribute-sets="padding-sm">
                    <fo:block xsl:use-attribute-sets="text-label">Date:</fo:block>
                    <fo:block border-bottom="1pt solid #4a5568"
                              padding-top="16pt"
                              margin-top="4pt">&#160;
                    </fo:block>
                  </fo:table-cell>
                </fo:table-row>
              </fo:table-body>
            </fo:table>
          </fo:block-container>

          <fo:block id="end-of-document"/>
        </fo:flow>
      </fo:page-sequence>
    </fo:root>
  </xsl:template>
</xsl:stylesheet>
