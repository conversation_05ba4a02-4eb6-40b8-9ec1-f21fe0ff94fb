<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">

    <!-- Root template -->
    <xsl:template match="/">
        <fo:root>
            <fo:layout-master-set>
                <fo:simple-page-master master-name="A4"
                                       page-height="29.7cm"
                                       page-width="21cm"
                                       margin-top="1cm"
                                       margin-bottom="1cm"
                                       margin-left="1.5cm"
                                       margin-right="1.5cm">
                    <fo:region-body margin-top="4.5cm" margin-bottom="2.5cm"/>
                    <fo:region-before extent="4cm"/>
                    <fo:region-after extent="2cm"/>
                </fo:simple-page-master>
            </fo:layout-master-set>

            <fo:page-sequence master-reference="A4">
                <!-- Header with logo, form title, and patient info -->
                <fo:static-content flow-name="xsl-region-before">
                    <!-- Logo and Title Row -->
                    <fo:table width="100%" table-layout="fixed" margin-bottom="0.3cm">
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="60%"/>
                        <fo:table-column column-width="20%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <!-- Logo in left column -->
                                <fo:table-cell display-align="center">
                                    <fo:block>
                                        <xsl:if test="formData/formStyle/logoUrl">
                                            <fo:external-graphic src="url({formData/formStyle/logoUrl})" content-width="2cm" content-height="scale-to-fit"/>
                                        </xsl:if>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Title in middle column -->
                                <fo:table-cell display-align="center">
                                    <fo:block font-size="16pt" font-weight="bold" color="{formData/formStyle/primaryColor}" text-align="center">
                                        <xsl:value-of select="formData/formTemplate/title"/>
                                    </fo:block>
                                    <fo:block font-size="9pt" color="{formData/formStyle/secondaryColor}" text-align="center">
                                        Form ID: <xsl:value-of select="formData/formTemplate/formId"/>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Form QR code in right column -->
                                <fo:table-cell display-align="center">
                                    <fo:block text-align="right">
                                        <!-- Use the SVG QR code from XML -->
                                        <xsl:if test="formData/formTemplate/formQrCodeSvg">
                                            <!-- Create a variable with the image path or URL -->
                                            <xsl:variable name="imagePath" select="formData/formTemplate/formQrCodeSvg"/>

                                            <!-- Use the variable in the src attribute -->
                                            <fo:external-graphic src="{$imagePath}"
                                                                 content-width="80pt"
                                                                 content-height="80pt"/>
                                        </xsl:if>

                                        <!-- Fallback placeholder if no SVG is available -->
                                        <xsl:if test="not(formData/formTemplate/formQrCodeSvg)">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 45 45">
                                                    <rect x="0" y="0" width="45" height="45" fill="#f0f0f0" stroke="#cccccc" stroke-width="1"/>
                                                    <text x="22.5" y="22.5" font-family="Arial" font-size="5" text-anchor="middle" fill="#336699">
                                                        FORM: <xsl:value-of select="formData/formTemplate/formId"/>
                                                    </text>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </xsl:if>
                                    </fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- Patient information section -->
                    <fo:table width="100%" table-layout="fixed" border="1pt solid #cccccc" background-color="#f5f5f5" margin-bottom="0.5cm">
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-column column-width="20%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-body>
                            <!-- Row 1: Name and DOB -->
                            <fo:table-row>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Patient Name:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/name">
                                                <xsl:value-of select="formData/patientInfo/name"/>
                                            </xsl:when>
                                            <xsl:otherwise>John Doe</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Date of Birth:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/dateOfBirth">
                                                <xsl:value-of select="formData/patientInfo/dateOfBirth"/>
                                            </xsl:when>
                                            <xsl:otherwise>01/01/1980</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <!-- Row 2: Patient ID and Date -->
                            <fo:table-row>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Patient ID:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/id">
                                                <xsl:value-of select="formData/patientInfo/id"/>
                                            </xsl:when>
                                            <xsl:otherwise>PT-12345678</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="9pt" font-weight="bold">Assessment Date:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="4pt">
                                    <fo:block font-size="10pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/assessmentDate">
                                                <xsl:value-of select="formData/assessmentDate"/>
                                            </xsl:when>
                                            <xsl:otherwise>DD/MM/YYYY</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <!-- Border line at bottom of header -->
                    <fo:block border-bottom="2pt solid #336699"></fo:block>
                </fo:static-content>

                <!-- Footer -->
                <fo:static-content flow-name="xsl-region-after">
                    <fo:table width="100%" table-layout="fixed" border-top="1pt solid #cccccc">
                        <fo:table-column column-width="33%"/>
                        <fo:table-column column-width="34%"/>
                        <fo:table-column column-width="33%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" padding-top="5pt">
                                        <xsl:value-of select="formData/formTemplate/formId"/>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" text-align="center" padding-top="5pt">
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/name">
                                                <xsl:value-of select="formData/patientInfo/name"/>
                                            </xsl:when>
                                            <xsl:otherwise>John Doe</xsl:otherwise>
                                        </xsl:choose>
                                        -
                                        <xsl:choose>
                                            <xsl:when test="formData/patientInfo/id">
                                                <xsl:value-of select="formData/patientInfo/id"/>
                                            </xsl:when>
                                            <xsl:otherwise>PT-12345678</xsl:otherwise>
                                        </xsl:choose>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="8pt" color="#666666" text-align="right" padding-top="5pt">
                                        Page <fo:page-number/> of <fo:page-number-citation-last ref-id="end-of-document"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:static-content>

                <!-- Main content -->
                <fo:flow flow-name="xsl-region-body">
                    <!-- Description -->
                    <xsl:if test="formData/formTemplate/description">
                        <fo:block font-size="10pt" margin-bottom="8pt" space-after="0.3cm" font-style="italic">
                            <xsl:value-of select="formData/formTemplate/description"/>
                        </fo:block>
                    </xsl:if>

                    <!-- Process each question -->
                    <xsl:for-each select="formData/formTemplate/questions/question">
                        <xsl:variable name="questionNumber" select="position()"/>
                        <xsl:variable name="questionType" select="type"/>

                        <!-- Question box -->
                        <fo:block-container margin-bottom="6pt"
                                            padding="6pt"
                                            border="1pt solid #cccccc"
                                            background-color="#f9f9f9">

                            <!-- Question heading with QR code -->
                            <fo:table width="100%" table-layout="fixed">
                                <fo:table-column column-width="85%"/>
                                <fo:table-column column-width="15%"/>
                                <fo:table-body>
                                    <fo:table-row>
                                        <!-- Question text -->
                                        <fo:table-cell>
                                            <fo:block font-size="10pt" font-weight="bold" margin-bottom="3pt">
                                                <xsl:value-of select="$questionNumber"/>. <xsl:value-of select="text"/>
                                            </fo:block>
                                        </fo:table-cell>

                                        <!-- QR code -->
                                        <fo:table-cell>
                                            <fo:block>
                                                <!-- Use the SVG QR code from XML -->
<!--                                                <xsl:if test="qrCodeSvg">-->
<!--                                                    <fo:instream-foreign-object>-->
<!--                                                        <xsl:value-of select="qrCodeSvg" disable-output-escaping="yes"/>-->
<!--                                                    </fo:instream-foreign-object>-->
<!--                                                </xsl:if>-->
                                                <!-- Fallback placeholder if no SVG is available -->
                                                <xsl:if test="not(qrCodeSvg)">
                                                    <fo:instream-foreign-object>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
                                                            <rect x="0" y="0" width="40" height="40" fill="#eeeeee" stroke="#cccccc" stroke-width="1"/>
                                                            <text x="20" y="20" font-family="Arial" font-size="5" text-anchor="middle">QR: <xsl:value-of select="id"/></text>
                                                        </svg>
                                                    </fo:instream-foreign-object>
                                                </xsl:if>
                                            </fo:block>
                                        </fo:table-cell>
                                    </fo:table-row>
                                </fo:table-body>
                            </fo:table>

                            <!-- Question options based on type -->
                            <xsl:choose>
                                <!-- Multiple choice options -->
                                <xsl:when test="$questionType = 'MULTIPLE_CHOICE'">
                                    <fo:block margin-top="4pt">
                                        <xsl:for-each select="options/option">
                                            <fo:table width="100%" table-layout="fixed" margin-bottom="2pt">
                                                <fo:table-column column-width="15pt"/>
                                                <fo:table-column column-width="proportional-column-width(1)"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <!-- Answer box -->
                                                        <fo:table-cell display-align="center">
                                                            <fo:block>
                                                                <fo:instream-foreign-object>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                                                                        <rect x="0.5" y="0.5" width="9" height="9" fill="white" stroke="#000000" stroke-width="1"/>
                                                                    </svg>
                                                                </fo:instream-foreign-object>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                        <!-- Option text -->
                                                        <fo:table-cell>
                                                            <fo:block font-size="9pt" margin-left="3pt">
                                                                <xsl:value-of select="."/>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </xsl:for-each>
                                    </fo:block>
                                </xsl:when>

                                <!-- Rating scale -->
<!--                                <xsl:when test="$questionType = 'RATING_SCALE'">-->
<!--                                    <fo:block margin-top="4pt">-->
<!--                                        <fo:table width="100%" table-layout="fixed">-->
<!--                                            <xsl:variable name="scaleMax">-->
<!--                                                <xsl:choose>-->
<!--                                                    <xsl:when test="options/option[1]">-->
<!--                                                        <xsl:value-of select="options/option[1]"/>-->
<!--                                                    </xsl:when>-->
<!--                                                    <xsl:otherwise>10</xsl:otherwise>-->
<!--                                                </xsl:choose>-->
<!--                                            </xsl:variable>-->

<!--                                            &lt;!&ndash; Create columns &ndash;&gt;-->
<!--                                            <xsl:call-template name="create-columns">-->
<!--                                                <xsl:with-param name="count" select="$scaleMax"/>-->
<!--                                                <xsl:with-param name="current" select="1"/>-->
<!--                                            </xsl:call-template>-->

<!--                                            <fo:table-body>-->
<!--                                                &lt;!&ndash; Scale labels &ndash;&gt;-->
<!--                                                <fo:table-row>-->
<!--                                                    <fo:table-cell number-columns-spanned="1">-->
<!--                                                        <fo:block font-size="8pt" text-align="center">Low</fo:block>-->
<!--                                                    </fo:table-cell>-->

<!--                                                    &lt;!&ndash; Middle cells need a block &ndash;&gt;-->
<!--                                                    <xsl:call-template name="create-empty-cells">-->
<!--                                                        <xsl:with-param name="count" select="$scaleMax - 2"/>-->
<!--                                                        <xsl:with-param name="current" select="1"/>-->
<!--                                                    </xsl:call-template>-->

<!--                                                    <fo:table-cell number-columns-spanned="1">-->
<!--                                                        <fo:block font-size="8pt" text-align="center">High</fo:block>-->
<!--                                                    </fo:table-cell>-->
<!--                                                </fo:table-row>-->

<!--                                                &lt;!&ndash; Numbers row &ndash;&gt;-->
<!--                                                <fo:table-row>-->
<!--                                                    <xsl:call-template name="create-number-cells">-->
<!--                                                        <xsl:with-param name="count" select="$scaleMax"/>-->
<!--                                                        <xsl:with-param name="current" select="1"/>-->
<!--                                                    </xsl:call-template>-->
<!--                                                </fo:table-row>-->

<!--                                                &lt;!&ndash; Checkbox row &ndash;&gt;-->
<!--                                                <fo:table-row>-->
<!--                                                    <xsl:call-template name="create-rating-boxes">-->
<!--                                                        <xsl:with-param name="count" select="$scaleMax"/>-->
<!--                                                        <xsl:with-param name="current" select="1"/>-->
<!--                                                    </xsl:call-template>-->
<!--                                                </fo:table-row>-->
<!--                                            </fo:table-body>-->
<!--                                        </fo:table>-->
<!--                                    </fo:block>-->
<!--                                </xsl:when>-->
                                <xsl:when test="$questionType = 'RATING_SCALE'">
                                    <fo:block margin-top="4pt">
                                        <xsl:variable name="scaleMax" select="count(options/option)"/>

                                        <fo:table width="100%" table-layout="fixed">
                                            <xsl:call-template name="create-columns">
                                                <xsl:with-param name="count" select="$scaleMax"/>
                                                <xsl:with-param name="current" select="1"/>
                                            </xsl:call-template>

                                            <fo:table-body>
                                                <!-- Scale labels -->
                                                <fo:table-row>
                                                    <fo:table-cell number-columns-spanned="1">
                                                        <fo:block font-size="8pt" text-align="center">Low</fo:block>
                                                    </fo:table-cell>

                                                    <xsl:call-template name="create-empty-cells">
                                                        <xsl:with-param name="count" select="$scaleMax - 2"/>
                                                        <xsl:with-param name="current" select="1"/>
                                                    </xsl:call-template>

                                                    <fo:table-cell number-columns-spanned="1">
                                                        <fo:block font-size="8pt" text-align="center">High</fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>

                                                <!-- Numbers row -->
                                                <fo:table-row>
                                                    <xsl:for-each select="options/option">
                                                        <fo:table-cell text-align="center">
                                                            <fo:block font-size="9pt">
                                                                <xsl:value-of select="."/>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </xsl:for-each>
                                                </fo:table-row>

                                                <!-- Checkbox row -->
                                                <fo:table-row>
                                                    <xsl:for-each select="options/option">
                                                        <fo:table-cell text-align="center" padding="2pt">
                                                            <fo:block>
                                                                <fo:instream-foreign-object>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                                                                        <rect x="0.5" y="0.5" width="9" height="9" fill="white" stroke="#000000" stroke-width="1"/>
                                                                    </svg>
                                                                </fo:instream-foreign-object>
                                                            </fo:block>
                                                        </fo:table-cell>
                                                    </xsl:for-each>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </xsl:when>

                                <!-- Clinical measure -->
                                <xsl:when test="$questionType = 'CLINICAL_MEASURE'">
                                    <fo:block margin-top="4pt">
                                        <fo:table width="100%" table-layout="fixed">
                                            <fo:table-column column-width="70%"/>
                                            <fo:table-column column-width="30%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell padding="3pt">
                                                        <fo:block border="1pt solid #336699" padding="4pt" background-color="white">
                                                            <!-- Input field -->
                                                            <fo:block>&#160;</fo:block>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="3pt">
                                                        <fo:block font-size="9pt" font-style="italic">
                                                            <xsl:choose>
                                                                <xsl:when test="options/option[1]">
                                                                    <xsl:value-of select="options/option[1]"/>
                                                                </xsl:when>
                                                                <xsl:otherwise>units</xsl:otherwise>
                                                            </xsl:choose>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </xsl:when>

                                <!-- Default case when type is not recognized -->
                                <xsl:otherwise>
                                    <fo:block margin-top="4pt">
                                        <fo:table width="100%" table-layout="fixed">
                                            <fo:table-column column-width="100%"/>
                                            <fo:table-body>
                                                <fo:table-row>
                                                    <fo:table-cell padding="3pt">
                                                        <fo:block border="1pt solid #336699" padding="4pt" background-color="white">
                                                            <!-- Default input field -->
                                                            <fo:block>&#160;</fo:block>
                                                        </fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </fo:table-body>
                                        </fo:table>
                                    </fo:block>
                                </xsl:otherwise>
                            </xsl:choose>
                        </fo:block-container>

                        <!-- Auto page break after every 8 questions (unless it's the last question) -->
                        <xsl:if test="position() mod 8 = 0 and position() != last()">
                            <fo:block break-after="page"/>
                        </xsl:if>
                    </xsl:for-each>

                    <!-- Signature section -->
                    <fo:block-container margin-top="10pt" margin-bottom="8pt">
                        <fo:table width="100%" table-layout="fixed">
                            <fo:table-column column-width="33%"/>
                            <fo:table-column column-width="34%"/>
                            <fo:table-column column-width="33%"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell padding="4pt">
                                        <fo:block font-size="9pt" font-weight="bold">Patient Signature:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="15pt">&#160;</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding="4pt">
                                        <fo:block font-size="9pt" font-weight="bold">Provider Signature:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="15pt">&#160;</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding="4pt">
                                        <fo:block font-size="9pt" font-weight="bold">Date:</fo:block>
                                        <fo:block border-bottom="1pt solid #000000" padding-top="15pt">&#160;</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block-container>

                    <fo:block id="end-of-document"/>
                </fo:flow>
            </fo:page-sequence>
        </fo:root>
    </xsl:template>

    <!-- Helper template for creating empty cells for the middle of the rating scale -->
    <xsl:template name="create-empty-cells">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <xsl:if test="$current &lt;= $count">
            <fo:table-cell>
                <fo:block>&#160;</fo:block>
            </fo:table-cell>

            <xsl:call-template name="create-empty-cells">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>

    <!-- Helper template for creating table columns -->
    <xsl:template name="create-columns">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <fo:table-column column-width="proportional-column-width(1)"/>

        <xsl:if test="$current &lt; $count">
            <xsl:call-template name="create-columns">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>

    <!-- Helper template for creating number cells -->
    <xsl:template name="create-number-cells">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <fo:table-cell text-align="center">
            <fo:block font-size="9pt">
                <xsl:value-of select="$current"/>
            </fo:block>
        </fo:table-cell>

        <xsl:if test="$current &lt; $count">
            <xsl:call-template name="create-number-cells">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>

    <!-- Helper template for creating proper rating boxes -->
    <xsl:template name="create-rating-boxes">
        <xsl:param name="count"/>
        <xsl:param name="current"/>

        <fo:table-cell text-align="center" padding="2pt">
            <fo:block>
                <fo:instream-foreign-object>
                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
                        <rect x="0.5" y="0.5" width="9" height="9" fill="white" stroke="#000000" stroke-width="1"/>
                    </svg>
                </fo:instream-foreign-object>
            </fo:block>
        </fo:table-cell>

        <xsl:if test="$current &lt; $count">
            <xsl:call-template name="create-rating-boxes">
                <xsl:with-param name="count" select="$count"/>
                <xsl:with-param name="current" select="$current + 1"/>
            </xsl:call-template>
        </xsl:if>
    </xsl:template>
</xsl:stylesheet>