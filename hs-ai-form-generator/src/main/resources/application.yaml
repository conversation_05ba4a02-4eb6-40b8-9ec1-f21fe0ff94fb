# Application configuration
spring:
  application:
    name: hs-ai-form-generator
  main:
    allow-bean-definition-overriding: true
server:
  port: 9900
  servlet:
    context-path: /v3/ai-form-generator

form:
  templates:
    base-dir: templates
    form-type:
      hra: hra/hra11.xsl

integration:
  survey:
    url: http://hs-survey-management:33000/v3/survey
  entity-data-service:
    url: http://hs-entity-data-service:32000/v3/entity

platform:
  logging:
    console-level: "info"
    json-console-level: "off"
    flat-file-level: "off"
    json-file-level: "off"
  oauth2:
    resourceserver:
      cors:
        allowed-origins: '*'
        allowed-methods: GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS,TRACE
        allowed-headers: X-XSRF-TOKEN,XSRF-TOKEN,CONTENT-TYPE
        allow-credentials: true
        max-age: 10
      developer-endpoints:
        - paths: /actuator/**,/**/actuator/**
          role: DEVELOPER
      public-endpoints:
        - paths: /swagger-*/**,/webjars/**,/v3/**,/**/actuator/**,/actuator/**

hs:
  oauth2:
    url: http://keycloak:8080/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: ${BACKEND_TO_BACKEND_CLIENT_ID}
      secret: ${BACKEND_TO_BACKEND_CLIENT_SECRET}

management:
  server:
    port: 9901
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: 'health,info,loggers'
  health:
    camunda:
      enabled: false
---
spring:
  config:
    activate:
      on-profile: local
  autoconfigure:
    # This overrides our security configuration
    exclude: org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
  cloud:
    config:
      enabled: false

hs:
  oauth2:
    url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: ${BACKEND_TO_BACKEND_CLIENT_ID}
      secret: ${BACKEND_TO_BACKEND_CLIENT_SECRET}

security:
  oauth2:
    client:
      client-id: hs-platform
      client-secret: uAcbVei2Nnj6tmQh2g7B4LOaqZwuRj7v
      scope: openid,profile,email,roles
      oauth-base-url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/auth
    resource:
      id: hs-platform
      token-info-uri: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
      user-info-uri: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/userinfo
      jwk:
        key-set-uri: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/certs

openapi:
  servers: http://localhost:9900/v3/ai-form-generator

