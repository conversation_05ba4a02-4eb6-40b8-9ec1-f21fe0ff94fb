{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "paths": {"/api/v1/template/{surveyTemplateId}": {"get": {"tags": ["survey-template-controller"], "operationId": "getSurveyTemplate", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "retrieveWithUserSpecificDefaultValues", "in": "query", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}, "put": {"tags": ["survey-template-controller"], "operationId": "updateSurveyTemplate", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "forceUpdate", "in": "query", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}, "delete": {"tags": ["survey-template-controller"], "operationId": "deleteSurveyTemplate", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/publish/{surveyTemplateId}": {"put": {"tags": ["survey-template-controller"], "operationId": "publishSurveyTemplates", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "forceUpdate", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/publish/multiple": {"put": {"tags": ["survey-template-controller"], "operationId": "publishSurveyTemplates_1", "parameters": [{"name": "bulkRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BulkRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}}, "/api/v1/form-options/{formOptionName}": {"get": {"tags": ["form-options-controller"], "operationId": "getFormOption", "parameters": [{"name": "formOptionName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormOption"}}}}}}, "put": {"tags": ["form-options-controller"], "operationId": "updateFormOption", "parameters": [{"name": "formOptionName", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormOption"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormOption"}}}}}}, "delete": {"tags": ["form-options-controller"], "operationId": "deleteFormOption", "parameters": [{"name": "formOptionName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/custom-form/{id}": {"get": {"tags": ["custom-form-controller"], "operationId": "getCustomFormById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomForm"}}}}}}, "put": {"tags": ["custom-form-controller"], "operationId": "updateCustomForm", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomForm"}}}}}}, "delete": {"tags": ["custom-form-controller"], "operationId": "deleteCustomForm", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/answered-survey/{answeredSurveyId}": {"get": {"tags": ["answered-survey-controller"], "operationId": "getAnsweredSurvey", "parameters": [{"name": "answeredSurveyId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}, "put": {"tags": ["answered-survey-controller"], "operationId": "updateAnsweredSurvey", "parameters": [{"name": "answeredSurveyId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnswerSurveyRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveAnsweredSurveyResponse"}}}}}}, "delete": {"tags": ["answered-survey-controller"], "operationId": "deleteAnsweredSurvey", "parameters": [{"name": "answeredSurveyId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template": {"post": {"tags": ["survey-template-controller"], "operationId": "addSurveyTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/version/{surveyHeaderId}": {"post": {"tags": ["survey-template-controller"], "operationId": "addSurveyTemplateVersion", "parameters": [{"name": "surveyHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/regenerate-feedback/{surveyTemplateId}": {"post": {"tags": ["survey-template-controller"], "operationId": "regenerateFeedback", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/delete/multiple": {"post": {"tags": ["survey-template-controller"], "operationId": "deleteSurveyTemplates", "parameters": [{"name": "bulkRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BulkRequest"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/form-options": {"post": {"tags": ["form-options-controller"], "operationId": "addFormOption", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormOption"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormOption"}}}}}}}, "/api/v1/custom-form": {"post": {"tags": ["custom-form-controller"], "operationId": "addCustomForm", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomForm"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomForm"}}}}}}}, "/api/v1/comment": {"post": {"tags": ["survey-comment-controller"], "operationId": "addSurveyComment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyCommentDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyCommentDto"}}}}}}}, "/api/v1/answered-survey": {"post": {"tags": ["answered-survey-controller"], "operationId": "addAnsweredSurvey", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnswerSurveyRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveAnsweredSurveyResponse"}}}}}}}, "/api/v1/answered-survey/delete/multiple": {"post": {"tags": ["answered-survey-controller"], "operationId": "deleteAnsweredSurveys", "parameters": [{"name": "bulkRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BulkRequest"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v3/answered-survey/retake/template/name/{surveyTemplateName}": {"get": {"tags": ["answered-survey-controller-v-3"], "operationId": "getAnsweredSurveyForRetakeByTemplateName", "parameters": [{"name": "surveyTemplateName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurveyV2"}}}}}}}, "/api/v3/answered-survey/retake/template/id/{surveyTemplateId}": {"get": {"tags": ["answered-survey-controller-v-3"], "operationId": "getAnsweredSurveyForRetakeByTemplateId", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurveyV2"}}}}}}}, "/api/v2/answered-survey/retake/template/name/{surveyTemplateName}": {"get": {"tags": ["answered-survey-controller-v-2"], "operationId": "getAnsweredSurveyForRetakeByTemplateName_1", "parameters": [{"name": "surveyTemplateName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v2/answered-survey/retake/template/id/{surveyTemplateId}": {"get": {"tags": ["answered-survey-controller-v-2"], "operationId": "getAnsweredSurveyForRetakeByTemplateId_1", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v2/answered-survey/name/{surveyName}/latest": {"get": {"tags": ["answered-survey-controller-v-2"], "operationId": "getLatestAnsweredSurveyByName", "parameters": [{"name": "surveyName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurveyV2"}}}}}}}, "/api/v1/template/version/latest/filter": {"get": {"tags": ["survey-template-controller"], "operationId": "filterLatestVersions", "parameters": [{"name": "surveyTemplateFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SurveyTemplateFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSurveyTemplate"}}}}}}}, "/api/v1/template/version/latest/filter/dto": {"get": {"tags": ["survey-template-controller"], "operationId": "filterLatestVersionDtos", "parameters": [{"name": "surveyTemplateFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SurveyTemplateFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSurveyTemplateDto"}}}}}}}, "/api/v1/template/version-actions/filter": {"get": {"tags": ["survey-template-controller"], "operationId": "filterSurveyVersionActions", "parameters": [{"name": "surveyVersionActionFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SurveyVersionActionFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSurveyVersionAction"}}}}}}}, "/api/v1/template/header/name/{surveyHeaderName}/latest": {"get": {"tags": ["survey-template-controller"], "operationId": "getLatestSurveyTemplatesByHeaderName", "parameters": [{"name": "surveyHeaderName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/header/name/{surveyHeaderName}/latest-populated": {"get": {"tags": ["survey-template-controller"], "operationId": "getLatestPopulatedSurveyTemplatesByHeaderName", "parameters": [{"name": "surveyHeaderName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}, "/api/v1/template/header/id/{surveyHeaderId}": {"get": {"tags": ["survey-template-controller"], "operationId": "getSurveyTemplatesByHeaderIds", "parameters": [{"name": "surveyHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyTemplate"}}}}}}}}, "/api/v1/template/header/id/{surveyHeaderId}/dto": {"get": {"tags": ["survey-template-controller"], "operationId": "getSurveyTemplateDtosByHeaderIds", "parameters": [{"name": "surveyHeaderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyTemplateDto"}}}}}}}}, "/api/v1/template/filter": {"get": {"tags": ["survey-template-controller"], "operationId": "filter", "parameters": [{"name": "surveyTemplateFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SurveyTemplateFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSurveyTemplate"}}}}}}}, "/api/v1/template/filter/dto": {"get": {"tags": ["survey-template-controller"], "operationId": "filterDto", "parameters": [{"name": "surveyTemplateFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SurveyTemplateFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSurveyTemplateDto"}}}}}}}, "/api/v1/template/download": {"get": {"tags": ["survey-template-controller"], "operationId": "downloadSurveyTemplates", "parameters": [{"name": "bulkRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BulkRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/v1/option-measure/all": {"get": {"tags": ["option-measure-controller"], "operationId": "getAll", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OptionMeasure"}}}}}}}}, "/api/v1/health-attribute/all": {"get": {"tags": ["health-attribute-controller"], "operationId": "getAll_1", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v1/form-options/all": {"get": {"tags": ["form-options-controller"], "operationId": "getAll_2", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FormOption"}}}}}}}}, "/api/v1/custom-form/name/{customFormName}": {"get": {"tags": ["custom-form-controller"], "operationId": "getCustomFormByName", "parameters": [{"name": "customFormName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomForm"}}}}}}}, "/api/v1/custom-form/filter": {"get": {"tags": ["custom-form-controller"], "operationId": "filter_1", "parameters": [{"name": "customFormFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CustomFormFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCustomForm"}}}}}}}, "/api/v1/comment/{id}": {"get": {"tags": ["survey-comment-controller"], "operationId": "getSurveyCommentById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyCommentDto"}}}}}}, "delete": {"tags": ["survey-comment-controller"], "operationId": "deleteSurveyComment", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/comment/filter": {"get": {"tags": ["survey-comment-controller"], "operationId": "filter_2", "parameters": [{"name": "surveyCommentFilter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SurveyCommentFilter"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageSurveyCommentDto"}}}}}}}, "/api/v1/answered-survey/template": {"get": {"tags": ["answered-survey-controller"], "operationId": "getBySurveyName", "parameters": [{"name": "surveyName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v1/answered-survey/template/{templateId}": {"get": {"tags": ["answered-survey-controller"], "operationId": "getByTemplateId", "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v1/answered-survey/retake/template/name/{surveyTemplateName}": {"get": {"tags": ["answered-survey-controller"], "operationId": "getAnsweredSurveyForRetakeByTemplateName_2", "parameters": [{"name": "surveyTemplateName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v1/answered-survey/retake/template/id/{surveyTemplateId}": {"get": {"tags": ["answered-survey-controller"], "operationId": "getAnsweredSurveyForRetakeByTemplateId_2", "parameters": [{"name": "surveyTemplateId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v1/answered-survey/result": {"get": {"tags": ["answered-survey-controller"], "operationId": "getAnsweredSurveyResultResponse", "parameters": [{"name": "surveyName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurveyResultDto"}}}}}}}, "/api/v1/answered-survey/recalculate-feedback": {"get": {"tags": ["answered-survey-controller"], "operationId": "recalculateFeedback", "parameters": [{"name": "surveyName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyFeedback"}}}}}}}, "/api/v1/answered-survey/name/{surveyName}/latest": {"get": {"tags": ["answered-survey-controller"], "operationId": "getLatestAnsweredSurveyByName_1", "parameters": [{"name": "surveyName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnsweredSurvey"}}}}}}}, "/api/v1/answered-survey/history": {"get": {"tags": ["answered-survey-controller"], "operationId": "getAnsweredSurveyHistory", "parameters": [{"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AnsweredSurveyHistoryFilter"}}, {"name": "memberEntityNo", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAnsweredSurveySubmissionDto"}}}}}}}, "/api/v1/answered-survey/filter": {"get": {"tags": ["answered-survey-controller"], "operationId": "filter_3", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "answered<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AnsweredSurveyFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAnsweredSurvey"}}}}}}}, "/api/v1/answered-survey/filter/dto": {"get": {"tags": ["answered-survey-controller"], "operationId": "filterDto_1", "parameters": [{"name": "pagingRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagingRequest"}}, {"name": "answered<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AnsweredSurveyFilter"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageAnsweredSurveyDto"}}}}}}}, "/api/v1/answered-survey/aggregated": {"get": {"tags": ["aggregated-answered-survey-controller"], "operationId": "getAggregatedAnswers", "parameters": [{"name": "surveyName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "memberEntityNumbers", "in": "query", "required": true, "schema": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AggregatedAnswer"}}}}}}}}}, "components": {"schemas": {"Question": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "key": {"type": "string"}, "type": {"type": "string"}, "defaultValue": {"type": "object"}, "hideExpression": {"type": "string"}, "healthAttribute": {"type": "string"}, "focus": {"type": "boolean"}, "hide": {"type": "boolean"}, "weight": {"minimum": 0.0, "exclusiveMinimum": false, "type": "number"}, "validation": {"$ref": "#/components/schemas/QuestionValidation"}, "questionSurveyUnit": {"$ref": "#/components/schemas/QuestionSurveyUnit"}, "props": {"type": "object", "additionalProperties": {"type": "object"}}, "expressions": {"type": "object", "additionalProperties": {"type": "string"}}, "fieldGroup": {"type": "array", "items": {"$ref": "#/components/schemas/Question"}}, "validators": {"$ref": "#/components/schemas/QuestionValidators"}, "wrappers": {"type": "array", "items": {"type": "string"}}, "className": {"type": "string"}}}, "QuestionSurveyUnit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyUnit": {"$ref": "#/components/schemas/SurveyUnit"}}}, "QuestionValidation": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "show": {"type": "boolean"}, "messages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "QuestionValidators": {"type": "object", "properties": {"validation": {"type": "array", "items": {"type": "string"}}}}, "SurveyBanding": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "from": {"type": "integer", "format": "int64"}, "to": {"type": "integer", "format": "int64"}}}, "SurveyCommentDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyVersionNumber": {"type": "integer", "format": "int64"}, "surveyHeaderId": {"type": "integer", "format": "int64"}, "adminUserName": {"type": "string"}, "commentText": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}}}, "SurveyTemplate": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyHeaderId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "identifier": {"type": "string"}, "surveyBandings": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyBanding"}}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/Question"}}, "versionNumber": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "PUBLISHED", "DELETED"]}, "surveyDescription": {"type": "string"}, "surveyType": {"type": "string", "enum": ["SURVEY", "QUIZ"]}, "surveyFeedback": {"type": "string"}, "maxScore": {"type": "number"}, "options": {"type": "object", "additionalProperties": {"type": "string"}}, "comments": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyCommentDto"}}, "numQuestions": {"type": "integer", "format": "int64"}, "pinned": {"type": "boolean"}}}, "SurveyUnit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "metricName": {"type": "string"}, "imperialName": {"type": "string"}, "metricUnitMeasure": {"type": "string"}, "imperialUnitMeasure": {"type": "string"}, "metricToImperialRate": {"type": "string"}, "unitIdentifier": {"type": "string"}}}, "BulkRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "FormOption": {"type": "object", "properties": {"name": {"type": "string"}, "valueType": {"type": "string", "enum": ["BOOLEAN", "TEXT", "INTEGER", "DECIMAL"]}}}, "CustomForm": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "jsonData": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}}, "AnswerQuestionRequest": {"type": "object", "properties": {"answeredQuestionId": {"type": "integer", "format": "int64"}, "questionId": {"type": "integer", "format": "int64"}, "answeredValue": {"type": "object"}}}, "AnswerSurveyRequest": {"type": "object", "properties": {"answeredSurveyId": {"type": "integer", "format": "int64"}, "surveyTemplateId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}, "answeredQuestions": {"type": "array", "items": {"$ref": "#/components/schemas/AnswerQuestionRequest"}}, "memberEntityNo": {"type": "integer", "format": "int64"}}}, "AnsweredQuestionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "question": {"$ref": "#/components/schemas/QuestionDto"}, "answeredValue": {"type": "object"}}}, "QuestionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "healthAttribute": {"type": "string"}, "props": {"type": "object", "additionalProperties": {"type": "object"}}}}, "SaveAnsweredSurveyResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyName": {"type": "string"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}, "submittedAt": {"type": "string", "format": "date-time"}, "memberEntityNo": {"type": "integer", "format": "int64"}, "answeredQuestions": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredQuestionDto"}}, "surveyScoring": {"$ref": "#/components/schemas/SurveyScoring"}}}, "SurveyScoring": {"type": "object", "properties": {"scoringResultType": {"type": "string", "enum": ["SUM", "PERCENTAGE", "SUM_AND_PERCENTAGE"]}, "maxScore": {"type": "number"}, "surveyScore": {"type": "number"}, "surveyScorePercentage": {"type": "number"}}}, "AnsweredQuestionV2": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "questionId": {"type": "integer", "format": "int64"}, "answeredValue": {"type": "object"}}}, "AnsweredSurveyV2": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyTemplate": {"$ref": "#/components/schemas/SurveyTemplate"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}, "answeredQuestions": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredQuestionV2"}}, "submissionDate": {"type": "string", "format": "date-time"}, "memberEntityNo": {"type": "integer", "format": "int64"}, "loggedInUserMemberEntityNo": {"type": "integer", "format": "int64"}, "surveyScoring": {"$ref": "#/components/schemas/SurveyScoring"}}}, "AnsweredQuestion": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "question": {"$ref": "#/components/schemas/Question"}, "answeredValue": {"type": "object"}}}, "AnsweredSurvey": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyTemplate": {"$ref": "#/components/schemas/SurveyTemplate"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}, "answeredQuestions": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredQuestion"}}, "submissionDate": {"type": "string", "format": "date-time"}, "memberEntityNo": {"type": "integer", "format": "int64"}, "loggedInUserMemberEntityNo": {"type": "integer", "format": "int64"}, "surveyScoring": {"$ref": "#/components/schemas/SurveyScoring"}}}, "SurveyTemplateFilter": {"type": "object", "properties": {"name": {"type": "string"}, "status": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["IN_PROGRESS", "PUBLISHED", "DELETED"]}}, "surveyType": {"type": "string", "enum": ["SURVEY", "QUIZ"]}, "sortBy": {"type": "string", "enum": ["SURVEY_NAME", "SURVEY_ID", "SURVEY_TYPE", "SURVEY_STATUS", "LAST_PUBLISHED_DATE"]}, "sortDirection": {"type": "string", "enum": ["ASC", "DESC"]}}}, "PagingRequest": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}}}, "PageSurveyTemplate": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyTemplate"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"sort": {"$ref": "#/components/schemas/SortObject"}, "offset": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "unpaged": {"type": "boolean"}, "paged": {"type": "boolean"}}}, "SortObject": {"type": "object", "properties": {"empty": {"type": "boolean"}, "sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}}}, "PageSurveyTemplateDto": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyTemplateDto"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "SurveyTemplateDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyHeaderId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "questions": {"type": "integer", "format": "int64"}, "answeredSurveyCount": {"type": "integer", "format": "int64"}, "versionNumber": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "PUBLISHED", "DELETED"]}, "surveyType": {"type": "string", "enum": ["SURVEY", "QUIZ"]}}}, "SurveyVersionActionFilter": {"type": "object", "properties": {"adminUserId": {"type": "integer", "format": "int64"}, "actionTypes": {"type": "array", "items": {"type": "string", "enum": ["CREATE", "EDIT", "DELETE", "COMMENT", "PUBLISH"]}}, "surveyHeaderId": {"type": "integer", "format": "int64"}}}, "PageSurveyVersionAction": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyVersionAction"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "SurveyComment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyVersionId": {"type": "integer", "format": "int64"}, "surveyHeaderId": {"type": "integer", "format": "int64"}, "adminUserName": {"type": "string"}, "commentText": {"type": "string"}, "createTimeInstant": {"type": "string", "format": "date-time"}}}, "SurveyVersionAction": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyName": {"type": "string"}, "surveyId": {"type": "integer", "format": "int64"}, "surveyVersionId": {"type": "integer", "format": "int64"}, "surveyVersionNumber": {"type": "integer", "format": "int64"}, "surveyHeaderId": {"type": "integer", "format": "int64"}, "adminUserId": {"type": "integer", "format": "int64"}, "actionType": {"type": "string", "enum": ["CREATE", "EDIT", "DELETE", "COMMENT", "PUBLISH"]}, "comment": {"$ref": "#/components/schemas/SurveyComment"}, "createTime": {"type": "string", "format": "date-time"}}}, "OptionMeasure": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "descr": {"type": "string"}, "surveyUnits": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyUnit"}}}}, "CustomFormFilter": {"type": "object", "properties": {"name": {"type": "string"}}}, "PageCustomForm": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/CustomForm"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "SurveyCommentFilter": {"type": "object", "properties": {"surveyHeaderId": {"type": "integer", "format": "int64"}}}, "PageSurveyCommentDto": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/SurveyCommentDto"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AnswerDto": {"type": "object", "properties": {"healthAttribute": {"type": "string"}, "answeredValue": {"type": "string"}, "attributeContent": {"type": "array", "items": {"type": "string"}}}}, "AnsweredSurveyResultDto": {"type": "object", "properties": {"surveyName": {"type": "string"}, "surveyFeedback": {"type": "string"}, "answeredSurveyId": {"type": "integer", "format": "int64"}, "options": {"type": "object", "additionalProperties": {"type": "string"}}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredSurveyResultResponse"}}, "surveyScoring": {"$ref": "#/components/schemas/SurveyScoring"}}}, "AnsweredSurveyResultResponse": {"type": "object", "properties": {"description": {"type": "string"}, "label": {"type": "string"}, "answerDto": {"type": "array", "items": {"$ref": "#/components/schemas/AnswerDto"}}}}, "QuestionResponse": {"type": "object", "properties": {"question": {"$ref": "#/components/schemas/Question"}, "content": {"type": "array", "items": {"type": "string"}}}}, "SurveyFeedback": {"type": "object", "properties": {"surveyFeedback": {"type": "array", "items": {"$ref": "#/components/schemas/QuestionResponse"}}}}, "AnsweredSurveyHistoryFilter": {"type": "object", "properties": {"surveyName": {"type": "string"}, "sortBy": {"type": "string", "enum": ["ID", "SUBMITTED_AT"]}, "sortDirection": {"type": "string", "enum": ["ASC", "DESC"]}}}, "AnsweredSurveySubmissionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "submissionDate": {"type": "string", "format": "date-time"}, "surveyScoring": {"$ref": "#/components/schemas/SurveyScoring"}}}, "PageAnsweredSurveySubmissionDto": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredSurveySubmissionDto"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AnsweredSurveyFilter": {"type": "object", "properties": {"surveyTemplateId": {"type": "integer", "format": "int64"}}}, "PageAnsweredSurvey": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredSurvey"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AnsweredSurveyDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "surveyTemplateId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "DONE", "RETAKE_IN_PROGRESS", "PENDING"]}, "answeredQuestionsCount": {"type": "integer", "format": "int64"}, "submittedAt": {"type": "string", "format": "date-time"}, "surveyScoring": {"$ref": "#/components/schemas/SurveyScoring"}}}, "PageAnsweredSurveyDto": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredSurveyDto"}}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AggregatedAnswer": {"type": "object", "properties": {"questionId": {"type": "integer", "format": "int64"}, "questionLabel": {"type": "string"}, "answeredValuesByMembers": {"type": "array", "items": {"$ref": "#/components/schemas/AnsweredValueByMembers"}}}}, "AnsweredValueByMembers": {"type": "object", "properties": {"answeredValue": {"type": "string"}, "memberEntityNumbers": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}}}}