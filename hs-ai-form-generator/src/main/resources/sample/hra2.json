{"id": 2388582, "surveyHeaderId": 324268, "name": "VHR", "identifier": null, "surveyBandings": [{"id": 2392388, "name": "defaultbanding", "from": 0, "to": 100}], "createTime": "2024-11-05T13:38:36.962+00:00", "updateTime": "2024-11-05T13:38:38.697+00:00", "questions": [{"id": 2388583, "key": "fieldGroup1", "type": "custom-horizontal-stepper", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392350, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"fieldIndex": -1, "appearance": "outline", "prepopulateOnRetake": true, "displayName": "-1 - Custom Horizontal Stepper", "column": 1, "options": [], "layoutType": "custom-horizontal-stepper", "disabled": false, "label": " ", "placeholder": "", "row": 1, "type": "custom-horizontal-stepper"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[{\"type\":\"custom-horizontal-stepper\",\"newType\":\"chips-stepper\",\"condition\":\"DONE\"},{\"type\":\"custom-horizontal-stepper\",\"newType\":\"chips-stepper\",\"condition\":\"RETAKE_IN_PROGRESS\"}],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [{"id": 2388596, "key": "fieldGroup3", "type": "form-group", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2397488, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "About you", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [{"id": 2388675, "key": "key95760", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388692, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "1 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "About you", "type": "header", "fieldIndex": 1, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "placeholder": "", "row": 1, "fontWeight": "font-medium"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2388832, "key": "key4", "type": "paragraph", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": true, "hide": false, "weight": 0, "validation": {"id": 2388849, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"backgroundColor": "bg-transparent", "prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "2 - <PERSON><PERSON>", "column": 1, "description": "", "label": "Your health and unique identity are important to us. We strive to create an inclusive experience for every member of our diverse community.  To achieve this, we ask the following question to help us better understand you so we can better shape your experience.", "type": "paragraph", "fieldIndex": 2, "appearance": "outline", "options": [], "fontSize": "text-xs", "disabled": false, "placeholder": "", "row": 1, "fontWeight": "font-normal"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2388732, "key": "key5", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "SexAtBirth", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2388755, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "3 - <PERSON><PERSON>", "column": 1, "description": "Your biological sex is the sex you were assigned at birth. We ask this because biological sex is one of the factors our health risk calculator uses to generate recommendations and results.\n", "wrapper": "step", "label": "What is your sex registered at birth? ", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 3, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2388753, "value": "Female", "label": "Female", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "SexAtBirth", "points": null, "feedbackContents": []}, {"id": 2388754, "value": "Male", "label": "Male", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "SexAtBirth", "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": "", "attribute": false, "chipType": "standard"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2388641, "key": "key7", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "GenderIdentification", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388665, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "4 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Is the gender you identify with the same as your sex registered at birth?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 4, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2388662, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388663, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388664, "value": "PreferNotToAnswer", "label": "Prefer not to say", "overrideLabel": "Prefer not to say", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "attribute": false, "placeholder": "", "chipType": "standard"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2388610, "key": "key13578", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "LifestyleHealthAssessment", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388634, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "5 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "How would you describe your overall health during the last year?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 5, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2388631, "value": "Poor", "label": "Poor", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388629, "value": "Fair", "label": "Fair", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388630, "value": "Good", "label": "Good", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388632, "value": "VeryGood", "label": "Very Good", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388633, "value": "Excellent", "label": "Excellent", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2388701, "key": "key39877", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "DemographicQualificationHighest", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388725, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "6 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "What is your highest level of education?\n", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 6, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2388720, "value": "DidNotCompleteHighSchool", "label": "Did not complete High School", "overrideLabel": "Did not complete High School", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388724, "value": "HighSchool", "label": "High School graduate or GED", "overrideLabel": "High School graduate or GED", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388721, "value": "SomeCollege", "label": "Some College", "overrideLabel": "Some College", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388723, "value": "CollegeDegree", "label": "College graduate", "overrideLabel": "College graduate", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388722, "value": "PostgraduateDegreeFromUniversity", "label": "Postgraduate", "overrideLabel": "Postgraduate", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2388799, "key": "key39878", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "DemographicHispanicLatinoSpanishOrigin", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388825, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "7 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "", "wrapper": "step", "label": "Are you of Hispanic, Latino or Spanish origin?", "type": "radio", "multipleAnswers": 0, "required": true, "fieldIndex": 7, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2388824, "value": "Yes", "label": "Yes", "overrideLabel": "Yes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388822, "value": "No", "label": "No", "overrideLabel": "No", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388821, "value": "Unknown", "label": "Unavailable or unknown to me ", "overrideLabel": "Unavailable or unknown to me ", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388823, "value": "DeclineToAnswer", "label": "I decline to answer", "overrideLabel": "I decline to answer", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2388762, "key": "key1453857", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "DemographicRaceBlackAfricanAmerican", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388789, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "8 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "What race do you identify with? (One or more categories may be marked.)", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 8, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": false, "options": [{"id": 2388782, "value": "AmerIndianAlaskanNative", "label": "American Indian/Alaskan Native", "overrideLabel": "American Indian/Alaskan Native", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388781, "value": "Asian", "label": "Asian", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388786, "value": "BlackAfricanAmerican", "label": "Black or African American", "overrideLabel": "Black or African American", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388783, "value": "HawaiianPacificIslander", "label": "Native Hawaiian/Other Pacific Islander", "overrideLabel": "Native Hawaiian/Other Pacific Islander", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388784, "value": "White", "label": "White", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388787, "value": "Other", "label": "Other", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388785, "value": "Unknown", "label": "Unavailable or unknown to me", "overrideLabel": "Unavailable or unknown to me", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2388788, "value": "DeclineToAnswer", "label": "I decline to answer", "overrideLabel": "I decline to answer", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": "", "fieldType": "chips"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389364, "key": "fieldGroup8", "type": "form-group", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2397461, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Physical activity and fitness", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2389566, "key": "key95761", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389582, "show": false, "messages": {"min": "This value should be more than undefined", "max": "This value should be less than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "9 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Physical activity and fitness\n", "type": "header", "fieldIndex": 9, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "row": 1, "placeholder": "", "fontWeight": "font-medium"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389588, "key": "key26629", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "PhysicalActivityExerciseDaysPerWeek", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389615, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "10 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "On average how many days a week do you exercise?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 10, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2389607, "value": "0", "label": "0", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389609, "value": "1", "label": "1", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389612, "value": "2", "label": "2", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389613, "value": "3", "label": "3", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389614, "value": "4", "label": "4", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389610, "value": "5", "label": "5", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389611, "value": "6", "label": "6", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389608, "value": "7", "label": "7", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389378, "key": "key26630", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "PhysicalActivityExerciseMinutes", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389406, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "11 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "", "wrapper": "step", "label": "On the days you do exercise, on average, about how many minutes do you spend doing so?", "type": "radio", "required": true, "fieldIndex": 11, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2389401, "value": "FifteenMinute", "label": "15 minutes", "overrideLabel": "15 minutes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389402, "value": "ThirtyMinute", "label": "30 minutes", "overrideLabel": "30 minutes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389404, "value": "FortyFiveMinute", "label": "45 minutes", "overrideLabel": "45 minutes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389403, "value": "SixtyMinute", "label": "60 minutes", "overrideLabel": "60 minutes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389399, "value": "SeventyFiveMinute", "label": "75 minutes", "overrideLabel": "75 minutes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389400, "value": "NinetyMinute", "label": "90 minutes", "overrideLabel": "90 minutes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389405, "value": "120orMoreMinute", "label": "120 minutes or more", "overrideLabel": "120 minutes or more", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"7\",\"id\":\"139672\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"1\",\"id\":\"139672\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"2\",\"id\":\"139672\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"3\",\"id\":\"139672\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"4\",\"id\":\"139672\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"5\",\"id\":\"139672\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"6\",\"id\":\"139672\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{}}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2389622, "key": "key26631", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "PhysicalActivityExerciseIntensity", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389645, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "12 - <PERSON><PERSON>", "column": 1, "description": "Light activity could be a slow, easy walk or bike ride or light housekeeping, like washing dishes. Moderate activity would be a quick walk or bike ride or any activity where you could talk but not sing. Any cardio activity where you could say only a few words before taking a breath, like running, cycling quickly, or jumping rope, would be considered vigorous activity.\n", "wrapper": "step", "label": "How intense are these exercise sessions?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 12, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2389644, "value": "Low", "label": "Light", "overrideLabel": "Light", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389642, "value": "Moderate", "label": "Moderate", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389643, "value": "Vigorous", "label": "Vigorous", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389641, "value": "High", "label": "Combination", "overrideLabel": "Combination", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"1\",\"id\":\"361639\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"2\",\"id\":\"361639\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"3\",\"id\":\"361639\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"4\",\"id\":\"361639\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"5\",\"id\":\"361639\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"6\",\"id\":\"361639\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup8?.key26629\",\"option\":\"7\",\"id\":\"361639\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389497, "key": "key26632", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "InactiveTimePerDay", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389524, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "13 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "Think about the time you spend sitting. Examples include watching television, playing video games, using a computer, sitting at work or school, and sitting during your commute.", "wrapper": "step", "label": "On an average day, how much time do you spend seated or otherwise inactive?", "type": "radio", "required": true, "fieldIndex": 13, "requiredErrorMessage": "This field is required", "buttonType": "stacked", "appearance": "outline", "divider": true, "floatLabel": "always", "options": [{"id": 2389518, "value": "ZerotoTwoHours", "label": "0 - 2 hours", "overrideLabel": "0 - 2 hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389520, "value": "ThreetoFourHours", "label": "3 - 4 hours", "overrideLabel": "3 - 4 hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389523, "value": "FivetoSixHours", "label": "5 - 6 hours", "overrideLabel": "5 - 6 hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389517, "value": "SeventoEightHours", "label": "7 - 8 hours", "overrideLabel": "7 - 8 hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389522, "value": "NinetoTenHours", "label": "9 - 10 hours", "overrideLabel": "9 - 10 hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389519, "value": "EleventoTwelveHours", "label": "11 - 12 hours", "overrideLabel": "11 - 12 hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389521, "value": "ThirteenorMoreHours", "label": "13 or more hours", "overrideLabel": "13 or more hours", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2389413, "key": "fieldGroup266275", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389491, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": ""}, "fieldGroup": [{"id": 2389454, "key": "key21045", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "V02MaxValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389481, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 100", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "14 - Input", "description": "Your fitness device may provide your VO₂ Max value based on your exercise activity, or a fitness specialist can assess it for you. Fitness apps and specialists may use different terms when classifying your fitness level using VO₂ Max. ", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 14, "requiredErrorMessage": "This field is required", "min": 1, "divider": false, "options": [], "disabled": false, "row": 1, "placeholder": "V02 Max value", "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "numberRestriction": "allowBoth", "max": 100, "column": 1, "label": "What is your V02 Max value?", "textInput": true, "appearance": "outline", "suffixText": "mL/kg/min"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup8?.fieldGroup266275?.key266276\",\"option\":\"I'm not sure\",\"id\":\"362770\",\"config\":\"disable\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2389427, "key": "key266276", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389447, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "15 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 15, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2389446, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389531, "key": "key39875", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "PhysicalActivitySatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389559, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "16 - <PERSON><PERSON><PERSON>", "column": 1, "description": "We ask these next questions to understand how you feel about your current physical activity so that we can better support you with guidance and recommendations. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How satisfied are you with your current level of physical activity?", "type": "drop-slider", "required": true, "fieldIndex": 16, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389549, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389550, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389551, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389557, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389552, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389553, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389554, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389555, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389548, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389556, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389558, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389652, "key": "key95778", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "PhysicalActivityMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389680, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "17 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important do you feel it is to increase your current level of physical activity? ", "type": "drop-slider", "required": true, "fieldIndex": 17, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389672, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389670, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389671, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389677, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389669, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389673, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389674, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389678, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389675, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389676, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389679, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390475, "key": "fieldGroup2", "type": "form-group", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2397452, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Nutrition\n", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2390527, "key": "key95762", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390543, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "18 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Nutrition\n", "type": "header", "fieldIndex": 18, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "placeholder": "", "row": 1, "fontWeight": "font-medium"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390549, "key": "key10", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksWholeGrainsPerDay", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392671, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "19 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "Examples of whole grains include brown and wild rice, barley, oats, buckwheat, and quinoa, as well as whole wheat and whole grain couscous, pasta, bread, crackers, or breakfast cereals. For reference, 1 ounce (28 grams) of whole grains is one slice of bread, 1/2 c cooked whole grains or 1 cup of cereal.\n", "wrapper": "step", "label": "\nIn a typical day, how many ounces (grams) of whole grains do you eat? \n", "type": "radio", "required": true, "fieldIndex": 19, "requiredErrorMessage": "This field is required", "buttonType": "stacked", "appearance": "outline", "divider": true, "floatLabel": "always", "options": [{"id": 2390570, "value": "Never", "label": "Never due to food allergies or medical reasons", "overrideLabel": "Never due to food allergies or medical reasons", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390573, "value": "LessThanOne", "label": "Less than 1 ounce (28 grams) per day", "overrideLabel": "Less than 1 ounce (28 grams) per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390571, "value": "OnetoTwo", "label": "1-2 ounces (28-83 grams) per day", "overrideLabel": "1-2 ounces (28-83 grams) per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390572, "value": "ThreetoFour", "label": "3-4 ounces (84-139 grams) per day", "overrideLabel": "3-4 ounces (84-139 grams) per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390574, "value": "FiveorMore", "label": "5+ ounces (140+ grams) per day", "overrideLabel": "5+ ounces (140+ grams) per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390489, "key": "key8", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksNutsPerWeek", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392689, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "20 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "Examples of nuts and seeds include walnuts, sunflower seeds, flax seed, pecans, pistachios, peanut butter, and almond butter without added salt or sugar. For reference, one ounce (28 grams) is one small handful.\n", "wrapper": "step", "label": "In a typical week, how many ounces (grams) of nuts and seeds do you eat?", "type": "radio", "required": true, "fieldIndex": 20, "requiredErrorMessage": "This field is required", "buttonType": "stacked", "appearance": "outline", "divider": true, "floatLabel": "always", "options": [{"id": 2390511, "value": "Never", "label": "Never due to food allergies or medical reasons", "overrideLabel": "Never due to food allergies or medical reasons", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390515, "value": "LessThanOne", "label": "Less than 1 ounce (28 grams) per week", "overrideLabel": "Less than 1 ounce (28 grams) per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390510, "value": "One", "label": "1 ounce (28 grams) per week", "overrideLabel": "1 ounce (28 grams) per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390513, "value": "OneandHalf", "label": "1 ½ ounces  (42 grams) per week", "overrideLabel": "1 ½ ounces  (42 grams) per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390516, "value": "Two", "label": "2 ounces (56 grams) per week", "overrideLabel": "2 ounces (56 grams) per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390514, "value": "TwoandHalf", "label": "2 ½ ounces (70 grams) per week", "overrideLabel": "2 ½ ounces (70 grams) per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390512, "value": "ThreeorMore", "label": "3+ ounces (84 grams) per week", "overrideLabel": "3+ ounces (84 grams) per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390692, "key": "key9", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksLegumesPerWeek", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392551, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "21 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "Examples of legumes include cooked, canned, or dried beans, lentils, chickpeas, or split peas. For reference, a cup is approximately the size of a tennis ball. \n", "wrapper": "step", "label": "In a typical week, how many cups of cooked beans, peas or lentils do you eat?\n", "type": "radio", "required": true, "fieldIndex": 21, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2390719, "value": "Never", "label": "Never due to food allergies or medical reasons", "overrideLabel": "Never due to food allergies or medical reasons", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390717, "value": "LessThanOne", "label": "Less than 1 cup per week ", "overrideLabel": "Less than 1 cup per week ", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390713, "value": "One", "label": "1 cup per week  ", "overrideLabel": "1 cup per week  ", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390715, "value": "OneandHalf", "label": "1 ½ cups per week", "overrideLabel": "1 ½ cups per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390714, "value": "Two", "label": "2 cups per week", "overrideLabel": "2 cups per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390718, "value": "TwoandHalf", "label": "2 ½ cups per week", "overrideLabel": "2 ½ cups per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390716, "value": "ThreeorMore", "label": "3+ cups per week", "overrideLabel": "3+ cups per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390837, "key": "key77", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksFruitPerDay", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392579, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "22 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "Fruit can be fresh, frozen, cooked, canned without added sugar or dried, however, don’t count fruit flavored juice or dried fruit with added sugar. For reference, a cup of fruit is equal to a piece of fresh fruit about the size of a baseball, 1 cup cut fruit, 1/2 c dried fruit or 1 cup of 100% fruit juice.", "wrapper": "step", "label": "In a typical day, how many cups of fruit do you eat?", "type": "radio", "required": true, "fieldIndex": 22, "requiredErrorMessage": "This field is required", "buttonType": "stacked", "appearance": "outline", "divider": true, "floatLabel": "always", "options": [{"id": 2390858, "value": "LessThanOne", "label": "Less than 1 cup per day", "overrideLabel": "Less than 1 cup per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390861, "value": "One", "label": "1 cup per day", "overrideLabel": "1 cup per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390862, "value": "OneandHalf", "label": "1 ½ cups per day", "overrideLabel": "1 ½ cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390859, "value": "Two", "label": "2 cups per day", "overrideLabel": "2 cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390863, "value": "TwoandHalf", "label": "2 ½ cups per day", "overrideLabel": "2 ½ cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390860, "value": "ThreeorMore", "label": "3+ cups per day", "overrideLabel": "3+ cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390620, "key": "key666", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksVegPerDay", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392588, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "23 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "Aim for a variety of vegetables, including dark leafy greens, red and orange vegetables and starchy vegetables like beets and sweet potatoes. Vegetables can be fresh, frozen, canned without added salt or dried, but don't count pickled vegetables or legumes in this category. A cup of vegetables is equal to 1 cup raw or cooked vegetable, 2 cups leafy greens or 1 cup 100% vegetable juice.  ", "wrapper": "step", "label": "In a typical day, how many cups of vegetables do you eat?", "type": "radio", "required": true, "fieldIndex": 23, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2390646, "value": "LessThanOne", "label": "Less than 1 cup per day", "overrideLabel": "Less than 1 cup per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390643, "value": "One", "label": "1 cup per day", "overrideLabel": "1 cup per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390645, "value": "OneandHalf", "label": "1 ½ cups per day", "overrideLabel": "1 ½ cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390641, "value": "Two", "label": "2 cups per day", "overrideLabel": "2 cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390644, "value": "TwoandHalf", "label": "2 ½ cups per day", "overrideLabel": "2 ½ cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390642, "value": "ThreeorMore", "label": "3+ cups per day", "overrideLabel": "3+ cups per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390730, "key": "key2", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksRedMeatPerDay", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392597, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "24 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "Examples of red meat include beef, lamb, and pork. However, it does not include processed meat like sausage or deli meat or other meats like venison, turkey, chicken, or fish. For reference, 3 ounces (85 grams) of red meat is approximately the size of a deck of playing cards.\n", "wrapper": "step", "label": "In a typical week, how often do your meals and snacks include red meat?\n", "type": "radio", "required": true, "fieldIndex": 24, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2390753, "value": "Never", "label": "Never", "overrideLabel": "Never", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390751, "value": "LessThanOne", "label": "Less than 1 time per week", "overrideLabel": "Less than 1 time per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390756, "value": "One", "label": "1 time a week", "overrideLabel": "1 time a week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390754, "value": "Two", "label": "2 times per week", "overrideLabel": "2 times per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390755, "value": "Three", "label": "3 times per week", "overrideLabel": "3 times per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390752, "value": "FourorMore", "label": "4+ times per week", "overrideLabel": "4+ times per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390874, "key": "key55", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksProcessedMeatPerDay", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392606, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "25 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "Processing refers to meat that is cured, fermented, or salted. Examples include bacon, sausage, hot dogs, ham, deli meats, canned meats, and jerky. For reference, 3 ounces (85 grams) of processed meat is approximately the size of a deck of playing cards.", "wrapper": "step", "label": "In a typical week, how often do your meals and snacks include processed meat?", "type": "radio", "required": true, "fieldIndex": 25, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2390895, "value": "Never", "label": "Never", "overrideLabel": "Never", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390896, "value": "LessThanOne", "label": "Less than 1 time per week", "overrideLabel": "Less than 1 time per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390897, "value": "One", "label": "1 time a week", "overrideLabel": "1 time a week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390898, "value": "Two", "label": "2 times per week ", "overrideLabel": "2 times per week ", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390899, "value": "Three", "label": "3 times per week ", "overrideLabel": "3 times per week ", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390900, "value": "FourorMore", "label": "4+ times per week", "overrideLabel": "4+ times per week", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390767, "key": "key42", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "MealsAndSnacksSugarBeveragesPerDay", "focus": false, "hide": false, "weight": null, "validation": {"id": 2395131, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "26 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "Sugar sweetened beverages include carbonated beverages, energy drinks, fruit drinks, and hot beverages with added sugar. Sugar sweetened beverages do not include 100% fruit and vegetable juices.", "wrapper": "step", "label": "In a typical day, how often do you have sugar sweetened beverages?", "type": "radio", "required": true, "fieldIndex": 26, "requiredErrorMessage": "This field is required", "buttonType": "stacked", "appearance": "outline", "divider": true, "floatLabel": "always", "options": [{"id": 2390788, "value": "Never", "label": "Never", "overrideLabel": "Never", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390790, "value": "LessThanOne", "label": "Less than 1 per day", "overrideLabel": "Less than 1 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390792, "value": "One", "label": "1 per day", "overrideLabel": "1 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390789, "value": "Two", "label": " 2 per day", "overrideLabel": " 2 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390793, "value": "Three", "label": " 3 per day", "overrideLabel": " 3 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390791, "value": "FourorMore", "label": "4+ per day", "overrideLabel": "4+ per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390804, "key": "key3", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "TableSaltAtMealtime", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390830, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "27 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "Salt includes coarse or fine salt, sea salt, Himalayan salt, and herb salt. Salt does not include plain dried herbs and spices without added salt.", "wrapper": "step", "label": "How often do you add table salt to your food at mealtimes?\n\n", "type": "radio", "required": true, "fieldIndex": 27, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2390826, "value": "Always", "label": "Always", "overrideLabel": "Always", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390827, "value": "MostOfTheTime", "label": "Most of the time", "overrideLabel": "Most of the time", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390829, "value": "Sometimes", "label": "Sometimes", "overrideLabel": "Sometimes", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390828, "value": "Infrequently", "label": "Infrequently", "overrideLabel": "Infrequently", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390825, "value": "Never", "label": "Never", "overrideLabel": "Never", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2390585, "key": "key95769", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "DietSatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2390613, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "28 - <PERSON><PERSON><PERSON>", "column": 1, "description": "We ask these next questions to understand how you feel about your current eating habits so that we can better support you with guidance and recommendations. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How satisfied are you with your current eating habits?", "type": "drop-slider", "required": true, "fieldIndex": 28, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2390607, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390605, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390602, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390604, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390606, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390612, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390609, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390608, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390610, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390603, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390611, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2390657, "key": "key95779", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "DietMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2390685, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "29 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important do you feel it is to improve your eating habits?", "type": "drop-slider", "required": true, "fieldIndex": 29, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2390680, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390682, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390683, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390674, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390679, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390676, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390675, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390684, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390678, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390677, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390681, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390920, "key": "fieldGroup4", "type": "form-group", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2397470, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Biometrics and health conditions", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2390934, "key": "key95763", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390951, "show": false, "messages": {"min": "This value should be more than undefined", "max": "This value should be less than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "30 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Biometrics and health conditions\n", "type": "header", "fieldIndex": 30, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "row": 1, "placeholder": "", "fontWeight": "font-medium"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391597, "key": "key21", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "<PERSON><PERSON><PERSON><PERSON>", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391621, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "31 - Radio", "tabindex": -1, "hideFieldUnderline": true, "column": 1, "description": "It's important to know if you're pregnant or planning to be, as your nutritional requirements and health care are important factors that support your health and the needs of your growing baby.\n", "wrapper": "step", "label": "Are you currently pregnant or planning to become pregnant? \n", "type": "radio", "required": true, "fieldIndex": 31, "requiredErrorMessage": "This field is required", "buttonType": "stacked", "appearance": "outline", "divider": true, "floatLabel": "always", "options": [{"id": 2391620, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391618, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391619, "value": "PreferNotToSay", "label": "Prefer not to say", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[{\"chosenMemberData\":\"gender\",\"expression\":\"show\",\"gender\":\"female\"},{\"chosenMemberData\":\"age\",\"expression\":\"show\",\"age\":{\"minAge\":18,\"maxAge\":55}}]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2391665, "key": "key530380", "type": "length", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalHeightValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391682, "show": false, "messages": {"inchesMin": "", "min": "This value should not be less than undefined", "inchesMax": "", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "feetMax": "", "feetMin": "", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "32 - Length", "column": 1, "description": "", "wrapper": "step", "label": "How tall are you?", "type": "length", "required": true, "fieldIndex": 32, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2391628, "key": "key41", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalWeightValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391655, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 1400", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "33 - Input", "description": "", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 33, "requiredErrorMessage": "This field is required", "min": 1, "divider": true, "options": [], "disabled": false, "placeholder": "No. of lb", "row": 1, "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "numberRestriction": "allowBoth", "max": 1400, "column": 1, "label": "How much do you weigh?", "textInput": true, "appearance": "outline", "suffixText": "lb"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391809, "key": "fieldGroup375807", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391886, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391849, "key": "key51", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalWaistCircumferenceValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391876, "show": false, "messages": {"min": "This value should not be less than 0", "max": "This value should not be more than 125", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "34 - Input", "description": "Just like your weight, your waist measurement is another way to assess your health risks. We use both to get a better picture of your body composition.\n", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 34, "requiredErrorMessage": "This field is required", "min": 0, "divider": false, "options": [], "disabled": false, "row": 1, "placeholder": "No. of inches", "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "max": 125, "numberRestriction": "allowBoth", "column": 1, "label": "What is your waist circumference?\n", "textInput": true, "appearance": "outline", "suffixText": "inches"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup375807?.key375808\",\"option\":\"I don't know\",\"id\":\"375808\",\"config\":\"disable\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391823, "key": "key375808", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391842, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "35 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 35, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2391841, "value": "I don't know", "label": "I don't know", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391693, "key": "key95772", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "WeightSatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391721, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "36 - <PERSON><PERSON><PERSON>", "column": 1, "description": "We ask the following questions to understand how you feel about your current weight so that we can better support you with guidance and recommendations. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How satisfied are you with your current weight?", "type": "drop-slider", "required": true, "fieldIndex": 36, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2391719, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391710, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391718, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391714, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391711, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391715, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391712, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391717, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391716, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391720, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391713, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2391892, "key": "key95784", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "WeightMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391920, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "37 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important do you feel it is to manage your weight, regardless of your current weight status?", "type": "drop-slider", "required": true, "fieldIndex": 37, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2391914, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391911, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391917, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391909, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391918, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391915, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391912, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391916, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391919, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391910, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391913, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2391927, "key": "fieldGroup261558", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392004, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391968, "key": "key19", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalFastingGlucoseValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391994, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 1000", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "38 - Input", "description": "A fasting glucose or fasting plasma glucose (FPG) test is another measure that helps determine your risk for prediabetes and diabetes. It is also used to help people with diabetes monitor their condition.", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 38, "requiredErrorMessage": "This field is required", "min": 1, "options": [], "disabled": false, "placeholder": "Glucose value", "row": 1, "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "max": 1000, "numberRestriction": "allowBoth", "column": 1, "label": "What is your fasting glucose value?", "textInput": true, "appearance": "outline", "suffixText": "mg/dL"}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup261558?.key261559\",\"option\":\"I'm not sure\",\"id\":\"261559\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391941, "key": "key261559", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391961, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "39 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 39, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2391960, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2392051, "key": "fieldGroup262833", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392188, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2392152, "key": "key20", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalHbA1cLevel", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2392178, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 20", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "40 - Input", "description": "HbA1c or A1C stands for hemoglobin A1c, a measure of your blood glucose level over time. This test is used to assess your risk for developing diabetes or manage diabetes for those have already been diagnosed.", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 40, "requiredErrorMessage": "This field is required", "min": 1, "options": [], "disabled": false, "placeholder": "HbA1c (A1C) value", "row": 1, "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "numberRestriction": "allowBoth", "max": 20, "column": 1, "label": "What is your HbA1c (A1C) value?", "textInput": true, "appearance": "outline", "suffixText": "%"}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup262833?.key262834\",\"option\":\"I'm not sure\",\"id\":\"262834\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2392093, "key": "key262834", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2392113, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "41 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "type": "custom-checkbox", "multipleAnswers": 0, "required": false, "fieldIndex": 41, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2392112, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2392065, "key": "key16", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MedicalCareDiabetes", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392086, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "42 - <PERSON><PERSON>", "column": 1, "description": "You are considered under the care of a doctor if you have a diagnosis of diabetes, a healthcare provider is following your care, or you’ve been instructed to take medication to manage diabetes.\n", "wrapper": "step", "label": "Are you under the care of a doctor for diabetes?", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 42, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2392084, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392085, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2392120, "key": "key264465", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2392145, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "43 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Do you take medication for diabetes as directed by your doctor?\t", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 43, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2392139, "value": "Always", "label": "Always", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392144, "value": "Most of the time", "label": "Most of the time", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392142, "value": "Sometimes", "label": "Sometimes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392143, "value": "Rarely", "label": "Rarely", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392140, "value": "Never", "label": "Never", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392141, "value": "Not prescribed", "label": "Not prescribed", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup262833?.key16\",\"option\":\"Yes\",\"id\":\"362590\",\"config\":\"show\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391313, "key": "fieldGroup78457", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391508, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": " ", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391418, "key": "key458148", "type": "paragraph", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391436, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"backgroundColor": "bg-transparent", "prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "44 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "What is your blood pressure value?", "type": "paragraph", "fieldIndex": 44, "appearance": "outline", "options": [], "disabled": false, "fontSize": "text-base", "row": 1, "placeholder": "", "fontWeight": "font-bold"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391442, "key": "key78460", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalSystolic", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391468, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 250", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "45 - Input", "description": "", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 45, "requiredErrorMessage": "This field is required", "min": 1, "divider": false, "options": [], "disabled": false, "row": 1, "placeholder": "Systolic value", "hideLabel": true, "minErrorMessage": "", "max": 250, "numberRestriction": "allowBoth", "column": 1, "label": "Systolic  (high number)", "textInput": true, "appearance": "outline", "suffixText": "mm Hg"}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup78457?.key245613\",\"option\":\"I'm not sure\",\"id\":\"245613\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391355, "key": "key78461", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalDiastolic", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391380, "show": false, "messages": {"min": "This value should not be less than 1", "max": " Diastolic blood pressure value can't be greater than or equal to the Systolic blood pressure value", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": " Diastolic blood pressure value can't be greater than or equal to the Systolic blood pressure value", "minErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "max": 250, "numberRestriction": "allowBoth", "displayName": "46 - Input", "column": 1, "description": "", "wrapper": "step", "label": "Diastolic  (low number)", "type": "suffix-input", "required": true, "fieldIndex": 46, "textInput": true, "requiredErrorMessage": "This field is required", "appearance": "outline", "min": 1, "options": [], "suffixText": "mm Hg", "disabled": false, "row": 1, "placeholder": "Diastolic value", "hideLabel": true}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup78457?.key245613\",\"option\":\"I'm not sure\",\"id\":\"245613\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{\"minKey\":\"key78460\"},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391481, "key": "key245613", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391501, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "47 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "type": "custom-checkbox", "multipleAnswers": 0, "required": false, "fieldIndex": 47, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2391500, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2391327, "key": "key81", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MedicalCareHighBloodPressure", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391348, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "48 - <PERSON><PERSON>", "column": 1, "description": "Hypertension is the medical term for high blood pressure. You are considered under the care of a doctor if you have a diagnosis of hypertension, a healthcare provider is following your care, or you’ve been ordered to take medication to manage your blood pressure.", "wrapper": "step", "label": "Are you under the care of a doctor for hypertension?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 48, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2391347, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391346, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2391390, "key": "key91", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MedicalBloodPressureTreatment", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391411, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "49 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Do you take medication for hypertension as directed by your doctor?", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 49, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2391409, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2391410, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup78457?.key81\",\"option\":\"Yes\",\"id\":\"363138\",\"config\":\"show\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391230, "key": "fieldGroup255093", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391307, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391244, "key": "key11", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalTotalCholesterolValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391270, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 1250", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "50 - Input", "description": "You can learn your total cholesterol levels from a blood cholesterol test. Your blood cholesterol is linked to your risk of heart disease.\n", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 50, "requiredErrorMessage": "This field is required", "min": 1, "options": [], "disabled": false, "placeholder": "Cholesterol value", "row": 1, "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "max": 1250, "numberRestriction": "allowBoth", "column": 1, "label": "What is your total cholesterol value? ", "textInput": true, "appearance": "outline", "suffixText": "mg/dL"}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup255093?.key255094\",\"option\":\"I'm not sure\",\"id\":\"255094\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391280, "key": "key255094", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391300, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "51 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 51, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2391299, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391514, "key": "fieldGroup256121", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391591, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391555, "key": "key18", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalLDLCholesterolValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391581, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 500", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "52 - Input", "description": "You may be more familiar with your total cholesterol number, but we are also looking for your LDL (low density lipoprotein.) It is one type of cholesterol that is included in most cholesterol tests. You may have heard it referred to as the \"bad\" cholesterol.", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 52, "requiredErrorMessage": "This field is required", "min": 1, "options": [], "disabled": false, "placeholder": "LDL value", "row": 1, "measurementUnits": 22, "hideLabel": true, "minErrorMessage": "", "max": 500, "numberRestriction": "allowBoth", "column": 1, "label": "What is your LDL cholesterol value? ", "textInput": true, "appearance": "outline", "suffixText": "mg/dL"}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup256121?.key256122\",\"option\":\"I'm not sure\",\"id\":\"256122\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391528, "key": "key256122", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391548, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "53 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 53, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2391547, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391149, "key": "fieldGroup257809", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391224, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391163, "key": "key257810", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalHDLCholesterolValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391187, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 500", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "minErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "numberRestriction": "allowBoth", "max": 500, "displayName": "54 - Input", "column": 1, "description": "You may be more familiar with your total cholesterol number, but we are also looking for your HDL (high density lipoprotein.) It is included in most cholesterol tests. You may have heard it referred to as the \"good\" cholesterol.", "wrapper": "step", "label": "What is your HDL cholesterol value? ", "type": "suffix-input", "required": true, "fieldIndex": 54, "requiredErrorMessage": "This field is required", "min": 1, "appearance": "outline", "options": [], "suffixText": "mg/dL", "disabled": false, "placeholder": "HDL value", "row": 1, "hideLabel": true}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup257809?.key257811\",\"option\":\"I'm not sure\",\"id\":\"257811\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391197, "key": "key257811", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391217, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "55 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 55, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2391216, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391728, "key": "fieldGroup259423", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391803, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2391742, "key": "key259424", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "MedicalTriglycerideValue", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391766, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 1000", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "minErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "numberRestriction": "allowBoth", "max": 1000, "displayName": "56 - Input", "column": 1, "description": "You may be more familiar with your total cholesterol number, but we are also looking for your triglycerides number. It is sometimes included in a cholesterol test.", "wrapper": "step", "label": "What is your triglyceride value? ", "type": "suffix-input", "required": true, "fieldIndex": 56, "requiredErrorMessage": "This field is required", "min": 1, "appearance": "outline", "options": [], "suffixText": "mg/dL", "disabled": false, "placeholder": "Triglycerides value", "row": 1, "hideLabel": true}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup4?.fieldGroup259423?.key259425\",\"option\":\"I'm not sure\",\"id\":\"259425\",\"config\":\"disable\"}],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2391776, "key": "key259425", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391796, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "57 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "multipleAnswers": 0, "type": "custom-checkbox", "required": false, "fieldIndex": 57, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2391795, "value": "I'm not sure", "label": "I'm not sure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390957, "key": "fieldGroup749077", "type": "dialog", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391102, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"fieldIndex": -1, "appearance": "outline", "prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON>", "column": 1, "options": [], "layoutType": "dialog", "disabled": false, "placeholder": "", "row": 1, "label": "label", "type": "dialog"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[{\"chosenMemberData\":\"Policy Roles\",\"expression\":\"show\",\"policyRoles\":\"SP\"}],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [{"id": 2391057, "key": "key7490766", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391073, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "58 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Medical history questions", "type": "header", "fieldIndex": 58, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "row": 1, "placeholder": "", "fontWeight": "font-normal"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391079, "key": "key7490767", "type": "paragraph", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391096, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"backgroundColor": "bg-transparent", "prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "59 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "In accordance with the Genetic Information Nondiscrimination Act of 2008 the completion of your medical history questions on this screen is voluntary. Any financial incentives associated with the completion of this HRA will be provided whether or not your medical hisotry questions are answered. The information will be used to personalize your Vitality experience and recommendations and will not be available to your employer on an individually identifiable basis. Please click ACCEPT if you voluntarily agree to provide this information.", "type": "paragraph", "fieldIndex": 59, "appearance": "outline", "options": [], "fontSize": "text-xs", "disabled": false, "row": 1, "placeholder": "", "fontWeight": "font-normal"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390970, "key": "fieldGroup7490763", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391040, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "label", "type": "form-group", "layout": "horizontal", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "justify-content": "justify-end", "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": ""}, "fieldGroup": [{"id": 2391012, "key": "key7490764", "type": "button", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391034, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"stacked": "standard", "prepopulateOnRetake": true, "color": "primary", "buttonValue": false, "displayName": "60 - <PERSON><PERSON>", "buttonStyle": "mdc-button--outlined mat-mdc-outlined-button", "icon": "none", "column": 1, "navigateTo": "", "description": "Decline", "wrapper": "form-field", "label": "label", "type": "button", "fieldIndex": 60, "buttonType": "reset", "appearance": "outline", "options": [], "disabled": false, "placeholder": "", "row": 1, "position": "justify-end", "validate": false}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390985, "key": "key7490765", "type": "button", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2391006, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"stacked": "standard", "prepopulateOnRetake": true, "color": "primary", "buttonValue": true, "displayName": "61 - <PERSON><PERSON>", "buttonStyle": "mdc-button--raised mat-mdc-raised-button", "icon": "none", "column": 1, "description": "Accept", "navigateTo": "", "wrapper": "form-field", "label": "label", "type": "button", "fieldIndex": 61, "buttonType": "submit", "appearance": "outline", "options": [], "disabled": false, "placeholder": "", "row": 1, "validate": false}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391046, "key": "modalResult", "type": "input", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": true, "weight": null, "validation": {"id": 2391055, "show": false, "messages": {}}, "questionSurveyUnit": null, "props": {"fieldIndex": 62, "hidden": true, "prepopulateOnRetake": true, "displayName": "62 - input", "options": [], "disabled": false, "placeholder": "", "label": "", "type": "input"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["form-field"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2391108, "key": "key76574", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2391142, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "63 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Have you ever been diagnosed with or prescribed medication for any of the following conditions?", "type": "chips", "multipleAnswers": 0, "required": false, "fieldIndex": 63, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2391131, "value": "Anxiety", "label": "Anxiety", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MentalHealthAnxiety", "points": null, "feedbackContents": []}, {"id": 2391130, "value": "Asthma", "label": "Asthma", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalAsthma", "points": null, "feedbackContents": []}, {"id": 2391127, "value": "Back pain", "label": "Back pain", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHistoryBackPain", "points": null, "feedbackContents": []}, {"id": 2391133, "value": "Cancer", "label": "Cancer", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalCancer", "points": null, "feedbackContents": []}, {"id": 2391139, "value": "Chronic lung disease", "label": "Chronic lung disease", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalChronicLungDisease", "points": null, "feedbackContents": []}, {"id": 2391137, "value": "Chronic pain", "label": "Chronic pain", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHistoryChronicPain", "points": null, "feedbackContents": []}, {"id": 2391128, "value": "Depression", "label": "Depression", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MentalHealthDepression", "points": null, "feedbackContents": []}, {"id": 2391129, "value": "Diabetes", "label": "Diabetes", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalDiabetes", "points": null, "feedbackContents": []}, {"id": 2391132, "value": "Heart disease", "label": "Heart disease", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHeartDisease", "points": null, "feedbackContents": []}, {"id": 2391135, "value": "High blood pressure", "label": "High blood pressure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHypertension", "points": null, "feedbackContents": []}, {"id": 2391134, "value": "High cholesterol", "label": "High cholesterol", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalCholesterolHigh", "points": null, "feedbackContents": []}, {"id": 2391136, "value": "Menopause", "label": "Menopause", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalMenopause", "points": null, "feedbackContents": []}, {"id": 2391141, "value": "Osteoporosis", "label": "Osteoporosis", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalOsteoporosis", "points": null, "feedbackContents": []}, {"id": 2391140, "value": "Prediabetes", "label": "Prediabetes", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalPrediabetes", "points": null, "feedbackContents": []}, {"id": 2391138, "value": "Stroke", "label": "Stroke", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalStroke", "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[{\"key\":\"fieldGroup749077\",\"option\":true,\"id\":\"749077\",\"config\":\"show\"}]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2392010, "key": "key749076", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2392044, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "64 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Have you ever been diagnosed with or prescribed medication for any of the following conditions?", "multipleAnswers": 0, "type": "chips", "required": false, "fieldIndex": 64, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2392030, "value": "Anxiety", "label": "Anxiety", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MentalHealthAnxiety", "points": null, "feedbackContents": []}, {"id": 2392036, "value": "Asthma", "label": "Asthma", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalAsthma", "points": null, "feedbackContents": []}, {"id": 2392042, "value": "Back pain", "label": "Back pain", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHistoryBackPain", "points": null, "feedbackContents": []}, {"id": 2392039, "value": "Cancer", "label": "Cancer", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalCancer", "points": null, "feedbackContents": []}, {"id": 2392043, "value": "Chronic lung disease", "label": "Chronic lung disease", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalChronicLungDisease", "points": null, "feedbackContents": []}, {"id": 2392029, "value": "Chronic pain", "label": "Chronic pain", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHistoryChronicPain", "points": null, "feedbackContents": []}, {"id": 2392033, "value": "Depression", "label": "Depression", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MentalHealthDepression", "points": null, "feedbackContents": []}, {"id": 2392031, "value": "Diabetes", "label": "Diabetes", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalDiabetes", "points": null, "feedbackContents": []}, {"id": 2392038, "value": "Heart disease", "label": "Heart disease", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHeartDisease", "points": null, "feedbackContents": []}, {"id": 2392040, "value": "High blood pressure", "label": "High blood pressure", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalHypertension", "points": null, "feedbackContents": []}, {"id": 2392034, "value": "High cholesterol", "label": "High cholesterol", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalCholesterolHigh", "points": null, "feedbackContents": []}, {"id": 2392035, "value": "Menopause", "label": "Menopause", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalMenopause", "points": null, "feedbackContents": []}, {"id": 2392041, "value": "Osteoporosis", "label": "Osteoporosis", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalOsteoporosis", "points": null, "feedbackContents": []}, {"id": 2392037, "value": "Prediabetes", "label": "Prediabetes", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalPrediabetes", "points": null, "feedbackContents": []}, {"id": 2392032, "value": "Stroke", "label": "Stroke", "overrideLabel": "", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": "MedicalStroke", "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"expressionConfigs\":[],\"modalConfigs\":[],\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[{\"chosenMemberData\":\"Policy Roles\",\"expression\":\"hide\",\"policyRoles\":\"SP\"}]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389987, "key": "fieldGroup2306841", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392449, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"layout": "vertical", "fieldIndex": -1, "appearance": "outline", "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "options": [], "layoutType": "form-group", "disabled": false, "label": "label", "placeholder": "", "row": 1, "type": "form-group"}, "expressions": {}, "fieldGroup": [{"id": 2390138, "key": "key2306842", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2390156, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "65 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Prevention", "type": "header", "fieldIndex": 65, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "placeholder": "", "row": 1, "fieldType": "header", "fontWeight": "font-medium"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390068, "key": "key2306843", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "BreastScreenLast2Years", "focus": false, "hide": false, "weight": null, "validation": {"id": 2390093, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "66 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Have you had a breast cancer screening (mammogram) within the last 2 years?", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 66, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2390091, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390088, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390090, "value": "DeclineToAnswer", "label": "Prefer not to say", "overrideLabel": "Prefer not to say", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390092, "value": "DontRemember", "label": "I don’t remember", "overrideLabel": "I don’t remember", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390089, "value": "NoDrNotNecessary", "label": "My doctor says it’s not necessary", "overrideLabel": "My doctor says it’s not necessary", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1, "fieldType": "chips"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[{\"chosenMemberData\":\"gender\",\"expression\":\"show\",\"gender\":\"female\"},{\"chosenMemberData\":\"age\",\"expression\":\"show\",\"age\":{\"minAge\":40,\"maxAge\":79}}],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":[],\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2390103, "key": "key2306844", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "CervicalScreenLast3Years", "focus": false, "hide": false, "weight": null, "validation": {"id": 2397479, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "67 - <PERSON><PERSON>", "column": 1, "description": "There are two standard screening tests used to detect cervical cancer – the pap smear test and the HPV test. Both tests are done by scraping cells from the cervix for testing. A pap smear test detects abnormal cells in the cervix, while the HPV test detects the HPV virus that causes cervical cancer. ", "wrapper": "step", "label": "Have you had a cervical cancer screening (pap smear or HPV test) within the last 3 years?", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 67, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2390126, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390123, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390124, "value": "DeclineToAnswer", "label": "Prefer not to say", "overrideLabel": "Prefer not to say", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390125, "value": "DontRemember", "label": "I don’t remember", "overrideLabel": "I don’t remember", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390127, "value": "NoDrNotNecessary", "label": "My doctor says it’s not necessary", "overrideLabel": "My doctor says it’s not necessary", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": "", "fieldType": "chips"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[{\"chosenMemberData\":\"age\",\"expression\":\"show\",\"age\":{\"minAge\":21,\"maxAge\":65}},{\"chosenMemberData\":\"gender\",\"expression\":\"show\",\"gender\":\"female\"}],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":[],\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389999, "key": "key2306846", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "ColonScreenLast5Years", "focus": false, "hide": false, "weight": null, "validation": {"id": 2396715, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "68 - <PERSON><PERSON>", "column": 1, "description": "There are two standard screening tests used to detect colon cancer – the colonoscopy and the stool (feces) test. A colonoscopy is performed by a doctor who looks at the inside of your colon with a scope. A stool (feces) test is done at home and detects cancer in your stool.", "wrapper": "step", "label": "Have you had a colon cancer screening (colonoscopy, sigmoidoscopy or stool test) within the last 5 years?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 68, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2390021, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390022, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390019, "value": "DeclineToAnswer", "label": "Prefer not to say", "overrideLabel": "Prefer not to say", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390020, "value": "DontRemember", "label": "I don’t remember", "overrideLabel": "I don’t remember", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390023, "value": "NoDrNotNecessary", "label": "My doctor says it’s not necessary", "overrideLabel": "My doctor says it’s not necessary", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1, "fieldType": "chips"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[{\"chosenMemberData\":\"age\",\"expression\":\"show\",\"age\":{\"minAge\":45,\"maxAge\":75}}],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":[],\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2390034, "key": "key2306847", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "FluVaccineLastYear", "focus": false, "hide": false, "weight": null, "validation": {"id": 2390058, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "69 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Did you get a flu vaccine within the last year?", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 69, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2390054, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390055, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390056, "value": "MedicalReason", "label": "No, due to medical reasons", "overrideLabel": "No, due to medical reasons", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}, {"id": 2390057, "value": "DeclineToAnswer", "label": "Prefer not to say", "overrideLabel": "Prefer not to say", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": "N/A", "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1, "fieldType": "chips"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389696, "key": "fieldGroup10", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392458, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Mental Health", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2389740, "key": "key95764", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389757, "show": false, "messages": {"min": "This value should be more than undefined", "max": "This value should be less than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "70 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Mental health\n", "type": "header", "fieldIndex": 70, "appearance": "outline", "options": [], "disabled": false, "fontSize": "text-xl", "placeholder": "", "row": 1, "fontWeight": "font-medium"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389863, "key": "key555", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "StressedOrOverwhelmedPastTwoWeeks", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389887, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "71 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "In the last two weeks, how often have you felt stressed or overwhelmed?\n", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 71, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2389884, "value": "Never", "label": "Never", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389882, "value": "Seldom", "label": "Almost never", "overrideLabel": "Almost never", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389886, "value": "Sometimes", "label": "Sometimes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389885, "value": "VeryOften", "label": "Fairly often", "overrideLabel": "Fairly often", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389883, "value": "Always", "label": "Very Often", "overrideLabel": "Very Often", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389894, "key": "key437618", "type": "paragraph", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389912, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"backgroundColor": "bg-transparent", "prepopulateOnRetake": true, "color": "text-neutral-900", "displayName": "72 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Over the last two weeks, how often have you been bothered by the following problems?\n", "type": "paragraph", "fieldIndex": 72, "appearance": "outline", "options": [], "fontSize": "text-base", "disabled": false, "placeholder": "", "row": 1, "fontWeight": "font-bold"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389918, "key": "key131234", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MentalHealthNervousAnxiousPastTwoWeeks", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389941, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "73 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Feeling nervous, anxious or on edge\n", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 73, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2389940, "value": "NotAtAll", "label": "Not at all", "overrideLabel": "Not at all", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389938, "value": "Several", "label": "Several days", "overrideLabel": "Several days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389937, "value": "MoreThanHalf", "label": "More than half the days", "overrideLabel": "More than half the days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389939, "value": "NearlyEvery", "label": "Nearly every day", "overrideLabel": "Nearly every day", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389763, "key": "key7777", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MentalHealthStopWorryingPastTwoWeeks", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389786, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "74 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Not being able to stop or control worrying\n", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 74, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2389782, "value": "NotAtAll", "label": "Not at all", "overrideLabel": "Not at all", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389783, "value": "Several", "label": "Several days", "overrideLabel": "Several days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389784, "value": "MoreThanHalf", "label": "More than half the days", "overrideLabel": "More than half the days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389785, "value": "NearlyEvery", "label": "Nearly every day", "overrideLabel": "Nearly every day", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389710, "key": "key888", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MentalHealthLittleInterestPastTwoWeeks", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389733, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "75 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Little interest or pleasure in doing things", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 75, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2389732, "value": "NotAtAll", "label": "Not at all", "overrideLabel": "Not at all", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389730, "value": "Several", "label": "Several days", "overrideLabel": "Several days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389731, "value": "MoreThanHalf", "label": "More than half the days", "overrideLabel": "More than half the days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389729, "value": "NearlyEvery", "label": "Nearly every day", "overrideLabel": "Nearly every day", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389948, "key": "key999", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "MentalHealthDepressedHopelessPastTwoWeeks", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389971, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "76 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Feeling down, depressed or hopeless\n", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 76, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2389967, "value": "NotAtAll", "label": "Not at all", "overrideLabel": "Not at all", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389968, "value": "Several", "label": "Several days", "overrideLabel": "Several days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389970, "value": "MoreThanHalf", "label": "More than half the days", "overrideLabel": "More than half the days", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389969, "value": "NearlyEvery", "label": "Nearly every day", "overrideLabel": "Nearly every day", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389793, "key": "key95777", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "MentalWellbeingSatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389821, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "77 - <PERSON><PERSON><PERSON>", "column": 1, "description": "We understand that managing stress and emotional challenges is a part of life. Helping us learn about how satisfied you are with the strategies and methods you use to cope will help us better support your wellbeing. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How satisfied are you with the strategies and methods you use to cope with stress or emotional challenges?", "type": "drop-slider", "required": true, "fieldIndex": 77, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389818, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389815, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389817, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389820, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389819, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389811, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389810, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389814, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389812, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389816, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389813, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389828, "key": "key95789", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "MentalWellbeingMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389856, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "78 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important is it to you to improve the methods you use to cope with stress or emotional challenges?", "type": "drop-slider", "required": true, "fieldIndex": 78, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389848, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389850, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389845, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389849, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389851, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389853, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389846, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389854, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389855, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389847, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389852, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390174, "key": "fieldGroup5", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392395, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "notWholeNumber": "Please type whole number", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Life and work satisfaction", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2390188, "key": "key95765", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390204, "show": false, "messages": {"min": "This value should be more than undefined", "max": "This value should be less than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "79 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Life and work satisfaction\n", "type": "header", "fieldIndex": 79, "appearance": "outline", "options": [], "fontSize": "text-xl", "disabled": false, "row": 1, "placeholder": "", "fontWeight": "font-medium"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390245, "key": "key3333", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "CurrentLifeSatisfaction", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390273, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "80 - <PERSON><PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Overall, how satisfied are you with your life nowadays?\n", "type": "drop-slider", "required": true, "fieldIndex": 80, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2390267, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390262, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390268, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390263, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390264, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390271, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390269, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390272, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390270, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390265, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390266, "value": "10", "label": "10 ", "overrideLabel": null, "description": "Completely", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2390210, "key": "key66", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "WorkSatisfaction", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390238, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "81 - <PERSON><PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Overall, how satisfied are you with your job?\n", "type": "drop-slider", "required": true, "fieldIndex": 81, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2390236, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390233, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390237, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390229, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390227, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390228, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390231, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390234, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390232, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390235, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390230, "value": "10", "label": "10", "overrideLabel": null, "description": "Completely", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390289, "key": "fieldGroup6", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2390466, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Absenteeism & Productivity\n", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2390443, "key": "key95766", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390460, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "82 - Head<PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Absenteeism and productivity\n", "type": "header", "fieldIndex": 82, "appearance": "outline", "options": [], "disabled": false, "fontSize": "text-xl", "row": 1, "placeholder": "", "fontWeight": "font-medium"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2390373, "key": "key666666", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleWorkingHoursExpectedByEmployerInTypicalWeek", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390398, "show": false, "messages": {"min": "This value should not be less than 0", "max": "This value should not be more than 168", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "max": 168, "numberRestriction": "allowBoth", "displayName": "83 - Input", "column": 1, "description": "", "wrapper": "step", "label": "In a typical week, how many hours does your employer expect you to work according to your contract (not including any paid or unpaid overtime)?\n", "type": "suffix-input", "required": true, "fieldIndex": 83, "textInput": true, "requiredErrorMessage": "This field is required", "appearance": "outline", "min": 0, "divider": true, "options": [], "suffixText": "hours", "disabled": false, "placeholder": "No. of hours", "row": 1, "hideLabel": true}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2390338, "key": "key444444", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleWorkingHoursInPast7Days", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390363, "show": false, "messages": {"min": "This value should not be less than 0", "max": "This value should not be more than 168", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "numberRestriction": "allowBoth", "max": 168, "displayName": "84 - Input", "column": 1, "description": "", "wrapper": "step", "label": "During the past seven days, how many hours did you actually work?\n", "type": "suffix-input", "required": true, "fieldIndex": 84, "textInput": true, "requiredErrorMessage": "This field is required", "appearance": "outline", "min": 0, "divider": true, "options": [], "suffixText": "hours", "disabled": false, "row": 1, "placeholder": "No. of hours", "hideLabel": true}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2390408, "key": "key33333", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleWellnessWorkHoursMissedInLast7Days", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390433, "show": false, "messages": {"min": "This value should not be less than 0", "max": "This value should not be more than 168", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "max": 168, "numberRestriction": "allowBoth", "displayName": "85 - Input", "column": 1, "description": "Include hours you missed on sick days, times you went in late, left early, etc., because of your health problems.\n", "wrapper": "step", "label": "During the past seven days, how many hours did you miss from work because of health problems? ", "type": "suffix-input", "required": true, "fieldIndex": 85, "requiredErrorMessage": "This field is required", "textInput": true, "appearance": "outline", "min": 0, "divider": true, "options": [], "suffixText": "hours", "disabled": false, "placeholder": "No. of hours", "row": 1, "hideLabel": true}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2390303, "key": "key5555555", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleWellnessProductivityInLast7Days", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2390331, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "86 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Think about days you were limited in the amount or kind of work you could do, days you accomplished less than you would like, or days you could not do your work as carefully as usual. If health problems affected your work only a little, choose a low number. Choose a high number if health problems affected your work a great deal.\n\nConsider only how much health problems affected productivity while you were working.\n", "wrapper": "step", "label": "During the past seven days, how much did your health problems (physical and/or mental) affect your productivity while you were working?\n\n", "type": "drop-slider", "required": true, "fieldIndex": 86, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": false, "options": [{"id": 2390323, "value": "0", "label": "0", "overrideLabel": null, "description": "Health problems had no effect on my work", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390324, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390330, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390321, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390325, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390326, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390328, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390322, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390320, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390327, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2390329, "value": "10", "label": "10", "overrideLabel": null, "description": "Health problems completely prevented me from working", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2388864, "key": "fieldGroup7", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389358, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Alcohol and tobacco", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2389204, "key": "key95767", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389221, "show": false, "messages": {"min": "This value should be more than undefined", "max": "This value should be less than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "87 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Alcohol and tobacco\n", "type": "header", "fieldIndex": 87, "appearance": "outline", "options": [], "disabled": false, "fontSize": "text-xl", "row": 1, "placeholder": "", "fontWeight": "font-medium"}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2389009, "key": "key222111", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "LifestyleAlcoholDrinker", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389030, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "88 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "step", "label": "Do you drink alcohol?", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 88, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2389028, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389029, "value": "No", "label": "No", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389037, "key": "key333111", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleAlcoholConsumptionFrequencyPerWeek", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389063, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 250", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "89 - Input", "description": "Different drinks have different amounts of alcohol in them. One bottle of beer or cider, a glass of wine, and a shot of liquor are each considered a serving.\n", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 89, "requiredErrorMessage": "This field is required", "min": 1, "divider": true, "options": [], "disabled": false, "row": 1, "placeholder": "No. of servings", "hideLabel": true, "minErrorMessage": "", "numberRestriction": "allowBoth", "max": 250, "column": 1, "label": "On average, how many alcoholic drinks do you have per week? \n", "textInput": true, "appearance": "outline", "suffixText": "servings"}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key222111\",\"option\":\"Yes\",\"id\":\"361865\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2388913, "key": "key444111", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "LifestyleAlcoholFrequencyPerOccasion", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388937, "show": false, "messages": {"min": "This value should not be less than undefined", "notWholeNumber": "Please type whole number", "max": "This value should not be more than undefined", "notDecimal": "Please type decimal number", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "outOfRange": "Date is out of range", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "90 - <PERSON><PERSON>", "column": 1, "description": "Consider an occasion as a period of time when you're drinking. If you change your type of drink or your setting in between drinks, it still counts as one occasion.\n", "wrapper": "step", "label": "Which best describes how often you have more than 5 drinks (male sex) or 4 drinks (female sex) on one occasion?", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 90, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2388934, "value": "Daily", "label": "Daily or almost daily", "overrideLabel": "Daily or almost daily", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388932, "value": "Weekly", "label": "Weekly", "overrideLabel": "Weekly", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388935, "value": "Monthly", "label": "Monthly", "overrideLabel": "Monthly", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388933, "value": "LessThanMonth", "label": "Occasionally", "overrideLabel": "Occasionally", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388936, "value": "Never", "label": "Never", "overrideLabel": "Never", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key222111\",\"option\":\"Yes\",\"id\":\"361865\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2388878, "key": "key95770", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "AlcoholConsumptionSatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2388906, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "91 - <PERSON><PERSON><PERSON>", "column": 1, "description": "We’re interested in your thoughts and feelings about your alcohol consumption patterns. Your answers will guide us in offering support and resources that best fit your needs. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How satisfied are you with your current alcohol consumption patterns?", "type": "drop-slider", "required": true, "fieldIndex": 91, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2388903, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388898, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388901, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388900, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388899, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388904, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388905, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388896, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388902, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388895, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388897, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key222111\",\"option\":\"Yes\",\"id\":\"361865\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389102, "key": "key95780", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "AlcoholConsumptionMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389130, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "92 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important do you feel it is to improve your alcohol consumption patterns?", "type": "drop-slider", "required": true, "fieldIndex": 92, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389121, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389125, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389126, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389124, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389123, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389128, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389127, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389119, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389122, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389129, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389120, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key222111\",\"option\":\"Yes\",\"id\":\"361865\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389073, "key": "key555111", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "LifestyleDoYouSmokeCigarettes", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389095, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "93 - <PERSON><PERSON>", "column": 1, "description": "Answer yes if you currently smoke every day or on occasion.\n", "wrapper": "step", "label": "Do you smoke cigarettes?\n\n", "multipleAnswers": 0, "type": "chips", "required": true, "fieldIndex": 93, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "standard", "divider": true, "options": [{"id": 2389092, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389093, "value": "NoUsedTo", "label": "No, but I used to", "overrideLabel": "No, but I used to", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389094, "value": "Never", "label": "No, never", "overrideLabel": "No, never", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389172, "key": "key594083", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleSmokingConsumptionPerDay", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389197, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "94 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "", "wrapper": "step", "label": "On average, how many cigarettes do you smoke per day?", "type": "radio", "required": true, "fieldIndex": 94, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2389196, "value": "LessThan10perDay", "label": "<10 per day", "overrideLabel": "<10 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389194, "value": "10to20PerDay", "label": "10-20 per day", "overrideLabel": "10-20 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389193, "value": "21to30PerDay", "label": "21-30 per day", "overrideLabel": "21-30 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389195, "value": "MoreThan30PerDay", "label": ">30 per day", "overrideLabel": ">30 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"Yes\",\"id\":\"361841\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2389137, "key": "key594084", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleYearsSmoking", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389162, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 99", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "minErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "max": 99, "numberRestriction": "allowBoth", "displayName": "95 - Input", "column": 1, "description": "If you have been smoking for less than 1 year, your answer is 1.", "wrapper": "step", "label": "For how many years have you been smoking?", "type": "suffix-input", "required": true, "fieldIndex": 95, "requiredErrorMessage": "This field is required", "appearance": "outline", "min": 1, "divider": true, "options": [], "suffixText": "years", "disabled": false, "row": 1, "placeholder": "", "hideLabel": true}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"Yes\",\"id\":\"361841\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2389262, "key": "key439644", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleStopSmokingYearsAgo", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389287, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 99", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "minErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "max": 99, "numberRestriction": "allowBoth", "displayName": "96 - Input", "column": 1, "description": "", "wrapper": "step", "label": "How many years ago did you stop smoking?\n", "type": "suffix-input", "required": true, "fieldIndex": 96, "requiredErrorMessage": "This field is required", "appearance": "outline", "min": 1, "divider": false, "options": [], "suffixText": "years", "disabled": false, "placeholder": "No. of years", "row": 1, "hideLabel": true}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"NoUsedTo\",\"id\":\"361841\",\"config\":\"show\"},{\"key\":\"fieldGroup1?.fieldGroup7?.key479348\",\"option\":\"Less than a year\",\"id\":\"483366\",\"config\":\"disable\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": ["notWholeNumber", "notDecimal"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2388947, "key": "key479348", "type": "custom-checkbox", "defaultValue": [], "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2388967, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "97 - Checkbox", "column": 1, "description": "", "wrapper": "step", "label": " ", "type": "custom-checkbox", "multipleAnswers": 0, "required": false, "fieldIndex": 97, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2388966, "value": "Less than a year", "label": "Less than a year", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"NoUsedTo\",\"id\":\"361841\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":{},\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389297, "key": "key594085", "type": "radio", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestylePreviousSmokingConsumptionPerDay", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389322, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "98 - Radio", "tabindex": -1, "column": 1, "hideFieldUnderline": true, "description": "", "wrapper": "step", "label": "On average, how many cigarettes did you smoke per day?", "type": "radio", "required": true, "fieldIndex": 98, "requiredErrorMessage": "This field is required", "appearance": "outline", "buttonType": "stacked", "divider": true, "floatLabel": "always", "options": [{"id": 2389320, "value": "LessThan10PerDay", "label": "<10 per day", "overrideLabel": "<10 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389318, "value": "10to20PerDay", "label": "10-20 per day", "overrideLabel": "10-20 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389321, "value": "21to30PerDay", "label": "21-30 per day", "overrideLabel": "21-30 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389319, "value": "MoreThan30PerDay", "label": ">30 per day", "overrideLabel": ">30 per day", "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"NoUsedTo\",\"id\":\"361841\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": "formlyRadioFlex"}, {"id": 2389329, "key": "key777111", "type": "chips", "defaultValue": [], "hideExpression": null, "healthAttribute": "LifestyleTobaccoOtherThanCigarettes", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2389351, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "99 - <PERSON><PERSON>", "column": 1, "description": "Examples include e-cigarettes, cigars, pipes, smokeless tobacco, or hookah. Select yes if you use any of these or other type of tobacco product.", "wrapper": "step", "label": "Do you use tobacco products, other than cigarettes? ", "type": "chips", "multipleAnswers": 0, "required": true, "fieldIndex": 99, "requiredErrorMessage": "This field is required", "buttonType": "standard", "appearance": "outline", "divider": true, "options": [{"id": 2389348, "value": "Yes", "label": "Yes", "overrideLabel": "", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389350, "value": "NoUsedTo", "label": "No, but I used to", "overrideLabel": "No, but I used to", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389349, "value": "Never", "label": "No, never", "overrideLabel": "No, never", "description": "", "singleAnswer": true, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2389227, "key": "key95771", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "TobaccoUsageSatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389255, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "100 - Slider", "column": 1, "description": "We understand that quitting smoking is a significant step.  To better assist you, we’d like to learn more about your thoughts and feelings about quitting.  Your answers will help us tailor our support to meet your needs and goals. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How ready and willing are you to try to quit smoking? ", "type": "drop-slider", "required": true, "fieldIndex": 100, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389251, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all ready", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389248, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389249, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389246, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389245, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389247, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389250, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389254, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389244, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389252, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389253, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely ready", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "row": 1, "placeholder": ""}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"Yes\",\"id\":\"361841\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2388974, "key": "key95783", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "TobaccoUsageMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2389002, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "101 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important is it to you to quit smoking?", "type": "drop-slider", "required": true, "fieldIndex": 101, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2389000, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388991, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388998, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388999, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388996, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388993, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2389001, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388997, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388994, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388992, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2388995, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[{\"key\":\"fieldGroup1?.fieldGroup7?.key555111\",\"option\":\"Yes\",\"id\":\"361841\",\"config\":\"show\"}],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":{},\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2392203, "key": "fieldGroup9", "type": "form-group", "defaultValue": "{}", "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392344, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "-1 - <PERSON><PERSON> Layout", "column": 1, "label": "Sleep", "type": "form-group", "layout": "vertical", "fieldIndex": -1, "appearance": "outline", "options": [], "layoutType": "form-group", "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": "{\"validationConfigs\":{},\"statusChangeConfigs\":{},\"changeTypeConfigs\":{},\"memberDataConfig\":[]}"}, "fieldGroup": [{"id": 2392322, "key": "key95768", "type": "header", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2392338, "show": false, "messages": {"min": "This value should be more than undefined", "max": "This value should be less than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"color": "text-neutral-900", "prepopulateOnRetake": true, "displayName": "102 - <PERSON><PERSON>", "column": 1, "description": "", "wrapper": "form-field", "label": "Sleep\n", "type": "header", "fieldIndex": 102, "appearance": "outline", "options": [], "disabled": false, "fontSize": "text-xl", "row": 1, "placeholder": "", "fontWeight": "font-medium"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2392287, "key": "key211322222", "type": "suffix-input", "defaultValue": null, "hideExpression": null, "healthAttribute": "LifestyleSleepHoursPerDay", "focus": false, "hide": false, "weight": 0, "validation": {"id": 2392312, "show": false, "messages": {"min": "This value should not be less than 1", "max": "This value should not be more than 24", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"maxErrorMessage": "", "numberInput": true, "prepopulateOnRetake": true, "displayName": "103 - Input", "description": "", "wrapper": "step", "type": "suffix-input", "required": true, "fieldIndex": 103, "requiredErrorMessage": "This field is required", "min": 1, "divider": true, "options": [], "disabled": false, "placeholder": "No. of hours", "row": 1, "hideLabel": true, "minErrorMessage": "", "numberRestriction": "allowBoth", "max": 24, "column": 1, "label": "How many hours do you usually sleep in a 24-hour period?\n", "textInput": true, "appearance": "outline", "suffixText": "hours"}, "expressions": {}, "fieldGroup": [], "validators": {"validation": ["notDecimal", "notWholeNumber"]}, "wrappers": ["step", "form-field"], "className": null}, {"id": 2392217, "key": "key95776", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "SleepSatisfaction", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392245, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "104 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Now that we understand your average hours of sleep, we’d like your input on how you feel about the quality of your sleep to better guide you in the program. Select 0 for any area that does not apply.", "wrapper": "step", "label": "How satisfied are you with the quality of your sleep?", "type": "drop-slider", "required": true, "fieldIndex": 104, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2392238, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392241, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392244, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392243, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392234, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392242, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392237, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392239, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392240, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392236, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392235, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely satisfied", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}, {"id": 2392252, "key": "key95788", "type": "drop-slider", "defaultValue": null, "hideExpression": null, "healthAttribute": "SleepMotivationToImprove", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392280, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"prepopulateOnRetake": true, "displayName": "105 - <PERSON><PERSON><PERSON>", "column": 1, "description": "Select 0 for any area that does not apply.", "wrapper": "step", "label": "How important do you feel it is to improve the quality of your sleep?", "type": "drop-slider", "required": true, "fieldIndex": 105, "requiredErrorMessage": "This field is required", "appearance": "outline", "divider": true, "options": [{"id": 2392270, "value": "0", "label": "0", "overrideLabel": null, "description": "Not at all important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392278, "value": "1", "label": "1", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392279, "value": "2", "label": "2", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392273, "value": "3", "label": "3", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392269, "value": "4", "label": "4", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392277, "value": "5", "label": "5", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392271, "value": "6", "label": "6", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392274, "value": "7", "label": "7", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392275, "value": "8", "label": "8", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392272, "value": "9", "label": "9", "overrideLabel": null, "description": "", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}, {"id": 2392276, "value": "10", "label": "10", "overrideLabel": null, "description": "Extremely important", "singleAnswer": false, "isCorrectAnswer": null, "healthAttribute": null, "points": null, "feedbackContents": []}], "disabled": false, "placeholder": "", "row": 1}, "expressions": {"expressionProperties": ""}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": ["step"], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}], "validators": {"validation": []}, "wrappers": [], "className": null}, {"id": 2392359, "key": "key140868", "type": "button", "defaultValue": null, "hideExpression": null, "healthAttribute": "N/A", "focus": false, "hide": false, "weight": null, "validation": {"id": 2392382, "show": false, "messages": {"min": "This value should not be less than undefined", "max": "This value should not be more than undefined", "minLength": "This value  must be at least undefined characters long.", "pattern": "Invalid pattern", "required": "This field is required"}}, "questionSurveyUnit": null, "props": {"stacked": "stacked", "prepopulateOnRetake": true, "color": "primary", "buttonValue": true, "displayName": "106 - <PERSON><PERSON>", "buttonStyle": "mdc-button--raised mat-mdc-raised-button", "icon": "none", "column": 1, "navigateTo": "", "description": "Save changes", "wrapper": "form-field", "label": "label", "type": "button", "fieldIndex": 106, "appearance": "outline", "buttonType": "submit", "options": [], "disabled": false, "position": "justify-center", "row": 1, "placeholder": "", "validate": true}, "expressions": {"expressionProperties": "{\"memberDataConfig\":[],\"expressionConfigs\":[],\"validationConfigs\":{},\"changeTypeConfigs\":[],\"statusChangeConfigs\":[{\"condition\":\"IN_PROGRESS\"}],\"modalConfigs\":[]}"}, "fieldGroup": [], "validators": {"validation": []}, "wrappers": [], "className": null}], "versionNumber": 7, "status": "PUBLISHED", "surveyDescription": "", "surveyType": "SURVEY", "surveyFeedback": "", "maxScore": null, "options": {"scoringEnabled": "false", "prepopulateOnRetake": "true", "downloadEnabled": "false", "showFeedbackAfterEachQuestion": "false", "showFeedback": "false"}, "comments": null, "numQuestions": 106, "pinned": false}