package com.vitality.ai.fop.domain;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@Getter
@RequiredArgsConstructor
public enum FormType {
    HRA("VHR", "Health Review Assessment");

    private final String surveyTemplateName;
    private final String displayName;

    public static Optional<FormType> findBySurveyTemplateName(String surveyTemplateName) {
        for (FormType type : values()) {
            if (type.getSurveyTemplateName().equals(surveyTemplateName))
                return Optional.of(type);
        }
        return Optional.empty();
    }
}
