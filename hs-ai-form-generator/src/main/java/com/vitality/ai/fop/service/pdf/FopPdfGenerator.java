package com.vitality.ai.fop.service.pdf;

import com.vitality.ai.fop.domain.FormRenderingContext;
import com.vitality.ai.fop.domain.FormType;
import com.vitality.ai.fop.domain.PatientInfo;
import com.vitality.ai.fop.domain.PdfFile;
import com.vitality.ai.fop.service.FormGenerationException;
import com.vitality.ai.fop.service.FormRenderingContextMarshaller;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.fop.apps.FOUserAgent;
import org.apache.fop.apps.Fop;
import org.apache.fop.apps.FopFactory;
import org.apache.fop.apps.MimeConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Templates;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.sax.SAXResult;
import javax.xml.transform.stream.StreamSource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.StringReader;
import java.util.Date;
import java.util.Map;

/**
 * Service for generating PDF forms from templates
 * Includes QR code generation and embedding in XML
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FopPdfGenerator implements PdfGenerator {

    @Value("${form.templates.base-dir:templates}")
    private String templatesBaseDir;

    private final FormRenderingContextMarshaller contextMarshaller;
    private final Environment environment;
    private final FopFactory fopFactory;
    private final TransformerFactory transformerFactory;
    private final Map<String, Templates> templateCache;

    /**
     * Generate a PDF form from the given template and style
     *
     * @param formData Complete form data
     * @return PDF content as byte array
     */
    @Override
    public PdfFile generatePdfFile(FormRenderingContext formData) {
        try {
            log.info("Start generating {} form PDF for {}",
                formData.getFormType(), formData.getPatientInfo().getEntityNo());

            long start = System.currentTimeMillis();

            String formDataXml = contextMarshaller.marshalToXml(formData);
            String template = getTemplateForFormType(formData.getFormType());

            byte[] bytes = generatePdfFileWithFop(formDataXml, template);

            PdfFile pdfFile = new PdfFile(buildFilename(formData), bytes);

            log.info("Finished generating {} form PDF for {} in {} ms",
                formData.getFormType(), formData.getPatientInfo().getEntityNo(), System.currentTimeMillis() - start);

            return pdfFile;
        } catch (Exception e) {
            log.error("Error generating PDF form: {}", e.getMessage(), e);
            throw new FormGenerationException("Failed to generate PDF form", e);
        }
    }

    /**
     * Process FOP transformation to generate PDF
     *
     * @param xmlString       The XML string from FormMarshaller
     * @param xslTemplateName The XSL template name
     * @return PDF content as byte array
     * @throws Exception If there's an error during processing
     */
    private byte[] generatePdfFileWithFop(String xmlString, String xslTemplateName) throws Exception {
        log.info("Processing FOP transformation with template: {}", xslTemplateName);

        FOUserAgent foUserAgent = fopFactory.newFOUserAgent();
        foUserAgent.setProducer("Medical Form Generator");
        foUserAgent.setCreator("Medical Form System");
        foUserAgent.setCreationDate(new Date());

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Fop fop = fopFactory.newFop(MimeConstants.MIME_PDF, foUserAgent, outputStream);

        Templates templates = getOrCompileTemplate(xslTemplateName);
        Transformer transformer = templates.newTransformer();

        Source source = new StreamSource(new StringReader(xmlString));
        Result result = new SAXResult(fop.getDefaultHandler());

        log.info("Starting XSLT transformation and FOP processing");
        transformer.transform(source, result);
        log.info("FOP processing completed successfully");

        return outputStream.toByteArray();
    }

    private Templates getOrCompileTemplate(String xslTemplateName) {
        return templateCache.computeIfAbsent(xslTemplateName, templateName -> {
            try {
                long start = System.currentTimeMillis();
                String path = templatesBaseDir + File.separator + templateName;
                try (InputStream xslStream = new ClassPathResource(path).getInputStream()) {
                    return transformerFactory.newTemplates(new StreamSource(xslStream));
                } finally {
                    log.info("Compiled template {} in {} ms", xslTemplateName, System.currentTimeMillis() - start);
                }
            } catch (Exception e) {
                throw new FormGenerationException("Failed to compile template: " + templateName, e);
            }
        });
    }

    private static String buildFilename(FormRenderingContext formData) {
        String filename = formData.getFormType().name();
        PatientInfo patientInfo = formData.getPatientInfo();
        if (StringUtils.hasLength(patientInfo.getPolicyCardNo())) {
            filename += "_" + patientInfo.getPolicyCardNo();
        }
        filename += "_" + System.currentTimeMillis() + ".pdf";
        return filename;
    }

    private String getTemplateForFormType(FormType formType) {
        String propertyKey = "form.templates.form-type." + formType.name().toLowerCase();
        return environment.getProperty(propertyKey);
    }
}
