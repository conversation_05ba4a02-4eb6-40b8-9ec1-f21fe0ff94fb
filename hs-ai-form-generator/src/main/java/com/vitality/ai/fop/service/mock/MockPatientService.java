package com.vitality.ai.fop.service.mock;

import com.vitality.ai.fop.domain.PatientInfo;
import com.vitality.ai.fop.service.PatientService;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for retrieving patient information
 * In a real application, this would connect to a patient database or EMR system
 */
@Service
@Profile("offline")
public class MockPatientService implements PatientService {

    // Mock patient database for demonstration
    private final Map<Long, PatientInfo> patientDatabase = new HashMap<>();

    /**
     * Constructor to initialize sample patient data
     */
    public MockPatientService() {
        // Initialize with some sample patients
        patientDatabase.put(12345678L, PatientInfo.builder()
            .entityNo(12345678)
            .policyCardNo("VS12345678")
            .name("<PERSON>")
            .dateOfBirth("15/04/1975")
            .gender("Male")
            .build());

        patientDatabase.put(87654321L, PatientInfo.builder()
            .entityNo(87654321)
            .policyCardNo("VS02203395")
            .name("<PERSON>")
            .dateOfBirth("23/09/1982")
            .gender("Female")
            .build());

        patientDatabase.put(11223344L, PatientInfo.builder()
            .entityNo(11223344)
            .policyCardNo("VS01202696")
            .name("Robert Johnson")
            .dateOfBirth("30/11/1968")
            .gender("Male")
            .build());
    }

    /**
     * Get patient information by ID
     *
     * @param entityNo The patient ID
     * @return PatientInfo object or null if not found
     */
    @Override
    public PatientInfo getPatientInfo(long entityNo) {
        return patientDatabase.get(entityNo);
    }


}
