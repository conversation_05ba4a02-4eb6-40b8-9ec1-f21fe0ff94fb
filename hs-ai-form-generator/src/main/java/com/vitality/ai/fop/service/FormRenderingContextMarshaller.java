package com.vitality.ai.fop.service;

import com.vitality.ai.fop.domain.FormRenderingContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;

/**
 * Service for converting form data to/from JSON and XML formats
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FormRenderingContextMarshaller {

    /**
     * Convert FormData object to XML string
     *
     * @param formData The form data to convert
     * @return XML representation of the form data
     */
    public String marshalToXml(FormRenderingContext formData) {
        try {
            XmlMapper xmlMapper = createConfiguredXmlMapper();
            StringWriter writer = new StringWriter();
            xmlMapper.writeValue(writer, formData);
            return writer.toString();
        } catch (Exception e) {
            log.error("Error marshalling form data to XML: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert form data to XML", e);
        }
    }

    /**
     * Convert FormData object to JSON string
     *
     * @param formData The form data to convert
     * @return JSON representation of the form data
     */
    public String marshalToJson(FormRenderingContext formData) {
        try {
            ObjectMapper jsonMapper = createConfiguredJsonMapper();
            return jsonMapper.writeValueAsString(formData);
        } catch (Exception e) {
            log.error("Error marshalling form data to JSON: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert form data to JSON", e);
        }
    }

    /**
     * Convert XML string to FormData object
     *
     * @param xml The XML string to convert
     * @return FormData object
     */
    public FormRenderingContext unmarshalFromXml(String xml) {
        try {
            XmlMapper xmlMapper = createConfiguredXmlMapper();
            return xmlMapper.readValue(xml, FormRenderingContext.class);
        } catch (Exception e) {
            log.error("Error unmarshalling XML to form data: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert XML to form data", e);
        }
    }

    /**
     * Convert JSON string to FormData object
     *
     * @param json The JSON string to convert
     * @return FormData object
     */
    public FormRenderingContext unmarshalFromJson(String json) {
        try {
            ObjectMapper jsonMapper = createConfiguredJsonMapper();
            return jsonMapper.readValue(json, FormRenderingContext.class);
        } catch (Exception e) {
            log.error("Error unmarshalling JSON to form data: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert JSON to form data", e);
        }
    }

    /**
     * Write FormData as XML to a file
     *
     * @param formData The form data to write
     * @param file The file to write to
     */
    public void writeToXmlFile(FormRenderingContext formData, File file) throws IOException {
        XmlMapper xmlMapper = createConfiguredXmlMapper();
        xmlMapper.writeValue(file, formData);
    }

    /**
     * Write FormData as JSON to a file
     *
     * @param formData The form data to write
     * @param file The file to write to
     */
    public void writeToJsonFile(FormRenderingContext formData, File file) throws IOException {
        ObjectMapper jsonMapper = createConfiguredJsonMapper();
        jsonMapper.writeValue(file, formData);
    }

    /**
     * Create a configured XML mapper
     *
     * @return Configured XmlMapper
     */
    private XmlMapper createConfiguredXmlMapper() {
        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.enable(SerializationFeature.INDENT_OUTPUT);
        xmlMapper.configure(ToXmlGenerator.Feature.WRITE_XML_DECLARATION, true);
        return xmlMapper;
    }

    /**
     * Create a configured JSON mapper
     *
     * @return Configured ObjectMapper
     */
    private ObjectMapper createConfiguredJsonMapper() {
        ObjectMapper jsonMapper = new ObjectMapper();
        jsonMapper.enable(SerializationFeature.INDENT_OUTPUT);
        return jsonMapper;
    }
}
