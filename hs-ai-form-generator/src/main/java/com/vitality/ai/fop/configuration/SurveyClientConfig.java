package com.vitality.ai.fop.configuration;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import za.co.discovery.health.survey.ApiClient;
import za.co.discovery.health.survey.api.SurveyTemplateControllerApi;

@Configuration
public class SurveyClientConfig {

    private final RestTemplate restTemplate;

    @Value("${integration.survey.url}")
    private String surveyManagementBaseUrl;

    public SurveyClientConfig(@Qualifier("b2bRestTemplate") RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Bean
    public SurveyTemplateControllerApi surveyTemplateControllerApi() {
        return new SurveyTemplateControllerApi(getApiClient(surveyManagementBaseUrl));
    }

    private ApiClient getApiClient(String baseUrl) {
        ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(baseUrl);
        return apiClient;
    }
}
