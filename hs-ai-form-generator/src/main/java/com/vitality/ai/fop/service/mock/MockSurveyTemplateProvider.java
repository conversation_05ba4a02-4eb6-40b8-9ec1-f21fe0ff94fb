package com.vitality.ai.fop.service.mock;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.fop.domain.FormType;
import com.vitality.ai.fop.service.survey.SurveyTemplateProvider;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import za.co.discovery.health.survey.domain.SurveyTemplate;

import java.io.IOException;
import java.io.InputStream;

@Component
@Profile("offline")
public class MockSurveyTemplateProvider implements SurveyTemplateProvider {

    private static final String TEMPLATE_PATH = "/sample/hra2.json";

    private final ObjectMapper mapper;

    public MockSurveyTemplateProvider() {
        mapper = new ObjectMapper();
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @Override
    public SurveyTemplate getSurveyTemplate(String name) {
        if (!FormType.HRA.getSurveyTemplateName().equalsIgnoreCase(name)) {
            throw new IllegalArgumentException("Survey template name " + name + " isn't supported");
        }

        ClassPathResource resource = new ClassPathResource(TEMPLATE_PATH);
        try (InputStream inputStream = resource.getInputStream()) {
            return mapper.readValue(inputStream, SurveyTemplate.class);
        } catch (IOException e) {
            throw new IllegalStateException("Failed to read survey template from " + resource.getPath(), e);
        }
    }
}
