package com.vitality.ai.fop.service;

import com.vitality.ai.fop.domain.PatientInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import za.co.discovery.health.entity.api.EntityApi;
import za.co.discovery.health.entity.domain.VMemberEmployerView;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

@Component
@Profile("!offline")
@RequiredArgsConstructor
@Slf4j
public class EntityDataPatientService implements PatientService {

    private static final String BIRTH_DATE_FORMAT = "dd/MM/yyyy";
    private final EntityApi entityApi;

    @Override
    public PatientInfo getPatientInfo(long entityNo) {
        try {
            VMemberEmployerView entity = entityApi.getMemberEmployerView(entityNo, LocalDate.now());
            
            if (entity == null) {
                throw new PatientNotFoundException("Patient not found: " + entityNo);
            }
            
            return mapToPatientInfo(entity, entityNo);
        } catch (PatientNotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to fetch patient data for entityNo: {}", entityNo, e);
            throw new PatientNotFoundException("Patient data not available: " + entityNo, e);
        }
    }
    
    private PatientInfo mapToPatientInfo(VMemberEmployerView entity, long entityNo) {
        PatientInfo patientInfo = new PatientInfo();
        patientInfo.setEntityNo(entityNo);
        patientInfo.setPolicyCardNo(entity.getPolCardNo());
        patientInfo.setName(entity.getFirstName() + " " + entity.getLastName());

        OffsetDateTime dateOfBirth = entity.getDateOfBirth();
        if (dateOfBirth != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BIRTH_DATE_FORMAT);
            patientInfo.setDateOfBirth(formatter.format(dateOfBirth));
        }
        patientInfo.setGender(entity.getGender());
        return patientInfo;
    }
}
