package com.vitality.ai.fop.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRootName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Main container class for form data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("formData")
public class FormRenderingContext {
    private FormType formType;

    // Form template containing questions and structure
    private FormTemplate formTemplate;

    // Styling information for the form
    private FormStyle formStyle;

    // Patient information
    private PatientInfo patientInfo;

    // Assessment date
    private String assessmentDate;

    // Form version
    private String version;

    // Form completion status (draft, submitted, etc.)
    private String status;

    // Form category
    private String category;

    // Department or facility
    private String department;

    // Additional metadata
    private FormMetadata metadata;
}
