package com.vitality.ai.fop.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Class representing styling options for a question
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QuestionStyle {
    // Background color for the question
    private String backgroundColor;

    // Text color for the question
    private String textColor;

    // Border color for the question
    private String borderColor;

    // Whether to emphasize this question
    private <PERSON><PERSON><PERSON> emphasized;

    // Custom CSS classes to apply
    private String customClass;
}
