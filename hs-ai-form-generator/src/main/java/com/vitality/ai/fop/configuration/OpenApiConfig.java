package com.vitality.ai.fop.configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.servers.Server;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@RequiredArgsConstructor
public class OpenApiConfig {

    @Value("${openapi.servers:}")
    private List<String> serverUrls;

    @Bean
    public OpenAPI getOpenAPIServer() {
        final List<Server> serverList = serverUrls.stream()
            .map(url -> {
                final Server server = new Server();
                server.setUrl(url);
                return server;
            }).toList();
        return new OpenAPI().servers(serverList);
    }
}
