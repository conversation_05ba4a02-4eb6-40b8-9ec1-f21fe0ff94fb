package com.vitality.ai.fop.service;

import com.vitality.ai.fop.domain.FormQuestion;
import com.vitality.ai.fop.domain.FormTemplate;
import com.vitality.ai.fop.domain.FormType;
import com.vitality.ai.fop.domain.survey.SurveyQuestionAdapter;
import com.vitality.ai.fop.domain.survey.SurveyQuestionOptions;
import com.vitality.ai.fop.domain.survey.SurveyQuestionType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import za.co.discovery.health.survey.domain.Question;
import za.co.discovery.health.survey.domain.SurveyTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Component
public class FormTemplateFactoryImpl implements FormTemplateFactory {

    @Override
    public FormTemplate createTemplate(SurveyTemplate surveyTemplate) {
        Objects.requireNonNull(surveyTemplate);
        List<Question> questions = surveyTemplate.getQuestions();

        Objects.requireNonNull(questions);
        List<FormQuestion> formQuestions = new ArrayList<>();

        questions.forEach(question -> extractQuestions(question, formQuestions));
        return buildFormTemplate(surveyTemplate, formQuestions);
    }

    private static FormTemplate buildFormTemplate(SurveyTemplate surveyTemplate, List<FormQuestion> formQuestions) {
        FormType formType = FormType.findBySurveyTemplateName(surveyTemplate.getName()).orElse(null);
        String title = formType == null ? surveyTemplate.getName() : formType.getDisplayName();

        return FormTemplate.builder()
            .formId(generateFormId(surveyTemplate, formType))
            .title(title)
            .questions(formQuestions)
            .build();
    }

    private void extractQuestions(Question question, List<FormQuestion> formQuestions) {
        if (question == null) {
            return;
        }

        SurveyQuestionAdapter questionAdapter = new SurveyQuestionAdapter(question);
        if (!questionAdapter.hasLabel()) {
            return;
        }

        List<String> options = questionAdapter.getOptions().stream()
            .map(SurveyQuestionOptions::getLabel)
            .filter(Objects::nonNull)
            .toList();

        SurveyQuestionType.findById(question.getType())
            .ifPresent(type -> {
                String label = getLabel(question.getProps());
                if (StringUtils.hasText(label)) {
                    FormQuestion build = FormQuestion.builder()
                        .id(question.getKey())
                        .healthAttribute(question.getHealthAttribute())
                        .text(label)
                        .suffixText(getSuffixText(question.getProps()))
                        .type(type.getQuestionType())
                        .options(options)
                        .build();
                    formQuestions.add(build);
                }
            });

        List<Question> fieldGroup = question.getFieldGroup();
        if (!CollectionUtils.isEmpty(fieldGroup)) {
            if (fieldGroup.size() == 2) {
                Question nestedQuestion = fieldGroup.getFirst();
                extractQuestions(nestedQuestion, formQuestions);
            } else {
                for (Question childQuestion : fieldGroup) {
                    extractQuestions(childQuestion, formQuestions);
                }
            }
        }
    }

    private String getLabel(Map<String, Object> props) {
        return getStringProperty(props, "label");
    }

    private String getSuffixText(Map<String, Object> props) {
        return getStringProperty(props, "suffixText");
    }

    private String getStringProperty(Map<String, Object> props, String key) {
        if (props == null || !props.containsKey(key)) {
            return null;
        }
        Object value = props.get(key);
        return value == null ? null : value.toString();
    }

    private static String generateFormId(SurveyTemplate surveyTemplate, FormType formType) {
        String formPrefix;
        if (formType == null) {
            formPrefix = String.valueOf(surveyTemplate.getId());
        } else {
            formPrefix = formType.name();
        }

        return formPrefix
            + "-V" + surveyTemplate.getVersionNumber()
            + "-" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
            + "-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
    }
}
