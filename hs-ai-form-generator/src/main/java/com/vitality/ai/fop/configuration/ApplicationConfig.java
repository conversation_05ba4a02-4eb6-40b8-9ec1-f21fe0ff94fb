package com.vitality.ai.fop.configuration;

import org.apache.fop.apps.FopFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import javax.xml.transform.TransformerFactory;
import javax.xml.transform.Templates;
import javax.xml.transform.stream.StreamSource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Configuration class for Apache FOP and other components
 */
@Configuration
public class ApplicationConfig {

    /**
     * Configure FOP Factory as singleton with base URI for resource resolution
     */
    @Bean
    public FopFactory fopFactory(@Value("${form.templates.base-dir}") String path) throws IOException {
        File baseDir = new ClassPathResource(path).getFile();
        return FopFactory.newInstance(baseDir.toURI());
    }

    /**
     * Configure TransformerFactory for XSLT processing
     */
    @Bean
    public TransformerFactory transformerFactory() {
        return TransformerFactory.newInstance();
    }

    /**
     * Template cache for compiled XSL templates
     */
    @Bean
    public Map<String, Templates> templateCache() {
        return new ConcurrentHashMap<>();
    }
}
