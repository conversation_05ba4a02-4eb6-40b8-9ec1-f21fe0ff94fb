package com.vitality.ai.fop.domain.survey;

import com.vitality.ai.fop.domain.QuestionType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@Getter
@RequiredArgsConstructor
public enum SurveyQuestionType {
    CUSTOM_CHECKBOX("custom-checkbox", QuestionType.MULTIPLE_CHOICE),
    CHIPS("chips", QuestionType.MULTIPLE_CHOICE),
    RADIO("radio", QuestionType.MULTIPLE_CHOICE),
    DROP_SLIDER("drop-slider", QuestionType.RATING_SCALE),
    LENGTH_INPUT("length-input", QuestionType.LENGTH),
    TEXTAREA("textarea", QuestionType.PARAGRAPH),
    SUFFIX_INPUT("suffix-input", QuestionType.TEXT),
    MEASURE("measure", QuestionType.CLINICAL_MEASURE);

    private final String id;
    private final QuestionType questionType;

    public static Optional<SurveyQuestionType> findById(String id) {
        for (SurveyQuestionType type : values()) {
            if (type.id.equals(id)) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }
}
