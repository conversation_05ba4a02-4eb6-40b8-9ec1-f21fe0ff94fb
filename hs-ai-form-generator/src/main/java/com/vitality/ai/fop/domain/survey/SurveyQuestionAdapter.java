package com.vitality.ai.fop.domain.survey;

import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import za.co.discovery.health.survey.domain.Question;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
public class SurveyQuestionAdapter {
    private final Question question;

    public String getLabel() {
        Object label = getProps().get("label");
        return label == null ? null : label.toString();
    }

    public boolean hasLabel() {
        return getLabel() != null;
    }

    @SuppressWarnings("unchecked")
    public List<SurveyQuestionOptions> getOptions() {
        Map<String, Object> props = getProps();
        List options = (List) props.get("options");

        if (CollectionUtils.isEmpty(options)) {
            return List.of();
        }

        return (List<SurveyQuestionOptions>) options.stream()
                .map(option -> new SurveyQuestionOptions((Map<String, Object>) option))
                .toList();
    }

    private Map<String, Object> getProps() {
        Map<String, Object> props = question.getProps();
        return props == null ? Map.of() : props;
    }
}
