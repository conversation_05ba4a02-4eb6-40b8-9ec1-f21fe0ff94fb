package com.vitality.ai.fop.controller;

import com.vitality.ai.fop.domain.FormRenderingContext;
import com.vitality.ai.fop.domain.FormType;
import com.vitality.ai.fop.domain.PdfFile;
import com.vitality.ai.fop.service.FormGenerationException;
import com.vitality.ai.fop.service.FormRenderingContextFactory;
import com.vitality.ai.fop.service.PatientNotFoundException;
import com.vitality.ai.fop.service.pdf.FopPdfGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for form generation endpoints
 */
@RestController
@RequestMapping("/api/forms")
@RequiredArgsConstructor
@Slf4j
public class FormController {

    private final FormRenderingContextFactory formDataFactory;
    private final FopPdfGenerator pdfGenerator;

    /**
     * Generate a medical assessment questionnaire
     *
     * @param formType Type of form to generate (standard, clinical, custom)
     * @param entityNo Patient ID to pre-populate patient information
     * @return PDF file as response
     */
    @Operation(summary = "Generate medical assessment form",
        description = "Generates a PDF medical assessment questionnaire.")
    @ApiResponse(responseCode = "200", description = "PDF file download",
        content = @Content(mediaType = "application/pdf",
            schema = @Schema(type = "string", format = "binary", example = "binary data")))
    @GetMapping(value = "/generate", produces = MediaType.APPLICATION_PDF_VALUE)
    public ResponseEntity<Resource> generateMedicalAssessment(
        @RequestParam("formType") FormType formType,
        @RequestParam("entityNo") long entityNo) {

        try {
            log.info("Generating medical assessment form of type: {}", formType);
            FormRenderingContext renderingContext = formDataFactory.createContext(entityNo, formType);
            PdfFile pdfFile = pdfGenerator.generatePdfFile(renderingContext);
            return buildResponse(pdfFile);
        } catch (PatientNotFoundException e) {
            log.warn("Patient not found: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (FormGenerationException e) {
            log.error("Form generation failed: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Unexpected error generating form: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    private static ResponseEntity<Resource> buildResponse(PdfFile pdfFile) {
        return ResponseEntity.ok()
            .contentType(MediaType.APPLICATION_PDF)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + pdfFile.getFilename() + "\"")
            .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(pdfFile.getPdfContent().length))
            .body(new ByteArrayResource(pdfFile.getPdfContent()));
    }
}
