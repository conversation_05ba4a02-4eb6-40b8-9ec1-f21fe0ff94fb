package com.vitality.ai.fop.service.survey;

import com.vitality.ai.fop.service.FormGenerationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import za.co.discovery.health.survey.api.SurveyTemplateControllerApi;
import za.co.discovery.health.survey.domain.SurveyTemplate;


@Component
@Profile("!offline")
@RequiredArgsConstructor
@Slf4j
public class RemoteSurveyTemplateProvider implements SurveyTemplateProvider {

    private final SurveyTemplateControllerApi surveyTemplateControllerApi;

    @Override
    public SurveyTemplate getSurveyTemplate(String name) {
        try {
            return surveyTemplateControllerApi.getLatestSurveyTemplatesByHeaderName(name);
        } catch (Exception e) {
            log.error("Failed to fetch survey template: {}", name, e);
            throw new FormGenerationException("Survey template not available: " + name, e);
        }
    }
}
