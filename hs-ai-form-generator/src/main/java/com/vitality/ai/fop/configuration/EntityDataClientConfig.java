package com.vitality.ai.fop.configuration;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.web.client.RestTemplate;
import za.co.discovery.health.entity.ApiClient;
import za.co.discovery.health.entity.api.EntityApi;

@Profile("!mock")
@RequiredArgsConstructor
@Configuration
public class EntityDataClientConfig {
    private final RestTemplate restTemplate;

    @Value("${integration.entity-data-service.url}")
    private String entityDataBaseUrl;

    @Bean
    @Primary
    public EntityApi entityControllerApi() {
        return new EntityApi(getApiClient());
    }

    private ApiClient getApiClient() {
        final ApiClient apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(entityDataBaseUrl);
        return apiClient;
    }
}
