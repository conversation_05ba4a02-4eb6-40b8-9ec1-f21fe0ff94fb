import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

buildscript {
    ext {
        springCloudVersion = "2023.0.3"
    }
}

plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'java'
    id 'idea'
    id 'com.google.cloud.tools.jib'
    id 'za.co.discovery.publisher.aws-codeartifact-publisher'
    id "jacoco"
    id "org.sonarqube" version "3.5.0.2730"
}

group = 'com.vitality.fop'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = JavaVersion.VERSION_21

idea {
    module { generatedSourceDirs += file('src/generated/java') }
    module { generatedSourceDirs += file('build/generated/src/main/java') }
}

sourceSets {
    main { java { srcDirs += file('src/generated/java') } }
    main { java { srcDirs += file('build/generated/src/main/java') } }
}

jar {
    enabled = true
}

bootJar {
    manifest {
        attributes 'Start-Class': 'com.vitality.ai.fop.FopApplication'
    }
}

repositories {
    mavenCentral()
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

dependencies {
    implementation platform(project(':hs-platform-ai'))
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.projectlombok:lombok'
    implementation 'org.openapitools:jackson-databind-nullable'
    implementation 'javax.annotation:javax.annotation-api'
    implementation 'com.google.code.findbugs:jsr305'

    implementation 'org.springframework.security:spring-security-oauth2-client'
    implementation('org.springframework.security.oauth.boot:spring-security-oauth2-autoconfigure:2.2.8.RELEASE') {
        exclude group: 'org.bouncycastle', module: 'bcpkix-jdk15on'
    }

    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    // SVG support
    implementation 'org.apache.xmlgraphics:batik-svg-dom'
    implementation 'org.apache.xmlgraphics:batik-svggen'

    implementation 'org.apache.xmlgraphics:fop'
    implementation 'org.apache.xmlgraphics:xmlgraphics-commons'

    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-xml'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    // Swagger
    implementation 'za.co.discovery.health.hs:hs-starter-swagger'

    // LLAMA OCR
    implementation 'org.apache.pdfbox:pdfbox'
    implementation 'org.apache.httpcomponents:httpclient'
    implementation 'com.google.code.gson:gson'
    implementation 'org.json:json'

    testImplementation 'za.co.discovery.health.hs:hs-starter-test:20250714'
    testAnnotationProcessor 'org.projectlombok:lombok'
}

tasks.named('test') {
    useJUnitPlatform()
}

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-ai-form-generator:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['9900', '9901']
        jvmFlags = [
            '-XX:MinRAMPercentage=60.0',
            '-XX:MaxRAMPercentage=80.0',
            '-XX:+PrintFlagsFinal',
            '-XshowSettings:vm',
            '-Djava.awt.headless=true',
            '-Dprism.order=sw',
            '-Dprism.allowhidpi=false'
        ]
        entrypoint = ['/bin/sh', '-c', 'apk add --no-cache fontconfig ttf-dejavu && exec java $JAVA_OPTS -cp $(cat /app/jib-classpath-file) $(cat /app/jib-main-class-file)']
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.ai.fop.FopApplication'
    }
    allowInsecureRegistries = true
}

tasks.register("hs-survey", GenerateTask) {
    generatorName = "java"
    inputSpec = "$projectDir/src/main/resources/openapi/hs-survey.json".toString()
    outputDir = "$projectDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.survey.domain"
    apiPackage = "za.co.discovery.health.survey.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "resttemplate",
        generateApiTests  : "false",
        generateModelTests: "false",
    ]
    typeMappings = [
        OffsetDateTime: "Date"
    ]
    importMappings = [
        "java.time.OffsetDateTime": "java.util.Date"
    ]
}

tasks.register("hs-entity-data-service", GenerateTask) {
    generatorName = "java"
    inputSpec = "$projectDir/src/main/resources/openapi/hs-entity-data-service.json".toString()
    outputDir = "$projectDir/build/generated".toString()
    modelPackage = "za.co.discovery.health.entity.domain"
    apiPackage = "za.co.discovery.health.entity.api"
    configOptions = [
        dateLibrary       : "java8",
        useTags           : "true",
        library           : "resttemplate",
        generateApiTests  : "false",
        generateModelTests: "false"
    ]
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        failOnViolation = true
        rule {
            limit {
                minimum = 0.1
            }
        }
    }
}

compileJava.dependsOn tasks."hs-survey", tasks."hs-entity-data-service"
