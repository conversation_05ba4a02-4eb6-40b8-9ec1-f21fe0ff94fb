plugins {
    id "java-platform"
}

ext {
    apacheHttpClientVersion = "4.5.14"
    apachePdfBoxVersion = "3.0.0"
    apacheXmlFopVersion = "2.10"
    apacheXmlSvgVersion = "1.16"
    commonTextVersion = "1.9"
    commonsIOVersion = "2.16.1"
    commonsLangVersion = "3.12.0"
    commonsValidatorVersion = "1.7"
    googleAuthVersion = "1.35.0"
    googleCloudVersion = "3.38.0"
    googleGsonVersion = "2.10.1"
    googleGuavaVersion = "33.4.8-jre"
    jacksonDatabindNullableVersion = "0.2.3"
    jakartaVersion = "6.0.0"
    javaxVersion = "1.3.2"
    jsonVersion = "20231013"
    langChain4JBetaVersion = "1.3.0-beta9"
    langChain4JVersion = "1.3.0"
    logstashVersion = "7.2"
    lokiVersion = "1.4.0"
    sapHanaDriverVersion = "2.12.9"
    swaggerStarterVersion = '20240902'
    swaggerAnnotationsVersion = "1.6.11"
    findBugsVersion = "3.0.2"
}

// This is your central dependency management
dependencies {
    constraints {
        //Database
        api "com.sap.cloud.db.jdbc:ngdbc:${sapHanaDriverVersion}"

        //AI
        api "dev.langchain4j:langchain4j-easy-rag:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-elasticsearch:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-ollama:${langChain4JVersion}"
        api "dev.langchain4j:langchain4j-embedding-store-filter-parser-sql:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-embeddings-all-minilm-l6-v2:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-embeddings-bge-small-en-v15-q:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-embeddings:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-pgvector:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-chroma:${langChain4JBetaVersion}"
        api "dev.langchain4j:langchain4j-vertex-ai-gemini-spring-boot-starter:${langChain4JBetaVersion}"

        //Logging
        api "net.logstash.logback:logstash-logback-encoder:${logstashVersion}"
        api "com.github.loki4j:loki-logback-appender:${lokiVersion}"

        //Swagger
        api "org.openapitools:jackson-databind-nullable:${jacksonDatabindNullableVersion}"
        api "za.co.discovery.health.hs:hs-starter-swagger:${swaggerStarterVersion}"
        api "io.swagger:swagger-annotations:${swaggerAnnotationsVersion}"
        api "com.google.code.findbugs:jsr305:${findBugsVersion}"

        //Google
        api "com.google.guava:guava:${googleGuavaVersion}"
        api "com.google.auth:google-auth-library-oauth2-http:${googleAuthVersion}"
        api "com.google.cloud:google-cloud-aiplatform:${googleCloudVersion}"
        api "com.google.code.gson:gson:${googleGsonVersion}"

        //Commons
        api "commons-io:commons-io:${commonsIOVersion}"
        api "commons-validator:commons-validator:${commonsValidatorVersion}"

        //Servlet
        api "jakarta.servlet:jakarta.servlet-api:${jakartaVersion}"

        api "javax.annotation:javax.annotation-api:${javaxVersion}"

        //Content
        api "org.apache.commons:commons-text:${commonTextVersion}"
        api "org.apache.commons:commons-lang3:${commonsLangVersion}"
        api "org.apache.pdfbox:pdfbox:${apachePdfBoxVersion}"

        //Http Connection Handling
        api "org.apache.httpcomponents:httpclient:${apacheHttpClientVersion}"

        //FOP Graphics Handling
        api "org.apache.xmlgraphics:batik-svg-dom:${apacheXmlSvgVersion}"
        api "org.apache.xmlgraphics:batik-svggen:${apacheXmlSvgVersion}"
        api "org.apache.xmlgraphics:fop:${apacheXmlFopVersion}"
        api "org.apache.xmlgraphics:xmlgraphics-commons:${apacheXmlFopVersion}"

        //Json
        api "org.json:json:${jsonVersion}"

    }
}
