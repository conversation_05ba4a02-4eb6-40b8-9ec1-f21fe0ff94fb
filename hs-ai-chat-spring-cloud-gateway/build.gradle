buildscript {
    ext {
        springCloudVersion = "2023.0.3"
    }
}

buildscript {
    ext {
        springCloudVersion = "2023.0.3"
    }
}

plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'com.google.cloud.tools.jib'
    id 'za.co.discovery.publisher.aws-codeartifact-publisher'
}

sourceCompatibility = JavaVersion.VERSION_21

repositories {
    mavenCentral()
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

dependencies {
    implementation platform(project(':hs-platform-ai'))
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    implementation 'za.co.discovery.health.hs:hs-starter-swagger'
}

springBoot {
    buildInfo {
        properties {
            time = null
        }
    }
}

bootJar {
    manifest {
        attributes 'Start-Class': 'com.vitality.ai.gateway.GatewayApplication'
    }
}

jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-ai-chat-spring-cloud-gateway:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['8200']
        jvmFlags = [
            '-XX:MinRAMPercentage=60.0',
            '-XX:MaxRAMPercentage=80.0',
            '-XX:+PrintFlagsFinal',
            '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.ai.gateway.GatewayApplication'
    }
    allowInsecureRegistries = true
}
