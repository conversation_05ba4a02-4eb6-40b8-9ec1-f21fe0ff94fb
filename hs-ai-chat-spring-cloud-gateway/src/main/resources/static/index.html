<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Information</title>
    <script>
        // Fetch user information and display it
        async function fetchUserInfo() {
            try {
                const response = await fetch('/user');
                if (!response.ok) {
                    throw new Error('Failed to fetch user info');
                }

                const userInfo = await response.json();

                if (userInfo.error) {
                    document.getElementById('error').textContent = userInfo.error;
                } else {
                    document.getElementById('username').textContent = userInfo.username || 'N/A';
                    document.getElementById('email').textContent = userInfo.email || 'N/A';
                    document.getElementById('fullName').textContent = userInfo.fullName || 'N/A';

                    // Display claims
                    const claimsContainer = document.getElementById('claims');
                    const claims = userInfo.claims || {};
                    Object.keys(claims).forEach(key => {
                        const claimItem = document.createElement('li');
                        claimItem.textContent = `${key}: ${JSON.stringify(claims[key])}`;
                        claimsContainer.appendChild(claimItem);
                    });

                    // Specific claims
                    document.getElementById('roles').textContent = userInfo.roles || 'No roles found';
                    document.getElementById('customClaim').textContent = userInfo.customClaim || 'No custom claim found';
                }
            } catch (error) {
                console.error('Error fetching user info:', error);
                document.getElementById('error').textContent = 'Error fetching user information.';
            }
        }

        document.addEventListener('DOMContentLoaded', fetchUserInfo);
    </script>
</head>
<body>
<h1>Welcome to Spring Cloud Gateway</h1>
<p>You are successfully authenticated!</p>

<h2>User Information</h2>
<ul>
    <li><strong>Username:</strong> <span id="username">Loading...</span></li>
    <li><strong>Email:</strong> <span id="email">Loading...</span></li>
    <li><strong>Full Name:</strong> <span id="fullName">Loading...</span></li>
</ul>

<h3>Claims</h3>
<ul id="claims">Loading claims...</ul>

<h3>Specific Claims</h3>
<ul>
    <li><strong>Roles:</strong> <span id="roles">Loading...</span></li>
    <li><strong>Custom Claim:</strong> <span id="customClaim">Loading...</span></li>
</ul>

<p id="error" style="color: red;"></p>
<a href="/logout">Logout</a>
</body>
</html>
