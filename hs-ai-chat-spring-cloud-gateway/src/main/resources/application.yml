server:
  port: 8200

spring:
  cloud:
    config:
      enabled: false
    gateway:
      default-filters:
        - TokenRelay
      routes:
        - id: internal-service
          uri: http://localhost:9900
          predicates:
            - Path=/internal/**
#          filters:
#            - RewritePath=/internal/(?<segment>.*), /${segment}

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8082/realms/master
          jwk-set-uri: http://localhost:8082/realms/master/protocol/openid-connect/certs
      client:
        registration:
          keycloak:
            client-id: spring-cloud-gateway
            client-secret: CVmocHWRdP4uGwYJmJ2LqDhsSHby23M8
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
            scope: openid, profile, email, roles
        provider:
          keycloak:
            issuer-uri: http://localhost:8082/realms/master

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always


logging:
  level:
    org.springframework.security: INFO
