package com.vitality.ai.gateway.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class UserController {

    @GetMapping("/user")
    public Map<String, Object> getUserInfo(Authentication authentication) {
        if (authentication == null) {
            return Map.of("error", "Authentication is not available");
        }

        if (authentication instanceof OAuth2AuthenticationToken) {
            OAuth2AuthenticationToken oauthToken = (OAuth2AuthenticationToken) authentication;
            Object principal = oauthToken.getPrincipal();

            if (principal instanceof OidcUser) {
                OidcUser oidcUser = (OidcUser) principal;

                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("username", oidcUser.getPreferredUsername());
                userInfo.put("email", oidcUser.getEmail());
                userInfo.put("fullName", oidcUser.getFullName());

                // Extract additional claims
                Map<String, Object> claims = oidcUser.getClaims();
                userInfo.put("claims", claims);

                // Example: Include specific claims if available
                userInfo.put("roles", claims.getOrDefault("roles", "No roles found"));
                userInfo.put("customClaim", claims.getOrDefault("customClaim", "No custom claim found"));

                return userInfo;
            }
        }

        return Map.of("error", "User information not available");
    }
}