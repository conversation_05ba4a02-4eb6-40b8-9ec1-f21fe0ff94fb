name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: '🚀 Features'
    labels:
      - 'feature'
  - title: '🐛 Bugfixes'
    labels:
      - 'bugfix'
  - title: '🔥 Hotfixes'
    labels:
      - 'hotfix'
  - title: '📜 Documentation'
    labels:
      - 'docs'
  - title: '🏗️ Build'
    labels:
      - 'build'
  - title: '🧰 Maintenance'
    labels:
      - 'chore'
      - 'refactor'
change-template: '- $TITLE @$AUTHOR (#$NUMBER)'
change-title-escapes: '\<*_&' # You can add # and @ to disable mentions, and add ` to disable code blocks.cture
version-resolver:
  major:
    labels:
      - 'major'
  minor:
    labels:
      - 'feature'
      - 'bugfix'
      - 'hotfix'
  patch:
    labels:
      - 'docs'
      - 'chore'
      - 'refactor'
  default: patch
replacers:
  - search: '/(feature|bugfix|hotfix|refactor|build|docs|chore)\/(SFSTRY\d{7})/g'
    replace: '<a href="https://vitalitysupport.service-now.com/nav_to.do?uri=rm_story.do?sysparm_query=number=$2" target="_blank">$2</a>'
  - search: '/(feature|bugfix|hotfix|refactor|build|docs|chore)\/(SFSTSK\d{7})/g'
    replace: '<a href="https://vitalitysupport.service-now.com/nav_to.do?uri=sn_safe_scrum_task.do?sysparm_query=number=$2" target="_blank">$2</a>'
  - search: '/(feature|bugfix|hotfix|refactor|build|docs|chore)\/(DFCT\d{7})/g'
    replace: '<a href="https://vitalitysupport.service-now.com/nav_to.do?uri=rm_defect.do?sysparm_query=number=$2" target="_blank">$2</a>'

exclude-labels:
  - 'non-release'

template: |
  ## Changes

  $CHANGES
