name: Push Release Note

on:
  release:
    types: [published]

jobs:
  fetch-and-push-notes:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout release central
        uses: actions/checkout@v3
        with:
          ref: 'main'
          repository: discovery-ltd/release-notes-center
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          path: release-central

      - name: Set up Git
        run: |
          echo "Release was published by ${GITHUB_ACTOR}"
          echo "Release author info: ${{ toJson(github.event.release.author) }}"
          git config --global user.name '${GITHUB_ACTOR}'
          git config --global user.email '<EMAIL>'

      - name: Fetch Release Notes
        run: |
          REPO_NAME="${REPO_NAME##*/}"
          mkdir -p $REPO_NAME
          mkdir -p release-central/$REPO_NAME
          MD_FILE=$REPO_NAME/${{ github.event.release.tag_name }}.md
          gh release view ${{ github.event.release.tag_name }} --repo ${{ github.repository }} --json body -q .body > $MD_FILE
          sed -E 's|@([a-zA-Z0-9_-]+)|[@\1](https://github.com/\1)|g; s|#([0-9]+)|[#\1](https://github.com/${{ github.repository }}/pull/\1)|g' $MD_FILE > release-central/$MD_FILE
          RELEASE_TITLE="# [${{ github.event.release.tag_name }}](https://github.com/${{ github.repository }}/releases/tag/${{ github.event.release.tag_name }})\n"
          echo -e "$RELEASE_TITLE $(cat release-central/$MD_FILE)" > release-central/$MD_FILE
          cat release-central/$MD_FILE
          echo "REPO_NAME=${REPO_NAME}" >> $GITHUB_ENV
          echo "TARGET_DIR=current_release/${REPO_NAME}" >> $GITHUB_ENV
          echo "SOURCE_DIR=release-central/${REPO_NAME}" >> $GITHUB_ENV
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_NAME: "${{ github.repository }}"
      - name: Log
        run: |
          echo "Source directory ${{ env.SOURCE_DIR }}"
          echo "Target directory ${{ env.TARGET_DIR }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_NAME: "${{ github.repository }}"
      - name: Pushes to another repository
        uses: cpina/github-action-push-to-another-repository@main
        with:
          source-directory: ${{ env.SOURCE_DIR }}
          destination-github-username: 'discovery-ltd'
          destination-repository-name: 'release-notes-center'
          target-directory: ${{ env.TARGET_DIR }}
          user-email: <EMAIL>
          target-branch: main
          commit-message:  Add release notes ${{ github.event.release.tag_name }} for ${{ github.repository }}
        env:
          API_TOKEN_GITHUB: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
