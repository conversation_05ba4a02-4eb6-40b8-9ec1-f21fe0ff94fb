name: Update Manifest Repo

on:
  release:
    types: [published]

jobs:
  clone-and-execute:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout manifests repository
        uses: actions/checkout@v3
        with:
          ref: 'tvg_prod'
          repository: 'discovery-ltd/hs-deployment-manifests'
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

      - name: Run shell script from other repository
        env:
          manifests_branch: 'tvg_prod'
          git_token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          release_tag: ${{ github.event.release.tag_name }}
        run: |
          git config --global user.name "${GITHUB_ACTOR}"
          git config --global user.email "${GITHUB_ACTOR_ID}+${GITHUB_ACTOR}@users.noreply.github.com"
          
          echo "Release Tag: $release_tag"
          release_assets_url=$(curl -s -H "Authorization: token $git_token" -H "Accept: application/vnd.github.v3+json" "https://api.github.com/repos/${GITHUB_REPOSITORY}/releases/tags/$release_tag" | jq -r '.assets_url')
          echo "Assets URL: $release_assets_url"
          
          assets_json=$(curl -s -H "Authorization: token $git_token" -H "Accept: application/vnd.github.v3+json" "$release_assets_url")
          echo "Assets JSON: $assets_json"
          
          echo "$assets_json" | jq -r '.[] | select(.name | endswith(".release")) | .url' | while read -r asset_url; do
            
            # We use the header 'Accept: application/octet-stream' to download the asset's binary content
            asset_content=$(curl -sL -H "Authorization: token $git_token" -H "Accept: application/octet-stream" "$asset_url")
            
            # Extracting the first line which contains the space separated values
            read -r chart_name chart_version app_version <<< "$(echo "$asset_content" | head -n 1)"
            
            echo "update Chart - $chart_name $chart_version $app_version"
          
            ./bin/update-versions.sh "$chart_name" "$app_version" "$chart_version"

            git add .
            git commit -m "release $chart_name $chart_version $app_version"
            git push origin "$manifests_branch"
          done
