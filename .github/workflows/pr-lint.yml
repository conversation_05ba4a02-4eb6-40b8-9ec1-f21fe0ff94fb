name: "Lint PR"

on:
  pull_request:
    # By default, a workflow only runs when a pull_request's activity type is opened, synchronize, or reopened. We
    # explicity override here so that PR titles are re-linted when the PR text content is edited.
    #
    # Possible values: https://help.github.com/en/actions/reference/events-that-trigger-workflows#pull-request-event-pull_request
    types: [ opened, edited, reopened, synchronize ]

jobs:
  main:
    name: Validate PR title
    runs-on: ubuntu-latest
    steps:
      - uses: morrisoncole/pr-lint-action@v1.7.0
        with:
          # Note: if you have branch protection rules enabled, the `GITHUB_TOKEN` permissions
          # won't cover dismissing reviews. Your options are to pass in a custom token
          # (perhaps by creating some sort of 'service' user and creating a personal access
          # token with the correct permissions) or to turn off `on-failed-regex-request-changes`
          # and use action failure to prevent merges instead (with
          # `on-failed-regex-fail-action: true`). See:
          # https://docs.github.com/en/actions/security-guides/automatic-token-authentication#permissions-for-the-github_token
          # https://docs.github.com/en/rest/pulls/reviews#dismiss-a-review-for-a-pull-request
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          title-regex: '^(feature|bugfix|hotfix|refactor|build|docs|chore)\/(SFSTRY|SFSTSK|DFCT)\d{7}( .+)?$'
          on-failed-regex-comment:
                "**Your pull request title doesn't match the required format.**
               \n
               \n Please use ``type/story-number`` format as prefix.
               \n For example: ``feature/SFSTRY1234567``, ``feature/SFSTSK1234567 ``, ``bugfix/DFCT1234567``.
               \n
               \n If you're providing additional description, make sure it's separated from the identifier by a space.
               \n For example: ``feature/SFSTRY1234567 Description``, ``feature/SFSTSK1234567 Description``, ``bugfix/DFCT1234567 Description``."
          on-failed-regex-fail-action: true
          on-failed-regex-create-review: true
          on-failed-regex-request-changes: true
          on-succeeded-regex-dismiss-review-comment: "PR title validated successfully."
