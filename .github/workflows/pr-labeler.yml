name: Issue PR Labeler

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
      - reopened

jobs:
  main:
    name: Issue PR Labeler
    runs-on: ubuntu-latest

    permissions:
      contents: read       # required to read configuration yml file
      issues: write        # required to add labels to issues
      pull-requests: write # required to add labels to pull requests

    steps:
      - name: Run Issue PR Labeler
        uses: hoho4190/issue-pr-labeler@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
#          disable-bot: true
#          config-file-name: labeler-config.yml

      - name: Set base branch label
        id: set-label
        run: |
          BASE_BRANCH=$(jq -r .pull_request.base.ref "$GITHUB_EVENT_PATH")
          if [ "$BASE_BRANCH" = "stage" ]; then
            echo "::set-output name=branch::release"
          else
            echo "::set-output name=branch::non-release"
          fi

      - name: Apply label
        uses: actions-ecosystem/action-add-labels@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          labels: ${{ steps.set-label.outputs.branch }}
