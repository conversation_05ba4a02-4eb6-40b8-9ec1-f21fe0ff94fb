name: Append Target Branch to PR Title

on:
  pull_request:
    types: [opened, edited, reopened, synchronize]

jobs:
  update-pr-title:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Append target branch to PR title
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          PR_NUMBER=$(jq --raw-output .pull_request.number "$GITHUB_EVENT_PATH")
          echo "PR Number: $PR_NUMBER"

          PR_DATA=$(gh pr view "$PR_NUMBER" --json title,baseRefName)
          BASE_BRANCH=$(echo "$PR_DATA" | jq --raw-output '.baseRefName' | tr '[:lower:]' '[:upper:]')  # Capitalize the base branch
          PR_TITLE=$(echo "$PR_DATA" | jq --raw-output '.title')

          # Check if the base branch is already in the title
          if ! echo "$PR_TITLE" | grep -qF "[$BASE_BRANCH]"; then
            NEW_TITLE="$PR_TITLE [$BASE_BRANCH]"
            echo "Updated PR Title: $NEW_TITLE"

            gh pr edit "$PR_NUMBER" --title "$NEW_TITLE"
          else
            echo "Base branch already in PR title. No update needed."
          fi
