plugins {
    id 'java-library'
    id 'idea'
    id 'io.spring.dependency-management'
    id "org.openapi.generator"
    id 'com.github.node-gradle.node' version '3.3.0'
    id 'productivity-plugin'
}

sourceCompatibility = JavaVersion.VERSION_21
group = 'com.vitality.ai'
version = '0.0.1-SNAPSHOT'

idea {
    module { generatedSourceDirs += file('src/generated/java') }
    module { generatedSourceDirs += file('build/generated/src/main/java') }
    module { generatedSourceDirs += file('build/generated/sources/annotationProcessor/java') }
}

sourceSets {
    main { java { srcDirs += file('src/generated/java') } }
    main { java { srcDirs += file('build/generated/src/main/java') } }
    main { java { srcDirs += file('build/generated/sources/annotationProcessor/java') } }
}

configurations {
    compileOnly { extendsFrom annotationProcessor }
}

dependencyManagement {
    imports {
        mavenBom 'org.springframework.boot:spring-boot-dependencies:3.3.6'
        mavenBom 'com.google.cloud:libraries-bom:26.61.0'
    }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
}

dependencies {
    implementation platform(project(':hs-platform-ai'))

    implementation 'org.springframework.boot:spring-boot-autoconfigure'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    //Spring
    implementation 'org.springframework.boot:spring-boot-starter'
    api 'org.apache.httpcomponents:httpclient'

    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    //Google
    api "com.google.guava:guava"
    api 'com.google.cloud:google-cloud-aiplatform'
    api 'com.google.auth:google-auth-library-oauth2-http'
    api 'com.google.code.gson:gson'

    testImplementation 'za.co.discovery.health.hs:hs-starter-test:20250714'
    testAnnotationProcessor 'org.projectlombok:lombok'

    //AI Related Libraries
    api "dev.langchain4j:langchain4j-vertex-ai-gemini-spring-boot-starter"
    api "dev.langchain4j:langchain4j-ollama"
    api "dev.langchain4j:langchain4j-embeddings"
    api "dev.langchain4j:langchain4j-embeddings-all-minilm-l6-v2"
    api "dev.langchain4j:langchain4j-elasticsearch"
    api "dev.langchain4j:langchain4j-pgvector"
    api "dev.langchain4j:langchain4j-embeddings-bge-small-en-v15-q"
    api "dev.langchain4j:langchain4j-embedding-store-filter-parser-sql"
    api 'dev.langchain4j:langchain4j-chroma'
    api("dev.langchain4j:langchain4j-easy-rag") {
        exclude group: "org.apache.logging.log4j", module: "log4j-api"
    }
}
