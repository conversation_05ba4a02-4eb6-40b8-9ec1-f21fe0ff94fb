package com.vitality.ai.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import lombok.Data;
import java.util.ArrayList;
import java.util.List;

@ConfigurationProperties(prefix = "vertex")
@Data
public class VertexProperties {

    private String project;
    private String location;
    private String model;
    private VertexCredentials credentials = new VertexCredentials();
    private List<String> allowedDns = new ArrayList<>();

    private float temperature;
    private int maxOutputTokens;
    private float topK;
    private float topP;
    private int seed;
}
