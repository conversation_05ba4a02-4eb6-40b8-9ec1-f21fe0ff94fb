package com.vitality.ai.properties;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VertexCredentials {

    private String credentialFile;

    // Properties matching vertex-ai-credentials.json structure
    private String type;

    @JsonProperty("project_id")
    private String projectId;

    @JsonProperty("private_key_id")
    private String privateKeyId;

    @JsonProperty("private_key")
    private String privateKey;

    @JsonProperty("client_email")
    private String clientEmail;

    @JsonProperty("client_id")
    private String clientId;

    @JsonProperty("auth_uri")
    private String authUri;

    @JsonProperty("token_uri")
    private String tokenUri;

    @JsonProperty("auth_provider_x509_cert_url")
    private String authProviderX509CertUrl;

    @JsonProperty("client_x509_cert_url")
    private String clientX509CertUrl;

    @JsonProperty("universe_domain")
    private String universeDomain;


    public boolean hasFileCredentials() {
        return credentialFile != null && !credentialFile.isEmpty();
    }

    public boolean hasDirectCredentials() {
        return privateKeyId != null && !privateKeyId.isEmpty() &&
            privateKey != null && !privateKey.isEmpty() &&
            clientEmail != null && !clientEmail.isEmpty() &&
            clientId != null && !clientId.isEmpty();
    }

}
