package com.vitality.ai.configuration;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.DisabledChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("!google")
@Slf4j
public class LocalVertexConfig {

    @Profile("!google")
    @Bean
    public ChatModel disabledChatModel() {
        return new DisabledChatModel();
    }
}
