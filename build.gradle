plugins {
  id 'idea'
  id 'java'
  id 'org.springframework.boot' version '3.3.11' apply false
  id 'io.spring.dependency-management' version '1.1.3' apply false
  id 'com.google.cloud.tools.jib' version '3.3.1' apply false
  id "org.openapi.generator" version "6.6.0" apply false
  id 'com.github.node-gradle.node' version '3.3.0' apply false
  id 'za.co.discovery.publisher.aws-codeartifact-publisher' version '1.0.0' apply false
}

allprojects {
  group = 'com.vitality'
  version = '0.0.1-SNAPSHOT'
  sourceCompatibility = JavaVersion.VERSION_21

  buildscript {
    repositories {
      mavenLocal()
      maven {
        name = 'd2hp-v1-d2hp-v1-artifacts'
        url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
        credentials {
          username "aws"
          password System.env.CODEARTIFACT_AUTH_TOKEN ?: System.getProperty("codeArtifactAuthToken")
        }
      }
      mavenCentral()
      maven { url 'https://repo.spring.io/release' }
    }
  }

  repositories {
    repositories {
      mavenLocal()
      maven {
        name = 'd2hp-v1-d2hp-v1-artifacts'
        url 'https://d2hp-v1-905860299560.d.codeartifact.us-east-1.amazonaws.com/maven/d2hp-v1-artifacts/'
        credentials {
          username "aws"
          password System.env.CODEARTIFACT_AUTH_TOKEN ?: System.getProperty("codeArtifactAuthToken")
        }
      }
      mavenCentral()
      maven { url 'https://repo.spring.io/release' }
    }
  }
}

subprojects {
  tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
  }
}

