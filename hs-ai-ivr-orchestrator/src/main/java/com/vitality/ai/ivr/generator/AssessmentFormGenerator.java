package com.vitality.ai.ivr.generator;

import com.vitality.ai.ivr.domain.form.FormTemplate;
import com.vitality.ai.ivr.domain.form.Question;
import com.vitality.ai.ivr.domain.form.QuestionType;
import com.vitality.ai.ivr.generator.model.AssessorSurvey;
import com.vitality.ai.ivr.generator.model.Option;
import com.vitality.ai.ivr.generator.model.QuestionWrapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
public class AssessmentFormGenerator {

    public FormTemplate generateFromJson(File jsonFile) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        AssessorSurvey survey = mapper.readValue(jsonFile, AssessorSurvey.class);

        List<Question> questions = new ArrayList<>();
        for (QuestionWrapper wrapper : survey.getSurveyTemplate().getQuestions()) {
            extractQuestions(wrapper, questions);
        }

        return FormTemplate.builder()
                .formId("CG-" + UUID.randomUUID().toString().substring(0, 8))
                .title(survey.getSurveyTemplate().getName())
                .questions(questions)
                .build();
    }

    private void extractQuestions(QuestionWrapper node, List<Question> questions) {
        if (node.getProps() == null || node.getProps().getLabel() == null) return;

        List<String> options = new ArrayList<>();
        if (node.getProps().getOptions() != null) {
            for (Option o : node.getProps().getOptions()) {
                options.add(o.getLabel());
            }
        }

        QuestionType type = switch (node.getType()) {
            case "custom-checkbox",
                 "chips",
                 "radio" -> QuestionType.MULTIPLE_CHOICE;
            case "drop-slider" -> QuestionType.RATING_SCALE;
            case "length-input" -> QuestionType.LENGTH;
            case "textarea" -> QuestionType.PARAGRAPH;
            case "suffix-input" -> QuestionType.TEXT;
            case "measure" -> QuestionType.CLINICAL_MEASURE;
            default -> null;
        };

        String healthAttribute = node.getHealthAttribute();

        System.out.println("Found Question Type: " + node.getType());

        if (type != null) {
            questions.add(Question.builder()
                    .id(node.getKey())
                    .healthAttribute(healthAttribute)
                    .text(node.getProps().getLabel())
                    .suffixText(node.getProps().getSuffixText())
                    .type(type)
                    .options(options)
                    .build());
        }
        //To catch the form-group
        if (node.getFieldGroup() != null) {
            if (node.getFieldGroup().size() == 2) {
                QuestionWrapper questionWrapper = node.getFieldGroup().get(0);
                extractQuestions(questionWrapper, questions);
            } else {
                for (QuestionWrapper child : node.getFieldGroup()) {
                    extractQuestions(child, questions);
                }
            }
        }
    }
}