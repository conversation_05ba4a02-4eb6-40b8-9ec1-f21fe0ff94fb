package com.vitality.ai.ivr.controller;

import com.vitality.ai.ivr.delegate.IvrDelegate;
import com.vitality.ai.ivr.domain.activity.*;
import com.vitality.ai.ivr.domain.form.FormTemplate;
import com.vitality.ai.ivr.domain.hra.IvrAssessorSurveyRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/ivr")
@RequiredArgsConstructor
@Slf4j
//@TODO SECURITY CHECK
public class IvrController {

    private final IvrDelegate ivrDelegate;

    @GetMapping("/hra/template")
    public ResponseEntity<FormTemplate> getHra(@RequestParam(value = "templateName", required = false, defaultValue = "hra") String templateName) {
        FormTemplate hraFormTemplate = ivrDelegate.getHraFormTemplate();
        return ResponseEntity.ok(hraFormTemplate);
    }

    @PostMapping("/hra/submit")
    public ResponseEntity<IvrAssessorSurveyResponse> submitHraResponse(@RequestBody IvrAssessorSurveyRequest request) {
        log.info("Submitting HRA response: {}", request);
        return ResponseEntity.ok(IvrAssessorSurveyResponse.builder().build());
    }

    @PostMapping("/activity/complete")
    public ResponseEntity<ActivityCompleteResponse> completeActivity(@RequestBody ActivityCompleteRequest request) {
        log.info("Completing activity: {}", request);
        return ResponseEntity.ok(ActivityCompleteResponse.builder().build());
    }

    @PostMapping("/activity/status")
    public ResponseEntity<ActivityStatusResponse> checkActivityStatus(@RequestBody ActivityStatusRequest request) {
        // Logic to update the activity status
        log.info("Checking activity status: {}", request);
        return ResponseEntity.ok(ActivityStatusResponse.builder().build());
    }

}
