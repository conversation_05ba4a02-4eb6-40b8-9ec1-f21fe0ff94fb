package com.vitality.ai.ivr.delegate;

import com.vitality.ai.ivr.domain.form.FormTemplate;
import com.vitality.ai.ivr.generator.QuestionnaireGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IvrDelegate {

    private final QuestionnaireGenerator questionnaireGenerator;

    public FormTemplate getHraFormTemplate() {
        return questionnaireGenerator.hra();
    }


}
