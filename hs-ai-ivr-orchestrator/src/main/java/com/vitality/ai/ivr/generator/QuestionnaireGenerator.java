package com.vitality.ai.ivr.generator;

import com.vitality.ai.ivr.domain.form.FormTemplate;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

/**
 * Generator for questionnaire templates
 */
@RequiredArgsConstructor
@Service
public class QuestionnaireGenerator {

    private final AssessmentFormGenerator assessmentFormGenerator;

    @SneakyThrows
    public FormTemplate hra()  {
        ClassPathResource classPathResource = new ClassPathResource("/sample/hra.json");
        FormTemplate formTemplate = assessmentFormGenerator.generateFromJson(classPathResource.getFile());
        return formTemplate;
    }

}