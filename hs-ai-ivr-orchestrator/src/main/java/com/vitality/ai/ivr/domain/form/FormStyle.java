package com.vitality.ai.ivr.domain.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormStyle {
    // Basic styling
    private String primaryColor;
    private String secondaryColor;
    private String accentColor;
    private String fontFamily;

    // Layout options
    private String optionLayout; // vertical, grid
    private String optionStyle;  // circle, square, underlined

    // Color scheme
    private boolean darkMode;
    private String darkModePrimaryColor;
    private String darkModeAccentColor;

    // Content styling
    private String headerTextColor;
    private String questionBackgroundColor;

    // Branding
    private String logoUrl;

    // Advanced
    private String customCss;

    /**
     * Get default styling
     */
    public static FormStyle getDefaultStyle() {
        return FormStyle.builder()
                .primaryColor("#336699")
                .secondaryColor("#666666")
                .accentColor("#ff6b6b")
                .fontFamily("Helvetica, Arial, sans-serif")
                .optionLayout("vertical")
                .optionStyle("square")
                .darkMode(false)
                .darkModePrimaryColor("#1a3d66")
                .darkModeAccentColor("#ff9e80")
                .headerTextColor("#ffffff")
                .questionBackgroundColor("#f9f9f9")
                .build();
    }
}