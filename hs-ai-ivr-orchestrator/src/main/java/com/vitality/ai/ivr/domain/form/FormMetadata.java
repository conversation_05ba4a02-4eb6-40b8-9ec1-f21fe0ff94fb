package com.vitality.ai.ivr.domain.form;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Map;

/**
 * Class for storing form metadata
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FormMetadata {
    // When the form was created
    private ZonedDateTime createdAt;

    // Who created the form
    private String createdBy;

    // When the form was last updated
    private ZonedDateTime updatedAt;

    // Who last updated the form
    private String updatedBy;

    // When the form was submitted
    private ZonedDateTime submittedAt;

    // Who submitted the form
    private String submittedBy;

    // Custom tags for the form
    private Map<String, String> tags;

    // Additional custom properties
    private Map<String, Object> customProperties;
}