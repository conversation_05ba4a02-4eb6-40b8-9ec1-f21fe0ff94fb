package com.vitality.ai.ivr.domain.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ActivityStatusResponse {

    private String entityId;
    private String activityId;
    private String status;
    private LocalDate completionDate;

}
