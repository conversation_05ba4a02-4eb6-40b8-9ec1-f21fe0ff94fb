package com.vitality.ai.ivr.domain.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Class representing a question in a form
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Question {
    // Unique identifier for the question
    private String id;

    // Main text of the question
    private String text;

    // Additional description/instructions for the question
    private String description;

    // Type of question (determines rendering and input method)
    private QuestionType type;

    private String healthAttribute;

    // Available options for selection-based questions
    private List<String> options;

    // Whether the question is required to be answered
    private Boolean required;

    // Default value for the question
    private String defaultValue;

    // Validation pattern (e.g., regex for text inputs)
    private String validationPattern;

    // Error message to show when validation fails
    private String validationMessage;

    // Whether the question should be conditionally shown
    private Boolean conditional;

    // Logic defining when to show this question (e.g., "Q1 = Yes")
    private String conditionalLogic;

    private String suffixText;

    // Additional styling information
    private QuestionStyle style;
}