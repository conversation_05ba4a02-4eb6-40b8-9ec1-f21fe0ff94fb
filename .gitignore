/.gradle/
/.idea/
/build/
/Discovery ENT CA 01.crt
/Discovery Group Root CA.crt
/keystore.jks
/hs-ai-chat-agent-angular/build/
/hs-ai-chat-orchestrator/build/
/hs-common-ai-starter/build/
/hs-ai-document-scanner/build/
/hs-ai-form-generator/build/
/hs-ai-ivr-orchestrator/build/
/hs-ai-mail-orchestrator/build/
/hs-platform-ai/build/
/hs-ai-chat-spring-cloud-gateway/build/
/hs-ai-chat-spring-cloud-gateway/.gradle/
/hs-ai-chat-orchestrator/.gradle/
/hs-ai-chat-agent-angular/src/main/angular/member-matcher/node_modules/
/hs-ai-chat-agent-angular/src/main/angular/member-matcher/dist/
/hs-ai-chat-agent-angular/src/main/angular/member-matcher/.angular/
/hs-ai-chat-agent-angular/src/main/angular/member-matcher/.vscode/
/hs-ai-document-scanner/src/main/resources/encoded-aspect-172113-7c9d09e6a240.json
/DAG/loki-wal/checkpoint.004434/00000000
/DAG/loki-wal/checkpoint.004435.tmp/00000000
/DAG/loki-wal/00004436
/encoded-aspect-172113-7c9d09e6a240.json
/DAG/loki-wal/checkpoint.018486/00000000
/DAG/loki-wal/checkpoint.018487.tmp/00000000
/DAG/loki-wal/00018487
/DAG/loki-wal/00018488
