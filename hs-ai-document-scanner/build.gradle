buildscript {
    ext {
        springCloudVersion = "2023.0.3"
        springIntegationVersion = '6.3.3'
        springIntegationKafkaVersion = '6.3.4'
    }
}

plugins {
    id 'java'
    id 'idea'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'com.google.cloud.tools.jib'
    id "org.openapi.generator"
    id 'za.co.discovery.publisher.aws-codeartifact-publisher'
    id "jacoco"
    id "org.sonarqube" version "3.5.0.2730"
}

sourceCompatibility = JavaVersion.VERSION_21
group = 'com.vitality.chat'
version = '0.0.1-SNAPSHOT'

idea {
    module { generatedSourceDirs += file('src/generated/java') }
    module { generatedSourceDirs += file('build/generated/src/main/java') }
    module { generatedSourceDirs += file('build/generated/sources/annotationProcessor/java') }
}

sourceSets {
    main { java { srcDirs += file('src/generated/java') } }
    main { java { srcDirs += file('build/generated/src/main/java') } }
    main { java { srcDirs += file('build/generated/sources/annotationProcessor/java') } }
}

test {
    maxHeapSize = "2048m"
    testLogging.showStandardStreams = true
    useJUnitPlatform()
}

configurations {
    compileOnly { extendsFrom annotationProcessor }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://repo.spring.io/release' }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

dependencies {
    implementation platform(project(':hs-platform-ai'))
    implementation project(":hs-common-ai-starter")

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    //Spring
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.boot:spring-boot-starter'

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'org.apache.commons:commons-text'
    implementation 'commons-validator:commons-validator'

    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
//    implementation 'com.fasterxml.jackson.core:jackson-databind'

    implementation 'org.apache.httpcomponents:httpclient'
    // mapstruct
    implementation 'org.mapstruct:mapstruct:1.4.2.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.2.Final'
    //logging
    //To send logging to a json file or to a TCP port (on kubernetes):
    implementation "net.logstash.logback:logstash-logback-encoder"
    //Conditionals in the logback configs:
    implementation 'org.codehaus.janino:janino'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    //Swagger
    implementation 'org.openapitools:jackson-databind-nullable'
    implementation 'za.co.discovery.health.hs:hs-starter-swagger'

    implementation 'org.apache.pdfbox:pdfbox'

    implementation 'commons-io:commons-io'
    implementation 'org.hibernate.validator:hibernate-validator'
    implementation 'com.fasterxml.jackson.module:jackson-module-jsonSchema:2.17.2'

    testImplementation 'za.co.discovery.health.hs:hs-starter-test:20250714'
    testAnnotationProcessor 'org.projectlombok:lombok'

}

springBoot {
    buildInfo {
        properties {
            time = null
        }
    }
}

bootJar {
    manifest {
        attributes 'Start-Class': 'com.vitality.ai.OcrOrchestrationApplication'
    }
}



jib {
    ext {
        set('dockerImageTag', System.getenv("DOCKER_IMAGE_TAG") ?: 'latest')
    }
    from {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/integration/amazoncorretto:21-alpine3.19"
    }
    to {
        image = "905860299560.dkr.ecr.us-east-1.amazonaws.com/d2hp-v1/subsystem/hs-ai-document-scanner:${dockerImageTag}"
        tags = ['latest']
    }
    container {
        ports = ['34001']
        jvmFlags = [
                '-XX:MinRAMPercentage=60.0',
                '-XX:MaxRAMPercentage=80.0',
                '-XX:+PrintFlagsFinal',
                '-XshowSettings:vm'
        ]
        extraDirectories {
            // copy opentelemetry files from builder image to this image
            paths {
                path {
                    from = file('/otel')
                    into = '/app/otel'
                }
            }
        }
        creationTime = 'USE_CURRENT_TIMESTAMP'
        mainClass = 'com.vitality.ai.OcrOrchestrationApplication'
    }
    allowInsecureRegistries = true
}

sonar {
    properties {
        property "sonar.projectKey", "$rootProject.name"
        property "sonar.projectName", "$rootProject.name"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

jacocoTestCoverageVerification {
    violationRules {
        failOnViolation = true
        rule {
            limit {
                minimum = 0.1
            }
        }
    }
}
