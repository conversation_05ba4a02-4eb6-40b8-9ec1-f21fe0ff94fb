spring:
  application:
    name: hs-ai-document-scanner
  config:
    use-legacy-processing: true
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      enabled: false
  web:
    resources:
      static-locations: classpath:/static
      cache:
        period: 0
  mvc:
    view:
      prefix: /
      suffix: .html
    static-path-pattern: /**
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 32MB

server:
  port: 34001
  servlet:
    context-path: /v3/ai-document-scanner

openapi:
  servers: http://localhost:34001/v3/ai-document-scanner

# keep prompts in spring yaml to have ability to change it via spring config server
ai:
  prompts:
    structured:
      hra:
        role: "You are a specialized data extraction AI agent with OCR capabilities intended to recognize the content of scanned documents."
        context:
          - title: "Domain"
            messages:
              - "Provided document pages represent a scanned medical assessment questionnaire filled out by hand."
          - title: "Document layout"
            messages:
              - "First page contains title and patient information, patient info consists of: patient name, date of birth, policy card number."
              - "Last page contains signature block."
              - "Each page contains multiple questions and answers and footer, footer contains: document ID, patient name, policy card number, current page number, total page number."
        task: "Parse the attached medical assessment document pages. Extract the patient's information, content of all questions and answers, then respond with a JSON according to the provided JSON schema."
        instructions:
          - title: "General Extraction rules"
            messages:
              - "Use standard US date format MM/dd/YYYY (example: 04/08/1995)"
              - "If any date is missing, return null (not an empty string)"
              - "Pages must be ordered in ascending order"
          - title: "Patient Information Extraction"
            messages:
              - "Extract patient name, date of birth, and policy card number from the first page."
              - "Use footer content which also has patient name and policy card number to confirm correctness of data."
          - title: "Question & Answer Extraction"
            messages:
              - "Process questions in the exact order they appear."
              - "Extract the full, original question text (without numbering)."
              - "Preserve the original wording of both questions and answers."
              - "The answers array should be populated with the given answers, property must always be an array of strings, even if there is only one answer."
              - "Determine question type as follows:"
              - "CHECKBOX: The question has multiple-choice or option-based answers, answer is considered selected only if a clear mark (e.g., check, X, dot) is present."
              - "RATING_SCALE: The question has a scale from 0 to 10, answer is considered selected only if a single clear mark (e.g., check, X, dot) is present."
              - "TEXT: Free text or numeric entry written by hand."
              - "For CHECKBOX and RATING_SCALE, a response is valid only if a visible mark is detected in the answer area (e.g., check, X, dot, shading)."
              - "Empty or unmarked options in CHECKBOX questions must always be ignored and not included in answers."
              - "A question is NOT_ANSWERED if no options are visibly marked, even if options are listed."
              - "Determine answer status as follows: ANSWERED: If a clear answer is detected, NOT_ANSWERED: If no answer is detected, DOUBT: If there is any uncertainty about the answer or OCR engine can't read the answer."
              - "answerStatus always governs interpretation, the answers array may contain one or more strings if status=ANSWERED; it must be empty if status=NOT_ANSWERED or DOUBT."
              - "Assign a confidenceScore (0.0 to 1.0) to each detected answer, where 1.0 means full OCR confidence and 0.0 means unreadable."
              - "Never infer or assume answers. If uncertain, assign answerStatus=DOUBT and leave answers as an empty array."
              - "If handwriting is illegible or OCR result is below 0.5 confidenceScore, treat as DOUBT with answers=[]."
          - title: "Output Requirements"
            messages:
              - "The output must be a valid JSON object."
              - "The output must match the provided JSON schema."
              - "If unsure about a value, omit it (if optional) or set it to null (if required)."
