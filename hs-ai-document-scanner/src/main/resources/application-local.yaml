vertex:
  project: vitality-ai-ops
  location: us-central1
  #model: gemini-2.0-flash-001
  model: publishers/google/models/gemini-2.5-flash
#  model: publishers/google/models/gemini-2.5-pro

  credentials:
    credential-file: /Users/<USER>/vitality-ai-ops-1d214159ace6.json
#    type: service_account
#    projectId: hs-ai-chat-test
#    privateKeyId: bfa66072e1b542d524ab93f2f422ea22d2737704
#    privateKey: "test"
#    clientEmail: <EMAIL>
#    clientId: "100725217112964160298"
#    authUri: https://accounts.google.com/o/oauth2/auth
#    tokenUri: https://oauth2.googleapis.com/token
#    authProviderX509CertUrl: https://www.googleapis.com/oauth2/v1/certs
#    clientX509CertUrl: https://www.googleapis.com/robot/v1/metadata/x509/tornike-vertex-ai%40hs-ai-chat-test.iam.gserviceaccount.com
#    universeDomain: googleapis.com

  allowed-dns:
    - CN=paca.discovery.holdings.co.za
    - CN=WE2,O=Google Trust Services,C=US
  temperature: 0.1
  max-output-tokens: 50000
  top-k: 40
  top-p: 0.95
  seed: 1234
