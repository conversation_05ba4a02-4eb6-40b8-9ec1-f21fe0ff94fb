package com.vitality.ai.ocr.prompt.model.response;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
/**
 * Root class representing the medical form data structure
 */
public class MedicalForm implements Serializable {
    private String formId;
    private String formType;
    private Applicant applicant;
    private List<Answer> answers;

    public static MedicalForm getDummy() {
        MedicalForm medicalForm = new MedicalForm();
        medicalForm.setFormId("");
        medicalForm.setFormType("");
        medicalForm.setApplicant(Applicant.getDummy());
        ArrayList<Answer> response = new ArrayList<>();
        response.add(Answer.getDummy());
        medicalForm.setAnswers(response);
        return medicalForm;
    }

}
