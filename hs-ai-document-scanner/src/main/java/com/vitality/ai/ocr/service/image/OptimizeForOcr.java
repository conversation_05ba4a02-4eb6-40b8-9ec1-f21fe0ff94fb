package com.vitality.ai.ocr.service.image;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

@Slf4j
public class OptimizeForOcr implements ImageProcessor {
    private static final int scalePercent = 100;

    @Override
    public byte[] process(byte[] inputBytes) {
        try {
            BufferedImage original = ImageIO.read(new ByteArrayInputStream(inputBytes));
            if (original == null) {
                throw new IllegalArgumentException("Invalid image");
            }

            BufferedImage image = ImageTransformations.toGrayscale(original);

            image = ImageTransformations.stretchContrast(image);

//            try {
//                double skewAngle = ImageTransformations.detectSkewAngle(image);
//                if (Math.abs(skewAngle) > 0.5) { // Only rotate if skew is significant
//                    log.info("Detected skew angle: {} degrees, applying correction", skewAngle);
//                    image = ImageTransformations.rotateImage(image, -skewAngle); // Negative to correct the skew
//                } else {
//                    log.info("Skew angle {} degrees is minimal, skipping rotation", skewAngle);
//                }
//            } catch (Exception e) {
//                log.warn("Skew detection failed, skipping rotation: {}", e.getMessage());
//                // Continue processing without skew correction
//            }

            image = ImageTransformations.denoise(image);
            image = ImageTransformations.binarizeOtsu(image);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(image, ImageFormat.PNG, out);

            return out.toByteArray();
        } catch (Exception e) {
            throw new ImageProcessorException("Failed to process image", e);
        }
    }
}
