package com.vitality.ai.ocr.service;

import com.vitality.ai.ocr.config.PromptProperties;
import com.vitality.ai.ocr.model.prompt.PromptTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class PromptTemplateService {

    private final PromptProperties promptProperties;
    private final Map<String, PromptTemplate> promptCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void initializeCache() {
        if (promptProperties.getStructured() == null) {
            return;
        }

        promptProperties.getStructured().forEach((name, definition) -> {
            PromptTemplate promptTemplate = PromptTemplate.builder()
                .withRole(definition.getRole())
                .withTask(definition.getTask())
                .withContextSections(definition.getContext())
                .withInstructionSections(definition.getInstructions());

            promptCache.put(name, promptTemplate);
        });
    }

    public PromptTemplate getPromptTemplate(String promptName) {
        return promptCache.get(promptName);
    }

    public Map<String, PromptTemplate> getAllPromptTemplates() {
        return Collections.unmodifiableMap(promptCache);
    }
}
