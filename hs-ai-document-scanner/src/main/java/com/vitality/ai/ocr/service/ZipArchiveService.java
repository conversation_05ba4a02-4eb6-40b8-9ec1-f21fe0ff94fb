package com.vitality.ai.ocr.service;

import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class ZipArchiveService {

    public byte[] createImageZipArchive(List<byte[]> imagePages, String originalFilename, String extension) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            String baseName = getBaseName(originalFilename);

            for (int i = 0; i < imagePages.size(); i++) {
                String entryName = String.format("%s_page_%03d.%s", baseName, i + 1, extension);
                ZipEntry entry = new ZipEntry(entryName);
                zos.putNextEntry(entry);
                zos.write(imagePages.get(i));
                zos.closeEntry();
            }
        }
        return baos.toByteArray();
    }

    public String generateZipFilename(String originalFilename) {
        String baseName = getBaseName(originalFilename);
        return baseName + ".zip";
    }

    private String getBaseName(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "document";
        }
        String baseName = filename;
        if (baseName.toLowerCase().endsWith(".pdf")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }
        return baseName.replaceAll("[^a-zA-Z0-9_-]", "_");
    }
}
