package com.vitality.ai.ocr.controller;

import com.vitality.ai.ocr.model.prompt.PromptResponse;
import com.vitality.ai.ocr.model.prompt.PromptTemplate;
import com.vitality.ai.ocr.service.PromptTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.TreeMap;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/prompt")
public class PromptController {

    private final PromptTemplateService promptService;

    @GetMapping("{promptId}")
    public ResponseEntity<PromptResponse> getPrompt(@PathVariable String promptId) {
        PromptTemplate promptTemplate = promptService.getPromptTemplate(promptId);
        if (promptTemplate == null) {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(new PromptResponse(promptTemplate, promptTemplate.toString()));
    }


    @GetMapping("/list")
    public ResponseEntity<Map<String, PromptResponse>> listPrompts() {
        Map<String, PromptResponse> prompts = new TreeMap<>();
        promptService.getAllPromptTemplates().forEach((name, prompt) -> {
            prompts.put(name, new PromptResponse(prompt, prompt.toString()));
        });
        return ResponseEntity.ok(prompts);
    }

}
