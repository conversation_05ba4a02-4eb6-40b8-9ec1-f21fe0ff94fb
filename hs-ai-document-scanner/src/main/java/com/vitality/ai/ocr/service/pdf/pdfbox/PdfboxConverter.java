package com.vitality.ai.ocr.service.pdf.pdfbox;

import com.vitality.ai.ocr.model.file.BufferedFile;
import com.vitality.ai.ocr.model.file.FileHandle;
import com.vitality.ai.ocr.model.pdf.RenderedPdf;
import com.vitality.ai.ocr.service.ZipArchiveService;
import com.vitality.ai.ocr.service.image.OptimizeForOcr;
import com.vitality.ai.ocr.service.image.SimulateScannedPage;
import com.vitality.ai.ocr.service.pdf.PdfConstants;
import com.vitality.ai.ocr.service.pdf.PdfConverter;
import com.vitality.ai.ocr.service.pdf.PdfProcessingOptions;
import com.vitality.ai.ocr.service.pdf.PdfRenderer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Service for converting regular PDF documents into "scanned" PDFs where each page
 * is rendered as a raster image. This simulates the effect of physically scanning
 * a document, useful for testing OCR services or document processing workflows.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfboxConverter implements PdfConverter {

    private final PdfRenderer pdfRenderer;
    private final ZipArchiveService zipArchiveService;

    @Override
    public FileHandle convertToScannedPdf(FileHandle file, PdfProcessingOptions options) throws IOException {
        validateInputs(file.readAllBytes(), options.getDpi());

        log.info("Starting conversion of PDF (size: {}) to scanned format at {} DPI with noise level {} (grayscale: {})",
            FileUtils.byteCountToDisplaySize(file.getSize()), options.getDpi(), options.getNoiseLevel(), options.isGrayscale());

        try {
            long start = System.currentTimeMillis();

            byte[] scannedPdfBytes = convertDocumentToScannedPdf(file, options);

            log.info("Successfully converted PDF (size: {}) to scanned PDF {} with {} DPI and noise {} (grayscale: {}) in {} ms",
                FileUtils.byteCountToDisplaySize(file.getSize()),
                FileUtils.byteCountToDisplaySize(scannedPdfBytes.length),
                options.getDpi(), options.getNoiseLevel(), options.isGrayscale(), System.currentTimeMillis() - start);

            String outputFilename = generateOutputFilename(file.getName());
            return new BufferedFile(outputFilename, MediaType.APPLICATION_PDF_VALUE, scannedPdfBytes);
        } catch (Exception e) {
            log.error("Error converting PDF bytes to scanned format: {}", e.getMessage(), e);
            throw new IOException("Failed to convert PDF bytes to scanned format", e);
        }
    }

    /**
     * Core conversion logic that processes the PDDocument.
     */
    private byte[] convertDocumentToScannedPdf(FileHandle file, PdfProcessingOptions options) throws IOException {
        RenderedPdf renderedPdf = pdfRenderer.renderPagesToImages(file, options.toBuilder()
            .imageProcessors(List.of(new SimulateScannedPage(options.getNoiseLevel(), options.isGrayscale())))
            .build());

        try (PDDocument inputDocument = Loader.loadPDF(file.readAllBytes())) {
            try (PDDocument outputDocument = new PDDocument()) {
                for (int i = 0; i < renderedPdf.pages().size(); i++) {
                    byte[] imageBytes = renderedPdf.pages().get(i);
                    PDPage originalPage = inputDocument.getPage(i);

                    addImagePageToDocument(outputDocument, imageBytes, originalPage.getMediaBox());
                }

                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                outputDocument.save(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Add a BufferedImage as a page to the output PDF document.
     */
    private void addImagePageToDocument(PDDocument document, byte[] imageBytes, PDRectangle originalPageSize) throws IOException {
        // Create PDImageXObject from the image bytes
        PDImageXObject pdImage = PDImageXObject.createFromByteArray(document, imageBytes, "page_image");

        // Create new page with original dimensions
        PDPage page = new PDPage(originalPageSize);
        document.addPage(page);

        // Calculate scaling to fit image to page while maintaining aspect ratio
        float pageWidth = originalPageSize.getWidth();
        float pageHeight = originalPageSize.getHeight();
        float imageWidth = pdImage.getWidth();
        float imageHeight = pdImage.getHeight();

        float scaleX = pageWidth / imageWidth;
        float scaleY = pageHeight / imageHeight;
        float scale = Math.min(scaleX, scaleY);

        float scaledWidth = imageWidth * scale;
        float scaledHeight = imageHeight * scale;

        // Center the image on the page
        float x = (pageWidth - scaledWidth) / 2;
        float y = (pageHeight - scaledHeight) / 2;

        // Draw the image on the page
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);
        }
    }

    private void validateInputs(byte[] inputBytes, int dpi) {
        if (inputBytes == null || inputBytes.length == 0) {
            throw new IllegalArgumentException("Input bytes cannot be null or empty");
        }
        validateDpi(dpi);
    }

    private void validateDpi(int dpi) {
        if (dpi < PdfConstants.MIN_DPI) {
            throw new IllegalArgumentException("DPI must be at least 150 for readable output. Recommended: 300+");
        }
        if (dpi > PdfConstants.MAX_DPI) {
            log.warn("High DPI ({}) may result in very large file sizes and slow processing", dpi);
        }
    }

    @Override
    public FileHandle convertToImagesZip(FileHandle file, PdfProcessingOptions options) throws IOException {
        RenderedPdf renderedPdf = pdfRenderer.renderPagesToImages(file, options);
        byte[] zipBytes = zipArchiveService.createImageZipArchive(renderedPdf.pages(), file.getName(), renderedPdf.imageFormat());
        String outputFilename = zipArchiveService.generateZipFilename(file.getName());
        return new BufferedFile(outputFilename, "application/zip", zipBytes);
    }

    @Override
    public FileHandle convertToScannedImagesZip(FileHandle file, PdfProcessingOptions options) throws IOException {
        RenderedPdf renderedPdf = pdfRenderer.renderPagesToImages(file, options.toBuilder()
            .imageProcessors(List.of(new SimulateScannedPage(options.getNoiseLevel(), options.isGrayscale()))).build()
        );
        byte[] zipBytes = zipArchiveService.createImageZipArchive(renderedPdf.pages(), file.getName(), renderedPdf.imageFormat());
        String outputFilename = zipArchiveService.generateZipFilename(file.getName());
        return new BufferedFile(outputFilename, "application/zip", zipBytes);
    }

    @Override
    public FileHandle convertToOcrOptimizedImagesZip(FileHandle file, PdfProcessingOptions options) throws IOException {
        RenderedPdf renderedPdf = pdfRenderer.renderPagesToImages(file, options.toBuilder()
            .imageProcessors(List.of(new OptimizeForOcr())).build()
        );
        byte[] zipBytes = zipArchiveService.createImageZipArchive(renderedPdf.pages(), file.getName(), renderedPdf.imageFormat());
        String outputFilename = zipArchiveService.generateZipFilename(file.getName());
        return new BufferedFile(outputFilename, "application/zip", zipBytes);
    }

    private String generateOutputFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return "scanned_document.pdf";
        }

        String baseName = originalFilename;
        if (baseName.toLowerCase().endsWith(".pdf")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        return "scanned_" + baseName + ".pdf";
    }

}
