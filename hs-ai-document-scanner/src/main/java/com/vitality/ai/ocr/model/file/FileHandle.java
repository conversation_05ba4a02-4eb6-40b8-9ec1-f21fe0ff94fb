package com.vitality.ai.ocr.model.file;

import java.io.IOException;
import java.io.InputStream;

/**
 * Read-only file handle for accessing file content and metadata.
 */
public interface FileHandle extends AutoCloseable {

    String getName();

    String getContentType();

    long getSize();

    InputStream getInputStream() throws IOException;

    byte[] readAllBytes() throws IOException;

    String getHash();

    @Override
    default void close() throws IOException {
        // Default implementation does nothing
    }
}
