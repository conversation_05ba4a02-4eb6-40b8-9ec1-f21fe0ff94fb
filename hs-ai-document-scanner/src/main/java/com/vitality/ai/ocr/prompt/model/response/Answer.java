package com.vitality.ai.ocr.prompt.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
public class Answer implements Serializable {
    private String question;
    private String selectedAnswer;
    private String healthAttribute;

    public static Answer getDummy() {
        Answer answer = new Answer();
        answer.setQuestion("");
        answer.setSelectedAnswer("");
        answer.setHealthAttribute("");
        return answer;
    }
}
