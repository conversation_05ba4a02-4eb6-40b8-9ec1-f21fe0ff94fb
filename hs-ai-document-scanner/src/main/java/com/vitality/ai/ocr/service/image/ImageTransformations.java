package com.vitality.ai.ocr.service.image;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ByteLookupTable;
import java.awt.image.DataBuffer;
import java.awt.image.LookupOp;
import java.awt.image.Raster;
import java.awt.image.WritableRaster;
import java.util.Arrays;
import java.util.Random;
import java.util.random.RandomGenerator;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ImageTransformations {

    private static final Random random = new Random();

    /**
     * Performs contrast stretching (histogram normalization) by stretching pixel intensities
     * between the image's min and max brightness.
     */
    public static BufferedImage stretchContrast(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        WritableRaster in = image.getRaster();

        // Use a histogram for faster min/max calculation
        int[] histogram = new int[256];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                histogram[in.getSample(x, y, 0)]++;
            }
        }

        // Find min/max values from histogram
        int min = 0;
        while (min < 255 && histogram[min] == 0) min++;

        int max = 255;
        while (max > 0 && histogram[max] == 0) max--;

        // Skip if no stretch needed
        if (min == 0 && max == 255) {
            return image;
        }

        // Prepare lookup table for faster processing
        byte[] lut = new byte[256];
        if (max > min) {
            float scale = 255f / (max - min);
            for (int i = 0; i < 256; i++) {
                lut[i] = (byte) (i <= min ? 0 : (i >= max ? 255 : Math.round((i - min) * scale)));
            }
        } else {
            // Edge case: all pixels same value
            Arrays.fill(lut, (byte) 128);
        }

        // Apply lookup table transform
        LookupOp op = new LookupOp(new ByteLookupTable(0, lut), null);
        return op.filter(image, null);
    }

    /**
     * Gentle contrast enhancement specifically designed for documents with weak text.
     * Uses percentile-based stretching to avoid over-enhancing noise while boosting weak text.
     */
    public static BufferedImage enhanceContrast(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        WritableRaster in = image.getRaster();

        // Build histogram
        int[] histogram = new int[256];
        int totalPixels = 0;
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                histogram[in.getSample(x, y, 0)]++;
                totalPixels++;
            }
        }

        // Use percentile-based stretching (ignore extreme 2% on each end)
        int lowPercentile = (int) (totalPixels * 0.02);  // 2nd percentile
        int highPercentile = (int) (totalPixels * 0.98); // 98th percentile

        int min = 0, max = 255;
        int cumulative = 0;

        // Find 2nd percentile
        for (int i = 0; i < 256; i++) {
            cumulative += histogram[i];
            if (cumulative >= lowPercentile) {
                min = i;
                break;
            }
        }

        // Find 98th percentile
        cumulative = 0;
        for (int i = 255; i >= 0; i--) {
            cumulative += histogram[i];
            if (cumulative >= totalPixels - highPercentile) {
                max = i;
                break;
            }
        }

        // Ensure we have some range to work with
        if (max - min < 20) {
            // If range is too small, use gentle enhancement
            min = Math.max(0, min - 10);
            max = Math.min(255, max + 10);
        }

        // Create gentle lookup table (less aggressive than full stretch)
        byte[] lut = new byte[256];
        if (max > min) {
            float scale = 200f / (max - min); // Use 200 instead of 255 for gentler enhancement
            int offset = 27; // Start at 27 instead of 0 to preserve some gray levels

            for (int i = 0; i < 256; i++) {
                if (i <= min) {
                    lut[i] = (byte) offset;
                } else if (i >= max) {
                    lut[i] = (byte) (offset + 200);
                } else {
                    int enhanced = offset + Math.round((i - min) * scale);
                    lut[i] = (byte) Math.min(255, enhanced);
                }
            }
        } else {
            // Edge case: all pixels same value
            Arrays.fill(lut, (byte) 128);
        }

        // Apply lookup table transform
        LookupOp op = new LookupOp(new ByteLookupTable(0, lut), null);
        return op.filter(image, null);
    }

    /**
     * Applies a 3x3 median filter to denoise the grayscale image.
     * Pure Java implementation using fast sorting.
     */
    public static BufferedImage denoise(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();
        int[] medianBuffer = new int[9];

        // Skip edges to avoid bounds checking
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int idx = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        medianBuffer[idx++] = inRaster.getSample(x + dx, y + dy, 0);
                    }
                }

                // Fast median of 9 elements - partial sort
                for (int i = 0; i < 5; i++) {
                    int min_idx = i;
                    for (int j = i + 1; j < 9; j++) {
                        if (medianBuffer[j] < medianBuffer[min_idx]) {
                            min_idx = j;
                        }
                    }
                    // Swap
                    if (min_idx != i) {
                        int temp = medianBuffer[i];
                        medianBuffer[i] = medianBuffer[min_idx];
                        medianBuffer[min_idx] = temp;
                    }
                }

                outRaster.setSample(x, y, 0, medianBuffer[4]);
            }
        }

        // Copy edges from original
        for (int y = 0; y < height; y++) {
            outRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            outRaster.setSample(width - 1, y, 0, inRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            outRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            outRaster.setSample(x, height - 1, 0, inRaster.getSample(x, height - 1, 0));
        }

        return out;
    }

    /**
     * Adds realistic scanning noise with default light noise level.
     */
    public static BufferedImage addNoise(BufferedImage image) {
        return addNoise(image, 0.5);
    }

    /**
     * Adds realistic scanning noise to simulate scanned document artifacts.
     * Automatically detects grayscale vs color images.
     *
     * @param image      the image to add noise to
     * @param noiseLevel noise intensity (0.0-1.0), where 0.1 is light noise, 0.5 is moderate
     */
    public static BufferedImage addNoise(BufferedImage image, double noiseLevel) {
        BufferedImage out = copyImage(image);
        addUnevenLighting(out, noiseLevel * 0.3);
        addDustSpots(out, noiseLevel * 0.5);
        addCompressionNoise(out, noiseLevel * 0.8);
        return out;
    }

    private static BufferedImage copyImage(BufferedImage image) {
        BufferedImage out = new BufferedImage(image.getWidth(), image.getHeight(), image.getType());
        Graphics2D g = out.createGraphics();
        g.drawImage(image, 0, 0, null);
        g.dispose();
        return out;
    }

    private static void addUnevenLighting(BufferedImage image, double intensity) {
        if (intensity < 0.1) return;

        int width = image.getWidth();
        int height = image.getHeight();
        double centerX = width / 2.0;
        double centerY = height / 2.0;
        double maxDist = Math.sqrt(centerX * centerX + centerY * centerY);
        boolean isGrayscale = image.getType() == BufferedImage.TYPE_BYTE_GRAY;

        if (isGrayscale) {
            WritableRaster raster = image.getRaster();
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    double dist = Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    double factor = 1.0 - (dist / maxDist) * intensity * 0.1;
                    factor = Math.clamp(factor, 0.8, 1.2);
                    int pixel = raster.getSample(x, y, 0);
                    pixel = (int) Math.clamp(pixel * factor, 0, 255);
                    raster.setSample(x, y, 0, pixel);
                }
            }
        } else {
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    double dist = Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    double factor = 1.0 - (dist / maxDist) * intensity * 0.1;
                    factor = Math.clamp(factor, 0.8, 1.2);

                    int rgb = image.getRGB(x, y);
                    int r = (int) Math.clamp(((rgb >> 16) & 0xFF) * factor, 0, 255);
                    int g = (int) Math.clamp(((rgb >> 8) & 0xFF) * factor, 0, 255);
                    int b = (int) Math.clamp((rgb & 0xFF) * factor, 0, 255);

                    image.setRGB(x, y, (r << 16) | (g << 8) | b);
                }
            }
        }
    }

    private static void addDustSpots(BufferedImage image, double intensity) {
        int width = image.getWidth();
        int height = image.getHeight();
        int numSpots = (int) (width * height * intensity * 0.00003);
        RandomGenerator random = RandomGenerator.getDefault();
        boolean isGrayscale = image.getType() == BufferedImage.TYPE_BYTE_GRAY;

        for (int i = 0; i < numSpots; i++) {
            int spotX = random.nextInt(width);
            int spotY = random.nextInt(height);
            int spotSize = random.nextInt(4) + 1;
            double dustDarkness = 0.3 + random.nextDouble() * 0.7;

            for (int dy = -spotSize; dy <= spotSize; dy++) {
                for (int dx = -spotSize; dx <= spotSize; dx++) {
                    int x = spotX + dx;
                    int y = spotY + dy;
                    if (x >= 0 && x < width && y >= 0 && y < height) {
                        double distance = Math.sqrt((double) dx * dx + dy * dy);
                        if (distance <= spotSize) {
                            double edgeFactor = 1.0 - (distance / spotSize);
                            double finalDarkness = dustDarkness * edgeFactor;

                            if (isGrayscale) {
                                WritableRaster raster = image.getRaster();
                                int currentPixel = raster.getSample(x, y, 0);
                                int darkenedPixel = (int) (currentPixel * (1.0 - finalDarkness));
                                raster.setSample(x, y, 0, Math.max(0, darkenedPixel));
                            } else {
                                int rgb = image.getRGB(x, y);
                                int r = (int) Math.max(0, ((rgb >> 16) & 0xFF) * (1.0 - finalDarkness));
                                int g = (int) Math.max(0, ((rgb >> 8) & 0xFF) * (1.0 - finalDarkness));
                                int b = (int) Math.max(0, (rgb & 0xFF) * (1.0 - finalDarkness));
                                image.setRGB(x, y, (r << 16) | (g << 8) | b);
                            }
                        }
                    }
                }
            }
        }
    }

    private static void addCompressionNoise(BufferedImage image, double intensity) {
        int width = image.getWidth();
        int height = image.getHeight();
        boolean isGrayscale = image.getType() == BufferedImage.TYPE_BYTE_GRAY;

        for (int blockY = 0; blockY < height; blockY += 8) {
            for (int blockX = 0; blockX < width; blockX += 8) {
                if (random.nextDouble() < intensity * 0.3) {
                    int blockNoise = (int) (random.nextGaussian() * 12 * intensity);

                    for (int y = blockY; y < Math.min(blockY + 8, height); y++) {
                        for (int x = blockX; x < Math.min(blockX + 8, width); x++) {
                            if (isGrayscale) {
                                WritableRaster raster = image.getRaster();
                                int pixel = raster.getSample(x, y, 0);
                                pixel = Math.clamp(pixel + blockNoise, 0, 255);
                                raster.setSample(x, y, 0, pixel);
                            } else {
                                int rgb = image.getRGB(x, y);
                                int r = Math.clamp(((rgb >> 16) & 0xFF) + blockNoise, 0, 255);
                                int g = Math.clamp(((rgb >> 8) & 0xFF) + blockNoise, 0, 255);
                                int b = Math.clamp((rgb & 0xFF) + blockNoise, 0, 255);
                                image.setRGB(x, y, (r << 16) | (g << 8) | b);
                            }
                        }
                    }
                }
            }
        }
    }

    public static BufferedImage toGrayscale(BufferedImage src) {
        if (src.getType() == BufferedImage.TYPE_BYTE_GRAY) {
            return src;
        }
        BufferedImage gray = new BufferedImage(src.getWidth(), src.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g = gray.createGraphics();
        g.drawImage(src, 0, 0, null);
        g.dispose();
        return gray;
    }

    /**
     * Grades an image by evaluating brightness, contrast, noise, and uneven lighting.
     * Uses optimized batch sampling and avoids pixel-by-pixel loops.
     */
    public static ImageTransformations.Grade gradeImage(BufferedImage gray) {
        int w = gray.getWidth(), h = gray.getHeight();

        // Use DataBuffer directly for faster pixel access
        DataBuffer buffer = gray.getRaster().getDataBuffer();
        int numPixels = w * h;

        // Calculate using single pass algorithm
        long sum = 0, sumSq = 0;
        for (int i = 0; i < numPixels; i++) {
            int p = buffer.getElem(i);
            sum += p;
            sumSq += (long) p * p;
        }

        double mean = sum / (double) numPixels;
        double variance = (sumSq - ((double) sum * sum) / numPixels) / numPixels;
        double stdDev = Math.sqrt(variance);

        Raster raster = gray.getRaster();
        boolean uneven = checkLightingFast(raster, w, h, mean);
        int noise = estimateNoiseFast(raster, w, h);

        return new ImageTransformations.Grade((int) mean, (int) stdDev, uneven, noise);
    }

    /**
     * Detects uneven lighting across 4 corner blocks using mean pixel intensity and checks variation.
     */
    private static boolean checkLightingFast(Raster raster, int w, int h, double globalMean) {
        int blockSize = Math.min(16, Math.min(w, h) / 8); // Adaptive block size
        int[][] blocks = new int[4][];
        blocks[0] = getBlockSamples(raster, 0, 0, blockSize);
        blocks[1] = getBlockSamples(raster, w - blockSize, 0, blockSize);
        blocks[2] = getBlockSamples(raster, 0, h - blockSize, blockSize);
        blocks[3] = getBlockSamples(raster, w - blockSize, h - blockSize, blockSize);
        double[] means = Arrays.stream(blocks).mapToDouble(ImageTransformations::meanOf).toArray();
        double min = Arrays.stream(means).min().orElse(globalMean);
        double max = Arrays.stream(means).max().orElse(globalMean);
        return (max - min) > globalMean * 0.25;
    }

    /**
     * Estimates noise by measuring gradient differences on a fixed pixel stride grid.
     * High differences imply speckle noise or blur artifacts.
     */
    private static int estimateNoiseFast(Raster raster, int w, int h) {
        // Adaptive stride based on image size
        int stride = Math.max(1, Math.min(w, h) / 100);
        int count = 0;
        int[] neighborhood = new int[5]; // Center + 4 neighbors

        for (int y = stride; y < h - stride; y += stride) {
            for (int x = stride; x < w - stride; x += stride) {
                neighborhood[0] = raster.getSample(x, y, 0);      // Center
                neighborhood[1] = raster.getSample(x - stride, y, 0);  // Left
                neighborhood[2] = raster.getSample(x + stride, y, 0);  // Right
                neighborhood[3] = raster.getSample(x, y - stride, 0);  // Top
                neighborhood[4] = raster.getSample(x, y + stride, 0);  // Bottom

                int center = neighborhood[0];
                int diff = Math.abs(center - neighborhood[1]) +
                    Math.abs(center - neighborhood[2]) +
                    Math.abs(center - neighborhood[3]) +
                    Math.abs(center - neighborhood[4]);

                if (diff > 100) count++;
            }
        }
        return count;
    }

    /**
     * Extracts a square block of pixels from the raster at the given position.
     */
    private static int[] getBlockSamples(Raster r, int x, int y, int size) {
        int[] samples = new int[size * size];
        r.getSamples(x, y, size, size, 0, samples);
        return samples;
    }

    /**
     * Computes the mean (average) of an int array.
     */
    private static double meanOf(int[] data) {
        long sum = 0;
        for (int value : data) {
            sum += value;
        }
        return sum / (double) data.length;
    }

    /**
     * Detects skew angle using a reliable horizontal projection method.
     * This implementation fixes the issues in the original algorithm.
     */
    public static double detectSkewAngle(BufferedImage image) {
        // Convert to binary if needed
        BufferedImage binary = (image.getType() == BufferedImage.TYPE_BYTE_BINARY) ?
            image : toBinary(image);

        int w = binary.getWidth(), h = binary.getHeight();

        // Skip skew detection for very small images
        if (w < 100 || h < 100) {
            return 0.0;
        }

        double bestAngle = 0.0;
        double maxVariance = Double.MIN_VALUE;

        // Test angles from -5 to +5 degrees in 0.5 degree steps
        for (double angle = -5.0; angle <= 5.0; angle += 0.5) {
            double variance = calculateHorizontalProjectionVariance(binary, angle);

            // For text documents, correct alignment has MAXIMUM variance
            // (aligned text lines create sharp peaks in horizontal projection)
            if (variance > maxVariance) {
                maxVariance = variance;
                bestAngle = angle;
            }
        }

        return bestAngle;
    }

    /**
     * Calculates the variance of horizontal projection at a given angle.
     * Lower variance indicates better horizontal alignment of text lines.
     */
    private static double calculateHorizontalProjectionVariance(BufferedImage binary, double angleDegrees) {
        int w = binary.getWidth(), h = binary.getHeight();
        Raster raster = binary.getRaster();

        // Create horizontal projection array
        int[] projection = new int[h];

        double radians = Math.toRadians(angleDegrees);
        double cosTheta = Math.cos(radians);
        double sinTheta = Math.sin(radians);

        // For each pixel in the original image
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                // Check if this is a black pixel (text)
                if (raster.getSample(x, y, 0) == 0) {
                    // Apply rotation to find where this pixel projects to
                    int projY = (int) (-x * sinTheta + y * cosTheta);

                    // Add to projection if within bounds
                    if (projY >= 0 && projY < h) {
                        projection[projY]++;
                    }
                }
            }
        }

        // Calculate variance of projection
        // For text documents, aligned text creates high variance (sharp peaks)
        // We want to find the angle that maximizes this variance
        return computeVariance(projection);
    }

    /**
     * Computes variance of an integer array.
     */
    private static double computeVariance(int[] values) {
        if (values.length == 0) return 0.0;

        double sum = 0;
        for (int v : values) {
            sum += v;
        }
        double mean = sum / values.length;

        double sumSquaredDiff = 0;
        for (int v : values) {
            double diff = v - mean;
            sumSquaredDiff += diff * diff;
        }

        return sumSquaredDiff / values.length;
    }

    /**
     * Converts a grayscale image to binary using Otsu's threshold.
     * Simple implementation for skew detection.
     */
    private static BufferedImage toBinary(BufferedImage grayscale) {
        int w = grayscale.getWidth(), h = grayscale.getHeight();
        BufferedImage binary = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);

        // Simple threshold at 128 (good enough for skew detection)
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                int rgb = grayscale.getRGB(x, y);
                int gray = (rgb >> 16) & 0xFF; // Red channel for grayscale
                binary.setRGB(x, y, gray < 128 ? 0x000000 : 0xFFFFFF);
            }
        }

        return binary;
    }

    /**
     * Applies gentle denoising that preserves weak text (like footers).
     * Uses a smaller kernel and less aggressive filtering than the standard denoise method.
     */
    public static BufferedImage denoiseGentle(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();

        // Use a gentler approach: only apply median filter to obvious noise
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int center = inRaster.getSample(x, y, 0);

                // Get 3x3 neighborhood
                int[] neighbors = new int[9];
                int idx = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        neighbors[idx++] = inRaster.getSample(x + dx, y + dy, 0);
                    }
                }

                // Calculate variance in neighborhood
                double mean = 0;
                for (int val : neighbors) mean += val;
                mean /= 9;

                double variance = 0;
                for (int val : neighbors) {
                    variance += (val - mean) * (val - mean);
                }
                variance /= 9;

                // Apply gentle median filtering with adaptive threshold
                // Lower threshold to catch more subtle noise while preserving text
                if (variance > 100) { // Much lower threshold for subtle noise
                    // Sort for median
                    java.util.Arrays.sort(neighbors);
                    int median = neighbors[4];

                    // Only apply median if it's not too different from center (preserve text edges)
                    if (Math.abs(median - center) < 30) {
                        outRaster.setSample(x, y, 0, median);
                    } else {
                        outRaster.setSample(x, y, 0, center); // Keep original for text edges
                    }
                } else {
                    outRaster.setSample(x, y, 0, center); // Keep original for smooth areas
                }
            }
        }

        // Copy edges from original
        for (int y = 0; y < height; y++) {
            outRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            outRaster.setSample(width - 1, y, 0, inRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            outRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            outRaster.setSample(x, height - 1, 0, inRaster.getSample(x, height - 1, 0));
        }

        return out;
    }

    /**
     * Truly consistent denoising that applies to ALL pixels on ALL pages.
     * Uses a very gentle approach that always processes every pixel.
     */
    public static BufferedImage denoiseConsistent(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();

        // Apply gentle filtering to ALL pixels - no skipping
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int center = inRaster.getSample(x, y, 0);

                // Get 3x3 neighborhood
                int[] neighbors = new int[9];
                int idx = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        neighbors[idx++] = inRaster.getSample(x + dx, y + dy, 0);
                    }
                }

                // Sort for median
                java.util.Arrays.sort(neighbors);
                int median = neighbors[4];

                // ALWAYS apply gentle blending - no conditions that skip processing
                // Use very gentle blend: 90% original + 10% median
                int result = (center * 9 + median) / 10;

                outRaster.setSample(x, y, 0, result);
            }
        }

        // Copy edges from original
        for (int y = 0; y < height; y++) {
            outRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            outRaster.setSample(width - 1, y, 0, inRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            outRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            outRaster.setSample(x, height - 1, 0, inRaster.getSample(x, height - 1, 0));
        }

        return out;
    }

    /**
     * Alternative: Gaussian blur denoising that's guaranteed to process all pixels.
     * Uses a simple 3x3 Gaussian kernel for very gentle, consistent denoising.
     */
    public static BufferedImage denoiseGaussian(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();

        // 3x3 Gaussian kernel (normalized)
        double[][] kernel = {
            {1.0 / 16, 2.0 / 16, 1.0 / 16},
            {2.0 / 16, 4.0 / 16, 2.0 / 16},
            {1.0 / 16, 2.0 / 16, 1.0 / 16}
        };

        // Apply Gaussian blur to ALL pixels
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                double sum = 0;

                for (int ky = -1; ky <= 1; ky++) {
                    for (int kx = -1; kx <= 1; kx++) {
                        int pixel = inRaster.getSample(x + kx, y + ky, 0);
                        sum += pixel * kernel[ky + 1][kx + 1];
                    }
                }

                outRaster.setSample(x, y, 0, (int) Math.round(sum));
            }
        }

        // Copy edges from original
        for (int y = 0; y < height; y++) {
            outRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            outRaster.setSample(width - 1, y, 0, inRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            outRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            outRaster.setSample(x, height - 1, 0, inRaster.getSample(x, height - 1, 0));
        }

        return out;
    }

    /**
     * Hybrid denoising that combines median filtering for noise removal with Gaussian for smoothing.
     * This approach is specifically designed to handle scanner artifacts like the dots in your image.
     */
    public static BufferedImage denoiseHybrid(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster inRaster = image.getRaster();
        WritableRaster outRaster = out.getRaster();

        // Step 1: Apply median filter to ALL pixels to remove discrete noise points
        BufferedImage medianFiltered = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        WritableRaster medianRaster = medianFiltered.getRaster();

        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                // Get 3x3 neighborhood
                int[] neighbors = new int[9];
                int idx = 0;
                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        neighbors[idx++] = inRaster.getSample(x + dx, y + dy, 0);
                    }
                }

                // Sort for median - this removes isolated noise points
                java.util.Arrays.sort(neighbors);
                medianRaster.setSample(x, y, 0, neighbors[4]);
            }
        }

        // Copy edges for median filtered image
        for (int y = 0; y < height; y++) {
            medianRaster.setSample(0, y, 0, inRaster.getSample(0, y, 0));
            medianRaster.setSample(width - 1, y, 0, inRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            medianRaster.setSample(x, 0, 0, inRaster.getSample(x, 0, 0));
            medianRaster.setSample(x, height - 1, 0, inRaster.getSample(x, height - 1, 0));
        }

        // Step 2: Apply gentle Gaussian blur to smooth the result
        double[][] kernel = {
            {1.0 / 16, 2.0 / 16, 1.0 / 16},
            {2.0 / 16, 4.0 / 16, 2.0 / 16},
            {1.0 / 16, 2.0 / 16, 1.0 / 16}
        };

        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                double sum = 0;

                for (int ky = -1; ky <= 1; ky++) {
                    for (int kx = -1; kx <= 1; kx++) {
                        int pixel = medianRaster.getSample(x + kx, y + ky, 0);
                        sum += pixel * kernel[ky + 1][kx + 1];
                    }
                }

                outRaster.setSample(x, y, 0, (int) Math.round(sum));
            }
        }

        // Copy edges for final image
        for (int y = 0; y < height; y++) {
            outRaster.setSample(0, y, 0, medianRaster.getSample(0, y, 0));
            outRaster.setSample(width - 1, y, 0, medianRaster.getSample(width - 1, y, 0));
        }
        for (int x = 1; x < width - 1; x++) {
            outRaster.setSample(x, 0, 0, medianRaster.getSample(x, 0, 0));
            outRaster.setSample(x, height - 1, 0, medianRaster.getSample(x, height - 1, 0));
        }

        return out;
    }

    /**
     * Binarizes a grayscale image using Otsu's automatic threshold selection.
     * This method finds the optimal threshold that minimizes intra-class variance.
     *
     * @param grayscale the input grayscale image
     * @return binary image with optimal threshold applied
     */
    public static BufferedImage binarizeOtsu(BufferedImage grayscale) {
        int w = grayscale.getWidth(), h = grayscale.getHeight();

        // Calculate histogram
        int[] histogram = new int[256];
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                int rgb = grayscale.getRGB(x, y);
                int gray = (rgb >> 16) & 0xFF; // Red channel for grayscale
                histogram[gray]++;
            }
        }

        // Find optimal threshold using Otsu's method
        int threshold = calculateOtsuThreshold(histogram, w * h);

        // Apply threshold to create binary image
        BufferedImage binary = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_BINARY);
        for (int y = 0; y < h; y++) {
            for (int x = 0; x < w; x++) {
                int rgb = grayscale.getRGB(x, y);
                int gray = (rgb >> 16) & 0xFF;
                binary.setRGB(x, y, gray <= threshold ? 0x000000 : 0xFFFFFF);
            }
        }

        return binary;
    }

    /**
     * Calculates the optimal threshold using Otsu's method.
     * Finds the threshold that minimizes within-class variance.
     */
    private static int calculateOtsuThreshold(int[] histogram, int totalPixels) {
        double sum = 0;
        for (int i = 0; i < 256; i++) {
            sum += i * histogram[i];
        }

        double sumB = 0;
        int wB = 0;
        int wF = 0;
        double varMax = 0;
        int threshold = 0;

        for (int t = 0; t < 256; t++) {
            wB += histogram[t]; // Weight background
            if (wB == 0) continue;

            wF = totalPixels - wB; // Weight foreground
            if (wF == 0) break;

            sumB += (double) (t * histogram[t]);

            double mB = sumB / wB; // Mean background
            double mF = (sum - sumB) / wF; // Mean foreground

            // Calculate between-class variance
            double varBetween = (double) wB * (double) wF * (mB - mF) * (mB - mF);

            // Check if new maximum found
            if (varBetween > varMax) {
                varMax = varBetween;
                threshold = t;
            }
        }

        return threshold;
    }

    /**
     * Rotates a binary image using Graphics2D with bilinear interpolation and
     * white background fill.
     */
    public static BufferedImage rotateImage(BufferedImage src, double angleDegrees) {
        // Skip rotation if angle is too small
        if (Math.abs(angleDegrees) < 0.05) {
            return src;
        }

        double radians = Math.toRadians(angleDegrees);
        int w = src.getWidth();
        int h = src.getHeight();
        double sin = Math.abs(Math.sin(radians));
        double cos = Math.abs(Math.cos(radians));
        int newW = (int) Math.floor(w * cos + h * sin);
        int newH = (int) Math.floor(h * cos + w * sin);

        BufferedImage rotated = new BufferedImage(newW, newH, src.getType());
        Graphics2D g2d = rotated.createGraphics();

        // Use bilinear interpolation for grayscale images to preserve noise detail
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
            RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setBackground(Color.WHITE);
        g2d.clearRect(0, 0, newW, newH);
        g2d.translate(newW / 2.0, newH / 2.0);
        g2d.rotate(radians);
        g2d.translate(-w / 2.0, -h / 2.0);
        g2d.drawImage(src, 0, 0, null);
        g2d.dispose();

        return rotated;
    }

    /**
     * Scales an image to a specified percentage of its original size.
     *
     * @param image        The image to scale
     * @param scalePercent The percentage to scale (50 = half size, 200 = double size)
     * @return The scaled image
     */
    public static BufferedImage scaleImage(BufferedImage image, int scalePercent) {
        if (scalePercent <= 0) throw new IllegalArgumentException("Scale percentage must be positive");
        if (scalePercent == 100) return image; // No scaling needed

        int targetWidth = (int) (image.getWidth() * scalePercent / 100.0);
        int targetHeight = (int) (image.getHeight() * scalePercent / 100.0);

        // Use faster nearest neighbor scaling for binary images (better for OCR text)
        // This preserves the sharp edges of text better than bilinear
        Object hint = RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR;

        BufferedImage scaled = new BufferedImage(targetWidth, targetHeight, image.getType());
        Graphics2D g = scaled.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, hint);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
        g.drawImage(image, 0, 0, targetWidth, targetHeight, null);
        g.dispose();

        return scaled;
    }

    @Data
    @AllArgsConstructor
    public static class Grade {
        private int brightness;
        private int contrast;
        private boolean hasUnevenLighting;
        private int noiseLevel;
    }
}




















