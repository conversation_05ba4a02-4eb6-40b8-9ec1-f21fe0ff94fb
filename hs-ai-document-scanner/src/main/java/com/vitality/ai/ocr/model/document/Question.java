package com.vitality.ai.ocr.model.document;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

public record Question(Integer questionNumber,
                       String questionText,
                       AnswerType answerType,
                       AnswerStatus answerStatus,
                       int confidenceScore,
                       @JsonInclude(JsonInclude.Include.NON_NULL)
                       RatingScaleRange ratingScaleRange,
                       List<String> answerOptions,
                       @JsonInclude(JsonInclude.Include.ALWAYS)
                       List<String> answer) {
}
