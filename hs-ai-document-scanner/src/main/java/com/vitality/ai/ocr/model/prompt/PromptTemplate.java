package com.vitality.ai.ocr.model.prompt;

import com.vitality.ai.ocr.config.PromptSection;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
public class PromptTemplate {
    private String role;
    private String task;
    private final Map<String, List<String>> context = new LinkedHashMap<>();
    private final Map<String, List<String>> instructions = new LinkedHashMap<>();

    public static PromptTemplate builder() {
        return new PromptTemplate();
    }

    public PromptTemplate withRole(String role) {
        this.role = role;
        return this;
    }

    public PromptTemplate withContextSection(String name, List<String> content) {
        this.context.put(name, content);
        return this;
    }

    public PromptTemplate withTask(String task) {
        this.task = task;
        return this;
    }

    public PromptTemplate withInstructionsSection(String title, List<String> instructions) {
        this.instructions.put(title, instructions);
        return this;
    }

    public PromptTemplate withContextSections(List<PromptSection> sections) {
        if (sections != null) {
            sections.forEach(section ->
                this.context.put(section.getTitle(), section.getMessages()));
        }
        return this;
    }

    public PromptTemplate withInstructionSections(List<PromptSection> sections) {
        if (sections != null) {
            sections.forEach(section ->
                this.instructions.put(section.getTitle(), section.getMessages()));
        }
        return this;
    }

    public String toSystemPrompt() {
        StringBuilder sb = new StringBuilder();

        if (role != null && !role.isBlank()) {
            sb.append(role);
        }

        for (Map.Entry<String, List<String>> entry : context.entrySet()) {
            if (!sb.isEmpty()) sb.append("\n\n");
            sb.append(entry.getKey()).append(":\n");
            for (String contextItem : entry.getValue()) {
                sb.append("- ").append(contextItem).append("\n");
            }
        }

        return sb.toString().trim();
    }

    public String toUserPrompt() {
        StringBuilder sb = new StringBuilder();

        if (task != null && !task.isBlank()) {
            sb.append(task);
        }

        for (Map.Entry<String, List<String>> entry : instructions.entrySet()) {
            if (!sb.isEmpty()) sb.append("\n\n");
            sb.append(entry.getKey()).append(":\n");
            for (String instruction : entry.getValue()) {
                sb.append("- ").append(instruction).append("\n");
            }
        }

        return sb.toString().trim();
    }

    @Override
    public String toString() {
        String systemPrompt = toSystemPrompt();
        String userPrompt = toUserPrompt();

        if (systemPrompt.isEmpty()) {
            return userPrompt;
        }
        if (userPrompt.isEmpty()) {
            return systemPrompt;
        }

        return systemPrompt + "\n\n" + userPrompt;
    }
}
