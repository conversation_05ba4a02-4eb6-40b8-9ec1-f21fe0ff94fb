package com.vitality.ai.ocr.service.pdf.pdfbox;

import com.vitality.ai.ocr.model.file.FileHandle;
import com.vitality.ai.ocr.model.pdf.RenderedPdf;
import com.vitality.ai.ocr.service.image.ImageFormat;
import com.vitality.ai.ocr.service.image.ImageProcessor;
import com.vitality.ai.ocr.service.pdf.PdfProcessingOptions;
import com.vitality.ai.ocr.service.pdf.PdfRenderException;
import com.vitality.ai.ocr.service.pdf.PdfRenderer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Component;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
@Component
public class PdfboxRenderer implements PdfRenderer {

    public static final float DEFAULT_QUALITY = 0.85f;

    @Override
    public RenderedPdf renderPagesToImages(FileHandle file, PdfProcessingOptions options) {
        if (log.isInfoEnabled()) {
            String hash = file.getHash();
            log.info("Start rendering PDF (name: {}, size: {}, hash: {}) to raster images ({} dpi, format: {})",
                file.getName(), FileUtils.byteCountToDisplaySize(file.getSize()),
                hash, options.getDpi(), options.getImageFormat());
        }

        long start = System.currentTimeMillis();

        byte[] pdfBytes = getFileContent(file);

        try (PDDocument document = Loader.loadPDF(pdfBytes)) {
            int pageCount = document.getNumberOfPages();

            List<byte[]> pages = IntStream.range(0, pageCount)
                .parallel()
                .mapToObj(i -> {
                    long pageStart = System.currentTimeMillis();
                    try {
                        byte[] imageBytes = renderPageToImage(pdfBytes, i, options.getDpi(), options.getImageFormat());
                        imageBytes = processImage(imageBytes, options.getImageProcessors());

                        log.info("Rendered page {} of {} in {} ms",
                            i + 1, pageCount, System.currentTimeMillis() - pageStart);

                        return imageBytes;
                    } catch (IOException e) {
                        throw new PdfRenderException("Failed to render page " + i, e);
                    }
                })
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

            if (log.isInfoEnabled()) {
                String hash = file.getHash();
                log.info("Rendered {} pages of PDF (name: {}, size: {}, hash: {}) to raster images ({} dpi, format: {}) in {} ms",
                    pages.size(), file.getName(), FileUtils.byteCountToDisplaySize(file.getSize()),
                    hash, options.getDpi(), options.getImageFormat(), System.currentTimeMillis() - start);
            }
            return new RenderedPdf(pages, options.getDpi(), options.getImageFormat());
        } catch (Exception e) {
            throw new PdfRenderException("Failed to render PDF", e);
        }
    }

    private static byte[] getFileContent(FileHandle file) {
        try {
            return file.readAllBytes();
        } catch (IOException e) {
            throw new PdfRenderException("Failed to read PDF bytes", e);
        }
    }

    private static byte[] processImage(byte[] imageBytes, List<ImageProcessor> imageProcessors) {
        byte[] processedImage = imageBytes;
        if (imageProcessors != null) {
            for (ImageProcessor processor : imageProcessors) {
                if (processor != null) {
                    processedImage = processor.process(processedImage);
                }
            }
        }
        return processedImage;
    }

    private byte[] renderPageToImage(byte[] pdfBytes, int pageIndex, int dpi, String format) throws IOException {
        try (PDDocument document = Loader.loadPDF(pdfBytes)) {
            if (pageIndex < 0 || pageIndex >= document.getNumberOfPages()) {
                throw new IllegalArgumentException("Page index out of range: " + pageIndex);
            }
            PDFRenderer renderer = new PDFRenderer(document);
            BufferedImage image = renderer.renderImageWithDPI(pageIndex, dpi);
            return convertImageToBytes(image, format);
        }
    }

    private byte[] convertImageToBytes(BufferedImage image, String format) throws IOException {
        return convertImageToBytes(image, format, DEFAULT_QUALITY);
    }

    private byte[] convertImageToBytes(BufferedImage image, String format, float quality) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        if (ImageFormat.JPEG.equalsIgnoreCase(format) || ImageFormat.JPG.equalsIgnoreCase(format)) {
            ImageWriter writer = ImageIO.getImageWritersByFormatName(ImageFormat.JPEG).next();
            try (ImageOutputStream ios = ImageIO.createImageOutputStream(out)) {
                writer.setOutput(ios);

                ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(quality);

                writer.write(null, new IIOImage(image, null, null), param);
            } finally {
                writer.dispose();
            }
        } else {
            ImageIO.write(image, format, out);
        }

        return out.toByteArray();
    }
}
