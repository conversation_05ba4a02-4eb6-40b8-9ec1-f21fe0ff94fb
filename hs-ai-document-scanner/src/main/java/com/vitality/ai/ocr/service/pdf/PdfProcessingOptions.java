package com.vitality.ai.ocr.service.pdf;

import com.vitality.ai.ocr.service.image.ImageFormat;
import com.vitality.ai.ocr.service.image.ImageProcessor;
import lombok.Builder;
import lombok.Setter;
import lombok.Value;

import java.util.List;

@Value
@Builder
@Setter
public class PdfProcessingOptions {
    @Builder.Default
    int dpi = PdfConstants.DEFAULT_DPI;

    @Builder.Default
    String imageFormat = ImageFormat.PNG;

    List<ImageProcessor> imageProcessors;

    @Builder.Default
    double noiseLevel = 0.5;

    @Builder.Default
    boolean grayscale = false;

    public static PdfProcessingOptions defaults() {
        return PdfProcessingOptions.builder().build();
    }

    /**
     * Creates a new builder initialized with the values from this instance
     *
     * @return a new PdfProcessingOptionsBuilder with all fields pre-populated
     */
    public PdfProcessingOptionsBuilder toBuilder() {
        return PdfProcessingOptions.builder()
            .dpi(this.dpi)
            .imageFormat(this.imageFormat)
            .imageProcessors(this.imageProcessors)
            .noiseLevel(this.noiseLevel)
            .grayscale(this.grayscale);
    }
}
