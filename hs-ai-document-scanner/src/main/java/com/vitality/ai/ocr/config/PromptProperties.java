package com.vitality.ai.ocr.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "ai.prompts")
public class PromptProperties {
    private Map<String, PromptDefinition> structured;

    @Data
    public static class PromptDefinition {
        private String role;
        private List<PromptSection> context;
        private String task;
        private List<PromptSection> instructions;
    }
}
