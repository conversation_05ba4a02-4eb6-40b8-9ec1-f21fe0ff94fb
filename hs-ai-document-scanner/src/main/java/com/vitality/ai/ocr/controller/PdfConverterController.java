package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.ocr.model.file.BufferedFile;
import com.vitality.ai.ocr.model.file.FileHandle;
import com.vitality.ai.ocr.service.FileValidationService;
import com.vitality.ai.ocr.service.image.OptimizeForOcr;
import com.vitality.ai.ocr.service.pdf.PdfConstants;
import com.vitality.ai.ocr.service.pdf.PdfConverter;
import com.vitality.ai.ocr.service.pdf.PdfProcessingOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for converting regular PDFs to "scanned" PDFs.
 * This controller accepts PDF files and returns them as image-based PDFs,
 * simulating the effect of physically scanning a document.
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/pdf/convert")
@Tag(name = "PDF Converter", description = "Convert regular PDFs to scanned PDFs")
public class PdfConverterController {

    private final PdfConverter pdfConverter;
    private final FileValidationService fileValidationService;
    private final ObjectMapper objectMapper;

    /**
     * Convert a regular PDF to a "scanned" PDF where each page is rendered as an image.
     * This is useful for testing OCR services or document processing workflows that expect scanned documents.
     *
     * @param file The PDF file to convert
     * @param dpi  The resolution for rendering pages (optional, default: 300)
     * @return The converted "scanned" PDF as a downloadable file
     */
    @Operation(
        summary = "Convert PDF to Scanned PDF",
        description = "Converts a regular PDF document into a 'scanned' PDF where each page is rendered as a high-resolution image. " +
            "This simulates the effect of physically scanning a document, making it useful for testing OCR services " +
            "or document processing workflows that expect image-based PDFs."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully converted to scanned format",
            content = @Content(
                mediaType = MediaType.APPLICATION_PDF_VALUE,
                schema = @Schema(type = "string", format = "binary", description = "Scanned PDF file")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty, not a PDF, or invalid DPI",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF conversion",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/scanned-pdf", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_PDF_VALUE)
    public ResponseEntity<Resource> convertToScannedPdf(
        @Parameter(
            description = "PDF file to convert to scanned format",
            required = true,
            content = @Content(mediaType = MediaType.APPLICATION_PDF_VALUE)
        )
        @RequestParam("file") MultipartFile file,

        @Parameter(
            description = "Resolution for rendering pages in DPI (dots per inch). " +
                "Higher values produce better quality but larger files. " +
                "Minimum: 150, Recommended: 300, Maximum: 600",
            example = "300"
        )
        @RequestParam(value = "dpi", defaultValue = "300") int dpi,

        @Parameter(
            description = "Noise level for scanning effects (0.0-1.0). " +
                "0.0 = no noise, 0.5 = moderate noise, 1.0 = heavy noise",
            example = "0.5"
        )
        @RequestParam(value = "noiseLevel", defaultValue = "0.5") double noiseLevel,

        @Parameter(
            description = "Convert to grayscale (true) or preserve colors (false)",
            example = "true"
        )
        @RequestParam(value = "grayscale", defaultValue = "false") boolean grayscale) {

        log.info("Converting PDF: {} (size: {} bytes) to a scanned PDF at {} DPI with noise level {}",
            file.getOriginalFilename(), file.getSize(), dpi, noiseLevel);

        try {
            validateDpi(dpi);
            fileValidationService.validatePdfFile(file);

            FileHandle inputFile = new BufferedFile(file.getOriginalFilename(), file.getContentType(), file.getBytes());
            PdfProcessingOptions options = PdfProcessingOptions.builder()
                .dpi(dpi)
                .noiseLevel(noiseLevel)
                .grayscale(grayscale)
                .build();

            FileHandle result = pdfConverter.convertToScannedPdf(inputFile, options);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", result.getName());

            return ResponseEntity.ok()
                .headers(headers)
                .body(new ByteArrayResource(result.readAllBytes()));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF conversion: {}", e.getMessage());
            return createErrorResponse("Invalid input: " + e.getMessage(), HttpStatus.BAD_REQUEST.value());

        } catch (IOException e) {
            log.error("IO error during PDF conversion: {}", e.getMessage(), e);
            return createErrorResponse("Failed to process PDF: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value());

        } catch (Exception e) {
            log.error("Unexpected error during PDF conversion: {}", e.getMessage(), e);
            return createErrorResponse("Internal server error during PDF conversion", HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }


    @Operation(
        summary = "Convert PDF to Standard Images",
        description = "Renders each page of a PDF document into standard PNG images and returns them as a ZIP archive."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully rendered to images ZIP",
            content = @Content(
                mediaType = "application/zip",
                schema = @Schema(type = "string", format = "binary", description = "ZIP file containing PNG images")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty or not a PDF",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF rendering",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/images", consumes = "multipart/form-data", produces = "application/zip")
    public ResponseEntity<Resource> convertToImagesZip(
        @Parameter(
            description = "PDF file to render to images",
            required = true,
            content = @Content(mediaType = MediaType.APPLICATION_PDF_VALUE)
        )
        @RequestParam("file") MultipartFile file) {

        log.info("Render PDF: {} (size: {} bytes) to standard images",
            file.getOriginalFilename(), file.getSize());

        try {
            fileValidationService.validatePdfFile(file);
            FileHandle inputFile = new BufferedFile(file.getOriginalFilename(), file.getContentType(), file.getBytes());
            PdfProcessingOptions options = PdfProcessingOptions.builder().dpi(PdfConstants.DEFAULT_DPI).build();
            FileHandle result = pdfConverter.convertToImagesZip(inputFile, options);
            return createZipResponse(result.readAllBytes(), result.getName());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF rendering: {}", e.getMessage());
            return createErrorResponse("Invalid input: " + e.getMessage(), HttpStatus.BAD_REQUEST.value());
        } catch (Exception e) {
            log.error("Error during PDF rendering: {}", e.getMessage(), e);
            return createErrorResponse("Failed to render PDF to images", HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    @Operation(
        summary = "Convert PDF to Scanned Images",
        description = "Renders PDF pages with scanned document effects (noise, rotation, artifacts) and returns them as a ZIP archive."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully rendered to scanned images ZIP",
            content = @Content(
                mediaType = "application/zip",
                schema = @Schema(type = "string", format = "binary", description = "ZIP file containing scanned-style PNG images")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty or not a PDF",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF rendering",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/scanned-images", consumes = "multipart/form-data", produces = "application/zip")
    public ResponseEntity<Resource> convertToScannedImagesZip(
        @Parameter(
            description = "PDF file to render with scanned effects",
            required = true,
            content = @Content(mediaType = MediaType.APPLICATION_PDF_VALUE)
        )
        @RequestParam("file") MultipartFile file,
        @Parameter(
            description = "Resolution for rendering pages in DPI (dots per inch). " +
                "Higher values produce better quality but larger files. " +
                "Minimum: 150, Recommended: 300, Maximum: 600",
            example = "300"
        )
        @RequestParam(value = "dpi", defaultValue = "300") int dpi,

        @Parameter(
            description = "Noise level for scanning effects (0.0-1.0). " +
                "0.0 = no noise, 0.5 = moderate noise, 1.0 = heavy noise",
            example = "0.5"
        )
        @RequestParam(value = "noiseLevel", defaultValue = "0.5") double noiseLevel,
        @Parameter(
            description = "Convert to grayscale (true) or preserve colors (false)",
            example = "true"
        )
        @RequestParam(value = "grayscale", defaultValue = "false") boolean grayscale) {

        log.info("Render PDF: {} (size: {} bytes) to scanned images",
            file.getOriginalFilename(), file.getSize());

        try {
            validateDpi(dpi);
            fileValidationService.validatePdfFile(file);
            FileHandle inputFile = new BufferedFile(file.getOriginalFilename(), file.getContentType(), file.getBytes());
            PdfProcessingOptions options = PdfProcessingOptions.builder()
                .dpi(dpi)
                .noiseLevel(noiseLevel)
                .grayscale(grayscale)
                .build();

            FileHandle result = pdfConverter.convertToScannedImagesZip(inputFile, options);
            return createZipResponse(result.readAllBytes(), result.getName());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF rendering: {}", e.getMessage());
            return createErrorResponse("Invalid input: " + e.getMessage(), HttpStatus.BAD_REQUEST.value());
        } catch (Exception e) {
            log.error("Error during PDF rendering: {}", e.getMessage(), e);
            return createErrorResponse("Failed to render PDF to images", HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    @Operation(
        summary = "Convert PDF to OCR-Optimized Images",
        description = "Renders PDF pages optimized for OCR processing (enhanced contrast, noise reduction) and returns them as a ZIP archive."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully rendered to OCR-optimized images ZIP",
            content = @Content(
                mediaType = "application/zip",
                schema = @Schema(type = "string", format = "binary", description = "ZIP file containing OCR-optimized PNG images")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty or not a PDF",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF rendering",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/ocr-optimized-images", consumes = "multipart/form-data", produces = "application/zip")
    public ResponseEntity<Resource> convertToOcrOptimizedImagesZip(
        @Parameter(
            description = "PDF file to render with OCR optimizations",
            required = true,
            content = @Content(mediaType = MediaType.APPLICATION_PDF_VALUE)
        )
        @RequestParam("file") MultipartFile file) {

        log.info("Render PDF: {} (size: {} bytes) to OCR optimized images",
            file.getOriginalFilename(), file.getSize());

        try {
            fileValidationService.validatePdfFile(file);
            FileHandle inputFile = new BufferedFile(file.getOriginalFilename(), file.getContentType(), file.getBytes());
            PdfProcessingOptions options = PdfProcessingOptions.builder()
                .dpi(PdfConstants.DEFAULT_DPI)
                .imageProcessors(List.of(new OptimizeForOcr()))
                .build();
            FileHandle result = pdfConverter.convertToOcrOptimizedImagesZip(inputFile, options);
            return createZipResponse(result.readAllBytes(), result.getName());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF rendering: {}", e.getMessage());
            return createErrorResponse("Invalid input: " + e.getMessage(), HttpStatus.BAD_REQUEST.value());
        } catch (Exception e) {
            log.error("Error during PDF rendering: {}", e.getMessage(), e);
            return createErrorResponse("Failed to render PDF to images", HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    private ResponseEntity<Resource> createZipResponse(byte[] zipBytes, String outputFilename) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/zip"));
        headers.setContentDispositionFormData("attachment", outputFilename);

        return ResponseEntity.ok()
            .headers(headers)
            .body(new ByteArrayResource(zipBytes));
    }

    private void validateDpi(int dpi) {
        if (dpi < PdfConstants.MIN_DPI) {
            throw new IllegalArgumentException("DPI must be at least 150 for readable output. Recommended: 300+");
        }
        if (dpi > PdfConstants.MAX_DPI) {
            log.warn("High DPI ({}) may result in very large file sizes and slow processing", dpi);
        }
    }

    private ResponseEntity<Resource> createErrorResponse(String message, int statusCode) {
        return ResponseEntity.status(statusCode)
            .contentType(MediaType.APPLICATION_JSON)
            .body(new ByteArrayResource(createErrorJson(message).getBytes()));
    }

    private String createErrorJson(String message) {
        try {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", message);
            errorResponse.put("timestamp", Instant.now().toString());
            return objectMapper.writeValueAsString(errorResponse);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize error response: {}", e.getMessage(), e);
            return String.format("{\"error\": \"%s\", \"timestamp\": \"%s\"}",
                message.replace("\"", "\\\""), Instant.now().toString());
        }
    }

}
