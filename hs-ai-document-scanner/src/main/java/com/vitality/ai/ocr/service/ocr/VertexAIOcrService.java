package com.vitality.ai.ocr.service.ocr;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.ai.ocr.model.document.AiOcrResponse;
import com.vitality.ai.ocr.model.file.FileHandle;
import com.vitality.ai.ocr.model.pdf.RenderedPdf;
import com.vitality.ai.ocr.model.prompt.PromptTemplate;
import com.vitality.ai.ocr.service.PromptTemplateService;
import com.vitality.ai.ocr.service.image.OptimizeForOcr;
import com.vitality.ai.ocr.service.pdf.PdfConstants;
import com.vitality.ai.ocr.service.pdf.PdfProcessingOptions;
import com.vitality.ai.ocr.service.pdf.PdfRenderer;
import com.vitality.ai.properties.VertexProperties;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.Content;
import dev.langchain4j.data.message.ImageContent;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.TextContent;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.request.ChatRequest;
import dev.langchain4j.model.chat.request.ResponseFormat;
import dev.langchain4j.model.chat.request.ResponseFormatType;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.service.output.JsonSchemas;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class VertexAIOcrService implements AiOcrService {
    private final ChatModel chatModel;
    private final PdfRenderer pdfRenderer;
    private final PromptTemplateService promptService;
    private final ObjectMapper mapper;
    private final VertexProperties vertexProperties;

    @Override
    public AiOcrResponse performOcrForPdf(String promptId, FileHandle file) {
        try {
            if (!file.getContentType().toLowerCase().contains("pdf")) {
                throw new IllegalArgumentException("Unsupported content type: " + file.getContentType());
            }

            byte[] fileContent = file.readAllBytes();

            log.info("Start OCR for PDF file (name: {}, size: {}, hash: {}) with prompt {}",
                file.getName(), FileUtils.byteCountToDisplaySize(fileContent.length), file.getHash(), promptId);

            List<String> pagesBase64 = getDocumentPages(file);

            PromptTemplate promptTemplate = promptService.getPromptTemplate(promptId);

            logPrompt(promptTemplate);

            ChatRequest chatRequest = buildChatRequest(promptTemplate, pagesBase64);

            log.info("Sending OCR request to AI model '{}' for PDF file (name: {}, hash: {})",
                vertexProperties.getModel(), file.getName(), file.getHash());

            long start = System.currentTimeMillis();

            ChatResponse chatResponse = chatModel.chat(chatRequest);

            if (log.isDebugEnabled()) {
                log.debug(chatResponse.toString());
            }

            log.info("Received OCR response from AI model '{}' for PDF file (name: {}, hash: {}) in {} ms",
                vertexProperties.getModel(), file.getName(), file.getHash(), System.currentTimeMillis() - start);

            return mapper.readValue(chatResponse.aiMessage().text(), AiOcrResponse.class);
        } catch (Exception e) {
            throw new AiOcrException("Failed to parse response", e);
        }
    }

    @NotNull
    private List<String> getDocumentPages(FileHandle file) {
        PdfProcessingOptions options = PdfProcessingOptions.builder()
            .dpi(PdfConstants.OCR_DPI)
            .imageProcessors(List.of(new OptimizeForOcr()))
            .build();

        RenderedPdf renderedPdf = pdfRenderer.renderPagesToImages(file, options);
        List<String> pagesBase64 = renderedPdf.pages().stream()
            .map(page -> Base64.getEncoder().encodeToString(page)).toList();

        if (log.isInfoEnabled()) {
            long totalBase64Size = pagesBase64.stream().mapToLong(String::length).sum();

            try {
                byte[] fileContent = file.readAllBytes();

                log.info("Converted PDF file (name: {}, size: {}, hash: {}) to OCR optimized Base64 images (size: {}, dpi: {}, size: {})",
                    file.getName(), FileUtils.byteCountToDisplaySize(fileContent.length), file.getHash(),
                    renderedPdf.pages().size(), PdfConstants.OCR_DPI, FileUtils.byteCountToDisplaySize(totalBase64Size));
            } catch (IOException e) {
                throw new AiOcrException("Failed to read file content", e);
            }
        }

        return pagesBase64;
    }

    private static ChatRequest buildChatRequest(PromptTemplate promptTemplate, List<String> pagesBase64) {
        final ArrayList<Content> contents = new ArrayList<>();
        contents.add(new TextContent(promptTemplate.toUserPrompt()));
        pagesBase64.forEach(pageBase64 -> contents.add(
            new ImageContent(pageBase64, MediaType.IMAGE_PNG_VALUE, ImageContent.DetailLevel.AUTO)
        ));

        final UserMessage userMessage = UserMessage.from(contents);
        SystemMessage systemMessage = SystemMessage.from(promptTemplate.toSystemPrompt());
        final List<ChatMessage> chatMessageList = List.of(systemMessage, userMessage);

        ResponseFormat responseFormat = ResponseFormat.builder()
            .type(ResponseFormatType.JSON)
            .jsonSchema(JsonSchemas.jsonSchemaFrom(AiOcrResponse.class).orElseThrow())
            .build();

        return ChatRequest.builder()
            .messages(chatMessageList)
            .responseFormat(responseFormat)
            .build();
    }

    private static void logPrompt(PromptTemplate promptTemplate) {
        if (log.isDebugEnabled()) {
            String systemPrompt = promptTemplate.toSystemPrompt();
            log.debug("System message: {}", systemPrompt);

            String userPrompt = promptTemplate.toUserPrompt();
            log.debug("User message: {}", userPrompt);
        }
    }
}
