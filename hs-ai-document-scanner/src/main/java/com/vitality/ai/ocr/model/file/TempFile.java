package com.vitality.ai.ocr.model.file;

import com.google.common.hash.Hashing;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

/**
 * File implementation that stores content in a temporary file on disk.
 * Suitable for large files to avoid memory consumption.
 */
public class TempFile implements FileHandle {
    private final String name;
    private final String contentType;
    private final Path tempPath;
    private String hash;

    public TempFile(String name, String contentType) throws IOException {
        this.name = name;
        this.contentType = contentType;
        this.tempPath = Files.createTempFile("upload-", ".tmp");
    }

    public TempFile(MultipartFile multipartFile) throws IOException {
        this(multipartFile.getOriginalFilename(), multipartFile.getContentType());
        try (InputStream inputStream = multipartFile.getInputStream()) {
            Files.copy(inputStream, tempPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    @Override
    public long getSize() {
        try {
            return Files.size(tempPath);
        } catch (IOException e) {
            return -1;
        }
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return Files.newInputStream(tempPath);
    }

    @Override
    public byte[] readAllBytes() throws IOException {
        return Files.readAllBytes(tempPath);
    }

    @Override
    public String getHash() {
        if (hash == null) {
            try {
                hash = Hashing.murmur3_128().hashBytes(readAllBytes()).toString();
            } catch (IOException e) {
                // ignore
            }
        }
        return hash;
    }

    @Override
    public void close() throws IOException {
        Files.deleteIfExists(tempPath);
    }

    @Override
    public String toString() {
        return "TempFile{" +
            "name='" + name + '\'' +
            ", contentType='" + contentType + '\'' +
            ", tempPath=" + tempPath +
            ", size=" + getSize() +
            '}';
    }
}
