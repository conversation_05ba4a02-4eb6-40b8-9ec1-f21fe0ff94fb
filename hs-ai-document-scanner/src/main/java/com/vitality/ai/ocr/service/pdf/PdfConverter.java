package com.vitality.ai.ocr.service.pdf;

import com.vitality.ai.ocr.model.file.FileHandle;

import java.io.IOException;

public interface PdfConverter {
    FileHandle convertToScannedPdf(FileHandle inputFile, PdfProcessingOptions options) throws IOException;

    FileHandle convertToImagesZip(FileHandle inputFile, PdfProcessingOptions options) throws IOException;

    FileHandle convertToScannedImagesZip(FileHandle inputFile, PdfProcessingOptions options) throws IOException;

    FileHandle convertToOcrOptimizedImagesZip(FileHandle inputFile, PdfProcessingOptions options) throws IOException;
}
