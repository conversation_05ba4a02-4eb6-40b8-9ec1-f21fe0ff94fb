package com.vitality.ai.ocr;

import java.util.ArrayList;

public class PromptBuilder {
    private final StringBuilder builder = new StringBuilder();

    private String systemContext;
    private ArrayList<String> instruction = new ArrayList<>();
    private String example;
    private String userInput;
    private String output;

    private PromptBuilder() {}

    public static PromptBuilder create() {
        return new PromptBuilder();
    }

    public PromptBuilder withSystemContext(String context) {
        this.systemContext = context;
        return this;
    }

    public PromptBuilder withOutput(String output) {
        this.output = output;
        return this;
    }

    public PromptBuilder withInstruction(String instruction) {
        this.instruction.add(instruction);
        return this;
    }

    public PromptBuilder withExample(String example) {
        this.example = example;
        return this;
    }

    public PromptBuilder withUserInput(String input) {
        this.userInput = input;
        return this;
    }

    public String build() {
        if (systemContext != null && !systemContext.isBlank()) {
            builder.append("System:\n").append(systemContext).append("\n\n");
        }
        if (instruction != null && !instruction.isEmpty()) {
            builder.append("Instructions:\n");
            for (String instr : instruction) {
                builder.append("- ").append(instr).append("\n");
            }
        }
        if (example != null && !example.isBlank()) {
            builder.append("Example:\n").append(example).append("\n\n");
        }
        if (userInput != null && !userInput.isBlank()) {
            builder.append("Input:\n").append(userInput).append("\n");
        }

        if (output != null && !output.isBlank()) {
            builder.append("Output:\n").append(output).append("\n");
        }

        return builder.toString().trim();
    }
}
