package com.vitality.ai.ocr.controller;

import com.vitality.ai.ocr.model.document.AiOcrResponse;
import com.vitality.ai.ocr.model.file.BufferedFile;
import com.vitality.ai.ocr.service.ocr.AiOcrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/api/document/scan")
@RequiredArgsConstructor
public class DocumentScannerController {

    private final AiOcrService aiOcrService;

    @PostMapping(path = "/assessment", consumes = "multipart/form-data")
    public ResponseEntity<AiOcrResponse> scanAssessment(
        @RequestParam(value = "promptId", defaultValue = "hra") String promptId,
        @RequestParam("file") MultipartFile file) throws IOException {

        BufferedFile bufferedFile = new BufferedFile(file);
        AiOcrResponse response = aiOcrService.performOcrForPdf(promptId, bufferedFile);

        return ResponseEntity.ok(response);
    }
}
