package com.vitality.ai.ocr.prompt.model.response;

import lombok.Data;

import java.io.Serializable;

/**
 * Class representing the applicant/patient information
 */
@Data
public class Applicant implements Serializable {
    private String patientName;
    private String patientId;
    private String dateOfBirth;
    private String assessmentDate;

    public static Applicant getDummy() {
        Applicant applicant = new Applicant();
        applicant.setPatientName("");
        applicant.setPatientId("");
        applicant.setDateOfBirth("");
        applicant.setAssessmentDate("");
        return applicant;
    }
}
