package com.vitality.ai.ocr.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.vitality.ai.ocr.model.document.AiOcrResponse;
import com.vitality.ai.ocr.service.json.JsonSchemaGenerator;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/schema")
public class SchemaController {

    private final JsonSchemaGenerator schemaGenerator;

    public SchemaController(JsonSchemaGenerator schemaGenerator) {
        this.schemaGenerator = schemaGenerator;
    }

    @GetMapping("/ocr-response")
    public ResponseEntity<JsonNode> getOcrResponseSchema() {
        try {
            JsonNode schema = schemaGenerator.generateSchema(AiOcrResponse.class);
            return ResponseEntity.ok(schema);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}
