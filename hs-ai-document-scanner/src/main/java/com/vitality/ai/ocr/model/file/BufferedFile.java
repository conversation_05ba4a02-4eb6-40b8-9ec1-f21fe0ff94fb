package com.vitality.ai.ocr.model.file;

import com.google.common.hash.Hashing;
import org.apache.commons.io.FileUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Objects;

public class BufferedFile implements FileHandle {
    private final String name;
    private final String contentType;
    private final byte[] content;
    private String hash;

    public BufferedFile(String name, String contentType, byte[] content) {
        this.name = name;
        this.contentType = contentType;
        this.content = content != null ? content.clone() : new byte[0];
    }

    public BufferedFile(MultipartFile multipartFile) throws IOException {
        this(multipartFile.getOriginalFilename(), multipartFile.getContentType(), multipartFile.getBytes());
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    @Override
    public long getSize() {
        return content.length;
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(content);
    }

    @Override
    public byte[] readAllBytes() {
        return content;
    }

    @Override
    public String getHash() {
        if (hash == null) {
            hash = Hashing.murmur3_128().hashBytes(readAllBytes()).toString();
        }
        return hash;
    }

    // Legacy method for backward compatibility
    public byte[] content() {
        return readAllBytes();
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        BufferedFile that = (BufferedFile) o;
        return Objects.equals(name, that.name) &&
            Arrays.equals(content, that.content) &&
            Objects.equals(contentType, that.contentType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, Arrays.hashCode(content), contentType);
    }

    @Override
    public String toString() {
        return "BufferedFile{" +
            "name='" + name + '\'' +
            ", contentType='" + contentType + '\'' +
            ", size='" + FileUtils.byteCountToDisplaySize(content.length) + '\'' +
            '}';
    }
}
