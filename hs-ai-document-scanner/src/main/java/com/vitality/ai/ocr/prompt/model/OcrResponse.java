package com.vitality.ai.ocr.prompt.model;

import java.util.Map;

import com.vitality.ai.ocr.prompt.model.response.MedicalForm;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OcrResponse {
    private String rawText;
    private MedicalForm medicalForm;
    private String status;
    private String message;
    private int pageCount;

}