spring:
  application:
    name: hs-ai-document-scanner
  config:
    use-legacy-processing: true
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      enabled: false
  web:
    resources:
      static-locations: classpath:/static
      cache:
        period: 0
  mvc:
    view:
      prefix: /
      suffix: .html
    static-path-pattern: /**

server:
  port: 34001
  servlet:
    context-path: /v3/ocr

openapi:
  servers: http://localhost:34001/v3/ocr
